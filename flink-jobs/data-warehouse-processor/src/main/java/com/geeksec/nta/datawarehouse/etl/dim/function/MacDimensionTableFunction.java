package com.geeksec.nta.datawarehouse.etl.dim.function;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import com.geeksec.common.utils.net.MacUtils;
import com.geeksec.common.utils.time.DateTimeUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * MAC维度表处理函数，用于生成符合dim_mac表结构的维度数据
 *
 * 使用Flink Backend State进行缓存管理，支持TTL自动清理
 *
 * <AUTHOR>
 */
@Slf4j
public class MacDimensionTableFunction extends ProcessFunction<Row, Row> {

    private static final long serialVersionUID = 1L;

    /**
     * MAC维度数据输出标签
     */
    public static final OutputTag<Row> MAC_DIM_TAG = new OutputTag<Row>("mac-dimension") {
    };

    private final String macFieldName;
    private final Duration ttl;

    /**
     * MAC维度数据缓存状态
     */
    private transient ValueState<Map<String, Object>> macDimensionState;

    /**
     * 构造函数
     *
     * @param macFieldName MAC地址字段名
     */
    public MacDimensionTableFunction(String macFieldName) {
        // 默认24小时TTL
        this(macFieldName, Duration.ofHours(24));
    }

    /**
     * 构造函数
     *
     * @param macFieldName MAC地址字段名
     * @param ttl          状态TTL时间
     */
    public MacDimensionTableFunction(String macFieldName, Duration ttl) {
        this.macFieldName = macFieldName;
        this.ttl = ttl;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 配置状态TTL
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(ttl)
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        // 初始化MAC维度状态
        ValueStateDescriptor<Map<String, Object>> macStateDescriptor = new ValueStateDescriptor<>("mac-dimension-state",
                Types.MAP(Types.STRING, Types.GENERIC(Object.class)));
        macStateDescriptor.enableTimeToLive(ttlConfig);
        macDimensionState = getRuntimeContext().getState(macStateDescriptor);
    }

    @Override
    public void processElement(Row value, Context ctx, Collector<Row> out) throws Exception {
        try {
            String macAddress = getStringFieldValue(value, macFieldName);
            if (macAddress == null || macAddress.trim().isEmpty()) {
                out.collect(value);
                return;
            }

            // 检查状态中是否已存在该MAC的维度数据
            Map<String, Object> existingMacInfo = macDimensionState.value();
            if (existingMacInfo == null) {
                // 首次遇到该MAC，创建维度数据
                Map<String, Object> macInfo = createMacDimensionInfo(macAddress, value);
                macDimensionState.update(macInfo);

                // 创建维度表记录并输出到侧输出流
                Row macDimensionRow = createDimensionRow(macAddress, macInfo);
                ctx.output(MAC_DIM_TAG, macDimensionRow);

                log.debug("创建新的MAC维度数据: {}", macAddress);
            }

            // 继续传递原始数据
            out.collect(value);

        } catch (Exception e) {
            log.error("处理MAC维度数据时发生错误: {}", e.getMessage(), e);
            out.collect(value);
        }
    }

    /**
     * 创建MAC维度信息
     *
     * @param macAddress MAC地址
     * @param value      原始数据Row，用于提取VLAN信息
     * @return MAC维度信息Map
     */
    private Map<String, Object> createMacDimensionInfo(String macAddress, Row value) {
        Map<String, Object> macInfo = new HashMap<>(8);

        // 使用MacUtils获取详细的MAC地址信息
        Map<String, Object> detailedMacInfo = MacUtils.getMacInfo(macAddress);
        macInfo.putAll(detailedMacInfo);

        // 提取VLAN信息
        String vlanInfo = extractVlanInfo(value);
        macInfo.put("vlan_info", vlanInfo);

        return macInfo;
    }

    /**
     * 提取VLAN信息
     *
     * @param value 原始数据Row
     * @return VLAN信息字符串，如果没有VLAN信息则返回空字符串
     */
    private String extractVlanInfo(Row value) {
        try {
            // 尝试从不同可能的字段中提取VLAN信息

            // 1. 直接查找vlan_id字段
            String vlanId = getStringFieldValue(value, "vlan_id");
            if (vlanId != null && !vlanId.trim().isEmpty()) {
                return formatVlanInfo("vlan_id", vlanId);
            }

            // 2. 查找vlan_tag字段
            String vlanTag = getStringFieldValue(value, "vlan_tag");
            if (vlanTag != null && !vlanTag.trim().isEmpty()) {
                return formatVlanInfo("vlan_tag", vlanTag);
            }

            // 3. 查找vlan字段
            String vlan = getStringFieldValue(value, "vlan");
            if (vlan != null && !vlan.trim().isEmpty()) {
                return formatVlanInfo("vlan", vlan);
            }

            // 4. 从扩展JSON字段中提取VLAN信息
            String extJson = getStringFieldValue(value, "ext_json");
            if (extJson != null && !extJson.trim().isEmpty()) {
                String vlanFromJson = extractVlanFromJson(extJson);
                if (vlanFromJson != null && !vlanFromJson.trim().isEmpty()) {
                    return vlanFromJson;
                }
            }

            // 5. 从协议栈信息中推断VLAN（如果包含802.1Q标签）
            String protocolStack = getStringFieldValue(value, "protocol_stack");
            if (protocolStack != null && protocolStack.contains("802.1Q")) {
                return "802.1Q_tagged";
            }

            log.debug("未找到VLAN信息，MAC地址: {}", getStringFieldValue(value, macFieldName));
            return "";

        } catch (Exception e) {
            log.warn("提取VLAN信息时发生错误: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 格式化VLAN信息
     *
     * @param type VLAN类型
     * @param value VLAN值
     * @return 格式化后的VLAN信息
     */
    private String formatVlanInfo(String type, String value) {
        return String.format("%s:%s", type, value);
    }

    /**
     * 从JSON字符串中提取VLAN信息
     *
     * @param jsonStr JSON字符串
     * @return VLAN信息，如果没有找到则返回null
     */
    private String extractVlanFromJson(String jsonStr) {
        try {
            // 简单的JSON解析，查找VLAN相关字段
            // 这里使用简单的字符串匹配，实际项目中可以使用JSON库
            if (jsonStr.contains("\"vlan_id\"")) {
                int start = jsonStr.indexOf("\"vlan_id\"");
                int colonIndex = jsonStr.indexOf(":", start);
                if (colonIndex != -1) {
                    int valueStart = colonIndex + 1;
                    // 跳过空格和引号
                    while (valueStart < jsonStr.length() &&
                           (jsonStr.charAt(valueStart) == ' ' || jsonStr.charAt(valueStart) == '"')) {
                        valueStart++;
                    }
                    int valueEnd = valueStart;
                    while (valueEnd < jsonStr.length() &&
                           jsonStr.charAt(valueEnd) != ',' &&
                           jsonStr.charAt(valueEnd) != '}' &&
                           jsonStr.charAt(valueEnd) != '"') {
                        valueEnd++;
                    }
                    if (valueEnd > valueStart) {
                        String vlanId = jsonStr.substring(valueStart, valueEnd).trim();
                        return formatVlanInfo("vlan_id", vlanId);
                    }
                }
            }

            if (jsonStr.contains("\"vlan\"")) {
                int start = jsonStr.indexOf("\"vlan\"");
                int colonIndex = jsonStr.indexOf(":", start);
                if (colonIndex != -1) {
                    int valueStart = colonIndex + 1;
                    while (valueStart < jsonStr.length() &&
                           (jsonStr.charAt(valueStart) == ' ' || jsonStr.charAt(valueStart) == '"')) {
                        valueStart++;
                    }
                    int valueEnd = valueStart;
                    while (valueEnd < jsonStr.length() &&
                           jsonStr.charAt(valueEnd) != ',' &&
                           jsonStr.charAt(valueEnd) != '}' &&
                           jsonStr.charAt(valueEnd) != '"') {
                        valueEnd++;
                    }
                    if (valueEnd > valueStart) {
                        String vlan = jsonStr.substring(valueStart, valueEnd).trim();
                        return formatVlanInfo("vlan", vlan);
                    }
                }
            }

        } catch (Exception e) {
            log.warn("解析JSON中的VLAN信息时发生错误: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 创建符合维度表结构的MAC维度记录
     *
     * @param macAddress MAC地址
     * @param macInfo    MAC信息
     * @return 符合dim_mac表结构的Row
     */
    private Row createDimensionRow(String macAddress, Map<String, Object> macInfo) {
        Row dimensionRow = Row.withNames();

        // 设置主键字段
        dimensionRow.setField("mac", macAddress);

        // 设置丰富化字段
        dimensionRow.setField("vendor", macInfo.get("vendor"));
        dimensionRow.setField("oui", macInfo.get("oui"));
        dimensionRow.setField("is_locally_administered", macInfo.get("is_locally_administered"));
        dimensionRow.setField("is_multicast", macInfo.get("is_multicast"));
        dimensionRow.setField("is_potentially_randomized", macInfo.get("is_potentially_randomized"));
        dimensionRow.setField("is_virtualized", macInfo.get("is_virtualized"));

        // 设置VLAN信息字段
        dimensionRow.setField("vlan_info", macInfo.get("vlan_info"));

        // 设置聚合字段初始值
        dimensionRow.setField("total_bytes", 0L);
        dimensionRow.setField("total_packets", 0L);
        dimensionRow.setField("total_bytes_sent", 0L);
        dimensionRow.setField("total_bytes_received", 0L);
        dimensionRow.setField("total_packets_sent", 0L);
        dimensionRow.setField("total_packets_received", 0L);
        dimensionRow.setField("session_count", 1);
        dimensionRow.setField("total_duration", 0L);

        // 设置时间字段
        String currentTime = LocalDateTime.now().format(DateTimeUtils.DEFAULT_DATE_TIME_FORMATTER);
        dimensionRow.setField("first_seen", currentTime);
        dimensionRow.setField("last_seen", currentTime);
        dimensionRow.setField("create_time", currentTime);
        dimensionRow.setField("update_time", currentTime);

        return dimensionRow;
    }

    /**
     * 从Row中获取字符串字段值
     *
     * @param row       Row对象
     * @param fieldName 字段名
     * @return 字段值，如果不存在或类型不匹配则返回null
     */
    private String getStringFieldValue(Row row, String fieldName) {
        try {
            Set<String> fieldNames = row.getFieldNames(true);
            if (fieldNames != null && fieldNames.contains(fieldName)) {
                Object fieldValue = row.getField(fieldName);
                if (fieldValue instanceof String) {
                    return (String) fieldValue;
                }
            }
        } catch (Exception e) {
            log.warn("获取字符串字段值时发生错误，字段名: {}, 错误: {}", fieldName, e.getMessage());
        }
        return null;
    }
}
