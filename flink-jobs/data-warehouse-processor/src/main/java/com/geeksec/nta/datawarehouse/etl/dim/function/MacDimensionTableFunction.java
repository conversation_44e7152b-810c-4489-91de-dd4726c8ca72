package com.geeksec.nta.datawarehouse.etl.dim.function;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import com.geeksec.common.utils.net.MacUtils;
import com.geeksec.common.utils.time.DateTimeUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * MAC维度表处理函数，用于生成符合dim_mac表结构的维度数据
 *
 * 使用Flink Backend State进行缓存管理，支持TTL自动清理
 *
 * <AUTHOR>
 */
@Slf4j
public class MacDimensionTableFunction extends ProcessFunction<Row, Row> {

    private static final long serialVersionUID = 1L;

    /**
     * MAC维度数据输出标签
     */
    public static final OutputTag<Row> MAC_DIM_TAG = new OutputTag<Row>("mac-dimension") {
    };

    private final String macFieldName;
    private final Duration ttl;

    /**
     * MAC维度数据缓存状态
     */
    private transient ValueState<Map<String, Object>> macDimensionState;

    /**
     * 构造函数
     *
     * @param macFieldName MAC地址字段名
     */
    public MacDimensionTableFunction(String macFieldName) {
        // 默认24小时TTL
        this(macFieldName, Duration.ofHours(24));
    }

    /**
     * 构造函数
     *
     * @param macFieldName MAC地址字段名
     * @param ttl          状态TTL时间
     */
    public MacDimensionTableFunction(String macFieldName, Duration ttl) {
        this.macFieldName = macFieldName;
        this.ttl = ttl;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 配置状态TTL
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(ttl)
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        // 初始化MAC维度状态
        ValueStateDescriptor<Map<String, Object>> macStateDescriptor = new ValueStateDescriptor<>("mac-dimension-state",
                Types.MAP(Types.STRING, Types.GENERIC(Object.class)));
        macStateDescriptor.enableTimeToLive(ttlConfig);
        macDimensionState = getRuntimeContext().getState(macStateDescriptor);
    }

    @Override
    public void processElement(Row value, Context ctx, Collector<Row> out) throws Exception {
        try {
            String macAddress = getStringFieldValue(value, macFieldName);
            if (macAddress == null || macAddress.trim().isEmpty()) {
                out.collect(value);
                return;
            }

            // 检查状态中是否已存在该MAC的维度数据
            Map<String, Object> existingMacInfo = macDimensionState.value();
            if (existingMacInfo == null) {
                // 首次遇到该MAC，创建维度数据
                Map<String, Object> macInfo = createMacDimensionInfo(macAddress);
                macDimensionState.update(macInfo);

                // 创建维度表记录并输出到侧输出流
                Row macDimensionRow = createDimensionRow(macAddress, macInfo);
                ctx.output(MAC_DIM_TAG, macDimensionRow);

                log.debug("创建新的MAC维度数据: {}", macAddress);
            }

            // 继续传递原始数据
            out.collect(value);

        } catch (Exception e) {
            log.error("处理MAC维度数据时发生错误: {}", e.getMessage(), e);
            out.collect(value);
        }
    }

    /**
     * 创建MAC维度信息
     *
     * @param macAddress MAC地址
     * @return MAC维度信息Map
     */
    private Map<String, Object> createMacDimensionInfo(String macAddress) {
        Map<String, Object> macInfo = new HashMap<>(8);

        // 使用MacUtils获取详细的MAC地址信息
        Map<String, Object> detailedMacInfo = MacUtils.getMacInfo(macAddress);
        macInfo.putAll(detailedMacInfo);

        return macInfo;
    }



    /**
     * 创建符合维度表结构的MAC维度记录
     *
     * @param macAddress MAC地址
     * @param macInfo    MAC信息
     * @return 符合dim_mac表结构的Row
     */
    private Row createDimensionRow(String macAddress, Map<String, Object> macInfo) {
        Row dimensionRow = Row.withNames();

        // 设置主键字段
        dimensionRow.setField("mac", macAddress);

        // 设置丰富化字段
        dimensionRow.setField("vendor", macInfo.get("vendor"));
        dimensionRow.setField("oui", macInfo.get("oui"));
        dimensionRow.setField("is_locally_administered", macInfo.get("is_locally_administered"));
        dimensionRow.setField("is_multicast", macInfo.get("is_multicast"));
        dimensionRow.setField("is_potentially_randomized", macInfo.get("is_potentially_randomized"));
        dimensionRow.setField("is_virtualized", macInfo.get("is_virtualized"));

        // 设置聚合字段初始值
        dimensionRow.setField("total_bytes", 0L);
        dimensionRow.setField("total_packets", 0L);
        dimensionRow.setField("total_bytes_sent", 0L);
        dimensionRow.setField("total_bytes_received", 0L);
        dimensionRow.setField("total_packets_sent", 0L);
        dimensionRow.setField("total_packets_received", 0L);
        dimensionRow.setField("session_count", 1);
        dimensionRow.setField("total_duration", 0L);

        // 设置时间字段
        String currentTime = LocalDateTime.now().format(DateTimeUtils.DEFAULT_DATE_TIME_FORMATTER);
        dimensionRow.setField("first_seen", currentTime);
        dimensionRow.setField("last_seen", currentTime);
        dimensionRow.setField("create_time", currentTime);
        dimensionRow.setField("update_time", currentTime);

        return dimensionRow;
    }

    /**
     * 从Row中获取字符串字段值
     *
     * @param row       Row对象
     * @param fieldName 字段名
     * @return 字段值，如果不存在或类型不匹配则返回null
     */
    private String getStringFieldValue(Row row, String fieldName) {
        try {
            Set<String> fieldNames = row.getFieldNames(true);
            if (fieldNames != null && fieldNames.contains(fieldName)) {
                Object fieldValue = row.getField(fieldName);
                if (fieldValue instanceof String) {
                    return (String) fieldValue;
                }
            }
        } catch (Exception e) {
            log.warn("获取字符串字段值时发生错误，字段名: {}, 错误: {}", fieldName, e.getMessage());
        }
        return null;
    }
}
