package com.geeksec.nta.datawarehouse.etl.dim.function;

import java.time.Duration;
import java.util.Set;

import org.apache.flink.types.Row;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MAC维度表处理函数测试类
 *
 * <AUTHOR>
 */
class MacDimensionTableFunctionTest {

    private MacDimensionTableFunction function;

    @BeforeEach
    void setUp() {
        function = new MacDimensionTableFunction("src_mac", Duration.ofMinutes(30));
    }

    @Test
    void testExtractVlanInfoFromDirectField() {
        // 测试从直接字段提取VLAN信息
        Row row = Row.withNames();
        row.setField("src_mac", "00:11:22:33:44:55");
        row.setField("vlan_id", "100");

        // 使用反射调用私有方法进行测试
        String vlanInfo = extractVlanInfoUsingReflection(row);
        assertEquals("vlan_id:100", vlanInfo);
    }

    @Test
    void testExtractVlanInfoFromVlanTag() {
        // 测试从vlan_tag字段提取VLAN信息
        Row row = Row.withNames();
        row.setField("src_mac", "00:11:22:33:44:55");
        row.setField("vlan_tag", "200");

        String vlanInfo = extractVlanInfoUsingReflection(row);
        assertEquals("vlan_tag:200", vlanInfo);
    }

    @Test
    void testExtractVlanInfoFromJson() {
        // 测试从JSON字段提取VLAN信息
        Row row = Row.withNames();
        row.setField("src_mac", "00:11:22:33:44:55");
        row.setField("ext_json", "{\"vlan_id\": \"300\", \"other_field\": \"value\"}");

        String vlanInfo = extractVlanInfoUsingReflection(row);
        assertEquals("vlan_id:300", vlanInfo);
    }

    @Test
    void testExtractVlanInfoFromProtocolStack() {
        // 测试从协议栈信息推断VLAN
        Row row = Row.withNames();
        row.setField("src_mac", "00:11:22:33:44:55");
        row.setField("protocol_stack", "Ethernet/802.1Q/IP/TCP");

        String vlanInfo = extractVlanInfoUsingReflection(row);
        assertEquals("802.1Q_tagged", vlanInfo);
    }

    @Test
    void testExtractVlanInfoNoVlan() {
        // 测试没有VLAN信息的情况
        Row row = Row.withNames();
        row.setField("src_mac", "00:11:22:33:44:55");
        row.setField("other_field", "value");

        String vlanInfo = extractVlanInfoUsingReflection(row);
        assertEquals("", vlanInfo);
    }

    @Test
    void testExtractVlanInfoPriority() {
        // 测试VLAN信息提取的优先级（vlan_id > vlan_tag > vlan > ext_json > protocol_stack）
        Row row = Row.withNames();
        row.setField("src_mac", "00:11:22:33:44:55");
        row.setField("vlan_id", "100");
        row.setField("vlan_tag", "200");
        row.setField("vlan", "300");
        row.setField("ext_json", "{\"vlan_id\": \"400\"}");
        row.setField("protocol_stack", "Ethernet/802.1Q/IP/TCP");

        String vlanInfo = extractVlanInfoUsingReflection(row);
        assertEquals("vlan_id:100", vlanInfo); // 应该优先使用vlan_id
    }

    @Test
    void testFormatVlanInfo() {
        String formatted = formatVlanInfoUsingReflection("vlan_id", "100");
        assertEquals("vlan_id:100", formatted);
    }

    @Test
    void testExtractVlanFromJsonComplexCase() {
        // 测试复杂JSON的VLAN提取
        String jsonStr = "{\"timestamp\": 1234567890, \"vlan_id\": \"500\", \"src_ip\": \"***********\"}";
        String vlanInfo = extractVlanFromJsonUsingReflection(jsonStr);
        assertEquals("vlan_id:500", vlanInfo);
    }

    @Test
    void testExtractVlanFromJsonWithVlanField() {
        // 测试从JSON中提取vlan字段
        String jsonStr = "{\"vlan\": \"600\", \"other\": \"data\"}";
        String vlanInfo = extractVlanFromJsonUsingReflection(jsonStr);
        assertEquals("vlan:600", vlanInfo);
    }

    // 使用反射调用私有方法的辅助方法
    private String extractVlanInfoUsingReflection(Row row) {
        try {
            var method = MacDimensionTableFunction.class.getDeclaredMethod("extractVlanInfo", Row.class);
            method.setAccessible(true);
            return (String) method.invoke(function, row);
        } catch (Exception e) {
            throw new RuntimeException("反射调用失败", e);
        }
    }

    private String formatVlanInfoUsingReflection(String type, String value) {
        try {
            var method = MacDimensionTableFunction.class.getDeclaredMethod("formatVlanInfo", String.class, String.class);
            method.setAccessible(true);
            return (String) method.invoke(function, type, value);
        } catch (Exception e) {
            throw new RuntimeException("反射调用失败", e);
        }
    }

    private String extractVlanFromJsonUsingReflection(String jsonStr) {
        try {
            var method = MacDimensionTableFunction.class.getDeclaredMethod("extractVlanFromJson", String.class);
            method.setAccessible(true);
            return (String) method.invoke(function, jsonStr);
        } catch (Exception e) {
            throw new RuntimeException("反射调用失败", e);
        }
    }
}
