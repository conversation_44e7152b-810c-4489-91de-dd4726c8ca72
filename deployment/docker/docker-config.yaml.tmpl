host:
  # 主机名
  hostname: {{ (datasource "config").host.hostname }}
  # 主机IP
  ip: {{ (datasource "config").host.ip }}
  # 持久数据目录，位于系统盘，其中的数据不会迁移
  permanent_path: /opt/GeekSecData
  # 可变数据目录，与数据盘挂载关联，数据可能会随着磁盘的更换而迁移
  removable_path: /data
  # Docker overlay网络
  overlay_network: bdnet
  # 调试模式
  debug: false
zoo:
  # 是否使用 Docker 托管的 Volume
  use_docker_volume: false
  # 存放数据的目录（当 use_docker_volume 为 false 时生效）
  data_dir: zoo_cluster
kafka:
  # 是否使用 Docker 托管的 Volume
  use_docker_volume: false
  # 存放数据的目录（当 use_docker_volume 为 false 时生效）
  data_dir: kafka
mysql:
  # 是否使用 Docker 托管的 Volume
  use_docker_volume: false
  # 存放数据的目录（当 use_docker_volume 为 false 时生效）
  data_dir: mysql
es:
  # 是否使用 Docker 托管的 Volume
  use_docker_volume: false
  # 子目录名(父目录为数据盘)
  data_dir: .es
  # ES主节点(分布式条件需要人为配置下，涉及到若干主机名)
  initial_master_nodes: {{ (datasource "config").host.hostname }}-es
  # 分配给 ES 的内存（单位：GB）
  memory: 32
flink:
  # 是否使用 Docker 托管的 Volume
  use_docker_volume: false
  # 存放数据的目录（当 use_docker_volume 为 false 时生效）
  data_dir: flink
  # JobManager的内存分配（单位：GB）
  jobmanager_memory: 16
  # TaskManager的内存分配（单位：GB）
  taskmanager_memory: 128
  # TaskManager的副本数
  taskmanager_replicas: 2
  # TaskManager的task slots数
  taskmanager_taskslots: 512
redis:
  # 是否使用 Docker 托管的 Volume
  use_docker_volume: false
  # 存放数据的目录（当 use_docker_volume 为 false 时生效）
  data_dir: redis
