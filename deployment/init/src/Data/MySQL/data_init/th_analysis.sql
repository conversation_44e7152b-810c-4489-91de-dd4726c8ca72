SET
GLOBAL group_concat_max_len=18446744073709551615;
SET SESSION group_concat_max_len = 18446744073709551615;

use
th_analysis;

-- app_pro_value: table
DROP TABLE IF EXISTS `app_pro_value`;
CREATE TABLE `app_pro_value`
(
    `pro_id`    int(5) NOT NULL COMMENT '协议ID ---应用协议知识库---',
    `pro_value` longtext NOT NULL COMMENT '协议名称1',
    `pro_name`  longtext COMMENT '协议名称2',
    `pro_type`  longtext COMMENT '协议类型',
    `pro_exp`   longtext COMMENT '协议说明',
    `type`      int(2) DEFAULT '1' COMMENT '协议类型 1 为连接 2 为单包 3 tcp/udp负载',
    PRIMARY KEY (`pro_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- single_pro_value: table
DROP TABLE IF EXISTS `single_pro_value`;
CREATE TABLE `single_pro_value`
(
    `pro_id`    int(10) NOT NULL,
    `pro_value` varchar(255) NOT NULL,
    `pro_name`  varchar(255) NOT NULL,
    `pro_type`  varchar(255) NOT NULL,
    `pro_exp`   varchar(20000) DEFAULT '',
    PRIMARY KEY (`pro_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_alarm_num: table
DROP TABLE IF EXISTS `tb_alarm_num`;
CREATE TABLE `tb_alarm_num`
(
    `hour_times` int(10) NOT NULL COMMENT '//时间按一小时为粒度',
    `tag_family` int(13) NOT NULL COMMENT '//0 其他 1 侦察探测 2 武器投递 3 攻击突防 4 命令控制 5。控守操作',
    `num`        int(11) NOT NULL DEFAULT '0' COMMENT '//当前时间段的告警数量',
    `tkey`       bigint(20) unsigned NOT NULL,
    PRIMARY KEY (`tkey`),
    KEY          `hour_times` (`hour_times`),
    KEY          `tag_family` (`tag_family`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_app_pro_info: table
DROP TABLE IF EXISTS `tb_app_pro_info`;
CREATE TABLE `tb_app_pro_info`
(
    `id`     int(11) NOT NULL AUTO_INCREMENT COMMENT '//自增ID ',
    `port`   int(5) DEFAULT NULL COMMENT '端口',
    `appid`  int(6) DEFAULT NULL COMMENT 'app ID',
    `Ippro`  int(5) DEFAULT NULL COMMENT '17 udp  6 tcp ',
    `remark` longtext NOT NULL,
    PRIMARY KEY (`id`),
    KEY      `port` (`port`) USING BTREE,
    KEY      `appid` (`appid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8;

-- tb_attack_type: table
DROP TABLE IF EXISTS `tb_attack_type`;
CREATE TABLE `tb_attack_type`
(
    `attack_type_id`   int(10) NOT NULL COMMENT '//告警类型id',
    `attack_type_name` varchar(64) NOT NULL COMMENT '//告警类型名称',
    `remark`           varchar(2048) DEFAULT '' COMMENT '//备注 , 说明',
    `id`               bigint(11) NOT NULL AUTO_INCREMENT COMMENT '//自增id  ---告警表---',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8;

-- tb_atlas_history: table
DROP TABLE IF EXISTS `tb_atlas_history`;
CREATE TABLE `tb_atlas_history`
(
    `id`                int(11) NOT NULL AUTO_INCREMENT,
    `atlas_type`        int(1) NOT NULL COMMENT '探索类型（1-目标ID检索；2-关键词检索；3-子图遍历；）',
    `atlas_condition`   text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '探索条件',
    `created_time`      datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `last_updated_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 436 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


-- tb_batch_plugin: table
DROP TABLE IF EXISTS `tb_batch_plugin`;
CREATE TABLE `tb_batch_plugin`
(
    `id`             int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '//自增ID ---任务信息表---',
    `batch_id`       int(11) unsigned NOT NULL COMMENT '//批次ID',
    `plugin_id`      int(11) unsigned NOT NULL COMMENT '//插件ID',
    `should_log_def` int(11) unsigned NOT NULL COMMENT '//开启的插件iD默认值 ， 1 为开启 0  位关闭',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8;

-- tb_ddos_config: table
DROP TABLE IF EXISTS `tb_ddos_config`;
CREATE TABLE `tb_ddos_config`
(
    `id`           bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID ',
    `task_id`      int(11) NOT NULL COMMENT '任务ID',
    `ip`           varchar(255) NOT NULL COMMENT 'ip',
    `doss_json`    longtext     NOT NULL COMMENT 'json',
    `created_time` int(10) NOT NULL COMMENT '创建时间',
    `updated_time` int(10) NOT NULL COMMENT '修改时间',
    `hash`         varchar(255) NOT NULL COMMENT 'ddos_json的hash，用于校验重复',
    `state`        int(11) NOT NULL DEFAULT '1',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_ddos_state: table
DROP TABLE IF EXISTS `tb_ddos_state`;
CREATE TABLE `tb_ddos_state`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID -- ddos 配置---',
    `task_id`         int(11) NOT NULL COMMENT '任务ID',
    `state`           int(1) NOT NULL COMMENT '0表示停用，1表示开启',
    `ddos_param_json` text NOT NULL COMMENT '{"TimeInterval_Judge": 60, "BasicLine_bps": 1000, "BasicLine_PacketNum": 600, "CheckSum": [256, 256], "DDOS_CheckSum": [4, 4], "DDOS_SYN": 4, "DDOS_FIN": 4, "DDOS_DNS": 4, "DDOS_ICMP": 4, "DDOS_IGMP": 4, "DDOS_UDP": 4, "DDOS_Frag": 4, "DDOS_Multicast": 4, "MaxOffset_IP": 1250}，对应字段意思：',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1015 DEFAULT CHARSET=utf8;

INSERT INTO th_analysis.tb_ddos_state (id, task_id, state, ddos_param_json)
VALUES (1001, 1, 1,
        '{"Type":["IntraIP","LegalIP","All"],"IPv4Num":500000,"IPv6Num":1000,"RenewTime":6,"Param":{"TimeInterval_Judge":6,"BasicLine_bps":1000,"BasicLine_PacketNum":600,"CheckSum":[256,256],"DDOS_CheckSum":[4,4],"DDOS_SYN":4,"DDOS_FIN":4,"DDOS_DNS":4,"DDOS_ICMP":4,"DDOS_IGMP":4,"DDOS_UDP":4,"DDOS_Frag":4,"DDOS_Multicast":4,"MaxOffset_IP":1250}}');
INSERT INTO th_analysis.tb_ddos_state (id, task_id, state, ddos_param_json)
VALUES (1002, 0, 1,
        '{"Type":["IntraIP","LegalIP","All"],"IPv4Num":500000,"IPv6Num":1000,"RenewTime":6,"Param":{"TimeInterval_Judge":6,"BasicLine_bps":1000,"BasicLine_PacketNum":600,"CheckSum":[256,256],"DDOS_CheckSum":[4,4],"DDOS_SYN":4,"DDOS_FIN":4,"DDOS_DNS":4,"DDOS_ICMP":4,"DDOS_IGMP":4,"DDOS_UDP":4,"DDOS_Frag":4,"DDOS_Multicast":4,"MaxOffset_IP":1250}}');

-- tb_ddos_statistics_info: table
DROP TABLE IF EXISTS `tb_ddos_statistics_info`;
CREATE TABLE `tb_ddos_statistics_info`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID ---ddos过滤报数,统计信息,按时间统计---',
    `ddos_type`   int(10) NOT NULL,
    `task_id`     int(11) NOT NULL COMMENT '任务ID',
    `packet_num`  int(11) NOT NULL,
    `create_time` int(10) NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY           `ddos_statistics_type` (`ddos_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_device_id_to_ip: table
DROP TABLE IF EXISTS `tb_device_id_to_ip`;
CREATE TABLE `tb_device_id_to_ip`
(
    `device_id` varchar(64) NOT NULL,
    `ip`        varchar(64) NOT NULL COMMENT 'IP ',
    PRIMARY KEY (`device_id`),
    KEY         `targat_id` (`ip`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- tb_dns_server: table
DROP TABLE IF EXISTS `tb_dns_server`;
CREATE TABLE `tb_dns_server`
(
    `tkey`         bigint(20) unsigned NOT NULL,
    `ip`           varchar(64)   NOT NULL COMMENT 'IP ',
    `created_time` int(11) NOT NULL,
    `source`       int(11) NOT NULL COMMENT ' 来源  ',
    `client_dis`   varchar(2048) NOT NULL COMMENT ' clien',
    `task_id`      int(11) NOT NULL COMMENT '任务ID',
    PRIMARY KEY (`tkey`),
    KEY            `targat_id` (`ip`) USING BTREE,
    KEY            `task_id` (`task_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- tb_domain_alexa: table
DROP TABLE IF EXISTS `tb_domain_alexa`;
CREATE TABLE `tb_domain_alexa`
(
    `domain`       varchar(200) DEFAULT NULL,
    `alexa_rank`   int(20) DEFAULT NULL,
    `created_time` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_domain_whois: table
DROP TABLE IF EXISTS `tb_domain_whois`;
CREATE TABLE `tb_domain_whois`
(
    `domain`       varchar(200) NOT NULL COMMENT '域名名称',
    `whois`        varchar(200) DEFAULT NULL COMMENT '机构',
    `created_time` timestamp NULL DEFAULT NULL COMMENT '记录创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_download_task: table
DROP TABLE IF EXISTS `tb_download_task`;
CREATE TABLE `tb_download_task`
(
    `id`           int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id`      int(11) NOT NULL COMMENT '用户名id',
    `path`         varchar(255)  DEFAULT NULL COMMENT '文件路径',
    `query`        text COMMENT 'ES 下载 检索条件',
    `show_query`   varchar(2048) DEFAULT NULL COMMENT '前端展示条件',
    `type`         int(11) NOT NULL DEFAULT '0' COMMENT '全量下载为1，部分下载为0',
    `session_id`   text COMMENT 'session 列表信息 ',
    `state`        int(3) NOT NULL DEFAULT '0' COMMENT '0 准备数据 1可下载 2重新下载 3已删除 4待删除',
    `created_time` int(11) unsigned NOT NULL,
    `end_time`     int(11) unsigned DEFAULT NULL COMMENT '数据存储时间',
    `status`       int(3) NOT NULL DEFAULT '1' COMMENT '数据状态 0 删除 1存在',
    `task_id`      varchar(200)  DEFAULT NULL COMMENT '任务ID（数组）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8;

-- tb_download_task_register: table
DROP TABLE IF EXISTS `tb_download_task_register`;
CREATE TABLE `tb_download_task_register`
(
    `id`             int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id`        int(11) NOT NULL COMMENT '用户id',
    `path`           varchar(255) DEFAULT NULL COMMENT '日志保存路径',
    `query`          text NOT NULL COMMENT '查询条件',
    `type`           int(11) NOT NULL COMMENT '日志状态，1 待执行，2 准备数据，3 待下载，4 已删除，-1错误',
    `download_count` int(11) NOT NULL DEFAULT '0' COMMENT '下载次数',
    `delete_time`    int(11) DEFAULT NULL COMMENT '删除时间',
    `update_time`    int(11) DEFAULT NULL COMMENT '修改时间',
    `create_time`    int(11) DEFAULT NULL COMMENT '创建时间',
    `task_type`      int(3) NOT NULL DEFAULT '1' COMMENT '1 会话分析、2 会话聚合、3 元数据_SSL、4 元数据_HTTP、5 元数据_DNS ',
    `status`         int(3) NOT NULL DEFAULT '1' COMMENT '0 已删除 1 存在',
    `error_msg`      varchar(255) DEFAULT NULL COMMENT '任务失败的原因',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=52 DEFAULT CHARSET=utf8;

-- tb_es_field: table
DROP TABLE IF EXISTS `tb_es_field`;
CREATE TABLE `tb_es_field`
(
    `id`       int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `es_field` text NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

-- tb_filter_config: table
DROP TABLE IF EXISTS `tb_filter_config`;
CREATE TABLE `tb_filter_config`
(
    `id`           bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID ---fifter 筛选配置---',
    `task_id`      int(11) NOT NULL COMMENT '任务ID',
    `ip`           varchar(255) NOT NULL COMMENT 'ip',
    `filter_json`  longtext     NOT NULL COMMENT 'json',
    `created_time` int(10) NOT NULL COMMENT '创建时间',
    `updated_time` int(10) NOT NULL COMMENT '更新时间',
    `hash`         varchar(255) NOT NULL COMMENT 'filter_json的hash，用于校验重复',
    `type`         tinyint(4) NOT NULL DEFAULT '0' COMMENT '0  端口  1 ippro  2 网段',
    `status`       tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态  1:正常  0:删除',
    PRIMARY KEY (`id`),
    KEY            `idx_task_id` (`task_id`),
    KEY            `idx_hash` (`hash`)
) ENGINE=InnoDB AUTO_INCREMENT=10004 DEFAULT CHARSET=utf8;

-- tb_filter_state: table
DROP TABLE IF EXISTS `tb_filter_state`;
CREATE TABLE `tb_filter_state`
(
    `id`      int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID ---筛选 fifter 配置---',
    `task_id` int(11) NOT NULL COMMENT '任务ID',
    `state`   int(1) NOT NULL COMMENT '0表示保留，1表示丢弃',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;

-- tb_filter_statistics_info: table
DROP TABLE IF EXISTS `tb_filter_statistics_info`;
CREATE TABLE `tb_filter_statistics_info`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID ---筛选规则统计---',
    `filter_id`   int(11) NOT NULL COMMENT '过滤ID',
    `batch_id`    int(11) NOT NULL COMMENT '批次ID',
    `packet_num`  int(11) NOT NULL COMMENT '过滤的包数',
    `bytes`       bigint(20) unsigned NOT NULL COMMENT '过滤掉的字节数',
    `create_time` int(10) NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_forensics_white_list: table
DROP TABLE IF EXISTS `tb_forensics_white_list`;
CREATE TABLE `tb_forensics_white_list`
(
    `id`                      bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID ---规则表---',
    `server_ip`               varchar(255) NOT NULL,
    `port`                    varchar(255) NOT NULL,
    `app_id`                  int(11) NOT NULL,
    `rule_id`                 int(11) NOT NULL COMMENT '规则ID',
    `rule_level`              int(11) NOT NULL COMMENT '规则级别',
    `rule_name`               text         NOT NULL COMMENT '规则名称',
    `rule_desc`               text         NOT NULL COMMENT '规则描述',
    `rule_state`              varchar(255) NOT NULL DEFAULT '生效' COMMENT '规则状态',
    `rule_size`               bigint(20) unsigned NOT NULL COMMENT '数据量',
    `total_sum_bytes`         bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '命中数据总量',
    `last_size_time`          int(10) NOT NULL COMMENT '最新命中时间',
    `capture_mode`            int(11) NOT NULL COMMENT '采集模式',
    `rule_json`               longtext     NOT NULL COMMENT 'json字符串',
    `created_time`            int(10) NOT NULL COMMENT '创建时间',
    `updated_time`            int(10) NOT NULL COMMENT '修改时间',
    `rule_hash`               varchar(255) NOT NULL COMMENT '规则hash',
    `rule_family`             int(3) DEFAULT '0' COMMENT '0 其他 1 侦察探测 2 武器投递 3 攻击突防 4 命令控制 5。控守操作',
    `byte_ps`                 bigint(20) NOT NULL COMMENT '每秒限速',
    `save_bytes`              bigint(20) NOT NULL COMMENT '存储大小，负数不限',
    `pb_drop`                 int(11) NOT NULL COMMENT 'pb留存',
    `pcap_drop`               int(11) NOT NULL COMMENT 'pcap留存',
    `lib_respond_open`        int(11) NOT NULL COMMENT '开启动态库响应',
    `lib_respond_lib`         varchar(255) NOT NULL COMMENT '库路径',
    `lib_respond_config`      varchar(255) NOT NULL COMMENT '库配置路径',
    `lib_respond_session_end` bigint(20) unsigned NOT NULL COMMENT '末尾响应',
    `lib_respond_pkt_num`     bigint(20) NOT NULL COMMENT '包数',
    `task_id`                 int(10) NOT NULL COMMENT '任务ID',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `server_ip_port_app_id` (`server_ip`,`port`,`app_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_inside_cert: table
DROP TABLE IF EXISTS `tb_inside_cert`;
CREATE TABLE `tb_inside_cert`
(
    `cert_sha1` varchar(64) NOT NULL COMMENT '证书sha1 //内部证书表',
    `task_id`   int(11) NOT NULL COMMENT '任务ID',
    `link_ip`   longtext    NOT NULL COMMENT '关联ip',
    `remark`    longtext    NOT NULL COMMENT '备注',
    PRIMARY KEY (`cert_sha1`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_inside_domain: table
DROP TABLE IF EXISTS `tb_inside_domain`;
CREATE TABLE `tb_inside_domain`
(
    `domain_name` varchar(255) NOT NULL COMMENT '域名 //内部域名表',
    `task_id`     int(11) NOT NULL COMMENT '任务ID',
    `type`        int(10) NOT NULL DEFAULT '1' COMMENT '0代表精确域名，1代表N级域名',
    `link_ip`     longtext     NOT NULL COMMENT '关联ip',
    `remark`      longtext     NOT NULL COMMENT '备注',
    PRIMARY KEY (`domain_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_inside_ip: table
DROP TABLE IF EXISTS `tb_inside_ip`;
CREATE TABLE `tb_inside_ip`
(
    `ip`      varchar(255) NOT NULL COMMENT 'ip //内部ip表',
    `task_id` int(11) NOT NULL COMMENT '任务ID',
    `remark`  longtext     NOT NULL COMMENT '备注',
    PRIMARY KEY (`ip`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_internal_net: table
DROP TABLE IF EXISTS `tb_internal_net`;
CREATE TABLE `tb_internal_net`
(
    `id`                 int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一主键id',
    `task_id`            int(20) NOT NULL COMMENT '任务id',
    `inter_ip`           varchar(50) NOT NULL COMMENT '内网ip地址',
    `ip_mask`            varchar(50) NOT NULL COMMENT '子网掩码',
    `mac`                varchar(50) NOT NULL COMMENT 'mac地址',
    `created_time`       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_modified_time` datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `tb_internal_net_id_uindex` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=78 DEFAULT CHARSET=utf8 COMMENT='内网ip网段表';

-- tb_knowledge_alarm: table
DROP TABLE IF EXISTS `tb_knowledge_alarm`;
CREATE TABLE `tb_knowledge_alarm`
(
    `knowledge_alarm_id` int(10) NOT NULL COMMENT '//告警id',
    `alarm_name`         varchar(64) NOT NULL COMMENT '//告警名称',
    `attack_type`        int(11) NOT NULL COMMENT '攻击类型 1 非法接入          ',
    `attack_time`        varchar(256)  DEFAULT '' COMMENT '攻击类型 1 非法接入          ',
    `relation_tag_id`    varchar(2048) DEFAULT '' COMMENT '可能关联的告警名称 , 逗号分割',
    `exclude_tag_id`     varchar(2048) DEFAULT '' COMMENT '的告警名称 , 逗号分割',
    `black_list`         int(5) NOT NULL DEFAULT '0' COMMENT '黑名单权重',
    `remark`             varchar(2048) DEFAULT '' COMMENT '//备注 , 说明',
    `id`                 bigint(11) NOT NULL AUTO_INCREMENT COMMENT '//自增id  ---告警表---',
    PRIMARY KEY (`id`),
    UNIQUE KEY `knowledge_alarm_id` (`knowledge_alarm_id`)
) ENGINE=InnoDB AUTO_INCREMENT=139 DEFAULT CHARSET=utf8;

-- tb_line_analyze: table
DROP TABLE IF EXISTS `tb_line_analyze`;
CREATE TABLE `tb_line_analyze`
(
    `task_id`   int(11) NOT NULL COMMENT '任务ID',
    `type_name` varchar(255) NOT NULL COMMENT '//线路分析的json',
    `text`      longtext     NOT NULL COMMENT '//内网网段数量',
    `type`      int(11) NOT NULL COMMENT '//Mac数量',
    PRIMARY KEY (`type_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_line_analyze_param: table
DROP TABLE IF EXISTS `tb_line_analyze_param`;
CREATE TABLE `tb_line_analyze_param`
(
    `task_id`         int(11) NOT NULL COMMENT '任务ID',
    `type`            varchar(255) NOT NULL COMMENT '//LINE:线路分析参数',
    `max_segment_num` int(11) NOT NULL COMMENT '//内网网段数量',
    `max_mac_num`     int(11) NOT NULL COMMENT '//Mac数量',
    PRIMARY KEY (`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_lock_flow_forensic: table
DROP TABLE IF EXISTS `tb_lock_flow_forensic`;
CREATE TABLE `tb_lock_flow_forensic`
(
    `token`            varchar(255) NOT NULL,
    `last_access_time` bigint(20) NOT NULL,
    `effective_time`   int(11) NOT NULL DEFAULT '600'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_log_plug: table
DROP TABLE IF EXISTS `tb_log_plug`;
CREATE TABLE `tb_log_plug`
(
    `id`           bigint(11) NOT NULL AUTO_INCREMENT,
    `plug_name`    text         NOT NULL,
    `plug_type`    varchar(255) NOT NULL,
    `plug_json`    longtext     NOT NULL,
    `plug_remark`  longtext     NOT NULL,
    `created_time` int(10) NOT NULL,
    `plug_hash`    varchar(255) NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_mac_info_value: table
DROP TABLE IF EXISTS `tb_mac_info_value`;
CREATE TABLE `tb_mac_info_value`
(
    `registry`             varchar(255) DEFAULT NULL COMMENT '//MA-L为mac的前6位，MA-M为mac的前7位，MA-S为mac的前9位',
    `assignment`           varchar(255) DEFAULT NULL COMMENT '//匹配mac前9位，匹配mac前7位，匹配mac前6位',
    `organization_name`    text COMMENT '//厂家信息',
    `organization_address` text COMMENT '//厂家地址'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_model: table
DROP TABLE IF EXISTS `tb_model`;
CREATE TABLE `tb_model`
(
    `model_id`        int(11) NOT NULL COMMENT '模型Id',
    `model_name`      varchar(255)  DEFAULT NULL COMMENT '模型名称',
    `model_hash`      varchar(255)  DEFAULT NULL COMMENT '模型Hash',
    `remark`          varchar(1024) DEFAULT NULL COMMENT '模型描述',
    `model_state`     int(11) NOT NULL DEFAULT '1' COMMENT '模型状态',
    `model_path`      varchar(255)  DEFAULT NULL COMMENT '模型路径',
    `pb_drop`         bigint(20) NOT NULL COMMENT 'pb留存',
    `pcap_drop`       bigint(20) NOT NULL COMMENT 'pcap留存',
    `total_sum_bytes` bigint(20) NOT NULL DEFAULT '0' COMMENT '模型命中数量',
    `last_size_time`  int(10) NOT NULL COMMENT '最新命中时间',
    PRIMARY KEY (`model_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_model_info: table
DROP TABLE IF EXISTS `tb_model_info`;
CREATE TABLE `tb_model_info`
(
    `model_id`        int(11) NOT NULL COMMENT '模型ID',
    `model_name`      varchar(1024) NOT NULL,
    `model_algorithm` varchar(512)  NOT NULL COMMENT '算法',
    `remark`          varchar(2048) NOT NULL COMMENT '描述',
    `state`           int(2) NOT NULL DEFAULT '1' COMMENT '1 生效  0 失效',
    `update_time`     int(10) NOT NULL COMMENT '更新时间',
    `created_time`    int(10) NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`model_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_network_config: table
DROP TABLE IF EXISTS `tb_network_config`;
CREATE TABLE `tb_network_config`
(
    `id`                bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID ---网络配置---',
    `task_id`           int(11) NOT NULL COMMENT '任务ID',
    `device_name`       text         NOT NULL COMMENT '设备名称',
    `mac`               varchar(255) NOT NULL COMMENT 'mac',
    `organization_name` text COMMENT '厂商信息',
    `network_json`      longtext     NOT NULL COMMENT 'json',
    `state`             int(1) NOT NULL DEFAULT '0' COMMENT '状态：0待处理，1生效',
    `created_time`      int(10) NOT NULL COMMENT '创建时间',
    `updated_time`      int(10) NOT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_network_flow: table
DROP TABLE IF EXISTS `tb_network_flow`;
CREATE TABLE `tb_network_flow`
(
    `id`           int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '//自增ID ---任务信息表---',
    `pcie_id`      varchar(24) NOT NULL COMMENT '//任务ID',
    `flow_name`    text        NOT NULL COMMENT '//任务名称',
    `network_type` longtext    NOT NULL COMMENT '//任务备注',
    `created_time` int(10) NOT NULL COMMENT '//保存任务的时间',
    `state`        int(1) NOT NULL DEFAULT '0' COMMENT '//0代表历史任务，1代表当前任务',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;

-- tb_network_state: table
DROP TABLE IF EXISTS `tb_network_state`;
CREATE TABLE `tb_network_state`
(
    `id`                 int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID -- 网络 配置---',
    `task_id`            int(11) NOT NULL COMMENT '任务ID',
    `state`              int(1) NOT NULL COMMENT '0表示停用，1表示开启',
    `network_param_json` text NOT NULL COMMENT '{"TimeInterval_Statistics": 3600, "TimeInterval_Alert": 6, "Param": {"BasicLine_PacketNum": 60, "Times_ARP": 4, "Times_LLDP": 4, "Times_BroadCast": 4}}',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1015 DEFAULT CHARSET=utf8;

-- tb_network_statistics_info: table
DROP TABLE IF EXISTS `tb_network_statistics_info`;
CREATE TABLE `tb_network_statistics_info`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID //网络防御过滤的包数',
    `type`        int(11) unsigned NOT NULL,
    `batch_id`    int(10) NOT NULL COMMENT '批次ID',
    `packet_num`  int(11) NOT NULL COMMENT '过滤的包数',
    `bytes`       bigint(20) unsigned NOT NULL COMMENT '过滤掉的字节数',
    `create_time` int(10) NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_pb_save: table
DROP TABLE IF EXISTS `tb_pb_save`;
CREATE TABLE `tb_pb_save`
(
    `all_pb_num`   bigint(20) unsigned DEFAULT NULL COMMENT '启动后日志提取总条目数',
    `all_pb_bytes` bigint(20) unsigned DEFAULT NULL COMMENT '启动后日志提取总字节数by',
    `num_30s_pb`   bigint(20) unsigned DEFAULT NULL COMMENT '30s内日志提取条目数',
    `bytes_30s_pb` bigint(20) unsigned DEFAULT NULL COMMENT '30S 30s内日志提取字节数b',
    `pb_ps`        bigint(20) DEFAULT NULL COMMENT '日志提取速度(条目数每秒)',
    `times`        bigint(20) DEFAULT NULL COMMENT '创建时间',
    `task_id`      int(11) DEFAULT NULL COMMENT '任务ID',
    `batch_id`     int(11) DEFAULT NULL COMMENT '批次ID',
    `device_id`    int(11) unsigned DEFAULT '0' COMMENT '//设备ID',
    `id`           bigint(11) NOT NULL AUTO_INCREMENT,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- tb_plugin: table
DROP TABLE IF EXISTS `tb_plugin`;
CREATE TABLE `tb_plugin`
(
    `plugin_id`   int(11) NOT NULL COMMENT '插件Id',
    `plugin_name` varchar(255)  DEFAULT NULL COMMENT '插件名称',
    `plugin_type` int(5) DEFAULT NULL COMMENT '插件类型  1 全流量 2 协议解析',
    `remark`      varchar(1024) DEFAULT NULL COMMENT '模型描述',
    PRIMARY KEY (`plugin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_port_info_value: table
DROP TABLE IF EXISTS `tb_port_info_value`;
CREATE TABLE `tb_port_info_value`
(
    `Port`         int(11) DEFAULT NULL COMMENT '端口 //端口信息列表',
    `Tcp_Pro_Name` varchar(255) DEFAULT NULL COMMENT 'TCP协议名',
    `Tcp_Pro_Id`   varchar(255) DEFAULT NULL COMMENT 'TCP协ID',
    `Udp_Pro_Name` varchar(255) DEFAULT NULL COMMENT 'UDP协议名',
    `Udp_Pro_Id`   varchar(255) DEFAULT NULL COMMENT 'UDP协议ID',
    `Remark`       text COMMENT '说明'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- tb_rule: table
DROP TABLE IF EXISTS `tb_rule`;
CREATE TABLE `tb_rule`
(
    `id`                      bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID ---规则表---',
    `task_id`                 int(11) NOT NULL COMMENT '规则ID',
    `rule_id`                 int(11) NOT NULL COMMENT '规则ID',
    `rule_level`              int(11) NOT NULL COMMENT '规则级别',
    `rule_name`               varchar(256) NOT NULL COMMENT '规则名称',
    `rule_desc`               text         NOT NULL COMMENT '规则描述',
    `status`                  tinyint(4) NOT NULL COMMENT '规则状态 1:正常 0:删除',
    `rule_state`              varchar(20)  NOT NULL COMMENT '规则状态 失效 or 生效',
    `rule_size`               bigint(20) unsigned NOT NULL COMMENT '数据量',
    `total_sum_bytes`         bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '命中数据总量',
    `last_size_time`          int(10) NOT NULL COMMENT '最新命中时间',
    `capture_mode`            int(11) NOT NULL COMMENT '采集模式',
    `rule_json`               longtext     NOT NULL COMMENT 'json字符串',
    `created_time`            int(10) NOT NULL COMMENT '创建时间',
    `updated_time`            int(10) NOT NULL COMMENT '修改时间',
    `rule_hash`               varchar(255) NOT NULL COMMENT '规则hash',
    `rule_family`             int(3) DEFAULT '0' COMMENT '0 其他 1 侦察探测 2 武器投递 3 攻击突防 4 命令控制 5。控守操作',
    `byte_ps`                 bigint(20) NOT NULL COMMENT '每秒限速',
    `save_bytes`              bigint(20) NOT NULL COMMENT '存储大小，负数不限',
    `pb_drop`                 int(11) NOT NULL COMMENT 'pb留存',
    `pcap_drop`               int(11) NOT NULL COMMENT 'pcap留存',
    `lib_respond_open`        int(11) NOT NULL COMMENT '开启动态库响应',
    `lib_respond_lib`         varchar(255) NOT NULL COMMENT '库路径',
    `lib_respond_config`      varchar(255) NOT NULL COMMENT '库配置路径',
    `lib_respond_session_end` bigint(20) unsigned NOT NULL COMMENT '末尾响应',
    `rule_type`               varchar(20)  NOT NULL COMMENT '包含的规则，逗号分割 1:ip规则 2:协议规则 3:特征规则 4:正则规则 5:域名规则',
    `lib_respond_pkt_num`     bigint(20) NOT NULL COMMENT '包数',
    `lib_data_so`             longtext COMMENT 'so文件base64字符串',
    `lib_data_conf`           longtext COMMENT 'conf文件base64字符串',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_rule_id` (`rule_id`) USING BTREE,
    KEY                       `idx` (`rule_hash`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=76 DEFAULT CHARSET=utf8;

-- tb_rule_info: table
DROP TABLE IF EXISTS `tb_rule_info`;
CREATE TABLE `tb_rule_info`
(
    `id`           bigint(11) NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `rule_id`      int(11) NOT NULL COMMENT '规则ID',
    `rule_level`   int(11) NOT NULL COMMENT '规则级别（威胁等级）',
    `rule_name`    text     NOT NULL COMMENT '规则名称（告警类型）',
    `rule_desc`    longtext NOT NULL COMMENT '规则描述（说明）',
    `capture_mode` int(11) NOT NULL COMMENT '采集模式',
    `created_time` int(10) NOT NULL COMMENT '创建时间',
    `updated_time` int(10) NOT NULL COMMENT '修改时间',
    `rule_type`    int(1) NOT NULL COMMENT '0为系统规则，1为用户规则',
    `rule_family`  int(3) NOT NULL DEFAULT '0' COMMENT '0 其他 1 侦察探测 2 武器投递 3 攻击突防 4 命令控制 5。控守操作',
    `task_id`      int(10) NOT NULL COMMENT '任务ID',
    PRIMARY KEY (`id`),
    KEY            `rule_id_index` (`rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_rule_lib_config: table
DROP TABLE IF EXISTS `tb_rule_lib_config`;
CREATE TABLE `tb_rule_lib_config`
(
    `id`          bigint(11) unsigned NOT NULL AUTO_INCREMENT,
    `lib_path`    longtext NOT NULL,
    `config_path` longtext NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='规则动态文件路径与配置路径';

-- tb_rule_statistic_info: table
DROP TABLE IF EXISTS `tb_rule_statistic_info`;
CREATE TABLE `tb_rule_statistic_info`
(
    `rule_id`    int(11) NOT NULL,
    `sum_bytes`  bigint(20) unsigned NOT NULL,
    `sum_packet` bigint(20) NOT NULL,
    `time`       int(10) NOT NULL,
    `device_id`  bigint(20) NOT NULL DEFAULT '0' COMMENT '-- 设备ID --',
    `id`         bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '---规则数据量表---',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1876459 DEFAULT CHARSET=utf8;

-- tb_session_id: table
DROP TABLE IF EXISTS `tb_session_id`;
CREATE TABLE `tb_session_id`
(
    `session_id`   varchar(255)  NOT NULL,
    `black_list`   int(11) NOT NULL,
    `white_list`   int(11) NOT NULL,
    `remark`       varchar(4096) NOT NULL,
    `created_time` int(10) NOT NULL,
    PRIMARY KEY (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_state_value: table
DROP TABLE IF EXISTS `tb_state_value`;
CREATE TABLE `tb_state_value`
(
    `id`          bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `type_name`   varchar(255) NOT NULL COMMENT '功能状态简写',
    `type_value`  text         NOT NULL COMMENT '功能状态详细说明',
    `field_name`  varchar(255) NOT NULL COMMENT '字段名称',
    `field_value` longtext     NOT NULL COMMENT '字段值',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_tag_attribute: table
DROP TABLE IF EXISTS `tb_tag_attribute`;
CREATE TABLE `tb_tag_attribute`
(
    `attribute_id`   int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `attribute_name` varchar(64) NOT NULL COMMENT '属性名称',
    `target_id`      int(11) DEFAULT NULL,
    PRIMARY KEY (`attribute_id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8;

-- tb_tag_attribute_rate: table
DROP TABLE IF EXISTS `tb_tag_attribute_rate`;
CREATE TABLE `tb_tag_attribute_rate`
(
    `id`           int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID ---规则表---',
    `tag_id`       int(11) NOT NULL COMMENT '标签ID //标签表',
    `attribute_id` int(11) NOT NULL COMMENT '签ID //标签表',
    PRIMARY KEY (`id`),
    KEY            `index_tb_app_tag_app` (`tag_id`),
    KEY            `index_tb_app_tag_id` (`attribute_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

-- tb_target_group: table
DROP TABLE IF EXISTS `tb_target_group`;
CREATE TABLE `tb_target_group`
(
    `id`   int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(256) NOT NULL COMMENT '目标组名称',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_target_remark: table
DROP TABLE IF EXISTS `tb_target_remark`;
CREATE TABLE `tb_target_remark`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一主键ID',
    `target_key`  varchar(200) NOT NULL COMMENT '目标key',
    `target_type` varchar(50)  NOT NULL COMMENT '目标类型',
    `remark`      varchar(200) NOT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='目标详情备注';

-- tb_task_analysis: table
DROP TABLE IF EXISTS `tb_task_analysis`;
CREATE TABLE `tb_task_analysis`
(
    `id`                int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '//自增ID ---任务信息表---',
    `user_id`           int(11) DEFAULT 1 NULL COMMENT '用户ID',
    `task_id`           int(11) DEFAULT NULL COMMENT '//任务ID',
    `task_name`         text     NOT NULL COMMENT '//任务名称',
    `netflow`           varchar(2048)     DEFAULT '[]' COMMENT '//任务管理的网卡 ，json 数组格式  , 为 tb_network_flow 的 flow_name ',
    `task_remark`       longtext COMMENT '//任务备注',
    `created_time`      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '//保存任务的时间',
    `task_state`        int(1) NOT NULL DEFAULT '0' COMMENT '//0代表历史任务，1代表当前任务  2 挂起任务',
    `last_suspend_time` datetime          DEFAULT NULL COMMENT '最后挂起时间',
    `suspend_times`     int(10) NOT NULL DEFAULT '0' COMMENT '挂起次数',
    `task_type`         int(1) DEFAULT '1' COMMENT '任务类型（1-在线任务；2-离线任务；）',
    `delete_status`     int(1) DEFAULT '0' COMMENT '删除状态（0-未删除；1-已删除；）',
    `create_time`       datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8;

-- tb_task_batch: table
DROP TABLE IF EXISTS `tb_task_batch`;
CREATE TABLE `tb_task_batch`
(
    `batch_id`                   int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '//自增批次ID ---批次信息表---',
    `task_id`                    int(11) NOT NULL COMMENT '//任务ID',
    `batch_remark`               longtext COMMENT '//批次描述',
    `fullflow_state`             varchar(3)     DEFAULT NULL COMMENT '//全流量留存，ON启用，OFF停用',
    `flowlog_state`              varchar(3)     DEFAULT NULL COMMENT '//流量日志留存，ON启用，OFF停用',
    `data_type`                  int(1) DEFAULT NULL COMMENT '//导入的数据类型，1（pcap），2（pb），3（探针数据）',
    `topology_state`             varchar(3)     DEFAULT 'OFF' COMMENT '//MAC日志留存，ON启用，OFF停用',
    `begin_time`                 int(11) unsigned DEFAULT NULL COMMENT '//导入开始时间',
    `end_time`                   int(11) unsigned DEFAULT NULL COMMENT '//导入结束时间',
    `data_begin_time`            int(11) unsigned DEFAULT NULL COMMENT '//数据开始时间',
    `data_end_time`              int(11) unsigned DEFAULT NULL COMMENT '//数据结束时间',
    `batch_bytes`                bigint(20) unsigned DEFAULT NULL COMMENT '//导入数据量',
    `batch_session`              int(11) DEFAULT NULL COMMENT '//导入会话量',
    `batch_alarm`                int(11) DEFAULT NULL COMMENT '//批次的告警量',
    `importrarnt_target`         int(11) DEFAULT '0' COMMENT '//高危目标数量',
    `batch_dir`                  text COMMENT '//当前任务批次数据路径',
    `report_path`                text COMMENT '//批次数据报告生成路径',
    `screening_conditions`       varchar(4096)  DEFAULT '' COMMENT '筛选条件',
    `avg_byte_pt_ps`             int(11) DEFAULT '0' COMMENT '//每线程每秒钟平均可写pcap字节数',
    `max_byte_pt_ps`             int(11) DEFAULT '0' COMMENT '//每线程每秒钟最多可写pcap字节数',
    `addr`                       varchar(236)   DEFAULT '' COMMENT '//批次地址',
    `task_update`                int(1) DEFAULT '0' COMMENT '//0代表初始化状态。，1代表 非初始化状态     更新标的时候更新',
    `full_flow_should_log_def`   int(2) DEFAULT '1' COMMENT '开启的插件iD默认值 ， 1 为开启 0  位关闭 ',
    `parse_proto_should_log_def` int(2) DEFAULT '1' COMMENT '开启的插件iD默认值 ， 1 为开启 0  位关闭 ',
    `state`                      int(2) DEFAULT '1' COMMENT '批次值 ， 1 为正在运行 0  位关闭 ',
    `task_type`                  int(1) DEFAULT '1' COMMENT '任务类型（1-在线任务；2-离线任务；）',
    `batch_type`                 int(1) DEFAULT '0' COMMENT '批次类型（1-服务器数据；2-数据上传；）',
    `filter_data_total`          bigint(20) DEFAULT '0' COMMENT '过滤数据量',
    `rule_hits_data_total`       bigint(20) DEFAULT '0' COMMENT '采集规则命中数据量',
    `whitelist_filter_total`     bigint(20) DEFAULT '0' COMMENT '白名单过滤量',
    `batch_status`               int(1) DEFAULT '1' COMMENT '批次状态（1-等待导入；2-正在导入；3-导入完成；）',
    `batch_progress`             decimal(20, 4) DEFAULT '0.0000' COMMENT '导入进度',
    `pcap_num`                   int(11) DEFAULT NULL COMMENT 'pcap包数量',
    PRIMARY KEY (`batch_id`)
) ENGINE=InnoDB AUTO_INCREMENT=195 DEFAULT CHARSET=utf8;

-- tb_offline_task_batch_file: table
DROP TABLE IF EXISTS `tb_offline_task_batch_file`;
CREATE TABLE `tb_offline_task_batch_file`
(
    `id`         int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `task_id`    bigint(20) DEFAULT NULL COMMENT '任务ID',
    `batch_id`   bigint(20) DEFAULT NULL COMMENT '批次ID',
    `batch_type` int(1) DEFAULT '0' COMMENT '批次类型（1-服务器数据；2-数据上传；）',
    `file_path`  varchar(2000) NOT NULL COMMENT '文件路径',
    `local_path` varchar(2000) DEFAULT NULL COMMENT '文件本地路径',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=494 DEFAULT CHARSET=utf8mb4 COMMENT='离线任务批次关联文件表';

-- tb_task_inside_ip: table
DROP TABLE IF EXISTS `tb_task_inside_ip`;
CREATE TABLE `tb_task_inside_ip`
(
    `id`           int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '//自增ID ---任务信息表---',
    `task_id`      int(11) NOT NULL COMMENT '//任务ID',
    `ip`           varchar(255) NOT NULL COMMENT 'ip 网段信息',
    `netmask`      varchar(255) NOT NULL COMMENT '网段循序 ',
    `mac`          varchar(255) NOT NULL COMMENT 'mac地址 ',
    `updated_time` varchar(255) NOT NULL COMMENT '修改时间 ',
    `created_time` int(11) unsigned NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_use_ip_position: table
DROP TABLE IF EXISTS `tb_use_ip_position`;
CREATE TABLE `tb_use_ip_position`
(
    `task_id`      int(11) NOT NULL,
    `ip`           varchar(42)  NOT NULL COMMENT '//ip',
    `net_mesh`     varchar(42)  NOT NULL COMMENT 'ip源码',
    `country`      varchar(128) NOT NULL COMMENT '国家',
    `province`     varchar(128) NOT NULL COMMENT '省份',
    `city`         varchar(128) NOT NULL COMMENT '城市',
    `remark`       varchar(256) NOT NULL COMMENT '备注',
    `created_time` int(11) NOT NULL COMMENT '创建时间',
    `updated_time` int(11) NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`ip`, `net_mesh`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_valset: table
DROP TABLE IF EXISTS `tb_valset`;
CREATE TABLE `tb_valset`
(
    `id`        int(11) NOT NULL AUTO_INCREMENT COMMENT 'pk，自增，VALSET_SEQ',
    `valset_id` varchar(255) DEFAULT NULL,
    `val_id`    varchar(255) DEFAULT NULL,
    `value`     varchar(255) DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8 COMMENT='字典表';

-- tb_white_list: table
DROP TABLE IF EXISTS `tb_white_list`;
CREATE TABLE `tb_white_list`
(
    `id`           int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `server_ip`    varchar(50) NOT NULL DEFAULT '',
    `port`         int(11) NOT NULL DEFAULT '-1',
    `app_id`       int(11) NOT NULL DEFAULT '-1' COMMENT '应用id',
    `domain`       varchar(50) NOT NULL DEFAULT '',
    `domain_type`  int(11) NOT NULL DEFAULT '0' COMMENT '域名类型\r\n精确域名 1\r\nn级域名 2',
    `filter`       int(11) NOT NULL DEFAULT '0' COMMENT '过滤DNS 4\r\n过滤HTTP 2\r\n过滤SSL 1\r\n\r\n7 表示三者都有\r\n6 表示前两者\r\n........',
    `netmask`      varchar(24) NOT NULL DEFAULT '0',
    `task_id`      int(11) NOT NULL,
    `state`        int(11) NOT NULL COMMENT '1 表示生效\r\n0 表示失效',
    `created_time` bigint(20) NOT NULL,
    `updated_time` bigint(20) NOT NULL,
    `name`         varchar(50) NOT NULL DEFAULT '' COMMENT '该白名单规则的名字\r\n可以认为它是主键id的一个别名\r\n用于后台添加规则\r\n前端用户操作时该字段不使用',
    `remark`       varchar(50) NOT NULL DEFAULT '' COMMENT '注释',
    `from`         int(11) NOT NULL DEFAULT '1' COMMENT '1 表示该记录来自前端\r\n2 表示该记录来自内部人员',
    `last_filter`  int(11) NOT NULL DEFAULT '0' COMMENT '上次过滤日志数',
    `last_total`   int(11) NOT NULL DEFAULT '0' COMMENT '上次日志总数',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_ip_port_app_task` (`server_ip`,`port`,`app_id`,`task_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_white_list_log: table
DROP TABLE IF EXISTS `tb_white_list_log`;
CREATE TABLE `tb_white_list_log`
(
    `id`            int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT ' 主键',
    `last_filter`   varchar(50) NOT NULL COMMENT '上次过滤日志数量',
    `last_total`    varchar(50) NOT NULL COMMENT '上次全部日志数量',
    `white_list_id` int(10) unsigned NOT NULL COMMENT '外键 white_list 的主键',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- tb_white_list_state: table
DROP TABLE IF EXISTS `tb_white_list_state`;
CREATE TABLE `tb_white_list_state`
(
    `id`      int(10) unsigned NOT NULL AUTO_INCREMENT,
    `task_id` int(11) NOT NULL,
    `state`   int(11) NOT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_task_id` (`task_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `tb_batch_plugin`;
CREATE TABLE `tb_batch_plugin`
(
    `id`             int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '//自增ID ---任务信息表---',
    `batch_id`       int(11) UNSIGNED NOT NULL COMMENT '//批次ID',
    `plugin_id`      int(11) UNSIGNED NOT NULL COMMENT '//插件ID',
    `should_log_def` int(11) UNSIGNED NOT NULL COMMENT '//开启的插件iD默认值 ， 1 为开启 0  位关闭',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `tb_disk_field`;
CREATE TABLE `tb_disk_field`
(
    `id`    int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一识别ID',
    `field` int(11) NULL DEFAULT NULL COMMENT '硬盘模式（读盘模式&换盘模式）',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `tb_tag_info`;
CREATE TABLE `tb_tag_info`
(
    `tag_id`             int(11) NOT NULL AUTO_INCREMENT COMMENT '标签ID //标签表',
    `tag_type`           int(11) NOT NULL DEFAULT 0 COMMENT '标签类型 //0删除，1生效',
    `tag_remark`         longtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '标签备注',
    `tag_explain`        longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '标签中文说明',
    `tag_text`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '标签内容',
    `tag_attr`           int(1) NULL DEFAULT 0 COMMENT '标签属性 0 全部 1 探针 2 分析平台 3 其他',
    `tag_num`            int(11) NOT NULL DEFAULT 0 COMMENT '标签数量',
    `tag_target_type`    int(11) NOT NULL DEFAULT 0 COMMENT '//0代表ip目标，1代表端口目标，2代表应用目标，3代表域名目标，4代表证书目标，5代表MAC目标，6代表连接目标 {目标类型} , 7 指纹 9999 所有',
    `default_black_list` int(3) NOT NULL DEFAULT 0 COMMENT '默认黑名单',
    `default_white_list` int(3) NOT NULL DEFAULT 0 COMMENT '默认白名单',
    `black_list`         int(3) NOT NULL DEFAULT 0 COMMENT '黑名单',
    `white_list`         int(3) NOT NULL DEFAULT 0 COMMENT '白名单',
    `created_time`       int(11) NOT NULL COMMENT '添加时间',
    `last_created_time`  int(11) NOT NULL COMMENT '最后添加时间',
    `tag_family`         int(3) NULL DEFAULT 0 COMMENT '0 其他 1 侦察探测 2 武器投递 3 攻击突防 4 命令控制 5。控守操作',
    `tag_class`          int(11) NOT NULL DEFAULT '0' COMMENT '标签分类（0-系统内置；1-用户添加；）',
    `user_id`            int(11) NOT NULL COMMENT '用户ID',
    PRIMARY KEY (`tag_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1000007 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `tb_finger_info`;
CREATE TABLE `tb_finger_info`
(
    `id`             int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一主键ID',
    `finger_es`      varchar(100) DEFAULT NULL COMMENT 'Finger_ES_ID',
    `finger_content` mediumtext COMMENT '流量中提取字段',
    `ja3_hash`       varchar(100) DEFAULT NULL COMMENT '指纹JA3Hash',
    `es_type`        varchar(200) DEFAULT NULL COMMENT 'ES中的源目的指纹',
    `finger_type`    varchar(100) DEFAULT NULL COMMENT '指纹类型',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1409 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

DROP TABLE IF EXISTS `tb_alarm_white`;
CREATE TABLE `tb_alarm_white`
(
    `id`       int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一主键ID',
    `victim`   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '受害者IP',
    `attacker` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '攻击者IP',
    `label`    varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '告警相关标签',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `tb_model_switch`;
CREATE TABLE `tb_model_switch`
(
    `id`         int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一主键ID',
    `model_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '模型名称',
    `switch`     int(11) NULL DEFAULT NULL COMMENT '模型开关',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `tb_alarm_output`;
CREATE TABLE `tb_alarm_output`
(
    `id`     int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一主键ID',
    `tool`   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '告警外发组件',
    `status` int(11) NULL DEFAULT NULL COMMENT '组件启用状态',
    `ip`     varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '组件IP',
    `port`   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '组件端口',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `tb_threat_info`;
CREATE TABLE `tb_threat_info`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一主键ID',
    `target`      varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'hash',
    `target_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '目标类型(domaincertip)',
    `tag_name`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '目标标签',
    `source`      varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '来源',
    `version`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '版本',
    `shash`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'hash',
    `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '入库时间',
    `valid_from`  datetime(0) NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '有效开始时间',
    `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP (0) COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 497613 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_query_history
-- ----------------------------
DROP TABLE IF EXISTS `tb_query_history`;
CREATE TABLE `tb_query_history`
(
    `id`             int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一识别ID',
    `user_id`        int(11) DEFAULT NULL COMMENT '用户ID',
    `user_name`      varchar(128) NOT NULL COMMENT '查询用户名称',
    `condition_text` text COMMENT '查询条件',
    `source`         varchar(16)  NOT NULL COMMENT '查询来源',
    `hit_count`      int(11) DEFAULT NULL COMMENT '命中记录数量',
    `cost_time`      int(11) DEFAULT NULL COMMENT '查询耗时（毫秒）',
    `query_count`    int(11) DEFAULT '0' COMMENT '前后查询次数',
    `query_time`     datetime DEFAULT CURRENT_TIMESTAMP COMMENT '查询时间',
    `query_cn`       text COMMENT '查询条件（前端用于回写）',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8 COMMENT='查询历史表';

-- ----------------------------
-- Table structure for tb_query_template
-- ----------------------------
DROP TABLE IF EXISTS `tb_query_template`;
CREATE TABLE `tb_query_template`
(
    `id`             int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一主键ID',
    `user_id`        int(11) NOT NULL,
    `user_name`      varchar(32) NOT NULL,
    `template_text`  text COMMENT '查询条件内容',
    `remark`         text COMMENT '模板备注',
    `created_time`   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    `last_used_time` datetime             DEFAULT NULL COMMENT '模板末次时间',
    `query_cn`       text COMMENT '查询条件（前端用于回写）',
    `query_count`    int(11) DEFAULT '0' COMMENT '前后查询次数',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COMMENT='查询模板表';

-- ----------------------------
-- Table structure for tb_cert_log_template
-- ----------------------------
DROP TABLE IF EXISTS `tb_cert_log_template`;
CREATE TABLE `tb_cert_log_template`
(
    `user_id`       int(11) NULL DEFAULT NULL,
    `template_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
    `created_time`  datetime(0) NULL DEFAULT NULL,
    `updated_time`  datetime(0) NULL DEFAULT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_cert_log_template
-- ----------------------------
INSERT INTO `tb_cert_log_template`
VALUES (1,
        '{\"ASN1MD5\":\"MD5\",\"ASN1SHA1\":\"SHA1\",\"CN\":\"使用者通用名\",\"CertID\":\"证书ID\",\"Duration\":\"有效周期_秒\",\"ImportTime\":\"导入时间\",\"FatherCertID\":\"父证书SHA1\",\"NotAfter\":\"有效期终止\",\"NotBefore\":\"有效期起始\",\"Usage\":\"密钥用法\",\"Version\":\"版本\",\"PemMD5\":\"PEM格式MD5\",\"PemSHA1\":\"PEM格式SHA1\",\"PublicKey\":\"公钥\",\"PublicKeyAlgorithm\":\"公钥算法\",\"PublicKeyAlgorithmLength\":\"公钥算法密钥长度\",\"SAN\":\"扩展域名\",\"SerialNumber\":\"序列号\",\"SignatureAlgorithm\":\"签名算法\",\"Extension\":\"扩展信息\",\"Extension.authorityInfoAccess\":\"- 授权信息访问\",\"Extension.authorityKeyIdentifier\":\"- 授权密钥标识符\",\"Extension.basicConstraints\":\"- 基本约束\",\"Extension.certificatePolicies\":\"- 证书策略\",\"Extension.crlDistributionPoints\":\"- CRL分发点\",\"Extension.extendedKeyUsage\":\"- 增强型密钥用法\",\"Extension.keyUsage\":\"- 密钥用法\",\"Extension.subjectAltName\":\"- 使用者可选名称\",\"Extension.subjectKeyIdentifier\":\"- 使用者密钥标识符\",\"Issuer\":\"颁发者\",\"Issuer.C\":\"-编辑国家\",\"Issuer.CN\":\"- 通用名\",\"Issuer.O\":\"- 组织名\",\"Issuer.OU\":\"- 组织部门\",\"Issuer.L\":\"- 地址\",\"Issuer.ST\":\"- 省份\",\"IssuerMD5\":\"颁发者哈希\",\"Subject\":\"使用者\",\"Subject.C\":\"- 国家\",\"Subject.CN\":\"- 通用名\",\"Subject.L\":\"- 地址\",\"Subject.O\":\"- 组织名\",\"Subject.OU\":\"- 组织部门\",\"Subject.ST\":\"- 省份\",\"SubjectMD5\":\"使用者哈希\"}',
        '2023-06-27 17:36:00', '2023-06-27 17:36:00');

-- ----------------------------
-- Table structure for tb_alarm_extend_target
-- ----------------------------
DROP TABLE IF EXISTS `tb_alarm_extend_target`;
CREATE TABLE `tb_alarm_extend_target`
(
    `id`           bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `alarm_id`     int(11) DEFAULT NULL COMMENT '告警id',
    `batch_id`     int(11) DEFAULT NULL COMMENT '批次id',
    `tag_id`       int(11) DEFAULT NULL COMMENT '标签id',
    `target_type`  varchar(256)   DEFAULT NULL COMMENT '标签类型',
    `target_name`  varchar(256)   DEFAULT NULL COMMENT '标签名称',
    `tag_family`   varchar(256)   DEFAULT NULL COMMENT '标签家族',
    `time`         int(10) DEFAULT NULL COMMENT '时间',
    `cnt`          bigint(20) DEFAULT NULL COMMENT '告警总数',
    `defense_info` varchar(2000)  DEFAULT NULL COMMENT '防御信息',
    `longitude`    decimal(10, 4) DEFAULT '0.0000' COMMENT '经度',
    `latitude`     decimal(10, 4) DEFAULT NULL COMMENT '纬度',
    `created_time` int(10) DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1349 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for tb_batch_offline_thd
-- ----------------------------
DROP TABLE IF EXISTS `tb_batch_offline_thd`;
CREATE TABLE `tb_batch_offline_thd`
(
    `id`           int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `task_id`      bigint(20) DEFAULT NULL COMMENT '任务ID',
    `batch_id`     bigint(20) DEFAULT NULL COMMENT '批次ID',
    `service_id`   int(11) DEFAULT '0' COMMENT '服务ID',
    `service_name` varchar(2000) DEFAULT NULL COMMENT '服务名称',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=560 DEFAULT CHARSET=utf8mb4 COMMENT='批次离线任务关联表';

-- ----------------------------
-- Table structure for ip_protocol
-- ----------------------------
DROP TABLE IF EXISTS `ip_protocol`;
CREATE TABLE `ip_protocol`
(
    `pro_id`          varchar(255) NOT NULL COMMENT '协议ID',
    `protocol_type`   varchar(255) NOT NULL COMMENT '协议名称',
    `protocol_remark` varchar(255) NOT NULL COMMENT '协议备注',
    PRIMARY KEY (`pro_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='IP协议表';

SET
FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- Table structure for tb_alarm_order
-- ----------------------------
DROP TABLE IF EXISTS `tb_alarm_order`;
CREATE TABLE `tb_alarm_order`
(
    `id`    int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一主键ID',
    `ip`    varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'ip',
    `port`  varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '端口',
    `topic` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'topic',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '告警订阅表' ROW_FORMAT = DYNAMIC;

SET
FOREIGN_KEY_CHECKS = 1;
