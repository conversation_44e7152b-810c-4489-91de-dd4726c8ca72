#!/bin/bash
echo "host:$1"
echo "port:$2"
echo "user:$3"
echo "pwd:$4"

host=$1
port=$2
user=$3
pwd=$4

ins=$PWD
cd $ins
echo $passd
mysql  -h$host -P$port -p$pwd -u$user -e "set global table_open_cache=16384;"
echo $passd
mysql -h$host -P$port -p$pwd -u$user -e "set global table_definition_cache=16384;"
echo $passd
#systemctl restart mysqld
sleep 10
echo $passd
mysql -h$host -P$port -p$pwd -u$user -e "CREATE DATABASE IF NOT EXISTS push_database;"
echo "创建push_database数据库"
mysql -h$host -P$port -p$pwd -u$user < ./push_database.sql
echo "push_database全部表结构初始化完毕"
mysql -h$host -P$port -p$pwd -u$user -e "CREATE DATABASE IF NOT EXISTS auth_db;"
echo "创建auth_db数据库"
mysql -h$host -P$port -p$pwd -u$user < ./auth_db.sql
echo "auth_db全部表结构初始化完毕"
mysql -h$host -P$port -p$pwd -u$user -e "CREATE DATABASE IF NOT EXISTS th_analysis;"
echo "创建th_analysis数据库"
mysql -h$host -P$port -p$pwd -u$user th_analysis  < ./th_analysis.sql
echo "th_analysis全部表结构初始化完毕"
mysql -h$host -P$port -p$pwd -u$user -e "CREATE DATABASE IF NOT EXISTS ip_domain_knowledge;"
echo "创建ip_domain_knowledge数据库"
mysql -h$host -P$port -p$pwd -u$user ip_domain_knowledge  < ./ip_domain_knowledge.sql
echo "ip_domain_knowledge全部表结构初始化完毕"
