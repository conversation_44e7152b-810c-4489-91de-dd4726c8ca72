CREATE TAG IF NOT EXISTS IP(ip_key STRING,ip_addr STRING,version FIXED_STRING(5),city FIXED_STRING(20),country FIXED_STRING(20),bytes INT64,packets INT64,recv_bytes INT64,send_bytes INT64,average_bps INT64,times INT32,first_seen TIMESTAMP,last_seen TIMESTAMP,black_list INT8,white_list INT8,remark STRING);
CREATE TAG IF NOT EXISTS MAC( mac STRING, times INT64, bytes INT64, average_bps INT64, vlan_info STRING, first_seen TIMESTAMP, last_seen TIMESTAMP, black_list INT8, white_list INT8, remark STRING);
CREATE TAG IF NOT EXISTS APPSERVICE( service_key STRING, ip_addr STRING, AppName STRING, dPort INT32, IPPro STRING, first_seen TIMESTAMP, last_seen TIMESTAMP);
CREATE TAG IF NOT EXISTS DOMAIN( domain_addr STRING, bytes INT64, average_bps INT64, alexa_rank INT64, first_seen TIMES<PERSON>MP, last_seen TIMESTAMP, black_list INT8, white_list INT8, remark STRING, whois STRING);
CREATE TAG IF NOT EXISTS FDOMAIN(fdomain_addr STRING, first_seen TIMESTAMP, last_seen TIMESTAMP);
CREATE TAG IF NOT EXISTS CERT( cert_id STRING, cert_md5 STRING, black_list INT8, white_list INT8, source_type INT8, remark STRING, first_seen TIMESTAMP, last_seen TIMESTAMP);
CREATE TAG IF NOT EXISTS ISSUER (issuer_md5 STRING, country STRING NULL COMMENT "国家C", common_name STRING NULL COMMENT "通用名称CN", object_name STRING NULL COMMENT "组织名称O", first_seen TIMESTAMP, last_seen TIMESTAMP);
CREATE TAG IF NOT EXISTS SUBJECT (subject_md5 STRING, country STRING NULL COMMENT "国家C", common_name STRING NULL COMMENT "通用名称CN", object_name STRING NULL COMMENT "组织名称O", first_seen TIMESTAMP, last_seen TIMESTAMP);
CREATE TAG IF NOT EXISTS ORG(org_name STRING,org_desc STRING,black_list INT8,white_list INT8,remark string, first_seen TIMESTAMP, last_seen TIMESTAMP);
CREATE TAG IF NOT EXISTS SSLFINGER( finger_id STRING, ja3_hash STRING, finger_desc STRING, type FIXED_STRING(10), first_seen TIMESTAMP, last_seen TIMESTAMP);
CREATE TAG IF NOT EXISTS UA(ua_id STRING,ua_str STRING,ua_desc STRING, first_seen TIMESTAMP, last_seen TIMESTAMP);
CREATE TAG IF NOT EXISTS DEVICE(device_name STRING, first_seen TIMESTAMP, last_seen TIMESTAMP);
CREATE TAG IF NOT EXISTS OS(os_name STRING, first_seen TIMESTAMP, last_seen TIMESTAMP);
-- CREATE TAG IF NOT EXISTS APPTYPE( Application STRING, black_list INT8, white_list INT8);
CREATE TAG IF NOT EXISTS LABEL( label_id INT64, label_name STRING, label_target_type INT64, label_desc STRING, first_seen TIMESTAMP, last_seen TIMESTAMP);
-- CREATE TAG IF NOT EXISTS TASK( task_id int NULL  COMMENT "任务ID", batch_id int32 NULL  COMMENT "批次ID", task_name   string NULL COMMENT "任务名称", task_remark string null COMMENT "任务信息", created_time timestamp NULL COMMENT  "任务创建时间", state int8 NULL  COMMENT "批次值（1为正在运行，0为关闭)");
-- CREATE TAG IF NOT EXISTS SURVEY_DATA (survey_key string ,ip_addr string ,port int32 ,protocol string,domain string NULL,country_name string NULL,city string NULL,icp string NULL,server string NULL,jarm string NULL,title string NULL,as_organization string NULL,source string NULL,header string NULL,banner string NULL,black_list INT8,white_list INT8,product_catalog string NULL,product_type string NULL,product_level string NULL,product_vendor string NULL);
CREATE TAG IF NOT EXISTS URL (url_key string NOT NULL,white_list INT8,black_list INT8, first_seen TIMESTAMP, last_seen TIMESTAMP);
CREATE TAG IF NOT EXISTS APP (app_name STRING,app_version STRING, first_seen TIMESTAMP, last_seen TIMESTAMP);
-- CREATE TAG IF NOT EXISTS BLOCKCHAIN (addr string NULL  COMMENT "地址", chain_source string NULL  COMMENT "来源", balance_account int NULL  COMMENT "余额", black_list int8 NULL  , white_list int8 NULL  );
CREATE EDGE IF NOT EXISTS src_bind();
CREATE EDGE IF NOT EXISTS dst_bind();
-- CREATE EDGE IF NOT EXISTS connect(app_name STRING,dport INT32,recv_bytes INT64,send_bytes INT64,recv_packets INT64,send_packets INT64,bytes INT64,packets INT64,average_bps INT64,first_time TIMESTAMP,last_time TIMESTAMP,session_cnt INT64);
CREATE EDGE IF NOT EXISTS client_app();
CREATE EDGE IF NOT EXISTS app_server();
CREATE EDGE IF NOT EXISTS domain_belong_to();
CREATE EDGE IF NOT EXISTS ua_connect_domain();
CREATE EDGE IF NOT EXISTS client_use_ua();
CREATE EDGE IF NOT EXISTS client_http_connect_domain();
CREATE EDGE IF NOT EXISTS server_http_connect_domain();
CREATE EDGE IF NOT EXISTS ua_belong_device();
CREATE EDGE IF NOT EXISTS ua_belong_os();
CREATE EDGE IF NOT EXISTS ua_belong_app();
CREATE EDGE IF NOT EXISTS client_ssl_connect_domain();
CREATE EDGE IF NOT EXISTS server_ssl_connect_domain();
CREATE EDGE IF NOT EXISTS client_use_cert(sni STRING);
CREATE EDGE IF NOT EXISTS server_use_cert(sni STRING);
CREATE EDGE IF NOT EXISTS client_connect_cert(sni STRING);
CREATE EDGE IF NOT EXISTS client_use_sslfinger();
CREATE EDGE IF NOT EXISTS server_use_sslfinger();
CREATE EDGE IF NOT EXISTS sni_bind();
CREATE EDGE IF NOT EXISTS sslfinger_connect_cert(sni STRING);
CREATE EDGE IF NOT EXISTS sslfinger_connect_domain();
CREATE EDGE IF NOT EXISTS client_query_domain(dns_type INT32,answer_type INT32);
CREATE EDGE IF NOT EXISTS client_query_dns_server(dns_type INT32,answer_type INT32);
CREATE EDGE IF NOT EXISTS dns_server_domain(dns_type String,answer_type INT32);
CREATE EDGE IF NOT EXISTS parse_to(dns_server STRING,final_parse BOOL, max_ttl INT32,min_ttl INT32);
CREATE EDGE IF NOT EXISTS cname();
CREATE EDGE IF NOT EXISTS cname_result();
-- CREATE EDGE IF NOT EXISTS task_belong_to(task_id int64, task_name string);
CREATE EDGE IF NOT EXISTS has_label(analysis_by STRING,remark STRING);
-- CREATE EDGE IF NOT EXISTS survey_related(source string NULL);
-- CREATE EDGE IF NOT EXISTS belong_to_org ();
-- CREATE EDGE IF NOT EXISTS url_related ();
-- CREATE EDGE IF NOT EXISTS client_use_blockchain ();
-- CREATE EDGE IF NOT EXISTS blockchain_deal ();
-- zntp
CREATE TAG IF NOT EXISTS ATTACKER( attacker_id STRING, ip_addr STRING);
CREATE TAG IF NOT EXISTS VICTIM( victim_id STRING, ip_addr STRING);

CREATE EDGE IF NOT EXISTS connect_mac(app_name STRING,dport INT32);
CREATE EDGE IF NOT EXISTS connect_ip(app_name STRING,dport INT32);
CREATE EDGE IF NOT EXISTS domain_belong_to_org ();
CREATE EDGE IF NOT EXISTS fDomain_belong_to_org ();
CREATE EDGE IF NOT EXISTS ip_belong_to_org ();
CREATE EDGE IF NOT EXISTS cert_belong_to_org ();
CREATE EDGE IF NOT EXISTS domain_url_related ();
CREATE EDGE IF NOT EXISTS ip_url_related ();
CREATE EDGE IF NOT EXISTS cert_url_related ();
CREATE EDGE IF NOT EXISTS sni_bind_fdomain();
CREATE EDGE IF NOT EXISTS special_business_port_service ();
CREATE EDGE IF NOT EXISTS issuer_related ();
CREATE EDGE IF NOT EXISTS subject_related ();
CREATE EDGE IF NOT EXISTS cert_belong_app ();
CREATE EDGE IF NOT EXISTS domain_belong_app ();
CREATE EDGE IF NOT EXISTS ip_belong_app ();
CREATE EDGE IF NOT EXISTS sslfinger_belong_app ();
-- zntp
CREATE EDGE IF NOT EXISTS make_attack ();
CREATE EDGE IF NOT EXISTS cert_sign_cert ();
CREATE EDGE IF NOT EXISTS cert_validate_domain ();
CREATE EDGE IF NOT EXISTS cert_validate_fDomain ();
CREATE EDGE IF NOT EXISTS cert_related_ip ();
-- CREATE EDGE IF NOT EXISTS app_related ();

CREATE TAG INDEX IF NOT EXISTS cert_first_time_index on CERT(first_time);
CREATE TAG INDEX IF NOT EXISTS cert_md5_index on CERT(cert_md5(100));
CREATE TAG INDEX IF NOT EXISTS domain_domain_addr_index on DOMAIN(domain_addr(255));
CREATE TAG INDEX IF NOT EXISTS issuer_common_name_index on ISSUER(common_name(255));
CREATE TAG INDEX IF NOT EXISTS subject_common_name_index on SUBJECT(common_name(255));
CREATE TAG INDEX IF NOT EXISTS org_org_name_index on ORG(org_name(255));


