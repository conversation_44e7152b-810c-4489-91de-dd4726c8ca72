import sys
sys.path.append("./")
from utils.other.geeksec_secure_log import get_log_error, get_log_info, get_log_warning
from utils.MySQL.mysql_helper import init_anay_sql, init_cert_sql, init_knowledge_sql
import yaml
import os
import pymysql
import subprocess


def init_mysql():
    # logger 初始化
    logger_info = get_log_info("MYSQL_INIT_INFO")
    logger_info.info('--------------------This is a name INFO log file--------------------\n')
    logger_error = get_log_error("MYSQL_INIT_ERROR")
    logger_error.error('--------------------This is a name ERROR log file--------------------\n')
    logger_warning = get_log_warning("MYSQL_INIT_WARING")
    logger_warning.warning('--------------------This is a name WARNING log file--------------------\n')
    with open('yaml/mysql/mysql.yaml', 'r') as f:
        config = yaml.safe_load(f)
    f.close()
    mysql_config = config['mysql_config']

    # 首先进行初始的数据库的sql文件的导入
    host, port, user, password = str(mysql_config['host']), str(mysql_config['port']), str(mysql_config['user']), str(mysql_config['password'])
    mysql_basic_dic = {'host': host, 'port': port, 'user': user, 'password': password}
    basic_init_db = mysql_config['basic_init_db']
    # 获取当前工作目录
    previous_dir = os.getcwd()
    try:
        path_to_shell = basic_init_db['path_to_shell']
        path_of_shell = basic_init_db['path_of_shell']
        shell_name = basic_init_db['shell_name']
        # 给sql初始化脚本赋予权限
        new_mode = 0o777
        os.chmod(path_to_shell, new_mode)
        # 切换到shell脚本的目录, 执行shell脚本
        os.chdir(path_of_shell)
        command = f"bash {shell_name} {host} {port} {user} {password}"
        # os.system(command)
        subprocess.check_call(command, shell=True)
        logger_info.info("mysql表结构初始化成功!")
    except Exception as e:
        logger_error.error(f"mysql表结构初始化失败,报错内容:{e}")

    # 切换回之前的工作目录
    os.chdir(previous_dir)

    # 初始化产品信息
    try:
        product_info = mysql_config['product_info']
        sql, db = product_info['sql'], product_info['db_name']
        conn = pymysql.connect(host=host, port=int(port), user=user, passwd=password, db=db)
        cursor = conn.cursor()
        cursor.execute(sql)
        # 提交更改并关闭连接
        conn.commit()
        cursor.close()
        conn.close()
        logger_info.info(f'成功初始化设备信息,sql:{sql}')
    except Exception as e:
        logger_error.error(f'设备信息初始化失败，报错内容：{e}')

    '''
    对项目相关的表进行分db分表的初始化,分为知识库表和交互式表
    主要区别是一个需要原始数据导入，一个不需要
    对项目要求的表，做健康性检查，存在性检查，检查表的条目数量
    '''
    project_db = mysql_config['project_db']
    for project_config in project_db:
        if project_config['enabled']:
            init_project(project_config, logger_info, logger_warning, logger_error, mysql_basic_dic)


def init_project(project_config, logger_info, logger_warning, logger_error, mysql_basic_dic):
    # 首先直接初始化所有的表
    project_name = project_config['project_name']
    if project_name == 'anay':
        init_anay_sql()
        init_knowledge_sql()
    elif project_name == 'cert':
        init_cert_sql()
    else:
        logger_error.error(f"没有找到项目：{project_name}的sql初始化函数")
        return
    # 对初始化后的数据进行存在化检查和数量检查
    if 'knowledge_db' in project_config:
        knowledge_db = project_config['knowledge_db']
        check_knowledge_db(knowledge_db, mysql_basic_dic, logger_info, logger_warning, logger_error)
    if 'record_db' in project_config:
        record_db = project_config['record_db']
        check_record_db(record_db, mysql_basic_dic, logger_info, logger_warning, logger_error)


def check_knowledge_db(knowledge_db, mysql_basic_dic, logger_info, logger_warning, logger_error):
    host, port, user, password = mysql_basic_dic['host'], mysql_basic_dic['port'], mysql_basic_dic['user'], mysql_basic_dic['password']
    for db in knowledge_db:
        try:
            # 连接数据库
            conn = pymysql.connect(host=host, port=int(port), user=user, passwd=password, db=db['db_name'])
            cursor = conn.cursor()
            for table in db['tables']:
                table_name = table['name']
                # 存在性检查，搜一条
                if table['existence_check']['enabled']:
                    sql = table['existence_check']['query']
                    cursor.execute(sql)
                    result = cursor.fetchone()
                    # 检查结果集是否为空
                    if result:
                        logger_info.info(f"{db}数据库的{table_name}表存在且有数据！")
                    else:
                        logger_error.error(f"{db}数据库的{table_name}表存在但没有数据，退出该表检查！")
                        break
                # health_check，条目数量
                if table['health_check']['enabled']:
                    row_num = table['health_check']['row_num']
                    sql = table['health_check']['query']
                    cursor.execute(sql)
                    result = cursor.fetchone()
                    total_rows = result[0]
                    # 检查结果集是否为空
                    if total_rows == row_num:
                        logger_info.info(f"{db}数据库的{table_name}表存在且数据总量满足要求！")
                    else:
                        logger_error.error(f"{db}数据库的{table_name}表存在但数据总量存在问题！")
            # 提交更改并关闭连接
            conn.commit()
            cursor.close()
            conn.close()
        except Exception as e:
            logger_error.error(f"{db}的knowledge_db部分检查失败，报错内容：{e}")


def check_record_db(record_db, mysql_basic_dic, logger_info, logger_warning, logger_error):
    host, port, user, password = mysql_basic_dic['host'], mysql_basic_dic['port'], mysql_basic_dic['user'], mysql_basic_dic['password']
    for db in record_db:
        try:
            # 连接数据库
            conn = pymysql.connect(host=host, port=int(port), user=user, passwd=password, db=db['db_name'])
            cursor = conn.cursor()
            for table in db['tables']:
                table_name = table['name']
                # 存在性检查，搜一条
                if table['existence_check']['enabled']:
                    sql = table['existence_check']['query']
                    cursor.execute(sql)
                    result = cursor.fetchone()
                    # 检查结果集是否为空
                    if result is None:
                        logger_info.info(f"{db}数据库的{table_name}表存在且没有数据！")
                    else:
                        logger_error.error(f"{db}数据库的{table_name}表存在但有数据，清空数据！")
                        sql = "DELETE FROM {table_name}"
                        cursor.execute(sql)
                        conn.commit()
            # 提交更改并关闭连接
            conn.commit()
            cursor.close()
            conn.close()
        except Exception as e:
            logger_error.error(f"{db}的record_db部分检查失败，报错内容：{e}")


if __name__ == '__main__':
    init_mysql()










































