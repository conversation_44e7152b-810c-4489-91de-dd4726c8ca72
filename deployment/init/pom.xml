<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.geeksec</groupId>
        <artifactId>nta</artifactId>
        <version>2.0.3-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>nta-setup</artifactId>
    <packaging>pom</packaging>

    <properties>
        <image.version>latest</image.version>
    </properties>

    <build>
        <plugins>
            <!-- 执行相关的shell脚本操作 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>1.6.0</version>
                <executions>
                    <!-- 执行相关的clean脚本 -->
                    <execution>
                        <id>execution-clean-scripts</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <executable>bash</executable>
                            <arguments>
                                <argument>-c</argument>
                                <argument>rm -rf src/tmp/*,
                                    bash deploy/clear.sh</argument>
                            </arguments>
                        </configuration>
                    </execution>

                    <!-- 执行相关的下载jar包的脚本 -->
                    <execution>
                        <id>execution-install-scripts</id>
                        <phase>install</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <executable>bash</executable>
                            <arguments>
                                <argument>-c</argument>
                                <argument>
                                    bash deploy/DockerBuild/download_flink_jar.sh
                                </argument>
                            </arguments>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- 执行相关的docker的操作 -->
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>1.0.0</version>
                <configuration>
                    <imageName>setup</imageName>
                    <dockerDirectory>./</dockerDirectory>
                </configuration>
                <executions>
                    <execution>
                        <id>build-image</id>
                        <phase>install</phase>
                        <goals>
                            <goal>build</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>tag-image</id>
                        <phase>install</phase>
                        <goals>
                            <goal>tag</goal>
                        </goals>
                        <configuration>
                            <image>setup</image>
                            <newName>hb.gs.lan/nta/setup:${image.version}</newName>
                        </configuration>
                    </execution>
                    <execution>
                        <id>push-image</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>push</goal>
                        </goals>
                        <configuration>
                            <imageName>hb.gs.lan/nta/setup:${image.version}</imageName>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>