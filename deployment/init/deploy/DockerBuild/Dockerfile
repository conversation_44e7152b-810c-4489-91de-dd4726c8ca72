FROM hb.gs.lan/proxy_cache/bitnami/python:3.8

ENV LANG=zh_CN.UTF-8

# 更换apt-get源为阿里云
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list

# 安装MySQL客户端
RUN apt-get update && \
    apt-get install -y default-mysql-client vim && \
    rm -rf /var/lib/apt/lists/*

# 下载python依赖
COPY ./deploy/env/requirements.txt /app/requirements.txt

RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple pip -U && \
    pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip install --no-cache-dir -r /app/requirements.txt && \
    rm /app/requirements.txt && \
    pip uninstall kafka-python --yes && \
    pip install --no-cache-dir kafka-python==2.0.2

# 复制当前文件到指定目录下
RUN mkdir /opt/work_space
ADD ./ /opt/work_space/geeksec_secure_boot

RUN cd /opt/work_space/geeksec_secure_boot/src

WORKDIR /opt/work_space/geeksec_secure_boot/src

ENTRYPOINT ["python", "main .py"]


