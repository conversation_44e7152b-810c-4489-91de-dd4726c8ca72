<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.geeksec</groupId>
    <artifactId>183-mvn-basic</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0</version>

    <repositories>
        <repository>
            <id>geeksec-releases</id>
            <url>http://nx.gs.lan/repository/geeksec-release/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
            </releases>
        </repository>
        <repository>
            <id>geeksec-snapshots</id>
            <url>http://nx.gs.lan/repository/geeksec-snapshot/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>geeksec-releases</id>
            <name>Releases Repository</name>
            <url>http://nx.gs.lan/repository/geeksec-release/</url>
        </repository>
        <snapshotRepository>
            <id>geeksec-snapshots</id>
            <name>Snapshot Repository</name>
            <url>http://nx.gs.lan/repository/geeksec-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>
</project>