#!/bin/bash
CONFIG_FILE="src/yaml/Flink/Flink_jar.yaml"
TARGET_DIR="src/Data/Flink/jars"
# 从yaml文件获取所有的jar任务
JAR_TASKS=$(yq e '.FlinkJarWatch.jars[].jar.task_name' $CONFIG_FILE)
# 循环遍历所有的jar任务
for task_name in $JAR_TASKS; do
    # 获取单个jar任务的详细信息
    GROUP_ID=$(yq -e ".FlinkJarWatch.jars[] | select(.jar.task_name == \"$task_name\") | .jar.group_id" $CONFIG_FILE)
    ARTIFACT_ID=$(yq -e ".FlinkJarWatch.jars[] | select(.jar.task_name == \"$task_name\") | .jar.artifact_id" $CONFIG_FILE)
    VERSION=$(yq -e ".FlinkJarWatch.jars[] | select(.jar.task_name == \"$task_name\") | .jar.version" $CONFIG_FILE)
    JAR_NAME="${ARTIFACT_ID}-${VERSION}.jar"
    DOWNLOAD_NAME="${ARTIFACT_ID}.jar"
    # 使用mvn命令将jar文件从Maven仓库下载到指定的目录,-Dmdep.stripVersion=true, 此参数可移除版本号
    mvn dependency:copy -Dartifact=$GROUP_ID:$ARTIFACT_ID:$VERSION -DoutputDirectory=$TARGET_DIR -Dmdep.stripVersion=true -U
    # 将下载下来的不带版本号的jar包修改为配置的版本
    mv $TARGET_DIR/$DOWNLOAD_NAME $TARGET_DIR/$JAR_NAME
    # 计算下载的jar包的MD5值
    JAR_MD5=$(md5sum $TARGET_DIR/$JAR_NAME | awk '{ print $1 }')
    # 更新yaml文件中的jar任务MD5值
    yq -e "(.FlinkJarWatch.jars[] | select(.jar.task_name == \"$task_name\") | .jar.jar_md5) = \"$JAR_MD5\"" -i $CONFIG_FILE
    yq -e "(.FlinkJarWatch.jars[] | select(.jar.task_name == \"$task_name\") | .jar.jar_name) = \"$JAR_NAME\"" -i $CONFIG_FILE
done