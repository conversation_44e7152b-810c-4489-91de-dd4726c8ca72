#!/bin/bash
# shellcheck disable=SC1091,SC2164,SC2034,SC1072,SC1073,SC1009

URL_TMPL="http://sfp.gs.lan/d/c17ef22ab4b540f1b2ce/files/?p=/common_lib_install_local_vproduct_forensics.4d19f4ad4f96acb001118c887c85ee0e0a8337c0.tar.gz&dl=1"
TH_ENGINE_SHARE_URL="http://sfp.gs.lan/api/v2.1/share-links/c17ef22ab4b540f1b2ce/dirents/?thumbnail_size=48&path=/"
TMP_FILES_LIST=$(curl -s $TH_ENGINE_SHARE_URL) #| python2 -c "import sys, json; print json.load(sys.stdin)['dirent_list']")
export TMP_FILES_LIST
python2 - <<END
import time
import datetime
import os
import json
file_list = json.loads(os.environ['TMP_FILES_LIST'])['dirent_list']

common_lib_final = None
time_final = None
for item in file_list:
    file_name = item['file_name']
    if item['is_dir'] or (not file_name.startswith("common_lib")) or (not file_name.endswith(".tar.gz")):
        continue
    lm_time = item['last_modified']
    lm_dt = datetime.datetime.strptime(lm_time[:-6], "%Y-%m-%dT%H:%M:%S")
    ts = time.mktime(lm_dt.timetuple())
    if lm_time > time_final:
       time_final = lm_time
       common_lib_final = item["file_path"]
print time_final
print common_lib_final
with open('/tmp/TMP_COMMON_LIB_URL', 'w') as f:
    f.write('http://sfp.gs.lan/d/c17ef22ab4b540f1b2ce/files/?p=' + common_lib_final + '&dl=1')

th_release_final = None
time_final = None
for item in file_list:
    file_name = item['file_name']
    if item['is_dir'] or (not file_name.startswith("th_release")) or (not file_name.endswith(".tar.gz")):
        continue
    lm_time = item['last_modified']
    lm_dt = datetime.datetime.strptime(lm_time[:-6], "%Y-%m-%dT%H:%M:%S")
    ts = time.mktime(lm_dt.timetuple())
    if lm_time > time_final:
       time_final = lm_time
       th_release_final = item["file_path"]
print time_final
print th_release_final
with open('/tmp/TMP_TH_RELEASE_URL', 'w') as f:
    f.write('http://sfp.gs.lan/d/c17ef22ab4b540f1b2ce/files/?p=' + th_release_final + '&dl=1')
END

wget --content-disposition $(cat /tmp/TMP_COMMON_LIB_URL)
wget --content-disposition $(cat /tmp/TMP_TH_RELEASE_URL)
