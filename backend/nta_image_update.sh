#!/bin/bash
# 183 镜像生成地址使用生成对应新的地址和push到harbor上
# 接受参数 1.版本号
VERSION=$1

# 如果VERSION为空，则退出
if [ -z $VERSION ]; then
    echo "缺少指定版本号"
    exit 1
fi

rm -rf ./GeeksecApiApplication.jar

mvn clean package -P analysis-test -Dmaven.test.skip=true

# 将target里的jar放到当前目录
cp ./launch/target/GeeksecApiApplication.jar ./

# 生成docker镜像
docker build . -t hb.gs.lan/nta/geeksec-be:$VERSION

# 推送到harbor
docker push hb.gs.lan/nta/geeksec-be:$VERSION
