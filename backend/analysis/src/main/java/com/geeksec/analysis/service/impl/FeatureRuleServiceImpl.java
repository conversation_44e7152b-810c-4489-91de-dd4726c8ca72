package com.geeksec.analysis.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.csv.CsvRow;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.geeksec.analysis.dao.FeatureRuleDao;
import com.geeksec.analysis.entity.FeatureRule;
import com.geeksec.analysis.entity.condition.FeatureRuleCondition;
import com.geeksec.analysis.entity.condition.FeatureRuleSearchCondition;
import com.geeksec.analysis.entity.condition.FilterDeleteCondition;
import com.geeksec.analysis.entity.vo.FeatureBaseVo;
import com.geeksec.analysis.entity.vo.FeatureCsvVo;
import com.geeksec.analysis.entity.vo.FeatureRuleVo;
import com.geeksec.analysis.service.AlarmService;
import com.geeksec.analysis.service.FeatureRuleService;
import com.geeksec.constants.Constants;
import com.geeksec.entity.common.PageResultVo;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.FeatureRuleEnum;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.util.IpUtils;
import com.geeksec.util.TimeUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.util.*;

@Service
@Log4j2
@DS("nta-db")
public class FeatureRuleServiceImpl implements FeatureRuleService {

    @Autowired
    private FeatureRuleDao featureRuleDao;

    @Autowired
    private AlarmService alarmService;

    @Override
    public ResultVo addFeatureRule(FeatureRuleCondition condition) {
        log.info("特征规则添加规则，condition={}", condition);
        //1.判断参数合法性
        checkParam(condition);
        //2.hash判断
        Integer taskId = condition.getTaskId();
        String ruleName = condition.getRuleName();
        QueryWrapper<FeatureRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_id", taskId);
        queryWrapper.eq("rule_name", ruleName);
        List<FeatureRule> oldList = featureRuleDao.selectList(queryWrapper);
        if (oldList != null && !oldList.isEmpty()) {
            throw new GkException(GkErrorEnum.FEATURE_RULE_REPEAT);
        }
        getHash(condition);
        String ruleTypes = delRuleType(condition);
        if (StringUtils.isEmpty(ruleTypes)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        FeatureRule insertRule = new FeatureRule();
        BeanUtils.copyProperties(condition, insertRule);
        insertRule.setRuleType(ruleTypes);
        insertRule.setRuleSize(0L);
        insertRule.setLastSizeTime(0);
        insertRule.setLibRespondSessionEnd(0L);
        String hash = condition.getRuleHash();
        FeatureRule oldRule = featureRuleDao.selectOneByHash(null, hash, taskId);
        if (oldRule != null) {
            throw new GkException(GkErrorEnum.FEATURE_RULE_REPEAT);
        }
        //3.获取最大id和hash
        Integer maxRuleId = featureRuleDao.getMaxRuleId();
        Integer insertRuleId = 35001;
        if (maxRuleId == null) {
            maxRuleId = insertRuleId;
        } else if (maxRuleId < (insertRuleId + Constants.RULE_MAX)) {
            maxRuleId += 1;
        }
        condition.setRuleId(maxRuleId);
        Map<String, Object> detailRespond = condition.getDetailRespond();
        if (detailRespond != null && !detailRespond.isEmpty()) {
            detailRespond.put("APPID", maxRuleId);
            insertRule.setLibDataSo(Base64.encode(JSONObject.toJSONString(detailRespond).getBytes()));
        }
        if (condition.getLibRespondOpen() == 1) {
            //so文件名修改
            String soName = maxRuleId + ".so";
            insertRule.setLibRespondLib(soName);
            condition.setLibRespondLib(soName);
            FeatureRuleCondition.Lib libRespond = condition.getLibRespond();
            libRespond.setLib(soName);
        }

        String ruleJson = JSONObject.toJSONString(condition);
        insertRule.setRuleHash(hash);
        insertRule.setRuleJson(ruleJson);
        //4.插入数据
        insertRule.setRuleId(maxRuleId);
        insertRule.setStatus(Constants.ON);
        insertRule.setCreatedTime((int) (new Date().getTime() / 1000));
        insertRule.setUpdatedTime((int) (new Date().getTime() / 1000));
        insertRule.setRuleState(Constants.STATE_ON);
        if (insertRule.getLibRespondConfig() == null)
            insertRule.setLibRespondConfig("");
        int insert = 0;
        for (int i = 0; i < 10; i++) {
            try {
                //这里尝试执行 最多10次
                insert = featureRuleDao.insert(insertRule);
                if (insert > 0) {
                    break;
                }
            } catch (DuplicateKeyException e) {
                insertRule.setRuleId(maxRuleId + 1);
            }
        }
        if (insert == 0) {
            throw new GkException(GkErrorEnum.FEATURE_RULE_ADD_ERROR);
        }
        return ResultVo.success();
    }


    /**
     * 参数校验
     *
     * @param condition
     * @return
     */
    private ResultVo checkParam(FeatureRuleCondition condition) {
        // 规则名称
        if (StringUtils.isEmpty(condition.getRuleName())) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        // 告警等级
        Integer ruleLevel = condition.getRuleLevel();
        if (ruleLevel < 1 || ruleLevel > 100) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        Integer taskId = condition.getTaskId();
        if (taskId == null || taskId < 0) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }

        //1.ip规则判断
        ResultVo r1 = checkType1(condition);
        if (r1 != null) return r1;
        //2.协议规则判断
        ResultVo r2 = checkType2(condition);
        if (r2 != null) return r2;
        //3.特征规则判断
        ResultVo r3 = checkType3(condition);
        if (r3 != null) return r3;
        //4.正则规则判断
        ResultVo r4 = checkType4(condition);
        if (r4 != null) return r4;
        //5.域名规则判断
        ResultVo r5 = checkType5(condition);
        if (r5 != null) return r5;

        //6.开启动态库响应
        ResultVo r6 = checkType6(condition);
        if (r6 != null) return r6;

        return null;
    }

    //1.ip规则判断
    private ResultVo checkType1(FeatureRuleCondition condition) {
        List<FeatureRuleCondition.IpRule> ipRules = condition.getIpRules();
        if (ipRules == null) {
            return null;
        }
        for (FeatureRuleCondition.IpRule ipRule : ipRules) {
            if (ipRule.getIpType() < 3) {
                JSONObject ipProMap = ipRule.getIpPro();
                if ((!ipProMap.containsKey("Positive")) && (!ipProMap.containsKey("Negative"))) {
                    //Positive  正选   Negative 反选
                    throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
                }
                List<Integer> portList = new ArrayList<>();
                for (Map.Entry<String, Object> entry : ipProMap.entrySet()) {
                    Object value = entry.getValue();
                    JSONArray array = (JSONArray) JSONObject.toJSON(value);
                    for (Object o : array) {
                        portList.add(Integer.valueOf(o.toString()));
                    }
                    ResultVo resultVo = checkPort(portList, 255);
                    if (resultVo != null)
                        return resultVo;
                }
            }
            Integer ipType = ipRule.getIpType();
            switch (ipType) {
                case 1:
                    //ip4
                    FeatureRuleCondition.IpV4 ipMaskV4 = ipRule.getIpMaskV4();
                    String ipv4 = ipMaskV4.getIp();
                    if (!IpUtils.isIpv4Str(ipv4)) {
                        throw new GkException(GkErrorEnum.IP_FORMAT_ERROR);
                    }
                    String mask = ipMaskV4.getMask();
                    if (StringUtils.isEmpty(mask)) {
                        ipMaskV4.setMask("***************");
                    }
                    mask = ipMaskV4.getMask();
                    String[] split = mask.split("\\.");
                    for (String s : split) {
                        Integer integer = Integer.valueOf(s);
                        if (integer < 0 || integer > 255) {
                            throw new GkException(GkErrorEnum.IP_FORMAT_ERROR);
                        }
                    }

                    break;
                case 2:
                    //ip6
                    String ipv6 = ipRule.getIpV6();
                    if (!IpUtils.isIpv6Str(ipv6)) {
                        throw new GkException(GkErrorEnum.IP_FORMAT_ERROR);
                    }
                    break;
                case 3:
                    //任意
                    ipRule.setIpV4("0");
                    break;
                default:
                    //ipType 值有误
                    throw new GkException(GkErrorEnum.IP_FORMAT_ERROR);
            }
            FeatureRuleCondition.PortRule portRule = ipRule.getPortRule();
            if (portRule == null) {
                throw new GkException(GkErrorEnum.PORT_FORMAT_ERROR);
            }
            Integer lowPort = portRule.getLowPort();
            Integer highPort = portRule.getHighPort();
            if (lowPort > highPort || lowPort < 1 || highPort >= 65535) {
                throw new GkException(GkErrorEnum.PORT_FORMAT_ERROR);
            }
            ResultVo intVo = checkInt(portRule);
            if (intVo != null) {
                return intVo;
            }
        }
        return null;
    }

    //2.协议规则判断
    private ResultVo checkType2(FeatureRuleCondition condition) {
        List<FeatureRuleCondition.ProRule> proRules = condition.getProRules();
        if (CollUtil.isEmpty(proRules)) {
            return null;
        }
        for (FeatureRuleCondition.ProRule proRule : proRules) {
            if (proRule.getProId() == null) {
                throw new GkException(GkErrorEnum.RULE_PROTOCOL_FORMAT_ERROR);
            }
            FeatureRuleCondition.PortRule portRule = proRule.getPortRule();
            Integer lowPort = portRule.getLowPort();
            Integer highPort = portRule.getHighPort();
            if (lowPort > highPort || lowPort < 1 || highPort >= 65535) {
                throw new GkException(GkErrorEnum.PORT_FORMAT_ERROR);
            }
            ResultVo resultVo = checkInt(proRule.getPortRule());
            if (resultVo != null) return resultVo;
        }

        return null;
    }

    //3.特征规则判断
    private ResultVo checkType3(FeatureRuleCondition condition) {
        List<FeatureRuleCondition.KeyRule> keyRules = condition.getKeyRules();
        if (CollUtil.isEmpty(keyRules)) {
            return null;
        }
        for (FeatureRuleCondition.KeyRule keyRule : keyRules) {
            String keyword = keyRule.getKeyword();
            if (keyRule.getProId() == null || StringUtils.isEmpty(keyword) || keyword.length() < 4 || keyword.length() > 1000)
                throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
            Integer isCaseSensive = keyRule.getIsCaseSensive();
            if (isCaseSensive == null || isCaseSensive < 0 || isCaseSensive > 1) {
                throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
            }
        }
        return null;
    }

    //4.正则规则判断
    private ResultVo checkType4(FeatureRuleCondition condition) {
        List<FeatureRuleCondition.RegexRule> regexRules = condition.getRegexRules();
        if (CollUtil.isEmpty(regexRules)) {
            return null;
        }
        for (FeatureRuleCondition.RegexRule regexRule : regexRules) {
            Integer property = regexRule.getProperty();
            if (property == null) {
                regexRule.setProperty(1);
            }
            if (regexRule.getProId() == null || StringUtils.isEmpty(regexRule.getRegex()) || regexRule.getRegex().length() > 1000) {
                throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
            }
        }

        return null;
    }

    //5.域名规则判断
    private ResultVo checkType5(FeatureRuleCondition condition) {
        List<FeatureRuleCondition.DomainRule> domainRules = condition.getDomainRules();
        if (domainRules == null) return null;
        for (FeatureRuleCondition.DomainRule domainRule : domainRules) {
            String name = domainRule.getDomain();
            Integer type = domainRule.getType();
            if (type == null || type < 1 || type > 2)
                throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
            if (StringUtils.isEmpty(name))
                throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        return null;
    }

    //6.开启动态库响应
    private ResultVo checkType6(FeatureRuleCondition condition) {
        Integer libRespondOpen = condition.getLibRespondOpen();
        String dataSo = condition.getLibDataSo();
        if (libRespondOpen != null && libRespondOpen == 1) {
            Long libRespondPktNum = condition.getLibRespondPktNum();
            if (libRespondPktNum == null) {
                throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
            }
            Map<String, Object> map = condition.getDetailRespond();
            if (map == null || map.size() < 1) {
                if (StringUtils.isEmpty(dataSo)) {
                    throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
                }
            } else {
                //如果是导入，这里APPID字段需要后续重新生成
                map.remove("APPID");
            }
            if (map != null && map.size() > 0) {
                Object expr = map.get("EXPR");
                if (expr == null || StringUtils.isEmpty(expr.toString()))
                    throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
                //复杂规则
                for (String key : map.keySet()) {
                    //key:字母ABC
                    if (!"EXPR".equals(key)) {
                        Object o = map.get(key);
                        FeatureRuleCondition.ComplexRule complexRule =
                                JSONObject.parseObject(JSONObject.toJSONString(o), FeatureRuleCondition.ComplexRule.class);
                        String type = complexRule.getType();
                        Map<String, String> rule = complexRule.getRule();
                        if (StringUtils.isEmpty(type)) {
                            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
                        }
                        if (rule == null || rule.size() < 1) {
                            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
                        }
                    }
                }
            }
        } else {
            condition.setLibRespondConfig("");
            condition.setLibRespondLib("");
            condition.setLibRespondPktNum(0L);
        }
        return null;
    }

    private ResultVo checkPort(List<Integer> port, Integer range) {
        if (port != null && port.size() > 0) {
            for (Integer k : port) {
                if (k < 0 || k > range) {
                    throw new GkException(GkErrorEnum.PORT_FORMAT_ERROR);
                }
            }
        } else {
            throw new GkException(GkErrorEnum.PORT_FORMAT_ERROR);
        }
        return null;
    }

    private ResultVo checkInt(FeatureRuleCondition.PortRule rule) {
        Integer sign = rule.getSign();
        Integer property = rule.getProperty();
        if (sign == null || sign < 0 || sign > 3) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        if (property == null || property < 0 || property > 7) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        return null;
    }


    private void getHash(FeatureRuleCondition condition) {
        //获取hash应避免ruleId
        condition.setRuleId(null);
        delDefaults(condition);

        String jsonString = JSONObject.toJSONString(condition);
        String md5 = DigestUtils.md5DigestAsHex(jsonString.getBytes());
        condition.setRuleHash(md5);
        condition.setRuleJson(jsonString);
    }

    //默认值的set
    private void delDefaults(FeatureRuleCondition rule) {
        Long bytePs = rule.getBytePs();
        if (bytePs == null) {
            rule.setBytePs(0L);
        }
        Long saveBytes = rule.getSaveBytes();
        if (saveBytes == null) {
            rule.setSaveBytes(-1L);
        }
        Integer libRespondOpen = rule.getLibRespondOpen();
        if (libRespondOpen != null && libRespondOpen == 1) {
            Long libRespondPktNum = rule.getLibRespondPktNum();
            rule.setLibRespondPktNum(libRespondPktNum);
            FeatureRuleCondition.Lib lib = new FeatureRuleCondition.Lib();
            lib.setPktNum(libRespondPktNum);
            lib.setLib(rule.getLibRespondLib());
            rule.setLibRespond(lib);
        }
    }

    private String delRuleType(FeatureRuleCondition condition) {
        // 混合规则添加使用
        StringBuilder builder = new StringBuilder();
        List<FeatureRuleCondition.IpRule> ipRules = condition.getIpRules();
        if (ipRules != null && !ipRules.isEmpty()) {
            builder.append("1,");
        }
        List<FeatureRuleCondition.ProRule> proRules = condition.getProRules();
        if (proRules != null && !proRules.isEmpty()) {
            builder.append("2,");
        }
        List<FeatureRuleCondition.KeyRule> keyRules = condition.getKeyRules();
        if (keyRules != null && !keyRules.isEmpty()) {
            builder.append("3,");
        }
        List<FeatureRuleCondition.RegexRule> regexRules = condition.getRegexRules();
        if (regexRules != null && !regexRules.isEmpty()) {
            builder.append("4,");
        }
        List<FeatureRuleCondition.DomainRule> domainRules = condition.getDomainRules();
        if (domainRules != null && !domainRules.isEmpty()) {
            builder.append("5,");
        }
        if (condition.getLibRespondOpen() == 1) {
            Map<String, Object> detailRespond = condition.getDetailRespond();
            if (detailRespond != null && !detailRespond.isEmpty()) {
                builder.append("7,");
            } else {
                builder.append("6,");
            }
        }
        return builder.toString();
    }

    @Override
    public Integer deleteFeatureRule(FilterDeleteCondition condition) {
        log.info("特征规则删除规则，condition={}", condition);
        return featureRuleDao.deleteFeatureRules(condition);
    }

    @Override
    public ResultVo updateFeatureRule(FeatureRuleCondition condition) {
        log.info("特征规则修改规则，condition={}", condition);
        condition.setRuleId(null);
        ResultVo errorVo = null;
        //1.判断参数合法性
        Long id = condition.getId();
        if (id == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        FeatureRule rule = featureRuleDao.selectById(id);
        if (rule == null || rule.getStatus() != Constants.ON) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        checkParam(condition);
        //2.hash判断
        Integer taskId = rule.getTaskId();
        String ruleName = condition.getRuleName();
        QueryWrapper<FeatureRule> queryWrapper = new QueryWrapper<>();
        //queryWrapper.eq("status", Constants.ON);
        queryWrapper.eq("rule_name", ruleName);
        //queryWrapper.eq("task_id",taskId);
        //排除自己
        queryWrapper.ne("id", id);
        List<FeatureRule> oldList = featureRuleDao.selectList(queryWrapper);
        if (oldList != null && oldList.size() > 0) {
            throw new GkException(GkErrorEnum.FEATURE_RULE_REPEAT);
        }
        getHash(condition);
        FeatureRule updateRule = new FeatureRule();
        BeanUtils.copyProperties(condition, updateRule);
        String hash = condition.getRuleHash();
        //排除自己
        FeatureRule oldRule = featureRuleDao.selectOneByHash(id, hash, taskId);
        if (oldRule != null) {
            throw new GkException(GkErrorEnum.FEATURE_RULE_REPEAT);
        }
        //3.获取hash
        Map<String, Object> detailRespond = condition.getDetailRespond();
        if (detailRespond != null && detailRespond.size() > 0) {
            //这里直接使用旧对象的id
            detailRespond.put("APPID", rule.getRuleId());
        }
        if (condition.getLibRespondOpen() == 1) {
            //so文件名修改
            String soName = rule.getRuleId() + ".so";
            updateRule.setLibRespondLib(soName);
            condition.setLibRespondLib(soName);
            FeatureRuleCondition.Lib libRespond = condition.getLibRespond();
            libRespond.setLib(soName);
        }
        condition.setRuleId(rule.getRuleId());
        String ruleJson = JSONObject.toJSONString(condition);
        updateRule.setId(rule.getId());
        updateRule.setRuleJson(ruleJson);

        //4.更新
        String ruleTypes = delRuleType(condition);
        updateRule.setRuleType(ruleTypes);
        updateRule.setTaskId(null);
        updateRule.setUpdatedTime((int) (new Date().getTime() / 1000));
        featureRuleDao.updateById(updateRule);
        return ResultVo.success();
    }

    @Override
    public PageResultVo<FeatureRuleVo> getFeatureRules(FeatureRuleSearchCondition condition) {
        log.info("特征规则：列表，condition={}", condition);
        PageHelper.startPage(condition.getCurrentPage(), condition.getPageSize());
        List<FeatureRule> list = featureRuleDao.getList(condition);
        PageInfo<FeatureRule> info = new PageInfo<>(list);
        List<FeatureRuleVo> resultList = new ArrayList<>();
        for (FeatureRule rule : list) {
            FeatureRuleVo vo = new FeatureRuleVo();
            BeanUtils.copyProperties(rule, vo);
            String ruleJson = rule.getRuleJson();
            FeatureRuleVo json = JSONObject.parseObject(ruleJson, FeatureRuleVo.class);
            vo.setDetailRespond(json.getDetailRespond());
            List<FeatureRuleCondition.IpRule> ipRules = json.getIpRules();
            vo.setIpRules(ipRules);
            if (ipRules != null && ipRules.size() > 0) {
                delIpType(vo.getIpRules());
            }
            vo.setKeyRules(json.getKeyRules());
            vo.setProRules(json.getProRules());
            vo.setRegexRules(json.getRegexRules());
            vo.setDomainRules(json.getDomainRules());
            resultList.add(vo);
        }
        PageResultVo<FeatureRuleVo> result = new PageResultVo();
        result.setRecords(resultList);
        result.setTotal(info.getTotal());
        return result;
    }

    @Override
    public FeatureRuleVo getFeatureRule(Long id) {
        log.info("特征规则：详情，condition={}", id);
        FeatureRule rule = featureRuleDao.selectById(id);
        if (rule == null) {
            return null;
        }
        FeatureRuleVo vo = new FeatureRuleVo();
        BeanUtils.copyProperties(rule, vo);
        String ruleJson = rule.getRuleJson();
        FeatureRuleVo json = JSONObject.parseObject(ruleJson, FeatureRuleVo.class);
        vo.setDetailRespond(json.getDetailRespond());
        List<FeatureRuleCondition.IpRule> ipRules = json.getIpRules();
        vo.setIpRules(ipRules);
        if (ipRules != null && ipRules.size() > 0) {
            delIpType(vo.getIpRules());
        }
        vo.setKeyRules(json.getKeyRules());
        vo.setProRules(json.getProRules());
        vo.setRegexRules(json.getRegexRules());
        vo.setDomainRules(json.getDomainRules());

        return vo;
    }

    @Override
    public Integer updateRuleState(Long id, String state) {
        log.info("特征规则：修改状态，id={},state={}", id, state);
        FeatureRule rule = new FeatureRule();
        rule.setRuleState(state);
        rule.setId(id);
        rule.setUpdatedTime((int) (new Date().getTime() / 1000));
        return featureRuleDao.updateById(rule);

    }

    @Override
    public ResultVo<FeatureCsvVo> csvImport(Integer taskId, List<CsvRow> rows) {
        log.info("csv导入特征规则开始");
        Map<String, FeatureRuleCondition> map = new HashMap<>();
        for (CsvRow csvRow : rows) {
            //打印出该行的内容信息
            List<String> rawList = csvRow.getRawList();
            if ("rule_id".equals(rawList.get(0))) {
                continue;
            }
            //idCard相同 表示这条数据属于同一对象
            String idCard = rawList.get(0);
            FeatureRuleCondition featureRuleCondition = null;
            if (map.containsKey(idCard)) {
                featureRuleCondition = map.get(idCard);
            } else {
                featureRuleCondition = new FeatureRuleCondition();
                map.put(idCard, featureRuleCondition);
            }
            featureRuleCondition.setTaskId(taskId);
            delJson(featureRuleCondition, rawList);
        }
        FeatureCsvVo result = new FeatureCsvVo();
        result.setTotalNum(map.size());
        int suc = 0;
        List<String> rep = new ArrayList<>();
        List<String> error = new ArrayList<>();
        for (String key : map.keySet()) {
            FeatureRuleCondition featureRuleCondition = map.get(key);
            String ruleName = featureRuleCondition.getRuleName();
            int one = ruleName.lastIndexOf("_");
            if (one > 0) {
                String time = ruleName.substring((one + 1));
                String newName = ruleName.substring(0, (one + 1));
                try {
                    //尝试执行时间戳转换
                    TimeUtil.getNormalTime(Long.parseLong(time));
                    ruleName = newName + new Date().getTime();
                } catch (Exception e) {
                    ruleName = newName + "_" + new Date().getTime();
                }
            } else {
                ruleName = ruleName + "_" + new Date().getTime();
            }
            featureRuleCondition.setRuleName(ruleName);
            ResultVo resultVo = addFeatureRule(featureRuleCondition);
            if (resultVo.getErr() == 0) {
                suc++;
                continue;
            } else {
                log.error("特征规则导入失败->{}", resultVo.getMsg());
                throw new GkException(GkErrorEnum.FEATURE_IMPORT_ERROR);
            }
        }
        result.setSucNum(suc);
        result.setErrorList(error);
        result.setRepeatList(rep);
        result.setFailNum(result.getTotalNum() - result.getSucNum());
        return ResultVo.success(result);
    }

    private void delJson(FeatureRuleCondition condition, List<String> rawList) {
        String ruleType = rawList.get(2);
        //下标1  是task_id  忽略的字段
        String json = rawList.get(3);
        switch (Objects.requireNonNull(FeatureRuleEnum.checkType(ruleType))) {
            case BASE:
                FeatureBaseVo baseCondition = JSONObject.parseObject(json, FeatureBaseVo.class);
                BeanUtils.copyProperties(baseCondition, condition);
                break;
            case DETAIL:
                HashMap hashMap = JSONObject.parseObject(json, HashMap.class);
                condition.setDetailRespond(hashMap);
                break;
            case IP:
                List<FeatureRuleCondition.IpRule> ipRules = JSONObject.parseArray(json, FeatureRuleCondition.IpRule.class);
                for (FeatureRuleCondition.IpRule ipRule : ipRules) {
                    FeatureRuleCondition.IpV4 ipMaskV4 = ipRule.getIpMaskV4();
                    String ipV6 = ipRule.getIpV6();
                    if (ipMaskV4 != null) {
                        ipRule.setIpType(1);
                    } else if (StringUtils.isNotEmpty(ipV6)) {
                        ipRule.setIpType(2);
                    } else
                        ipRule.setIpType(3);
                }
                condition.setIpRules(ipRules);
                break;
            case PRO:
                List<FeatureRuleCondition.ProRule> proRules = JSONObject.parseArray(json, FeatureRuleCondition.ProRule.class);
                condition.setProRules(proRules);
                break;
            case KEY:
                List<FeatureRuleCondition.KeyRule> keyRules = JSONObject.parseArray(json, FeatureRuleCondition.KeyRule.class);
                condition.setKeyRules(keyRules);
                break;
            case REGEX:
                List<FeatureRuleCondition.RegexRule> regexRules = JSONObject.parseArray(json, FeatureRuleCondition.RegexRule.class);
                condition.setRegexRules(regexRules);
                break;
            case DOMAIN:
                List<FeatureRuleCondition.DomainRule> domainRules = JSONObject.parseArray(json, FeatureRuleCondition.DomainRule.class);
                condition.setDomainRules(domainRules);
                break;
            default:
                //异常
        }
    }

    @Override
    public List<List<String>> getCsv(FilterDeleteCondition condition) {
        List<Long> ids = condition.getIds();
        String taskId = condition.getTaskId().toString();
        QueryWrapper<FeatureRule> queryWrapper = new QueryWrapper<>();
        if (ids != null && ids.size() > 0) {
            queryWrapper.in("id", ids);
        }
        queryWrapper.eq("task_id", taskId);
        queryWrapper.eq("status", Constants.ON);
        List<FeatureRule> featureRules = featureRuleDao.selectList(queryWrapper);
        List<List<String>> list = new ArrayList<>();
        List<String> title = new ArrayList<>();
        title.add("rule_id");
        title.add("task_id");
        title.add("rule_type");
        title.add("json");
        list.add(title);
        for (FeatureRule rule : featureRules) {
            //一行数据
            List<String> baseRows = new ArrayList<>();
            Integer ruleId = rule.getRuleId();
            baseRows.add(ruleId.toString());
            baseRows.add(taskId);
            baseRows.add("base_rule");
            list.add(baseRows);
            FeatureBaseVo baseVo = new FeatureBaseVo();
            BeanUtils.copyProperties(rule, baseVo);
            baseRows.add(JSONObject.toJSONString(baseVo));
            String ruleJson = rule.getRuleJson();
            FeatureRuleVo json = JSONObject.parseObject(ruleJson, FeatureRuleVo.class);
            Map<String, Object> detailRespond = json.getDetailRespond();
            if (detailRespond != null && detailRespond.size() > 0) {
                List<String> deRows = new ArrayList<>();
                deRows.add(ruleId.toString());
                deRows.add(taskId);
                deRows.add("detail_respond");
                deRows.add(JSONObject.toJSONString(detailRespond));
                list.add(deRows);
            }
            List<FeatureRuleCondition.IpRule> ipRules = json.getIpRules();
            if (ipRules != null && ipRules.size() > 0) {
                delIpType(ipRules);
                List<String> ipRows = new ArrayList<>();
                ipRows.add(ruleId.toString());
                ipRows.add(taskId);
                ipRows.add("ip_rules");
                ipRows.add(JSONObject.toJSONString(ipRules));
                list.add(ipRows);
            }
            List<FeatureRuleCondition.ProRule> proRules = json.getProRules();
            if (proRules != null && proRules.size() > 0) {
                List<String> proRows = new ArrayList<>();
                proRows.add(ruleId.toString());
                proRows.add(taskId);
                proRows.add("pro_rules");
                proRows.add(JSONObject.toJSONString(proRules));
                list.add(proRows);
            }
            List<FeatureRuleCondition.KeyRule> keyRules = json.getKeyRules();
            if (keyRules != null && keyRules.size() > 0) {
                List<String> keyRows = new ArrayList<>();
                keyRows.add(ruleId.toString());
                keyRows.add(taskId);
                keyRows.add("key_rules");
                keyRows.add(JSONObject.toJSONString(keyRules));
                list.add(keyRows);
            }
            List<FeatureRuleCondition.RegexRule> regexRules = json.getRegexRules();
            if (regexRules != null && regexRules.size() > 0) {
                List<String> reRows = new ArrayList<>();
                reRows.add(ruleId.toString());
                reRows.add(taskId);
                reRows.add("regex_rules");
                reRows.add(JSONObject.toJSONString(regexRules));
                list.add(reRows);
            }
            List<FeatureRuleCondition.DomainRule> domainRules = json.getDomainRules();
            if (domainRules != null && domainRules.size() > 0) {
                List<String> doRows = new ArrayList<>();
                doRows.add(ruleId.toString());
                doRows.add(taskId);
                doRows.add("domain_rules");
                doRows.add(JSONObject.toJSONString(domainRules));
                list.add(doRows);
            }
        }

        return list;
    }

    private void delIpType(List<FeatureRuleCondition.IpRule> ipRules) {
        for (FeatureRuleCondition.IpRule ipRule : ipRules) {
            String ipV6 = ipRule.getIpV6();
            String ipV4 = ipRule.getIpV4();
            FeatureRuleCondition.IpV4 ipMaskV4 = ipRule.getIpMaskV4();
            if (StringUtils.isNotBlank(ipV6))
                ipRule.setIpType(2);
            if (StringUtils.isNotBlank(ipV4))
                ipRule.setIpType(3);
            if (ipMaskV4 != null && StringUtils.isNotBlank(ipMaskV4.getIp())) {
                ipRule.setIpType(1);
            }
        }
    }

}
