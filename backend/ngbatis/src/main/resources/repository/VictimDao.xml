<mapper namespace="com.geeksec.ngbatis.repository.VictimDao">

    <select id="listVictimAllEdgeTypeAssociationNext" resultType="com.geeksec.ngbatis.vo.VertexEdgeNextVo">
        MATCH (VICTIM:VICTIM)
        WHERE id(VICTIM) == ${ng.valueFmt(condition.str)}
        RETURN DISTINCT
        "VICTIM" AS fromType,
        0 AS fromBlackList,
        VICTIM.VICTIM.victim_id AS fromAddr,
        "" AS toType,
        0 AS toBlackList,
        "" AS toAddr,
        true as sourceStatus,
        false as directionStatus,
        properties(VICTIM) AS vInfo,
        "" AS teage
        LIMIT 1

        @for( edgeItem in condition.edgeInfo ) {
            @if (edgeItem.edge == 'make_attack'){
                UNION ALL
                MATCH
                (VICTIM:VICTIM)-[make_attack:make_attack]-(ATTACKER:ATTACKER)
                WHERE
                id(VICTIM) == ${ng.valueFmt(condition.str)}
                RETURN
                "ATTACKER" AS fromType,
                0 AS fromBlackList,
                ATTACKER.ATTACKER.attack_id AS fromAddr,
                "VICTIM" AS toType,
                0 AS toBlackList,
                VICTIM.VICTIM.victim_id AS toAddr,
                false as sourceStatus,
                true as directionStatus,
                properties(ATTACKER) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
        @}
    </select>

</mapper>