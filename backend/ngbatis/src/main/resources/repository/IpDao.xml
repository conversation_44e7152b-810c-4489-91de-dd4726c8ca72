<mapper namespace="com.geeksec.ngbatis.repository.IpDao">

    <select id="listIpAllEdgeTypeAssociation" resultType="com.geeksec.ngbatis.vo.VertexEdgeVo">
        MATCH (IP:IP)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "" AS toType,
        0 AS toBlackList,
        "" AS toAddr,
        "" AS toLabel,
        "YES" AS middleType,
        "" AS middleId,
        "" AS middleLabel,
        true as sourceStatus,
        true as directionStatus
        LIMIT 1

        UNION ALL
        MATCH (IP:IP)-[src_bind:src_bind]->(MAC:MAC)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "MAC" AS toType,
        MAC.MAC.black_list AS toBlackList,
        MAC.MAC.mac AS toAddr,
        MAC.MAC.mac AS toLabel,
        "Folder" AS middleType,
        "src_bind" AS middleId,
        "s_MAC" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[dst_bind:dst_bind]->(MAC:MAC)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "MAC" AS toType,
        MAC.MAC.black_list AS toBlackList,
        MAC.MAC.mac AS toAddr,
        MAC.MAC.mac AS toLabel,
        "Folder" AS middleType,
        "dst_bind" AS middleId,
        "d_MAC" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[connect_ip:connect_ip]->(IP1:IP)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "IP" AS toType,
        IP1.IP.black_list AS toBlackList,
        IP1.IP.ip_addr AS toAddr,
        IP1.IP.ip_addr AS toLabel,
        "Folder" AS middleType,
        "connect_ip" AS middleId,
        "s_IP" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[client_query_domain:client_query_domain]->(DOMAIN:DOMAIN)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "DOMAIN" AS toType,
        DOMAIN.DOMAIN.black_list AS toBlackList,
        DOMAIN.DOMAIN.domain_addr AS toAddr,
        DOMAIN.DOMAIN.domain_addr AS toLabel,
        "Folder" AS middleType,
        "client_query_domain" AS middleId,
        "s_DOMAIN" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[client_query_dns_server:client_query_dns_server]->(IP1:IP)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "IP" AS toType,
        IP1.IP.black_list AS toBlackList,
        IP1.IP.ip_addr AS toAddr,
        IP1.IP.ip_addr AS toLabel,
        "Folder" AS middleType,
        "client_query_dns_server" AS middleId,
        "d_IP" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[dns_server_domain:dns_server_domain]->(DOMAIN:DOMAIN)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "DOMAIN" AS toType,
        DOMAIN.DOMAIN.black_list AS toBlackList,
        DOMAIN.DOMAIN.domain_addr AS toAddr,
        DOMAIN.DOMAIN.domain_addr AS toLabel,
        "Folder" AS middleType,
        "dns_server_domain" AS middleId,
        "d_DOMAIN" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[client_ssl_connect_domain:client_ssl_connect_domain]->(DOMAIN:DOMAIN)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "DOMAIN" AS toType,
        DOMAIN.DOMAIN.black_list AS toBlackList,
        DOMAIN.DOMAIN.domain_addr AS toAddr,
        DOMAIN.DOMAIN.domain_addr AS toLabel,
        "Folder" AS middleType,
        "client_ssl_connect_domain" AS middleId,
        "s_DOMAIN" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[server_ssl_connect_domain:server_ssl_connect_domain]->(DOMAIN:DOMAIN)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "DOMAIN" AS toType,
        DOMAIN.DOMAIN.black_list AS toBlackList,
        DOMAIN.DOMAIN.domain_addr AS toAddr,
        DOMAIN.DOMAIN.domain_addr AS toLabel,
        "Folder" AS middleType,
        "server_ssl_connect_domain" AS middleId,
        "d_DOMAIN" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[server_use_cert:server_use_cert]->(CERT:CERT)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "CERT" AS toType,
        CERT.CERT.black_list AS toBlackList,
        CERT.CERT.cert_id AS toAddr,
        CERT.CERT.cert_id AS toLabel,
        "Folder" AS middleType,
        "server_use_cert" AS middleId,
        "d_CERT" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[client_use_cert:client_use_cert]->(CERT:CERT)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "CERT" AS toType,
        CERT.CERT.black_list AS toBlackList,
        CERT.CERT.cert_id AS toAddr,
        CERT.CERT.cert_id AS toLabel,
        "Folder" AS middleType,
        "client_use_cert" AS middleId,
        "s_CERT" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[client_connect_cert:client_connect_cert]->(CERT:CERT)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "CERT" AS toType,
        CERT.CERT.black_list AS toBlackList,
        CERT.CERT.cert_id AS toAddr,
        CERT.CERT.cert_id AS toLabel,
        "Folder" AS middleType,
        "client_connect_cert" AS middleId,
        "d_CERT" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[server_use_sslfinger:server_use_sslfinger]->(SSLFINGER:SSLFINGER)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "SSLFINGER" AS toType,
        0 AS toBlackList,
        SSLFINGER.SSLFINGER.finger_id AS toAddr,
        SSLFINGER.SSLFINGER.finger_id AS toLabel,
        "Folder" AS middleType,
        "server_use_sslfinger" AS middleId,
        "d_SSLFINGER" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[client_use_sslfinger:client_use_sslfinger]->(SSLFINGER:SSLFINGER)
        WHERE id(IP) == $ip
        RETURN "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "SSLFINGER" AS toType,
        0 AS toBlackList,
        SSLFINGER.SSLFINGER.finger_id AS toAddr,
        SSLFINGER.SSLFINGER.finger_id AS toLabel,
        "Folder" AS middleType,
        "client_use_sslfinger" AS middleId,
        "s_SSLFINGER" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[client_http_connect_domain:client_http_connect_domain]->(DOMAIN:DOMAIN)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "DOMAIN" AS toType,
        DOMAIN.DOMAIN.black_list AS toBlackList,
        DOMAIN.DOMAIN.domain_addr AS toAddr,
        DOMAIN.DOMAIN.domain_addr AS toLabel,
        "Folder" AS middleType,
        "client_http_connect_domain" AS middleId,
        "s_DOMAIN" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[server_http_connect_domain:server_http_connect_domain]->(DOMAIN:DOMAIN)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "DOMAIN" AS toType,
        DOMAIN.DOMAIN.black_list AS toBlackList,
        DOMAIN.DOMAIN.domain_addr AS toAddr,
        DOMAIN.DOMAIN.domain_addr AS toLabel,
        "Folder" AS middleType,
        "server_http_connect_domain" AS middleId,
        "d_DOMAIN" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[client_app:client_app]->(APPSERVICE:APPSERVICE)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "APPSERVICE" AS toType,
        0 AS toBlackList,
        APPSERVICE.APPSERVICE.service_key AS toAddr,
        APPSERVICE.APPSERVICE.service_key AS toLabel,
        "Folder" AS middleType,
        "client_app" AS middleId,
        "s_APPSERVICE" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[client_use_ua:client_use_ua]->(UA:UA)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "UA" AS toType,
        0 AS toBlackList,
        UA.UA.ua_id AS toAddr,
        UA.UA.ua_id AS toLabel,
        "Folder" AS middleType,
        "client_use_ua" AS middleId,
        "d_UA" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[ip_belong_to_org:ip_belong_to_org]->(ORG:ORG)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "ORG" AS toType,
        ORG.ORG.black_list AS toBlackList,
        id(ORG) AS toAddr,
        ORG.ORG.org_name AS toLabel,
        "Folder" AS middleType,
        "ip_belong_to_org" AS middleId,
        "d_ORG" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP1:IP)-[connect_ip:connect_ip]->(IP:IP)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP1.IP.black_list AS fromBlackList,
        IP1.IP.ip_addr AS fromAddr,
        IP1.IP.ip_addr AS fromLabel,
        "IP" AS toType,
        IP.IP.black_list AS toBlackList,
        IP.IP.ip_addr AS toAddr,
        IP.IP.ip_addr AS toLabel,
        "Folder" AS middleType,
        "connect_ip" AS middleId,
        "d_IP" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP1:IP)-[client_query_dns_server:client_query_dns_server]->(IP:IP)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP1.IP.black_list AS fromBlackList,
        IP1.IP.ip_addr AS fromAddr,
        IP1.IP.ip_addr AS fromLabel,
        "IP" AS toType,
        IP.IP.black_list AS toBlackList,
        IP.IP.ip_addr AS toAddr,
        IP.IP.ip_addr AS toLabel,
        "Folder" AS middleType,
        "client_query_dns_server" AS middleId,
        "d_IP" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[parse_to:parse_to]-(DOMAIN:DOMAIN)
        WHERE id(IP) == $ip
        RETURN DISTINCT "DOMAIN" AS fromType,
        DOMAIN.DOMAIN.black_list AS fromBlackList,
        DOMAIN.DOMAIN.domain_addr AS fromAddr,
        DOMAIN.DOMAIN.domain_addr AS fromLabel,
        "IP" AS toType,
        IP.IP.black_list AS toBlackList,
        IP.IP.ip_addr AS toAddr,
        IP.IP.ip_addr AS toLabel,
        "Folder" AS middleType,
        "parse_to" AS middleId,
        "d_DOMAIN" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[cname_result:cname_result]-(DOMAIN:DOMAIN)
        WHERE id(IP) == $ip
        RETURN DISTINCT "DOMAIN" AS fromType,
        DOMAIN.DOMAIN.black_list AS fromBlackList,
        DOMAIN.DOMAIN.domain_addr AS fromAddr,
        DOMAIN.DOMAIN.domain_addr AS fromLabel,
        "IP" AS toType,
        IP.IP.black_list AS toBlackList,
        IP.IP.ip_addr AS toAddr,
        IP.IP.ip_addr AS toLabel,
        "Folder" AS middleType,
        "cname_result" AS middleId,
        "d_DOMAIN" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[app_server:app_server]-(APPSERVICE:APPSERVICE)
        WHERE id(IP) == $ip
        RETURN DISTINCT "APPSERVICE" AS fromType,
        0 AS fromBlackList,
        APPSERVICE.APPSERVICE.service_key AS fromAddr,
        APPSERVICE.APPSERVICE.service_key AS fromLabel,
        "IP" AS toType,
        IP.IP.black_list AS toBlackList,
        IP.IP.ip_addr AS toAddr,
        IP.IP.ip_addr AS toLabel,
        "Folder" AS middleType,
        "app_server" AS middleId,
        "d_APPSERVICE" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[ip_belong_app:ip_belong_app]->(APP:APP)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "APP" AS toType,
        0 AS toBlackList,
        id(APP) AS toAddr,
        id(APP) AS toLabel,
        "Folder" AS middleType,
        "ip_belong_app" AS middleId,
        "d_APP" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (IP:IP)-[ip_url_related:ip_url_related]->(URL:URL)
        WHERE id(IP) == $ip
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "URL" AS toType,
        URL.URL.black_list AS toBlackList,
        URL.URL.url_key AS toAddr,
        URL.URL.url_key AS toLabel,
        "Folder" AS middleType,
        "ip_url_related" AS middleId,
        "d_URL" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5
    </select>

    <select id="listIpAllEdgeTypeAssociationNext" resultType="com.geeksec.ngbatis.vo.VertexEdgeNextVo">
        MATCH (IP:IP)
        WHERE id(IP) == ${ng.valueFmt(condition.str)}
        RETURN DISTINCT
        "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "" AS toType,
        0 AS toBlackList,
        "" AS toAddr,
        "" AS toLabel,
        true as sourceStatus,
        false as directionStatus,
        null AS vInfo,
        "" AS teage
        LIMIT 1

        @for( edgeItem in condition.edgeInfo ) {
            @if (edgeItem.edge == 'src_bind'){
                UNION ALL
                MATCH
                (IP:IP)-[src_bind:src_bind]->(MAC:MAC)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND MAC.MAC.black_list >= ${edgeItem.weightLimit[0]} AND MAC.MAC.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "MAC" AS toType,
                MAC.MAC.black_list AS toBlackList,
                MAC.MAC.mac AS toAddr,
                MAC.MAC.mac AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(MAC) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'dst_bind'){
                UNION ALL
                MATCH
                (IP:IP)-[dst_bind:dst_bind]->(MAC:MAC)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND MAC.MAC.black_list >= ${edgeItem.weightLimit[0]} AND MAC.MAC.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "MAC" AS toType,
                MAC.MAC.black_list AS toBlackList,
                MAC.MAC.mac AS toAddr,
                MAC.MAC.mac AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(MAC) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'connect_ip'){
                UNION ALL
                MATCH
                (IP:IP)-[connect_ip:connect_ip]->(IP1:IP)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND IP1.IP.black_list >= ${edgeItem.weightLimit[0]} AND IP1.IP.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "IP" AS toType,
                IP1.IP.black_list AS toBlackList,
                IP1.IP.ip_addr AS toAddr,
                IP1.IP.ip_addr AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(IP1) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'client_query_domain'){
                UNION ALL
                MATCH
                (IP:IP)-[client_query_domain:client_query_domain]->(DOMAIN:DOMAIN)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND DOMAIN.DOMAIN.black_list >= ${edgeItem.weightLimit[0]} AND DOMAIN.DOMAIN.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "DOMAIN" AS toType,
                DOMAIN.DOMAIN.black_list AS toBlackList,
                DOMAIN.DOMAIN.domain_addr AS toAddr,
                DOMAIN.DOMAIN.domain_addr AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(DOMAIN) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'client_query_dns_server'){
                UNION ALL
                MATCH
                (IP:IP)-[client_query_dns_server:client_query_dns_server]->(IP1:IP)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND IP1.IP.black_list >= ${edgeItem.weightLimit[0]} AND IP1.IP.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "IP" AS toType,
                IP1.IP.black_list AS toBlackList,
                IP1.IP.ip_addr AS toAddr,
                IP1.IP.ip_addr AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(IP1) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'dns_server_domain'){
                UNION ALL
                MATCH
                (IP:IP)-[dns_server_domain:dns_server_domain]->(DOMAIN:DOMAIN)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND DOMAIN.DOMAIN.black_list >= ${edgeItem.weightLimit[0]} AND DOMAIN.DOMAIN.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "DOMAIN" AS toType,
                DOMAIN.DOMAIN.black_list AS toBlackList,
                DOMAIN.DOMAIN.domain_addr AS toAddr,
                DOMAIN.DOMAIN.domain_addr AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(DOMAIN) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'client_ssl_connect_domain'){
                UNION ALL
                MATCH
                (IP:IP)-[client_ssl_connect_domain:client_ssl_connect_domain]->(DOMAIN:DOMAIN)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND DOMAIN.DOMAIN.black_list >= ${edgeItem.weightLimit[0]} AND DOMAIN.DOMAIN.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "DOMAIN" AS toType,
                DOMAIN.DOMAIN.black_list AS toBlackList,
                DOMAIN.DOMAIN.domain_addr AS toAddr,
                DOMAIN.DOMAIN.domain_addr AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(DOMAIN) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'server_ssl_connect_domain'){
                UNION ALL
                MATCH
                (IP:IP)-[server_ssl_connect_domain:server_ssl_connect_domain]->(DOMAIN:DOMAIN)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND DOMAIN.DOMAIN.black_list >= ${edgeItem.weightLimit[0]} AND DOMAIN.DOMAIN.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "DOMAIN" AS toType,
                DOMAIN.DOMAIN.black_list AS toBlackList,
                DOMAIN.DOMAIN.domain_addr AS toAddr,
                DOMAIN.DOMAIN.domain_addr AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(DOMAIN) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'server_use_cert'){
                UNION ALL
                MATCH
                (IP:IP)-[server_use_cert:server_use_cert]->(CERT:CERT)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND CERT.CERT.black_list >= ${edgeItem.weightLimit[0]} AND CERT.CERT.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "CERT" AS toType,
                CERT.CERT.black_list AS toBlackList,
                CERT.CERT.cert_id AS toAddr,
                CERT.CERT.cert_id AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(CERT) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'client_use_cert'){
                UNION ALL
                MATCH
                (IP:IP)-[client_use_cert:client_use_cert]->(CERT:CERT)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND CERT.CERT.black_list >= ${edgeItem.weightLimit[0]} AND CERT.CERT.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "CERT" AS toType,
                CERT.CERT.black_list AS toBlackList,
                CERT.CERT.cert_id AS toAddr,
                CERT.CERT.cert_id AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(CERT) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'client_connect_cert'){
                UNION ALL
                MATCH
                (IP:IP)-[client_connect_cert:client_connect_cert]->(CERT:CERT)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND CERT.CERT.black_list >= ${edgeItem.weightLimit[0]} AND CERT.CERT.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "CERT" AS toType,
                CERT.CERT.black_list AS toBlackList,
                CERT.CERT.cert_id AS toAddr,
                CERT.CERT.cert_id AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(CERT) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'server_use_sslfinger'){
                UNION ALL
                MATCH
                (IP:IP)-[server_use_sslfinger:server_use_sslfinger]->(SSLFINGER:SSLFINGER)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "SSLFINGER" AS toType,
                0 AS toBlackList,
                SSLFINGER.SSLFINGER.finger_id AS toAddr,
                SSLFINGER.SSLFINGER.finger_id AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(SSLFINGER) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'client_use_sslfinger'){
                UNION ALL
                MATCH
                (IP:IP)-[client_use_sslfinger:client_use_sslfinger]->(SSLFINGER:SSLFINGER)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "SSLFINGER" AS toType,
                0 AS toBlackList,
                SSLFINGER.SSLFINGER.finger_id AS toAddr,
                SSLFINGER.SSLFINGER.finger_id AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(SSLFINGER) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'client_http_connect_domain'){
                UNION ALL
                MATCH
                (IP:IP)-[client_http_connect_domain:client_http_connect_domain]->(DOMAIN:DOMAIN)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND DOMAIN.DOMAIN.black_list >= ${edgeItem.weightLimit[0]} AND DOMAIN.DOMAIN.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "DOMAIN" AS toType,
                DOMAIN.DOMAIN.black_list AS toBlackList,
                DOMAIN.DOMAIN.domain_addr AS toAddr,
                DOMAIN.DOMAIN.domain_addr AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(DOMAIN) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'server_http_connect_domain'){
                UNION ALL
                MATCH
                (IP:IP)-[server_http_connect_domain:server_http_connect_domain]->(DOMAIN:DOMAIN)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND DOMAIN.DOMAIN.black_list >= ${edgeItem.weightLimit[0]} AND DOMAIN.DOMAIN.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "DOMAIN" AS toType,
                DOMAIN.DOMAIN.black_list AS toBlackList,
                DOMAIN.DOMAIN.domain_addr AS toAddr,
                DOMAIN.DOMAIN.domain_addr AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(DOMAIN) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'client_app'){
                UNION ALL
                MATCH
                (IP:IP)-[client_app:client_app]->(APPSERVICE:APPSERVICE)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "APPSERVICE" AS toType,
                0 AS toBlackList,
                APPSERVICE.APPSERVICE.service_key AS toAddr,
                APPSERVICE.APPSERVICE.service_key AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(APPSERVICE) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'client_use_ua'){
                UNION ALL
                MATCH
                (IP:IP)-[client_use_ua:client_use_ua]->(UA:UA)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "UA" AS toType,
                0 AS toBlackList,
                UA.UA.ua_id AS toAddr,
                UA.UA.ua_id AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(UA) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'ip_belong_to_org'){
                UNION ALL
                MATCH
                (IP:IP)-[ip_belong_to_org:ip_belong_to_org]->(ORG:ORG)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND ORG.ORG.black_list >= ${edgeItem.weightLimit[0]} AND ORG.ORG.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "ORG" AS toType,
                ORG.ORG.black_list AS toBlackList,
                id(ORG) AS toAddr,
                ORG.ORG.org_name AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(ORG) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'r_connect_ip'){
                UNION ALL
                MATCH
                (IP1:IP)-[connect_ip:connect_ip]->(IP:IP)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND IP1.IP.black_list >= ${edgeItem.weightLimit[0]} AND IP1.IP.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP1.IP.black_list AS fromBlackList,
                IP1.IP.ip_addr AS fromAddr,
                IP1.IP.ip_addr AS fromLabel,
                "IP" AS toType,
                IP.IP.black_list AS toBlackList,
                IP.IP.ip_addr AS toAddr,
                IP.IP.ip_addr AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(IP1) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'r_client_query_dns_server'){
                UNION ALL
                MATCH
                (IP1:IP)-[client_query_dns_server:client_query_dns_server]->(IP:IP)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                AND IP1.IP.black_list >= ${edgeItem.weightLimit[0]} AND IP1.IP.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP1.IP.black_list AS fromBlackList,
                IP1.IP.ip_addr AS fromAddr,
                IP1.IP.ip_addr AS fromLabel,
                "IP" AS toType,
                IP.IP.black_list AS toBlackList,
                IP.IP.ip_addr AS toAddr,
                IP.IP.ip_addr AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(IP1) AS vInfo,
                "client_query_dns_server" AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'parse_to'){
                UNION ALL
                MATCH
                (IP:IP)-[parse_to:parse_to]-(DOMAIN:DOMAIN)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                RETURN
                "DOMAIN" AS fromType,
                DOMAIN.DOMAIN.black_list AS fromBlackList,
                DOMAIN.DOMAIN.domain_addr AS fromAddr,
                DOMAIN.DOMAIN.domain_addr AS fromLabel,
                "IP" AS toType,
                IP.IP.black_list AS toBlackList,
                IP.IP.ip_addr AS toAddr,
                IP.IP.ip_addr AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(DOMAIN) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'cname_result'){
                UNION ALL
                MATCH
                (IP:IP)-[cname_result:cname_result]-(DOMAIN:DOMAIN)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                RETURN
                "DOMAIN" AS fromType,
                DOMAIN.DOMAIN.black_list AS fromBlackList,
                DOMAIN.DOMAIN.domain_addr AS fromAddr,
                DOMAIN.DOMAIN.domain_addr AS fromLabel,
                "IP" AS toType,
                IP.IP.black_list AS toBlackList,
                IP.IP.ip_addr AS toAddr,
                IP.IP.ip_addr AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(DOMAIN) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'app_server'){
                UNION ALL
                MATCH
                (IP:IP)-[app_server:app_server]-(APPSERVICE:APPSERVICE)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                RETURN
                "APPSERVICE" AS fromType,
                0 AS fromBlackList,
                APPSERVICE.APPSERVICE.service_key AS fromAddr,
                APPSERVICE.APPSERVICE.service_key AS fromLabel,
                "IP" AS toType,
                IP.IP.black_list AS toBlackList,
                IP.IP.ip_addr AS toAddr,
                IP.IP.ip_addr AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(APPSERVICE) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'cert_related_ip'){
                UNION ALL
                MATCH
                (IP:IP)-[cert_related_ip:cert_related_ip]-(CERT:CERT)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                RETURN
                "CERT" AS fromType,
                CERT.CERT.black_list AS fromBlackList,
                CERT.CERT.cert_id AS fromAddr,
                CERT.CERT.cert_id AS fromLabel,
                "IP" AS toType,
                IP.IP.black_list AS toBlackList,
                IP.IP.ip_addr AS toAddr,
                IP.IP.ip_addr AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(CERT) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'ip_belong_app'){
                UNION ALL
                MATCH
                (IP:IP)-[ip_belong_app:ip_belong_app]->(APP:APP)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "APP" AS toType,
                0 AS toBlackList,
                id(APP) AS toAddr,
                id(APP) AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(APP) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'ip_url_related'){
                UNION ALL
                MATCH
                (IP:IP)-[ip_url_related:ip_url_related]->(URL:URL)
                WHERE
                id(IP) == ${ng.valueFmt(condition.str)}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "URL" AS toType,
                URL.URL.black_list AS toBlackList,
                URL.URL.url_key AS toAddr,
                URL.URL.url_key AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(URL) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
        @}
    </select>

    <select id="countIpByAppServerEdge" resultType="com.geeksec.ngbatis.vo.IpCountVo">
        GO FROM ${ ng.join(ips,",","ng.valueFmt") }
        OVER app_server
        REVERSELY
        YIELD properties($^).ip_addr AS ip_addr
        | GROUP BY $-.ip_addr
        YIELD $-.ip_addr AS ipAddr, count(*) AS count
    </select>

    <select id="countIpByClientAppEdge" resultType="com.geeksec.ngbatis.vo.IpCountVo">
        GO FROM ${ ng.join(ips,",","ng.valueFmt") }
        OVER client_app
        YIELD properties($^).ip_addr AS ip_addr
        | GROUP BY $-.ip_addr
        YIELD $-.ip_addr AS ipAddr, count(*) AS count
    </select>

    <select id="listByIps" resultType="com.geeksec.ngbatis.vo.IpVo">
        MATCH (IP:IP)
        WHERE id(IP) IN ${ips}
        RETURN IP.IP.ip_addr AS ip,
        IP.IP.black_list AS blackList,
        IP.IP.city AS city,
        IP.IP.country AS country,
        IP.IP.first_seen AS firstTime,
        IP.IP.last_seen AS lastTime,
        IP.IP.send_bytes AS sendBytes,
        IP.IP.recv_bytes AS recvBytes
    </select>

    <select id="listHasLabelByIps" resultType="com.geeksec.ngbatis.vo.IpHasLabelVo">
        GO FROM ${ ng.join(ips,",","ng.valueFmt") }
        OVER has_label
        YIELD properties($^).ip_addr AS ip_addr, dst(edge) AS dst, properties($$).label_id AS label_id
        | GROUP BY $-.ip_addr
        YIELD $-.ip_addr AS ipAddr, collect($-.label_id) AS labels
    </select>

    <select id="countIpByDomainEdge" resultType="com.geeksec.ngbatis.vo.IpCountVo">
        GO FROM ${ ng.join(ips,",","ng.valueFmt") }
        OVER parse_to, cname_result
        BIDIRECT
        YIELD properties($^).ip_addr AS ip_addr, dst(edge) AS dst, src(edge) AS src
        | GROUP BY $-.ip_addr
        YIELD $-.ip_addr AS ipAddr, count(*) AS count
    </select>

    <select id="countIpByCertEdge" resultType="com.geeksec.ngbatis.vo.IpCountVo">
        GO FROM ${ ng.join(ips,",","ng.valueFmt") }
        OVER server_use_cert, client_use_cert, client_connect_cert
        YIELD properties($^).ip_addr AS ip_addr, dst(edge) AS dst
        | GROUP BY $-.ip_addr
        YIELD $-.ip_addr AS ipAddr, count(*) AS count
    </select>

    <select id="listExistIpProperties" resultType="com.geeksec.ngbatis.vo.ExistIpPropertiesVo">
        FETCH PROP ON IP ${ ng.join(ips,",","ng.valueFmt") }
        YIELD properties(vertex).ip_addr AS ip_addr
        | YIELD $-.ip_addr AS ipAddr where $-.ip_addr is not null
    </select>

    <select id="getIpInfo" resultType="java.util.Map">
        MATCH (ip:IP)
        WHERE id(ip) == $ip
        RETURN ip.IP.ip_key AS ipKey,
        ip.IP.ip_addr AS ipAddr,
        ip.IP.remark AS remark,
        ip.IP.average_bps AS averageBps,
        ip.IP.recv_bytes AS recvBytes,
        ip.IP.send_bytes AS sendBytes,
        ip.IP.first_seen AS firstTime,
        ip.IP.last_seen AS lastTime,
        ip.IP.black_list AS blackList,
        ip.IP.white_list AS whiteList,
        ip.IP.city AS city,
        ip.IP.country AS country
    </select>

    <select id="countAppServerByIp" resultType="com.geeksec.ngbatis.vo.IpCountVo">
        GO FROM ${ ng.valueFmt(ip) }
        OVER app_server REVERSELY
        YIELD src(edge) AS src, dst(edge) AS dst
        | GROUP BY $-.dst
        YIELD $-.dst AS ipAddr, count(*) AS count
    </select>

    <select id="countClientAppByIp" resultType="com.geeksec.ngbatis.vo.IpCountVo">
        GO FROM ${ ng.valueFmt(ip) }
        OVER client_app
        YIELD src(edge) AS src, dst(edge) AS dst
        | GROUP BY $-.src
        YIELD $-.src AS ipAddr, count(*) AS count
    </select>

    <select id="listParseToRelatedDomainsByIp" resultType="com.geeksec.ngbatis.vo.IpRelatedDomainsVo">
        GO FROM ${ ng.valueFmt(ip) }
        OVER parse_to REVERSELY
        YIELD src(edge) AS domain, dst(edge) AS ip
        | GROUP BY $-.ip
        YIELD $-.ip AS ipAddr,
        collect($-.domain) AS domainList
    </select>

    <select id="listServerRelatedDomainsByIp" resultType="com.geeksec.ngbatis.vo.IpRelatedDomainsVo">
        GO FROM ${ ng.valueFmt(ip) }
        OVER server_ssl_connect_domain, server_http_connect_domain
        YIELD src(edge) AS ip, dst(edge) AS domain
        | GROUP BY $-.ip
        YIELD $-.ip AS ipAddr,
        collect($-.domain) AS domainList
    </select>

    <select id="countFDomainNumByDomains" resultType="java.lang.Integer">
        GO FROM ${ ng.join(domains,",","ng.valueFmt") }
        OVER domain_belong_to
        YIELD dst(edge)
        | YIELD COUNT(*) AS count
    </select>

    <select id="listHasLabelByIp" resultType="java.lang.String">
        MATCH (A:IP)-[e:has_label]->(B:LABEL)
        WHERE id(A) == $ip
        RETURN DISTINCT B.LABEL.label_id AS labelId
    </select>

    <update id="updateRemark">
        UPDATE VERTEX ON ${ type } ${ ng.valueFmt(id) }
        SET remark = ${ ng.valueFmt(remark) }
    </update>

</mapper>