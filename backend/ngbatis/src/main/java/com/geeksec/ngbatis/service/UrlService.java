package com.geeksec.ngbatis.service;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;

import java.util.List;

/**
*@description: Url服务
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
public interface UrlService {

    /**
     * Url关联查询Next
     */
    List<VertexEdgeNextVo> getUrlNebulaAssociationNext(GraphNextInfoCondition condition);

}
