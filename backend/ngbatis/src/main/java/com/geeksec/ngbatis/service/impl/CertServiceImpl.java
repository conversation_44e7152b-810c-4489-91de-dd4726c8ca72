package com.geeksec.ngbatis.service.impl;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.condition.GraphPropertiesNextCondition;
import com.geeksec.ngbatis.repository.CertDao;
import com.geeksec.ngbatis.service.CertService;
import com.geeksec.ngbatis.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
*@description: Cert服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class CertServiceImpl implements CertService {

    @Autowired
    private CertDao certDao;

    /**
     * 证书关联查询
     */
    public List<VertexEdgeVo> getCertNebulaAssociation(String cert){
        return certDao.listCertAllEdgeTypeAssociation(cert);
    }

    /**
     * 域名关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getCertNebulaAssociationNext(GraphNextInfoCondition condition){
        return certDao.listCertAllEdgeTypeAssociationNext(condition);
    }

    /**
     * 证书属性关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> listCertNebulaNextByProperties(GraphPropertiesNextCondition condition){
        return certDao.listCertNebulaNextByProperties(condition);
    }

    /**
     * 根据ids查询存在证书属性
     */
    @Override
    public List<ExistCertPropertiesVo> listExistCertProperties(List<String> certs) {
        return certDao.listExistCertProperties(certs);
    }

    /**
     * 根据ids查询证书详情
     */
    @Override
    public List<CertVo> listByCerts(List<String> certs) {
        return certDao.listByCerts(certs);
    }

    /**
     * 根据ids查询客户端热度
     */
    @Override
    public List<CertRelatedIpsVo> listRelatedIpsByCerts(List<String> certs) {
        return certDao.listRelatedIpsByCerts(certs);
    }

    /**
     * 根据ids查询关联域名个数
     */
    @Override
    public List<CertCountVo> countDomainNumByCerts(List<String> certs) {
        return certDao.countDomainNumByCerts(certs);
    }

    /**
     * 根据ids查询关联服务器ip个数
     */
    @Override
    public List<CertCountVo> countServerIpNumByCerts(List<String> certs) {
        return certDao.countServerIpNumByCerts(certs);
    }

    /**
     * 根据ids查询关联SSL个数
     */
    @Override
    public List<CertCountVo> countSslIpNumByCerts(List<String> certs) {
        return certDao.countSslIpNumByCerts(certs);
    }

    /**
     * 查询证书详情
     */
    @Override
    public Map<String, Object> getCertInfo(String cert) {
        return certDao.getCertInfo(cert);
    }

    /**
     * 根据id查询标签
     */
    @Override
    public List<Long> listHasLabelByCert(String cert) {
        return certDao.listHasLabelByCert(cert);
    }

    /**
    * 根据id查询服务端热度
    */
    @Override
    public List<String> listRelatedServerIpsByCert(String cert) {
        return certDao.listRelatedServerIpsByCert(cert);
    }

    /**
     * 根据id查询客户端热度
     */
    @Override
    public List<String> listRelatedClientIpsByCert(String cert) {
        return certDao.listRelatedClientIpsByCert(cert);
    }

}
