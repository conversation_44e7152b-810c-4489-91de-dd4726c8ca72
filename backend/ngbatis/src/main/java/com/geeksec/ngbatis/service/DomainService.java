package com.geeksec.ngbatis.service;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.condition.GraphPropertiesNextCondition;
import com.geeksec.ngbatis.vo.*;

import java.util.List;
import java.util.Map;

/**
*@description: Ip服务
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
public interface DomainService {

    /**
    * 域名关联查询
    */
    List<VertexEdgeVo> getDomainNebulaAssociation(String domain);

    /**
     * 域名关联查询Next
     */
    List<VertexEdgeNextVo> getDomainNebulaAssociationNext(GraphNextInfoCondition condition);

    /**
     * 域名属性关联查询Next
     */
    List<VertexEdgeNextVo> listDomainNebulaNextByProperties(GraphPropertiesNextCondition condition);

    /**
     * 根据ids查询存在域名属性
     */
    List<ExistDomainPropertiesVo> listExistDomainProperties(List<String> domains);

    /**
     * 根据ids查询域名详情
     */
    List<DomainVo> listByDomains(List<String> domains);

    /**
     * 根据ids查询标签
     */
    List<DomainHasLabelVo> listHasLabelByDomains(List<String> domains);

    /**
     * 根据ids查询锚域名
     */
    List<FDomainVo> listFDomainByDomains(List<String> domains);

    /**
     * 根据id查询标签
     */
    DomainHasLabelVo getHasLabelByDomain(String domain);

    /**
     * 根据id查询兄弟域名数量
     */
    DomainCountVo countBrotherNumByDomain(String domain);

    /**
     * 根据ids查询反查ip
     */
    List<DomainRelatedIpsVo> listRelatedIpsByDomains(List<String> domains);

    /**
     * 根据ids查询CName该域名数量
     */
    List<DomainCountVo> countCnameDomainsByDomains(List<String> domains);

    /**
     * 根据ids查询CName指向该域名数量
     */
    List<DomainCountVo> countCnamePointDomainsByDomains(List<String> domains);

    /**
     * 根据ids查询请求该域名的ips
     */
    List<DomainRelatedIpsVo> listRequestDomainIpsByDomains(List<String> domains);

    /**
     * 根据ids查询回应类型具有cname的边
     */
    List<DomainRelatedIpsVo> listResponseTypeIpsByDomains(List<String> domains);

    /**
     * 根据ids查询dns_server_domain出现位置
     */
    List<DomainCountVo> countDnsServerDomainByDomains(List<String> domains);

    /**
     * 根据ids查询server_ssl_connect_domain出现位置
     */
    List<DomainCountVo> countServerSslConnectDomainByDomains(List<String> domains);

    /**
     * 根据ids查询server_http_connect_domain出现位置
     */
    List<DomainCountVo> countServerHttpConnectDomainByDomains(List<String> domains);

    /**
     * 根据ids查询sni_bind出现位置
     */
    List<DomainCountVo> countSniBindByDomains(List<String> domains);

    /**
     * 查询域名详情
     */
    Map<String, Object> getDomainInfo(String domain);

    /**
     * 根据id查询标签
     */
    List<Long> listHasLabelByDomain(String domain);

    /**
     * 根据id查询锚域名
     */
    String getFDomainByDomain(String domain);

    /**
     * 根据fid查询标签
     */
    List<Long> listHasLabelByFDomain(String domain);

    /**
     * 根据id查询cname_result指向IP数量
     */
    Integer countCnameResultByDomain(String domain);

    /**
     * 根据id查询parse_to指向IP数量
     */
    Integer countParseToByDomain(String domain);

    /**
     * 根据id查询客户端热度
     */
    DomainRelatedIpsVo listRelatedClientIpsByDomain(String domain);

    /**
     * 根据id查询关联证书数量
     */
    Integer countCertNumByDomain(String domain);

}
