package com.geeksec.ngbatis.service.impl;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.repository.AttackerDao;
import com.geeksec.ngbatis.service.AttackerService;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: Attack服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class AttackerServiceImpl implements AttackerService {

    @Autowired
    private AttackerDao attackerDao;

    /**
     * Attack关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getAttackNebulaAssociationNext(GraphNextInfoCondition condition) {
        return attackerDao.listAttackAllEdgeTypeAssociationNext(condition);
    }
}
