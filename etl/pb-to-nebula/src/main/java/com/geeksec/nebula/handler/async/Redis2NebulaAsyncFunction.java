package com.geeksec.nebula.handler.async;

import com.alibaba.fastjson.JSON;
import com.geeksec.common.loader.PropertiesLoader;
import com.geeksec.common.utils.InstanceofUtils;
import com.geeksec.common.utils.RedisUtils;
import com.geeksec.common.utils.TypeNameTransUtils;
import com.vesoft.nebula.client.graph.NebulaPoolConfig;
import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.data.ValueWrapper;
import com.vesoft.nebula.client.graph.exception.AuthFailedException;
import com.vesoft.nebula.client.graph.exception.ClientServerIncompatibleException;
import com.vesoft.nebula.client.graph.exception.IOErrorException;
import com.vesoft.nebula.client.graph.exception.NotValidConnectionException;
import com.vesoft.nebula.client.graph.net.NebulaPool;
import com.vesoft.nebula.client.graph.net.Session;
import java.io.UnsupportedEncodingException;
import java.net.UnknownHostException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.function.Supplier;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.apache.flink.streaming.api.functions.async.RichAsyncFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * <AUTHOR>
 * @Description：
 */

public class Redis2NebulaAsyncFunction extends RichAsyncFunction<Row, Row> {

    private static final Logger logger = LoggerFactory.getLogger(Redis2NebulaAsyncFunction.class);
    // 加上transient，不让其序列化
    private transient ExecutorService executorService = null;

    // nebula & jedis session 连接
    private static transient JedisPool jedisPool = null;
    private static transient NebulaPool nebulaPool = null;
    private static transient LinkedBlockingQueue<Session> sessionQueue = new LinkedBlockingQueue();


    private static PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");
    private static Integer REDIS_EXPIRE_SECOND = propertiesLoader.getInteger("redis.expire.time");
    // Nebula Configs
    private static final int nebulaPoolMaxConnSize = 1000;
    private static final int nebulaPoolMinConnSize = 50;
    private static final int nebulaPoolIdleTime = 180000;
    private static final int nebulaPoolTimeout = 300000;
    private static String nebulaCluster = propertiesLoader.getProperty("nebula.graph.addr");
    private static String userName = propertiesLoader.getProperty("nebula.graph.username");
    private static String password = propertiesLoader.getProperty("nebula.graph.password");
    private static Integer sessionQueueSize = propertiesLoader.getInteger("nebula.session.size");
    private static String space = propertiesLoader.getProperty("nebula.space.name");

    public static NebulaPoolConfig nebulaPoolConfig() {
        NebulaPoolConfig nebulaPoolConfig = new NebulaPoolConfig();
        nebulaPoolConfig.setMaxConnSize(nebulaPoolMaxConnSize);
        nebulaPoolConfig.setMinConnSize(nebulaPoolMinConnSize);
        nebulaPoolConfig.setIdleTime(nebulaPoolIdleTime);
        nebulaPoolConfig.setTimeout(nebulaPoolTimeout);
        return nebulaPoolConfig;
    }

    private static List<String> NO_VERIFY_EDGE_TAG = Arrays.asList("FDOMAIN_BELONG_EDGE", "UA_BELONG_DEVICE_EDGE", "UA_BELONG_OS_EDGE", "UA_BELONG_APPTYPE_EDGE",
            "TASK_BELONG_TO_EDGE","UA_BELONG_APP_EDGE","DOMAIN_BELONG_APP_EDGE","MAC_CONNECT_EDGE","IP_CONNECT_EDGE", "DOMAIN_BELONG_ORG_EDGE", "IP_BELONG_ORG_EDGE", "IP_APP_EDGE", "DSSLFINGER_APP_EDGE");
    private static List<String> NO_VERIFY_VERTEX_TAG = Collections.emptyList();

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 每个点和边的异步函数 使用同一个连接池 同一堆session队列
        jedisPool = RedisUtils.initJedisPool();
        logger.info("生成jedisPool成功! {}", jedisPool.getNumIdle(),jedisPool.hashCode());
        //nebulaPool = NebulaUtils.getNebulaPool(nebulaPoolConfig());
        //for (int i = 0; i < sessionQueueSize; i++) {
        //    sessionQueue.put(nebulaPool.getSession(userName, password, false));
        //}
        //logger.info("初始化session队列成功! hash->{} 队列长度: {}", sessionQueue.size(), sessionQueue.hashCode());

    }

    @Override
    public void asyncInvoke(Row row, final ResultFuture<Row> resultFuture) {
        CompletableFuture.supplyAsync(new Supplier<Row>() {
            @Override
            public Row get() {
                Jedis jedis = null;
                String redisKey = StringUtils.EMPTY;

                try {
                    jedis = jedisPool.getResource();
                    String rowSideOutType = row.getField(0).toString();

                    // 1.查询Redis中是否存在key值，若存在进行更新，若不存在进行put
                    // 2.同时在值为空时查询nebula中是否存在现有的数据
                    if (rowSideOutType.endsWith("TAG")) {
                        String tagType = TypeNameTransUtils.transferTagName(rowSideOutType);
                        redisKey = "TAG:" + row.getFieldAs(0).toString().split("_")[0] + ":" + row.getFieldAs(1).toString();
                        if (jedis.exists(redisKey)) {
                            // 存在已有的数据 进行更新
                            String existStr = jedis.get(redisKey);
//                            logger.info("redis 中存在 tag 数据 ,redis key is {},existStr is {}", redisKey, existStr);
                            HashMap<String, Object> queryResult = JSON.parseObject(existStr, HashMap.class);
                            if (NO_VERIFY_VERTEX_TAG.contains(rowSideOutType)) {
                                return row;
                            }
                            updateTagRowByRedisData(row, queryResult, tagType);
                            RedisUtils.setValueByRow(row, jedis, false);
                        } else {
                            // redis不存在数据
                            // 先去nebula查询是否有现存数据
                            String vid = row.getFieldAs(1);
                            //HashMap<String, Object> nebulaQueryResult = queryTagFromNebula(tagType, vid);
                            //if (MapUtils.isNotEmpty(nebulaQueryResult)) {
                            //    updateTagRowByNebulaQuery(row, nebulaQueryResult, tagType);
                            //} else {
                            //    initTagRowByType(row, rowSideOutType);
                            //}
                            // 处理平均传输字节
                            setAvgBytes(row, tagType);
                            RedisUtils.setValueByRow(row, jedis, true);
                        }
                    } else {
                        // 不存在需要更新字段的边，直接进行数据返回
                        if (NO_VERIFY_EDGE_TAG.contains(rowSideOutType)) {
                            return row;
                        }
                        String edgeType = TypeNameTransUtils.transferEdgeName(rowSideOutType);
                        redisKey = "EDGE:" + edgeType.toUpperCase() + ":" + row.getFieldAs(1).toString() + "_" + row.getFieldAs(2).toString();
                        if (jedis.exists(redisKey)) {
                            String existStr = jedis.get(redisKey);
//                            logger.info("redis 中存在 edge 数据 ,redis key is {},existStr is {}", redisKey, existStr);
                            HashMap<String, Object> queryResult = JSON.parseObject(existStr, HashMap.class);
                            updateEdgeByRedisData(row, queryResult, edgeType);
                            RedisUtils.setValueByRow(row, jedis, false);
                        } else {
                            String src = row.getFieldAs(1).toString();
                            String dst = row.getFieldAs(2).toString();

                            //HashMap<String, Object> nebulaQueryResult = queryEdgefromNebula(edgeType, src, dst);
                            //if (MapUtils.isNotEmpty(nebulaQueryResult)) {
                            //    updateEdgeRowByQuery(row, nebulaQueryResult, edgeType);
                            //}
                            RedisUtils.setValueByRow(row, jedis, true);
                        }
                    }
                    return row;
                } catch (Exception e) {
                    logger.error("异步查询失败!error--->{},row is -->{},redis key is {}", e, row, redisKey);
                    return row;
                } finally {
                    if (jedis != null) {
                        jedis.close();
                    }
                }
            }
        }).thenAccept((Row result) -> {
            if (!ObjectUtils.isEmpty(result)) {
                resultFuture.complete(Collections.singleton(result));
            } else {
                logger.info("当前无nebulaResult数据输入 : {}", result);
            }
        });
    }


    /**
     * 初次录入nebula 数据初始化
     *
     * @param row
     * @param rowSideOutType
     */
    private void initTagRowByType(Row row, String type) {
        Integer firstTime = row.getFieldAs(2);
        Integer lastTime = row.getFieldAs(3);
        Long bytes = 0L;
        switch (type) {
            case "IP_TAG":
                bytes = InstanceofUtils.intInstanceofLong(row.getFieldAs(10));
                if (lastTime - firstTime == 0) {
                    row.setField(17, Math.toIntExact(bytes));
                } else {
                    row.setField(17, (int) Math.ceil(bytes / lastTime - firstTime));
                }
                break;
            case "DOMAIN_TAG":
                bytes = InstanceofUtils.intInstanceofLong(row.getFieldAs(8));
                if (lastTime - firstTime == 0) {
                    row.setField(9, Math.toIntExact(bytes));
                } else {
                    row.setField(9, (int) Math.ceil(bytes / lastTime - firstTime));
                }
                break;
        }
    }

    private void updateTagRowByRedisData(Row row, HashMap<String, Object> queryResult, String tagType) {

//        logger.info("所修改row值为--->{},redis 查询数据———{}", row, queryResult);
        try {
            // 通用Redis更新firstSeen和LastSeen逻辑
            switch (tagType){
                case "MAC":
                case "APPSERVICE":
                    updateTimeByRedis(row, queryResult, 6, 7);
                    break;
                case "APK":
                case "UA":
                    updateTimeByRedis(row, queryResult, 4, 5);
                    break;
                case "FDOMAIN":
                    updateTimeByRedis(row, queryResult, 3, 4);
                    break;
                case "SSLFINGER":
                    updateTimeByRedis(row, queryResult, 5, 6);
                    break;
                default:
                    updateTimeByRedis(row, queryResult, 2, 3);
                    break;
            }

            // 字节数相关
            Long bytes = 0L;
            Long sendBytes = 0L;
            Long recvBytes = 0L;

            // 包数量相关
            Integer packets = 0;
            Integer sendPacket = 0;
            Integer recvPacket = 0;

            // 平均字节数
            int averagePbs = 0;

            Integer last = 0;
            Integer first = 0;

            switch (tagType) {
                case "IP":
                    last = row.getFieldAs(3);
                    first = row.getFieldAs(2);

                    Integer queryTimes = (Integer) queryResult.get("4");
                    Integer times = InstanceofUtils.longInstanceofInt(row.getField(4));

                    sendBytes = InstanceofUtils.intInstanceofLong(row.getFieldAs(15));
                    recvBytes = InstanceofUtils.intInstanceofLong(row.getFieldAs(16));
                    bytes = InstanceofUtils.intInstanceofLong(row.getFieldAs(10));

                    packets = InstanceofUtils.longInstanceofInt(row.getField(11));

                    row.setField(4, times + queryTimes);
                    row.setField(10, bytes.intValue() + (Integer) queryResult.get("10"));
                    row.setField(11, packets + (Integer) queryResult.get("11"));
                    row.setField(15, sendBytes.intValue() + (Integer) queryResult.get("15"));
                    row.setField(16, recvBytes.intValue() + (Integer) queryResult.get("16"));

                    if (last - first == 0) {
                        averagePbs = (int) (bytes.intValue() + (Integer) queryResult.get("10"));
                        row.setField(17, averagePbs);
                    } else {
                        averagePbs = (int) Math.ceil((bytes.intValue() + (Integer) queryResult.get("10")) / (last - first));
                        row.setField(17, averagePbs);
                    }
                    break;
                case "DOMAIN":
                    last = row.getFieldAs(3);
                    first = row.getFieldAs(2);

                    bytes = InstanceofUtils.intInstanceofLong(row.getFieldAs(8));
                    row.setField(8, bytes.intValue() + (Integer) queryResult.get("8"));
                    if (last - first == 0) {
                        averagePbs = (int) (bytes.intValue()) + (Integer) queryResult.get("8");
                        row.setField(9, averagePbs);
                    } else {
                        averagePbs = (int) Math.ceil((bytes.intValue() + (Integer) queryResult.get("8")) / (last - first));
                        row.setField(9, averagePbs);
                    }
                    break;
                case "MAC":
                    Integer macTimes = InstanceofUtils.longInstanceofInt(row.getFieldAs(2));
                    Integer queryMacTimes = (Integer) queryResult.get("2");
                    row.setField(2, macTimes + queryMacTimes);
                    bytes = InstanceofUtils.intInstanceofLong(row.getFieldAs(3));
                    row.setField(3, bytes.intValue() + (Integer) queryResult.get("3"));
            }
        } catch (Exception e) {
            System.out.println(e);
            logger.error("通过Redis存留数据更新Row流失败!error->{},row is {},redis data is {}", e, row, queryResult);
        }
    }

    private static void updateTimeByRedis(Row row, HashMap<String, Object> queryResult, int row1stTimePos, int rowlastPos) {
        Integer queryFirstTime = (Integer) queryResult.get(String.valueOf(row1stTimePos));
        Integer queryLastTime = (Integer) queryResult.get(String.valueOf(rowlastPos));

        Integer firstTime = InstanceofUtils.longInstanceofInt(row.getFieldAs(row1stTimePos));
        Integer lastTime = InstanceofUtils.longInstanceofInt(row.getFieldAs(rowlastPos));

        if (firstTime.longValue() > queryFirstTime) {
            row.setField(row1stTimePos, queryFirstTime);
        }
        if (lastTime < queryLastTime) {
            row.setField(rowlastPos, queryLastTime);
        }
    }

    private void updateEdgeByRedisData(Row row, HashMap<String, Object> queryResult, String edgeType) {
        try {
            // 通用更新逻辑
            Long querySessionCnt = InstanceofUtils.intInstanceofLong(queryResult.get("4"));

            Long sessionCnt = InstanceofUtils.intInstanceofLong(row.getFieldAs(4));

            row.setField(6, sessionCnt + querySessionCnt);

            // 字节数相关
            Long bytes = 0L;
            Long sendBytes = 0L;
            Long recvBytes = 0L;

            // 包数量相关
            Integer packets = 0;
            Integer sendPacket = 0;
            Integer recvPacket = 0;

            // 根据edgeType通过redis添加row
            switch (edgeType) {
                case "src_bind":
                    bytes = InstanceofUtils.intInstanceofLong(row.getField(7));
                    packets = InstanceofUtils.longInstanceofInt(row.getFieldAs(8));
                    row.setField(9, bytes + (InstanceofUtils.intInstanceofLong(queryResult.get("9"))));
                    row.setField(10, packets + (InstanceofUtils.longInstanceofInt(queryResult.get("10"))));
                    break;
                case "dst_bind":
                    bytes = InstanceofUtils.intInstanceofLong(row.getField(7));
                    packets = InstanceofUtils.longInstanceofInt(row.getFieldAs(8));
                    row.setField(9, bytes + (InstanceofUtils.intInstanceofLong(queryResult.get("9"))));
                    row.setField(10, packets + (InstanceofUtils.longInstanceofInt(queryResult.get("10"))));
                    break;
//                case "connect":
                case "ip_connect":
                case "mac_connect":
                    bytes = InstanceofUtils.intInstanceofLong(row.getFieldAs(7));
                    packets = InstanceofUtils.longInstanceofInt(row.getFieldAs(8));
                    sendBytes = InstanceofUtils.intInstanceofLong(row.getFieldAs(9));
                    recvBytes = InstanceofUtils.intInstanceofLong(row.getFieldAs(10));
                    sendPacket = InstanceofUtils.longInstanceofInt(row.getFieldAs(11));
                    recvPacket = InstanceofUtils.longInstanceofInt(row.getFieldAs(12));

                    Long queryBytes = InstanceofUtils.intInstanceofLong(queryResult.get("7"));
                    row.setField(9, bytes + queryBytes);

                    Integer queryPackets = InstanceofUtils.longInstanceofInt(queryResult.get("8"));
                    row.setField(10, packets + queryPackets);

                    Long querySendBytes = InstanceofUtils.intInstanceofLong(queryResult.get("9"));
                    row.setField(11, sendBytes + querySendBytes);

                    Long queryRecvBytes = InstanceofUtils.intInstanceofLong(queryResult.get("10"));
                    row.setField(12, recvBytes + queryRecvBytes);

                    Integer querySendPackets = InstanceofUtils.longInstanceofInt(queryResult.get("11"));
                    row.setField(13, querySendPackets);

                    Integer queryRecvPackets = InstanceofUtils.longInstanceofInt(queryResult.get("12"));
                    row.setField(14, queryRecvPackets);
                    break;
                case "parse_to":
                    Integer queryMaxTTL = (Integer) queryResult.get("7");
                    Integer maxTTL = row.getFieldAs(7);
                    Integer queryMinTTL = (Integer) queryResult.get("8");
                    Integer minTTL = row.getFieldAs(8);

                    if (queryMaxTTL > maxTTL) {
                        row.setField(7, queryMaxTTL);
                    }

                    if (queryMinTTL < minTTL) {
                        row.setField(8, queryMinTTL);
                    }
                    break;
                default:
                    // 基础边类型统一
                    // 1.client_http_connect_domain
                    // 2.server_http_connect_domain
                    // 3.cname
                    // 4.cname_result
                    // 5.client_ssl_connect_domain
                    // 6.server_ssl_connect_domain
                    // 7.client_use_sslfinger
                    // 8.server_use_sslfinger
                    // 9.sni_bind
                    // 10.sslfinger_connect_domain
                    // 11.ua_connect_domain
                    // 12.client_use_ua
                    // 13.client_app
                    // 14.app_server
                    break;
            }

        } catch (Exception e) {
            System.out.println(e);
            logger.error("从redis更新，填充Edge字段信息错误!error-->{},row-->{}", e, row);
        }
    }

    // 单独查询点的方法
    private HashMap<String, Object> queryTagFromNebula(String tagType, String vid) throws
            IOErrorException, UnsupportedEncodingException, NotValidConnectionException, ClientServerIncompatibleException, AuthFailedException, UnknownHostException, InterruptedException {

        String query = String.format("use %s;match (v:%s) where id(v) == \"%s\" return properties(v) as tagProps", space, tagType, vid);
        Session session = null;
        try {
            session = sessionQueue.take();
//            logger.info("获取nebula连接成功 {}! 当前池内活动连接数 ---> {}，查询 VID --->{}", session, nebulaPool.getActiveConnNum(), vid);
            ResultSet rs = session.execute(query);
            if (!rs.isSucceeded()) {
                logger.error(String.format("Execute: `%s', failed: %s", query, rs.getErrorMessage()));
            }
            if (rs.rowsSize() == 0) {
                // 数据库中不存在此vid的点
                return null;
            } else if (rs.rowsSize() == 1) {
                List<String> colNames = rs.keys();
                ResultSet.Record record = rs.rowValues(0);
                ValueWrapper value = record.get("tagProps");
                if (value.isEmpty() || value.isNull()) {
                    logger.info(String.format("debug error: resultset row size == 1 but edgeType empty ### type: %s, vid : %s", tagType, vid));
                    return null;
                } else {
                    // 点存在
                    HashMap<String, ValueWrapper> kvs = value.asMap();
//                    logger.info("debug Map: -->{}" + kvs);
                    HashMap<String, Object> result = new HashMap<>();
                    result.put("firstTime", kvs.get("first_time").asLong());
                    result.put("lastTime", kvs.get("last_time").asLong());
                    // 通过边类型判断填充返回值
                    result = addReusltFiledsByTagType(kvs, result, tagType);
                    return result;
                }
            }
        } catch (Exception e) {
            logger.error("查询Nebula现存数据库信息失败,error->", e);
            return null;
        } finally {
            sessionQueue.put(session);
        }
        return null;
    }

    // 单独边查询的方法
    private HashMap<String, Object> queryEdgefromNebula(String type, String src, String dst) throws
            IOErrorException, UnsupportedEncodingException, NotValidConnectionException, ClientServerIncompatibleException, AuthFailedException, UnknownHostException {
        String query = String.format("use %s; match (v0)-[e:`%s`]-(v1) where id(v0) == \"%s\" and id(v1) == \"%s\" return properties(e) as edgeProps, rank(e) as edgeRank;", space, type, src, dst);
        Session session = null;

        try {
            session = sessionQueue.take();
//            logger.info("获取nebula连接成功 {}! 当前池内活动连接数 ---> {}，查询 src--->{},dst --->{}", session, nebulaPool.getActiveConnNum(), src,dst);
            ResultSet rs = session.execute(query);
            if (rs.rowsSize() == 0) {
                // 两点之间不存在边
                return null;
            } else if (rs.rowsSize() == 1) {
                // 两点之间存在一条边(大部分情况下)
                List<String> colNames = rs.keys();
                ResultSet.Record record = rs.rowValues(0);
                ValueWrapper value = record.get("edgeProps");
                if (value.isEmpty() || value.isNull()) {
                    logger.info(String.format("debug error: resultset row size == 1 but edgeType empty ### type: %s, src: %s, dst: %s", type, src, dst));
                    return null;
                } else if (value.isMap()) {
                    HashMap<String, ValueWrapper> kvs = value.asMap();
//                    logger.info("debug Map: --> {}", kvs);
                    HashMap<String, Object> result = new HashMap<String, Object>();
                    result.put("firstTime", kvs.get("first_time").asLong());
                    result.put("lastTime", kvs.get("last_time").asLong());
                    result.put("sessionCnt", kvs.get("session_cnt").asLong());
                    // 通过边类型判断填充返回值
                    result = addResultFiledsByEdgeType(kvs, result, type);
                    return result;
                } else {
                    logger.info(String.format("debug error: type not correct ### type: %s, src: %s, dst: %s", type, src, dst));
                    return null;
                }
            } else {
                // 两点之间存在多条边(少部分情况)
                // aggregate
                // TODO
                return null;
            }
        } catch (Exception e) {
            logger.error("查询Nebula现存数据库信息失败,error->", e);
            return null;
        }
    }

    private HashMap<String, Object> addReusltFiledsByTagType
            (HashMap<String, ValueWrapper> kvs, HashMap<String, Object> resultMap, String tagType) {
        try {
            switch (tagType) {
                case "IP":
                    resultMap.put("ipKey", kvs.get("ip_key").asString());
                    resultMap.put("ipAddr", kvs.get("ip_addr").asString());
                    resultMap.put("version", kvs.get("version").asString());
                    ValueWrapper cityWrapper = kvs.get("city");
                    if (cityWrapper.isNull()) {
                        resultMap.put("city", StringUtils.EMPTY);
                    } else {
                        resultMap.put("city", kvs.get("city").asString());
                    }
                    ValueWrapper countryWrapper = kvs.get("country");
                    if (countryWrapper.isNull()) {
                        resultMap.put("country", StringUtils.EMPTY);

                    } else {
                        resultMap.put("country", kvs.get("country").asString());
                    }
                    resultMap.put("bytes", kvs.get("bytes").asLong());
                    resultMap.put("sendBytes", kvs.get("send_bytes").asLong());
                    resultMap.put("recvBytes", kvs.get("recv_bytes").asLong());
                    resultMap.put("packets", (int) kvs.get("packets").asLong());
                    resultMap.put("averageBps", kvs.get("average_bps").asLong());
                    resultMap.put("blackList", kvs.get("black_list").asLong());
                    resultMap.put("whiteList", kvs.get("white_list").asLong());
                    resultMap.put("remark", kvs.get("remark").asString());
                    resultMap.put("times", kvs.get("times").asLong());
                    return resultMap;
                case "DOMAIN":
                    resultMap.put("domainAddr", kvs.get("domain_addr").asString());
                    resultMap.put("blackList", kvs.get("black_list").asLong());
                    resultMap.put("whiteList", kvs.get("black_list").asLong());
                    resultMap.put("alexaRank", kvs.get("alexa_rank").asLong());
                    resultMap.put("remark", kvs.get("remark").asString());
                    resultMap.put("bytes", kvs.get("bytes").asLong());
                    resultMap.put("average_bps", kvs.get("average_bps").asLong());
                    resultMap.put("whois", kvs.get("whois").asString());
                    return resultMap;
                default:
                    return resultMap;
            }
        } catch (Exception e) {
            logger.error("填充EdgeType字段信息失败,error->", e);
            return resultMap;
        }
    }


    // 通过EdgeType和查询结果，填充返回
    private HashMap<String, Object> addResultFiledsByEdgeType
    (HashMap<String, ValueWrapper> kvs, HashMap<String, Object> resultMap, String type) throws
            UnsupportedEncodingException {
        try {
            switch (type) {
                case "src_bind":
                    resultMap.put("ip", kvs.get("ip").asString());
                    resultMap.put("mac", kvs.get("mac").asString());
                    resultMap.put("bytes", kvs.get("bytes").asLong());
                    resultMap.put("packets", (int) kvs.get("packets").asLong());
                    return resultMap;
                case "dst_bind":
                    resultMap.put("ip", kvs.get("ip").asString());
                    resultMap.put("mac", kvs.get("mac").asString());
                    resultMap.put("bytes", kvs.get("bytes").asLong());
                    resultMap.put("packets", (int) kvs.get("packets").asLong());
                    return resultMap;
                case "connect":
                    resultMap.put("appName", kvs.get("app_name").asString());
                    resultMap.put("dPort", kvs.get("dport").asLong());
                    resultMap.put("bytes", kvs.get("bytes").asLong());
                    resultMap.put("packets", (int) kvs.get("packets").asLong());
                    resultMap.put("sendBytes", kvs.get("send_bytes").asLong());
                    resultMap.put("recvBytes", kvs.get("recv_bytes").asLong());
                    resultMap.put("sendPackets", (int) kvs.get("send_packets").asLong());
                    resultMap.put("recvPackets", (int) kvs.get("recv_packets").asLong());
                    return resultMap;
                case "client_app":
                    resultMap.put("ip", kvs.get("ip").asString());
                    resultMap.put("appName", kvs.get("app_name").asString());
                    return resultMap;
                case "server_app":
                    resultMap.put("ip", kvs.get("ip").asString());
                    resultMap.put("appName", kvs.get("app_name").asString());
                    return resultMap;
                case "client_query_domain":
                    resultMap.put("dnsType", kvs.get("dns_type").asLong());
                    resultMap.put("answerType", kvs.get("answer_type").asLong());
                    return resultMap;
                case "client_query_dns_server":
                    resultMap.put("dnsType", kvs.get("dns_type").asLong());
                    resultMap.put("answerType", kvs.get("answer_type").asLong());
                    return resultMap;
                case "dns_server_domain":
                    // 特殊处理,此处从Nebula查出dns_type字段为String
                    resultMap.put("dnsType", Long.valueOf(kvs.get("dns_type").asString()));
                    resultMap.put("answerType", kvs.get("answer_type").asLong());
                    return resultMap;
                case "parse_to":
                    resultMap.put("dnsServer", kvs.get("dns_server").asString());
                    resultMap.put("finalParse", kvs.get("final_parse").asBoolean());
                    resultMap.put("maxTTL", (int) kvs.get("max_ttl").asLong());
                    resultMap.put("minTTL", (int) kvs.get("min_ttl").asLong());
                    return resultMap;
                case "client_use_cert":
                    resultMap.put("sni", kvs.get("sni").asString());
                    return resultMap;
                case "server_use_cert":
                    resultMap.put("sni", kvs.get("sni").asString());
                    resultMap.put("certId", kvs.get("cert_id").asString());
                    return resultMap;
                case "client_connect_cert":
                    resultMap.put("sni", kvs.get("sni").asString());
                    return resultMap;
                case "sslfinger_connect_cert":
                    resultMap.put("sni", kvs.get("sni").asString());
                    return resultMap;
                default:
                    // 基础边类型统一
                    // 1.client_http_connect_domain
                    // 2.server_http_connect_domain
                    // 3.cname
                    // 4.cname_result
                    // 5.client_ssl_connect_domain
                    // 6.server_ssl_connect_domain
                    // 7.client_use_sslfinger
                    // 8.server_use_sslfinger
                    // 9.sni_bind
                    // 10.sslfinger_connect_domain
                    // 11.ua_connect_domain
                    // 12.client_use_ua
                    return resultMap;
            }
        } catch (Exception e) {
            logger.error("填充EdgeType字段信息失败,error->", e);
            return resultMap;
        }
    }

    // 数据处理完毕，新增填充Row对应坑位
    private void updateEdgeRowByQuery(Row row, HashMap<String, Object> queryResult, String edgeType) {

        try {
            // 通用更新逻辑
            Long queryFirstTime = (Long) queryResult.get("firstTime");
            Long queryLastTime = (Long) queryResult.get("lastTime");
            Long querySessionCount = (Long) queryResult.get("sessionCnt");
            Integer queryPackets = 0;

            Integer firstTime = InstanceofUtils.longInstanceofInt(row.getField(4));
            Integer lastTime = InstanceofUtils.longInstanceofInt(row.getField(5));
            Long sessionCnt = row.getFieldAs(6);

            if (firstTime.longValue() > queryFirstTime) {
                firstTime = queryFirstTime.intValue();
            }
            if (lastTime < queryLastTime) {
                lastTime = queryLastTime.intValue();
            }
            sessionCnt = sessionCnt + querySessionCount;
            row.setField(4, firstTime);
            row.setField(5, lastTime);
            row.setField(6, sessionCnt);

            // 字节数相关
            Long bytes = 0L;
            Long sendBytes = 0L;
            Long recvBytes = 0L;

            // 包数量相关
            Integer packets = 0;
            Integer sendPacket = 0;
            Integer recvPacket = 0;

            // 根据edgeType添加row
            switch (edgeType) {
                case "src_bind":
                    bytes = row.getFieldAs(9);
                    packets = InstanceofUtils.longInstanceofInt(row.getFieldAs(10));
                    row.setField(7, queryResult.get("ip"));
                    row.setField(8, queryResult.get("mac"));
                    row.setField(9, (Long) queryResult.get("bytes") + bytes);
                    queryPackets = InstanceofUtils.longInstanceofInt(queryResult.get("packets"));
                    row.setField(10, queryPackets + packets);
                    break;
                case "dst_bind":
                    bytes = row.getFieldAs(9);
                    packets = InstanceofUtils.longInstanceofInt(row.getFieldAs(10));
                    row.setField(7, queryResult.get("ip"));
                    row.setField(8, queryResult.get("mac"));
                    row.setField(9, (Long) queryResult.get("bytes") + bytes);
                    queryPackets = InstanceofUtils.longInstanceofInt(queryResult.get("packets"));
                    row.setField(10, queryPackets + packets);
                    break;
                case "connect":
                    bytes = row.getFieldAs(9);
                    packets = InstanceofUtils.longInstanceofInt(row.getFieldAs(10));
                    sendBytes = row.getFieldAs(11);
                    recvBytes = row.getFieldAs(12);
                    sendPacket = row.getFieldAs(13);
                    recvPacket = row.getFieldAs(14);
                    row.setField(7, queryResult.get("appName"));
                    row.setField(8, queryResult.get("dPort"));
                    row.setField(9, (Long) queryResult.get("bytes") + bytes);
                    row.setField(10, (Integer) queryResult.get("packets") + packets);
                    row.setField(11, (Long) queryResult.get("sendBytes") + sendBytes);
                    row.setField(12, (Long) queryResult.get("recvBytes") + recvBytes);
                    row.setField(13, (Integer) queryResult.get("sendPackets") + sendPacket);
                    row.setField(14, (Integer) queryResult.get("recvPackets") + recvPacket);
                    break;
                case "client_app":
                    row.setField(7, queryResult.get("ip"));
                    row.setField(8, queryResult.get("appName"));
                    break;
                case "app_server":
                    row.setField(7, queryResult.get("ip"));
                    row.setField(8, queryResult.get("appName"));
                    break;
                case "client_query_domain":
                    row.setField(7, queryResult.get("dnsType"));
                    row.setField(8, queryResult.get("answerType"));
                    break;
                case "client_query_dns_server":
                    row.setField(7, queryResult.get("dnsType"));
                    row.setField(8, queryResult.get("answerType"));
                    break;
                case "dns_server_domain":
                    row.setField(7, queryResult.get("dnsType"));
                    row.setField(8, queryResult.get("answerType"));
                    break;
                case "parse_to":
                    row.setField(7, queryResult.get("dnsServer"));
                    row.setField(8, queryResult.get("finalParse"));

                    Integer queryMaxTTL = (Integer) queryResult.get("maxTTL");
                    Integer maxTTL = row.getFieldAs(9);
                    Integer queryMinTTL = (Integer) queryResult.get("minTTL");
                    Integer minTTL = row.getFieldAs(10);

                    if (queryMaxTTL > maxTTL) {
                        row.setField(9, queryMaxTTL);
                    } else {
                        row.setField(9, maxTTL);
                    }

                    if (queryMinTTL < minTTL) {
                        row.setField(10, queryMinTTL);
                    } else {
                        row.setField(10, maxTTL);
                    }
                    break;
                case "client_use_cert":
                    row.setField(7, queryResult.get("sni"));
                    break;
                case "server_use_cert":
                    row.setField(7, queryResult.get("sni"));
                    row.setField(8, queryResult.get("certId"));
                    break;
                case "client_connect_cert":
                    row.setField(7, queryResult.get("sni"));
                    break;
                case "sslfinger_connect_cert":
                    row.setField(7, queryResult.get("sni"));
                    break;
                default:
                    // 基础边类型统一
                    // 1.client_http_connect_domain
                    // 2.server_http_connect_domain
                    // 3.cname
                    // 4.cname_result
                    // 5.client_ssl_connect_domain
                    // 6.server_ssl_connect_domain
                    // 7.client_use_sslfinger
                    // 8.server_use_sslfinger
                    // 9.sni_bind
                    // 10.sslfinger_connect_domain
                    // 11.ua_connect_domain
                    // 12.client_use_ua
                    break;
            }
        } catch (Exception e) {
            logger.error("填充Edge字段信息错误!error-->{},row-->{}", e, row);
        }


    }


    private void updateTagRowByNebulaQuery(Row row, HashMap<String, Object> queryResult, String tagType) {

        try {
            // 通用更新逻辑
            Long queryFirstTime = (Long) queryResult.get("firstTime");
            Long queryLastTime = (Long) queryResult.get("lastTime");

            Integer firstTime = row.getFieldAs(2);
            Integer lastTime = row.getFieldAs(3);

            if (firstTime.longValue() > queryFirstTime) {
                firstTime = queryFirstTime.intValue();
            }
            if (lastTime < queryLastTime) {
                lastTime = queryLastTime.intValue();
            }

            row.setField(2, firstTime);
            row.setField(3, lastTime);

            // 字节数相关
            Long bytes = 0L;
            Long sendBytes = 0L;
            Long recvBytes = 0L;

            // 包数量相关
            Integer packets = 0;
            Integer sendPacket = 0;
            Integer recvPacket = 0;

            // 平均字节数
            int averagePbs = 0;

            // 根据tagType添加row
            switch (tagType) {
                case "IP":
                    Long times = (Long) queryResult.get("times");
                    Long queryTimes = row.getFieldAs(4);
                    times = times + queryTimes;
                    sendBytes = row.getFieldAs(15);
                    recvBytes = row.getFieldAs(16);
                    bytes = row.getFieldAs(10);
                    Object rowPacket = row.getField(11);
                    if (rowPacket instanceof Long) {
                        packets = ((Long) rowPacket).intValue();
                    } else {
                        packets = row.getFieldAs(11);
                    }
                    row.setField(4, times);
                    row.setField(5, queryResult.get("ipAddr"));
                    row.setField(6, queryResult.get("ipKey"));
                    row.setField(7, queryResult.get("version"));
                    row.setField(8, queryResult.get("city"));
                    row.setField(9, queryResult.get("country"));
                    row.setField(10, bytes + (Long) queryResult.get("bytes"));
                    row.setField(11, packets + (Integer) queryResult.get("packets"));
                    row.setField(12, queryResult.get("blackList"));
                    row.setField(13, queryResult.get("whiteList"));
                    row.setField(14, queryResult.get("remark"));
                    row.setField(15, sendBytes + (Long) queryResult.get("sendBytes"));
                    row.setField(16, recvBytes + (Long) queryResult.get("recvBytes"));
                    // 处理平均流量 （（发送字节数+接收字节数）/（批次中该IP出现的结束时间-批次中该IP的开始时间）；时间单位：秒）
                    if (lastTime - firstTime == 0) {
                        averagePbs = (int) (bytes + (Long) queryResult.get("bytes"));
                        row.setField(17, averagePbs);
                    } else {
                        averagePbs = (int) Math.ceil((bytes + (Long) queryResult.get("bytes")) / (lastTime - firstTime));
                        row.setField(17, averagePbs);
                    }
                    break;
                case "DOMAIN":
                    bytes = row.getFieldAs(8);
                    row.setField(8, bytes + (Long) queryResult.get("bytes"));
                    if (lastTime - firstTime == 0) {
                        averagePbs = (int) (bytes + (Long) queryResult.get("bytes"));
                        row.setField(9, averagePbs);

                    } else {
                        averagePbs = (int) Math.ceil((bytes + (Long) queryResult.get("bytes")) / (lastTime - firstTime));
                        row.setField(9, averagePbs);
                    }
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            logger.error("更新Tag字段信息失败！error--->{},row--->{}", e, row);
        }
    }

    public void setAvgBytes(Row row, String tagType) {
        Integer firstTime = 0;
        Integer lastTime = 0;
        Long bytes = 0L;

        switch (tagType) {
            case "IP":
                firstTime = InstanceofUtils.longInstanceofInt(row.getFieldAs(2));
                lastTime = InstanceofUtils.longInstanceofInt(row.getFieldAs(3));
                bytes = InstanceofUtils.intInstanceofLong(row.getFieldAs(10));
                if (lastTime - firstTime == 0) {
                    row.setField(17, bytes.intValue());
                } else if (lastTime > firstTime) {
                    row.setField(17, Math.ceil((bytes.intValue()) / (lastTime - firstTime)));
                }
                break;
            case "DOMAIN":
                firstTime = InstanceofUtils.longInstanceofInt(row.getFieldAs(2));
                lastTime = InstanceofUtils.longInstanceofInt(row.getFieldAs(3));
                bytes = InstanceofUtils.intInstanceofLong(row.getFieldAs(8));
                if (lastTime - firstTime == 0) {
                    row.setField(9, bytes.intValue());
                } else {
                    row.setField(9, Math.ceil((bytes.intValue()) / (lastTime - firstTime)));
                }
                break;
            default:
                return;
        }
    }


    @Override
    public void timeout(Row input, ResultFuture<Row> resultFuture) throws
            Exception {
//        logger.info("异步客户端链接超时 !,input data is {}", input);
        resultFuture.complete(Collections.singleton(null));
        asyncInvoke(input, resultFuture);
    }

    @Override
    public void close() throws Exception {
        // 关闭jedisPool
//        jedisPool.close();
        sessionQueue.clear();
        nebulaPool.close();
        logger.info("关闭jedis连接池 and nebula连接池！");
    }
}
