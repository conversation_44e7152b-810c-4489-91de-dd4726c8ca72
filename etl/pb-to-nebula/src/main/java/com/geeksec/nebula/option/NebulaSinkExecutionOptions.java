package com.geeksec.nebula.option;

import com.geeksec.common.loader.PropertiesLoader;
import org.apache.flink.connector.nebula.connection.NebulaClientOptions;
import org.apache.flink.connector.nebula.connection.NebulaGraphConnectionProvider;
import org.apache.flink.connector.nebula.connection.NebulaMetaConnectionProvider;
import org.apache.flink.connector.nebula.sink.NebulaEdgeBatchOutputFormat;
import org.apache.flink.connector.nebula.sink.NebulaVertexBatchOutputFormat;
import org.apache.flink.connector.nebula.statement.EdgeExecutionOptions;
import org.apache.flink.connector.nebula.statement.ExecutionOptions;
import org.apache.flink.connector.nebula.statement.VertexExecutionOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Collections;

/**
 * <AUTHOR>
 * @Description：
 */
public class NebulaSinkExecutionOptions {

    private static final Logger logger = LoggerFactory.getLogger(NebulaSinkExecutionOptions.class);
    public static final int BATCH_INTERVAL_MS = 100;

    private static PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");
    public static final String NEBULA_GRAPH_ADDR = propertiesLoader.getProperty("nebula.graph.addr");
    public static final String NEBULA_META_ADDR = propertiesLoader.getProperty("nebula.meta.addr");
    public static final String NEBULA_GRAPH_SPACE = propertiesLoader.getProperty("nebula.space.name");

    private static Integer capacity = 20;

    public static NebulaGraphConnectionProvider graphConnectionProvider = null;
    public static NebulaMetaConnectionProvider metaConnectionProvider = null;

    static {
        // Nebula Conn通用配置
        NebulaClientOptions nebulaClientOptions = new NebulaClientOptions.NebulaClientOptionsBuilder()
                .setGraphAddress(NEBULA_GRAPH_ADDR)
                .setMetaAddress(NEBULA_META_ADDR)
                .build();
        graphConnectionProvider = new NebulaGraphConnectionProvider(nebulaClientOptions);
        metaConnectionProvider = new NebulaMetaConnectionProvider(nebulaClientOptions);
    }

    /**
     * 处理Nebula Tag 入库格式
     *
     * @param tagType
     */
    public static NebulaVertexBatchOutputFormat handleTagFormat(String tagType) {
        switch (tagType) {
            case "ip_tag":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getIpTagExecutionOptions());
            case "mac_tag":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getMacTagExecutionOptions());
            case "app_tag":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getAppTagExecutionOptions());
            case "domain_tag":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getDomainTagExecutionOptions());
            case "fdomain_tag":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getFDomainTagExecutionOptions());
            case "ssl_finger_tag":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getSslFingerTagExceutionOptions());
            case "label_tag":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getLabelTagExectuionOptions());
            case "ua_tag":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getUATagExecutionOptions());
            case "device_tag":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getDeviceTagExecutionOptions());
            case "os_tag":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getOSTagExecutionOptions());
            case "org_tag":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getOrgTagExecutionOptions());
            case "apk_tag":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getApkTagExecutionOptions());
            default:
                return null;
        }
    }

    /**
     * Apk真正的手机应用 TAG
     *
     * @return
     */
    private static VertexExecutionOptions getApkTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setTag("APP")
                .setIdIndex(1)
                .setFields(Arrays.asList("app_name", "app_version", "first_seen", "last_seen"))
                .setPositions(Arrays.asList(2, 3, 4, 5))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    public static NebulaEdgeBatchOutputFormat handleEdgeFormat(String edgeType) {
        switch (edgeType) {
            case "ip_belong_app_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getIPAppEdgeExecutionOptions());
            case "sslfinger_belong_app_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getSslFingerAppEdgeExecutionOptions());
            case "src_bind_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getSrcBindEdgeExecutionOptions());
            case "dst_bind_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getDstBindEdgeExecutionOptions());
            case "ip_connect_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getIpConnectEdgeExecutionOptions());
            case "mac_connect_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getMacConnectEdgeExecutionOptions());
            case "fdomain_belong_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getFdomainBelongEdgeExecutionOptions());
            case "client_app_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getClientAppExecutionOptions());
            case "app_server_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getAppServerExecutionOptions());
            case "client_http_connect_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getClientIpDomainExecutionOptions());
            case "server_http_connect_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getServerIpDomainExecutionOptions());
            case "client_ssl_connect_domain_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getClientSslConnectDomainExecutionOptions());
            case "server_ssl_connect_domain_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getServerSslConnectDomainExecutionOptions());
            case "client_use_cert_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getClientUseCertExecutionOptions());
            case "server_use_cert_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getServerUseCertExecutionOptions());
            case "client_connect_cert_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getClientConnectCertExecutionOptions());
            case "client_use_sslfinger_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getClientUseSslFingerExecutionOptions());
            case "server_use_sslfinger_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getServerUseSslFingerExecutionOptions());
            case "sni_bind_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getSniBindExecutionOptions());
            case "sslfinger_connect_cert":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getSslFingerConnectCertExecutionOptions());
            case "sslfinger_connect_domain":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getSslFingerConnectDomainExecutionOptions());
            case "client_query_domain_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getClientQueryDomainExecutionOptions());
            case "client_query_dns_server_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getClientQueryDnsServerExecutionOptions());
            case "dns_server_domain_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getDnsServerDomainExecutionOptions());
            case "dns_parse_to_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getParseToExecutionOptions());
            case "cname_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getCnameExecutionOptions());
            case "cname_result_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getCnameResultExecutionOptions());
            case "ua_connect_domain_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getUAConnectDomainResultExecutionOptions());
            case "client_use_ua_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getClientUseUAResultExecutionOptions());
            case "ua_belong_device_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getUABelongDeviceExecutionOptions());
            case "ua_belong_os_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getUABelongOsExecutionOptions());
            case "ua_belong_apptype_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getUABelongApptypeExecutionOptions());
            case "sni_fdomain_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getSniFdomainEdgeExecutionOptions());
            case "cert_service_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getCertServiceEdgeExecutionOptions());
            case "cert_belong_app_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getCertAPPEdgeExecutionOptions());
            case "domain_belong_app_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getDomainAPPEdgeExecutionOptions());
            case "ua_belong_app_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getUaAPPEdgeExecutionOptions());
            case "domain_belong_org_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getDomainOrgEdgeExecutionOptions());
            case "ip_belong_org_edge":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider,getIpOrgEdgeExecutionOptions());
            case "finger_type_label":
            case "Http_Web_Login":
            case "random_finger_label":
            case "domain_ip_label":
            case "Port_Scan":
            case "dns_tunnel":
            case "app_scan":
            case "x-ray":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider,metaConnectionProvider,getLabelOptions());
            default:
                return null;
        }
    }

    private static EdgeExecutionOptions getIPAppEdgeExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("ip_belong_app")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    private static EdgeExecutionOptions getSslFingerAppEdgeExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("sslfinger_belong_app")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    private static EdgeExecutionOptions getDomainOrgEdgeExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("domain_belong_to_org")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    private static EdgeExecutionOptions getIpOrgEdgeExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("ip_belong_to_org")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    /**
     * IP connect
     */
    private static EdgeExecutionOptions getIpConnectEdgeExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("connect_ip")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3) //rank
                .setFields(Arrays.asList("app_name", "dport"))
                .setPositions(Arrays.asList(5, 6))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(200)
                .builder();
    }

    /**
     * MAC connect
     */
    private static EdgeExecutionOptions getMacConnectEdgeExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("connect_mac")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3) //rank
                .setFields(Arrays.asList("app_name", "dport"))
                .setPositions(Arrays.asList(5, 6))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(200)
                .builder();
    }

    /**
     * CERT ---> SERVICE ---> special_business_port_service
     *
     * @return
     */
    private static EdgeExecutionOptions getCertServiceEdgeExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("special_business_port_service")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    /**
     * CERT ---> APP ---> cert_belong_app
     *
     * @return
     */
    private static EdgeExecutionOptions getCertAPPEdgeExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("cert_belong_app")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    /**
     * Domain ---> APP ---> domain_belong_app
     *
     * @return
     */
    private static EdgeExecutionOptions getDomainAPPEdgeExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("domain_belong_app")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }
    /**
     * UA ---> APP ---> ua_belong_app
     *
     * @return
     */
    private static EdgeExecutionOptions getUaAPPEdgeExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("ua_belong_app")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    /**
     * SNI ---> FDOMAIN ---> sni_bind_fdomain
     *
     * @return
     */
    private static EdgeExecutionOptions getSniFdomainEdgeExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("sni_bind_fdomain")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    /**
     * src--->标签 所有打标签操作
     */
    private static EdgeExecutionOptions getLabelOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("has_label")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Arrays.asList("analysis_by", "remark"))
                .setPositions(Arrays.asList(3,4))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }


    /**
     * ClientIp--->Domain SSL访问 client_ssl_connect_domain_edge
     */
    private static EdgeExecutionOptions getClientSslConnectDomainExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("client_ssl_connect_domain")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(50)
                .builder();

    }

    /**
     * ServerIp--->Domain SSL应用 server_ssl_connect_domain
     */
    private static EdgeExecutionOptions getServerSslConnectDomainExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("server_ssl_connect_domain")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(50)
                .builder();
    }

    /**
     * ClientIP ---> Client_CERT 客户端使用证书 client_use_cert
     */
    private static EdgeExecutionOptions getClientUseCertExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("client_use_cert")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Arrays.asList("sni"))
                .setPositions(Arrays.asList(5))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    /**
     * ServerIP ---> Server_CERT 服务端使用证书 server_use_cert
     */
    private static EdgeExecutionOptions getServerUseCertExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("server_use_cert")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Arrays.asList("sni"))
                .setPositions(Arrays.asList(5))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    /**
     * ClientIP ---> Server_CERT 客户端访问证书 client_connect_cert
     */
    private static EdgeExecutionOptions getClientConnectCertExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("client_connect_cert")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Arrays.asList("sni"))
                .setPositions(Arrays.asList(5))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();

    }

    /**
     * ClientIP ---> Client_SSLFINGER 使用SSL指纹 client_use_sslfinger
     */
    private static EdgeExecutionOptions getClientUseSslFingerExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("client_use_sslfinger")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();

    }

    /**
     * ServerIP ---> Server_SSLFINGER 返回SSL指纹 server_use_sslfinger
     */
    private static EdgeExecutionOptions getServerUseSslFingerExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("server_use_sslfinger")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();

    }

    /**
     * DOMAIN ---> Server_CERT SNI关联 sni_bind
     */
    private static EdgeExecutionOptions getSniBindExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("sni_bind")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(50)
                .builder();

    }

    /**
     * Client_SSLFINGER ---> DOMAIN 指纹访问域名 sslfinger_connect_domain
     */
    private static EdgeExecutionOptions getSslFingerConnectDomainExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("sslfinger_connect_domain")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(50)
                .builder();
    }

    /**
     * Client_SSLFINGER ---> Server_CERT  指纹访问证书 sslfinger_connect_cert
     */
    private static EdgeExecutionOptions getSslFingerConnectCertExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("sslfinger_connect_cert")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Arrays.asList("sni"))
                .setPositions(Arrays.asList(5))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();

    }

    private static EdgeExecutionOptions getUAConnectDomainResultExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("ua_connect_domain")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(50)
                .builder();
    }

    private static EdgeExecutionOptions getClientUseUAResultExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("client_use_ua")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    /**
     * IP TAG 配置
     */
    private static VertexExecutionOptions getIpTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setTag("IP")
                .setIdIndex(1) // vid
                .setFields(Arrays.asList("first_seen", "last_seen", "times", "ip_addr", "ip_key", "version", "city", "country", "bytes", "packets", "black_list", "white_list", "remark", "send_bytes", "recv_bytes", "average_bps"))
                .setPositions(Arrays.asList(2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(200)
                .builder();

    }

    /**
     * MAC TAG配置
     */
    private static VertexExecutionOptions getMacTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setTag("MAC")
                .setIdIndex(1)
                .setFields(Arrays.asList("mac", "times", "bytes", "average_bps", "vlan_info", "first_seen", "last_seen", "black_list", "white_list", "remark"))
                .setPositions(Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    /**
     * 域名Domain TAG
     */
    private static VertexExecutionOptions getDomainTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setTag("DOMAIN")
                .setIdIndex(12)
                .setFields(Arrays.asList("domain_addr", "first_seen", "last_seen", "black_list", "white_list", "remark", "alexa_rank", "bytes", "average_bps", "whois"))
                .setPositions(Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(20)
                .builder();

    }

    /**
     * 锚域名FDomain TAG
     */
    private static VertexExecutionOptions getFDomainTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setTag("FDOMAIN")
                .setIdIndex(2)
                .setFields(Arrays.asList("fdomain_addr", "first_seen", "last_seen"))
                .setPositions(Arrays.asList(1, 3, 4))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(20)
                .builder();
    }

    /**
     * APPSERVICE_TAG
     */
    private static VertexExecutionOptions getAppTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setTag("APPSERVICE")
                .setIdIndex(1)
                .setFields(Arrays.asList("service_key", "ip_addr", "AppName", "dPort", "IPPro", "first_seen", "last_seen"))
                .setPositions(Arrays.asList(1, 2, 3, 4, 5, 6, 7))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    /**
     * SSL指纹 TAG
     */
    private static VertexExecutionOptions getSslFingerTagExceutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setTag("SSLFINGER")
                .setIdIndex(1)
                .setFields(Arrays.asList("finger_id", "ja3_hash", "finger_desc", "type", "first_seen", "last_seen"))
                .setPositions(Arrays.asList(1, 2, 3, 4, 5, 6))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    /**
     * UA代理 TAG
     *
     * @return
     */
    private static VertexExecutionOptions getUATagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setTag("UA")
                .setIdIndex(1)
                .setFields(Arrays.asList("ua_id", "ua_str", "ua_desc", "first_seen", "last_seen"))
                .setPositions(Arrays.asList(1, 2, 3, 4, 5))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    /**
     * OS系统 TAG
     *
     * @return
     */
    private static VertexExecutionOptions getOSTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setTag("OS")
                .setIdIndex(1)
                .setFields(Arrays.asList("os_name", "first_seen", "last_seen"))
                .setPositions(Arrays.asList(1, 2, 3))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    /**
     * Org组织 TAG
     *
     * @return
     */
    private static VertexExecutionOptions getOrgTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setTag("ORG")
                .setIdIndex(1)
                .setFields(Arrays.asList("org_name","org_desc","black_list","white_list","remark", "first_seen", "last_seen"))
                .setPositions(Arrays.asList(2, 3, 4, 5, 6, 7, 8))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    /**
     * DEVICE设备 TAG
     *
     * @return
     */
    private static VertexExecutionOptions getDeviceTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setTag("DEVICE")
                .setIdIndex(1)
                .setFields(Arrays.asList("device_name", "first_seen", "last_seen"))
                .setPositions(Arrays.asList(1, 2, 3))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    /**
     * LABEL 标签 TAG
     *
     * @return
     */
    private static VertexExecutionOptions getLabelTagExectuionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setTag("LABEL")
                .setIdIndex(1)
                .setFields(Arrays.asList("label_id", "label_name", "label_target_type", "label_desc", "first_seen", "last_seen"))
                .setPositions(Arrays.asList(1, 2, 3, 4, 5, 6))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(200)
                .builder();
    }


    /**
     * IP_MAC src_bind
     */
    private static EdgeExecutionOptions getSrcBindEdgeExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("src_bind")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3) //rank
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(500)
                .builder();
    }


    /**
     * IP_MAC dst_bind
     */
    private static EdgeExecutionOptions getDstBindEdgeExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("dst_bind")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3) //rank
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(500)
                .builder();
    }

    /**
     * IP MAC connect
     */
    private static EdgeExecutionOptions getConnectEdgeExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("connect")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3) //rank
                .setFields(Arrays.asList("session_cnt", "app_name", "dport", "bytes", "packets", "send_bytes", "recv_bytes", "send_packets", "recv_packets"))
                .setPositions(Arrays.asList(4, 5, 6, 7, 8, 9, 10, 11, 12))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(200)
                .builder();
    }

    /**
     * 归属域名 domain_belong_to
     */
    private static EdgeExecutionOptions getFdomainBelongEdgeExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("domain_belong_to")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3) // rank
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    /**
     * SERVER_APP_EDGE 访问服务
     */
    private static EdgeExecutionOptions getAppServerExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("app_server")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3) //rank
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(200)
                .builder();
    }


    /**
     * CLIENT_APP_EDGE 访问服务
     */
    private static EdgeExecutionOptions getClientAppExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("client_app")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3) //rank
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(200)
                .builder();
    }

    /**
     * Client IP--->DOMAIN HTTP 访问
     */
    private static EdgeExecutionOptions getClientIpDomainExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("client_http_connect_domain")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3) //rank
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(50)
                .builder();
    }

    /**
     * Server IP--->DOMAIN HTTP应用
     */
    private static EdgeExecutionOptions getServerIpDomainExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("server_http_connect_domain")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3) //rank
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(50)
                .builder();
    }

    /**
     * ClientIp客户端IP ---> Domain 域名 client_query_domain
     *
     * @return
     */
    private static EdgeExecutionOptions getClientQueryDomainExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("client_query_domain")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Arrays.asList("dns_type", "answer_type"))
                .setPositions(Arrays.asList(5, 6))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(50)
                .builder();
    }

    /**
     * ClientIP 客户端 ---> DNSServerIP DNS服务器IP client_query_dns_server
     */
    private static EdgeExecutionOptions getClientQueryDnsServerExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("client_query_dns_server")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Arrays.asList("dns_type", "answer_type"))
                .setPositions(Arrays.asList(5, 6))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(20)
                .builder();
    }

    /**
     * DNSServerIP DNS服务器IP ---> Domain dns_server_domain
     */
    private static EdgeExecutionOptions getDnsServerDomainExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("dns_server_domain")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Arrays.asList("dns_type", "answer_type"))
                .setPositions(Arrays.asList(5, 6))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(50)
                .builder();
    }

    /**
     * Domain ---> ServerIP ---> parse_to
     */
    private static EdgeExecutionOptions getParseToExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("parse_to")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Arrays.asList("dns_server", "final_parse", "max_ttl", "min_ttl"))
                .setPositions(Arrays.asList(5, 6, 7, 8))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(50)
                .builder();
    }

    /**
     * domain ---> cname 或 cname ---> cname --->cname
     */
    private static EdgeExecutionOptions getCnameExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("cname")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(50)
                .builder();
    }

    /**
     * CNAME ---> ServerIp ---> cname_result
     */
    private static EdgeExecutionOptions getCnameResultExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("cname_result")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();

    }

    /**
     * UA ---> DEVICE --->ua_belong_device
     *
     * @return
     */
    private static EdgeExecutionOptions getUABelongDeviceExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("ua_belong_device")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    /**
     * UA ---> OS ---> ua_belong_os
     *
     * @return
     */
    private static EdgeExecutionOptions getUABelongOsExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("ua_belong_os")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

    /**
     * UA ---> DEVICE --->ua_belong_apptype
     *
     * @return
     */
    private static EdgeExecutionOptions getUABelongApptypeExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("ua_belong_apptype")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();
    }

}
