package com.geeksec.nebula.handler;

import java.util.HashMap;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.types.Row;

/**
 * <AUTHOR>
 * @Description：窗口函数最终生成Row处理类
 */
public class WindowFunctionHandler {
    public static Row handleEdgeRowByRowType(Tuple3<String, String, String> key, HashMap<String, Object> resultMap) {
        String rowType = (String) resultMap.get("rowType");
        // 用于配置最基本的Row类型
        Row basicRow = new Row(7);
        switch (rowType) {
            case "SRC_BIND_EDGE":
            case "DST_BIND_EDGE":
                return createIpMacBindEdgeRow(key, resultMap);
            case "IP_CONNECT_EDGE":
            case "MAC_CONNECT_EDGE":
                return createConnecteEdgeRow(key, resultMap);
            case "CLIENT_APP_EDGE":
            case "APP_SERVER_EDGE":
                return createIpAppNameEdgeRow(key, resultMap);
            case "CLIENT_HTTP_CONNECT_DOMAIN_EDGE":
            case "CNAME_EDGE":
            case "CNAME_RESULT_EDGE":
            case "SERVER_HTTP_CONNECT_DOMAIN_EDGE":
            case "CLIENT_SSL_CONNECT_DOMAIN_EDGE":
            case "SERVER_SSL_CONNECT_DOMAIN_EDGE":
            case "CLIENT_USE_SSLFINGER_EDGE":
            case "SERVER_USE_SSLFINGER_EDGE":
            case "SNI_BIND_EDGE":
            case "SSLFINGER_CONNECT_DOMAIN_EDGE":
            case "CLIENT_USE_UA_EDGE":
            case "UA_CONNECT_DOMAIN_EDGE":
            case "UA_BELONG_APP_EDGE":
                return createCommonEdgeRowFields(key, resultMap, basicRow);
            case "CLIENT_QUERY_DOMAIN_EDGE":
            case "CLIENT_QUERY_DNS_SERVER_EDGE":
            case "DNS_SERVER_DOMAIN_EDGE":
                // 与CLIENT_QUERY_DOMAIN 属性相同，使用同一套
                return createClientQueryDomainEdgeRow(key, resultMap);
            case "DNS_PARSE_TO_EDGE":
                return createDnsParseToEdgeRow(key, resultMap);
            case "CLIENT_USE_CERT_EDGE":
            case "CLIENT_CONNECT_CERT_EDGE":
            case "SSLFINGER_CONNECT_CERT_EDGE":
                // 与CLIENT_USE_CERT_EDGE属性相同，使用同一套
                // 与CLIENT_USE_CERT_EDGE 属性相同，使用同一套
                return createClientUseCertEdgeRow(key, resultMap);
            case "SERVER_USE_CERT_EDGE":
                return createServerUseCertEdgeRow(key, resultMap);
            case "UA_BELONG_DEVICE_EDGE":
            case "UA_BELONG_OS_EDGE":
            case "UA_BELONG_APPTYPE_EDGE":
            case "FDOMAIN_BELONG_EDGE":
            case "SNI_FDOMAIN_EDGE":
            case "CERT_SERVICE_EDGE":
            case "DOMAIN_BELONG_APP_EDGE":
            case "CERT_BELONG_APP_EDGE":
            case "DOMAIN_BELONG_ORG_EDGE":
            case "IP_BELONG_ORG_EDGE":
            case "IP_APP_EDGE":
            case "DSSLFINGER_APP_EDGE":
                return createOriEdgeRowFields(key, resultMap);
            default:
                return null;
        }
    }

    public static Row handleTagRowByRowType(String key, HashMap<String, Object> resultMap) {
        String rowType = (String) resultMap.get("rowType");
        switch (rowType) {
            case "IP_TAG":
                return createIpTagRow(resultMap);
            case "DOMAIN_TAG":
                return createDomainTagRow(resultMap);
            case "MAC_TAG":
                return createMacTagRow(resultMap);
            case "APP_TAG":
                return createAppTagRow(resultMap);
            case "FDOMAIN_TAG":
                return createFDomainTagRow(resultMap);
            case "SSL_FINGER_TAG":
                return createSslFingerTagRow(resultMap);
            case "UA_TAG":
                return createUATagRow(resultMap);
            case "DEVICE_TAG":
                return createDeviceTagRow(resultMap);
            case "OS_TAG":
                return createOsTagRow(resultMap);
            case "APK_TAG":
                return createApkTagRow(resultMap);
            default:
                return null;
        }
    }

    private static Row createApkTagRow(HashMap<String, Object> resultMap) {
        Row apkTagRow = new Row(6);
        apkTagRow.setField(0, "APK_TAG");
        apkTagRow.setField(1, resultMap.get("apk_id"));
        apkTagRow.setField(2, resultMap.get("appName"));
        apkTagRow.setField(3, resultMap.get("appVersion"));
        apkTagRow.setField(4, resultMap.get("firstSeen"));
        apkTagRow.setField(5, resultMap.get("lastSeen"));
        return apkTagRow;
    }
    private static Row createIpTagRow(HashMap<String, Object> resultMap) {
        Row row = new Row(19);
        row.setField(0, "IP_TAG");
        row.setField(1, resultMap.get("ipAddr"));// vid
        row.setField(2, resultMap.get("firstSeen"));
        row.setField(3, resultMap.get("lastSeen"));
        row.setField(4, resultMap.get("times"));
        row.setField(5, resultMap.get("ipAddr"));
        row.setField(6, resultMap.get("ipKey"));
        row.setField(7, resultMap.get("version"));
        row.setField(8, resultMap.get("city"));
        row.setField(9, resultMap.get("country"));
        row.setField(10, resultMap.get("bytes"));
        row.setField(11, resultMap.get("packets"));
        row.setField(12, resultMap.get("blackList"));
        row.setField(13, resultMap.get("whiteList"));
        row.setField(14, resultMap.get("remark"));
        row.setField(15, resultMap.get("sendBytes"));
        row.setField(16, resultMap.get("recvBytes"));
        row.setField(17, 0L);
        row.setField(18, resultMap.get("task_id"));

        return row;
    }

    private static Row createDomainTagRow(HashMap<String, Object> resultMap) {
        Row row = new Row(13);
        row.setField(0, "DOMAIN_TAG");
        row.setField(1, resultMap.get("domainAddr"));
        row.setField(2, resultMap.get("firstSeen"));
        row.setField(3, resultMap.get("lastSeen"));
        row.setField(4, resultMap.get("blackList"));
        row.setField(5, resultMap.get("whiteList"));
        row.setField(6, resultMap.get("remark"));
        row.setField(7, resultMap.get("alexaRank"));
        row.setField(8, resultMap.get("bytes"));
        row.setField(9, 0L);
        row.setField(10, resultMap.get("whois"));
        row.setField(11, resultMap.get("task_id"));
        row.setField(12, resultMap.get("domain_id"));
        return row;
    }

    private static Row createMacTagRow(HashMap<String, Object> resultMap) {
        Row row = new Row(12);
        row.setField(0, "MAC_TAG");
        row.setField(1, resultMap.get("mac"));
        row.setField(2, resultMap.get("times"));
        row.setField(3, resultMap.get("bytes"));
        row.setField(4, 0);
        row.setField(5, resultMap.get("vlan_info"));
        row.setField(6, resultMap.get("firstSeen"));
        row.setField(7, resultMap.get("lastSeen"));
        row.setField(8, resultMap.get("black_list"));
        row.setField(9, resultMap.get("white_list"));
        row.setField(10, StringUtils.EMPTY);
        row.setField(11, resultMap.get("task_id"));
        return row;
    }

    private static Row createFDomainTagRow(HashMap<String, Object> resultMap) {
        Row row = new Row(5);
        row.setField(0, "FDOMAIN_TAG");
        row.setField(1, resultMap.get("fdomain"));
        row.setField(2, resultMap.get("fdomain_id"));
        row.setField(3, resultMap.get("firstSeen"));
        row.setField(4, resultMap.get("lastSeen"));
        return row;
    }

    private static Row createAppTagRow(HashMap<String, Object> resultMap) {
        Row row = new Row(8);
        row.setField(0, "APP_TAG");
        row.setField(1, resultMap.get("service_key"));
        row.setField(2, resultMap.get("ip_addr"));
        row.setField(3, resultMap.get("app_name"));
        row.setField(4, resultMap.get("port"));
        row.setField(5, resultMap.get("ip_pro"));
        row.setField(6, resultMap.get("firstSeen"));
        row.setField(7, resultMap.get("lastSeen"));

        return row;
    }

    private static Row createSslFingerTagRow(HashMap<String, Object> resultMap) {
        Row row = new Row(7);
        row.setField(0, "SSL_FINGER_TAG");
        row.setField(1, resultMap.get("finger_id"));
        row.setField(2, resultMap.get("ja3_hash"));
        row.setField(3, resultMap.get("desc"));
        row.setField(4, resultMap.get("type"));
        row.setField(5, resultMap.get("firstSeen"));
        row.setField(6, resultMap.get("lastSeen"));
        return row;
    }

    private static Row createUATagRow(HashMap<String, Object> resultMap) {
        Row row = new Row(6);
        row.setField(0, "UA_TAG");
        row.setField(1, resultMap.get("ua_id"));
        row.setField(2, resultMap.get("ua_str"));
        row.setField(3, resultMap.get("desc"));
        row.setField(4, resultMap.get("firstSeen"));
        row.setField(5, resultMap.get("lastSeen"));
        return row;
    }

    private static Row createDeviceTagRow(HashMap<String, Object> resultMap) {
        Row row = new Row(4);
        row.setField(0, "DEVICE_TAG");
        row.setField(1, resultMap.get("device_name"));
        row.setField(2, resultMap.get("firstSeen"));
        row.setField(3, resultMap.get("lastSeen"));
        return row;
    }

    private static Row createOsTagRow(HashMap<String, Object> resultMap) {
        Row row = new Row(4);
        row.setField(0, "OS_TAG");
        row.setField(1, resultMap.get("os_name"));
        row.setField(2, resultMap.get("firstSeen"));
        row.setField(3, resultMap.get("lastSeen"));
        return row;
    }

    private static Row createIpMacBindEdgeRow(Tuple3<String, String, String> key, HashMap<String, Object> resultMap) {
        Row row = new Row(9);
        createCommonEdgeRowFields(key, resultMap, row);
        row.setField(5, resultMap.get("ipAddr"));
        row.setField(6, resultMap.get("mac"));
        row.setField(7, resultMap.get("bytes"));
        row.setField(8, resultMap.get("packets"));
        return row;
    }

    private static Row createConnecteEdgeRow(Tuple3<String, String, String> key, HashMap<String, Object> resultMap) {
        Row row = new Row(13);
        createCommonEdgeRowFields(key, resultMap, row);
        row.setField(5, resultMap.get("appName"));
        row.setField(6, resultMap.get("dPort"));
        row.setField(7, resultMap.get("bytes"));
        row.setField(8, resultMap.get("packets"));
        row.setField(9, resultMap.get("sendBytes"));
        row.setField(10, resultMap.get("recvBytes"));
        row.setField(11, resultMap.get("sendPackets"));
        row.setField(12, resultMap.get("recvPackets"));
        return row;
    }

    private static Row createIpAppNameEdgeRow(Tuple3<String, String, String> key, HashMap<String, Object> resultMap) {
        Row row = new Row(7);
        createCommonEdgeRowFields(key, resultMap, row);
        row.setField(5, resultMap.get("ipAddr"));
        row.setField(6, resultMap.get("appName"));
        return row;
    }

    private static Row createClientQueryDomainEdgeRow(Tuple3<String, String, String> key, HashMap<String, Object> resultMap) {
        Row row = new Row(7);
        createCommonEdgeRowFields(key, resultMap, row);
        row.setField(5, resultMap.get("queryType"));
        row.setField(6, resultMap.get("answerType"));
        return row;
    }


    private static Row createDnsParseToEdgeRow(Tuple3<String, String, String> key, HashMap<String, Object> resultMap) {
        Row row = new Row(9);
        createCommonEdgeRowFields(key, resultMap, row);
        row.setField(5, resultMap.get("dnsServer"));
        row.setField(6, resultMap.get("finalParse"));
        row.setField(7, resultMap.get("maxTTL"));
        row.setField(8, resultMap.get("minTTL"));
        return row;
    }

    private static Row createClientUseCertEdgeRow(Tuple3<String, String, String> key, HashMap<String, Object> resultMap) {
        Row row = new Row(6);
        createCommonEdgeRowFields(key, resultMap, row);
        row.setField(5, resultMap.get("sni"));
        return row;
    }

    private static Row createServerUseCertEdgeRow(Tuple3<String, String, String> key, HashMap<String, Object> resultMap) {
        Row row = new Row(7);
        createCommonEdgeRowFields(key, resultMap, row);
        row.setField(5, resultMap.get("sni"));
        row.setField(6, resultMap.get("subCert"));
        return row;
    }


    private static Row createCommonEdgeRowFields(Tuple3<String, String, String> key, HashMap<String, Object> resultMap, Row row) {
        row.setField(0, resultMap.get("rowType"));
        row.setField(1, key.getField(0).toString());
        row.setField(2, key.getField(1).toString());
        row.setField(3, 0);
        row.setField(4, resultMap.get("sessionCnt"));
        return row;
    }

    private static Row createOriEdgeRowFields(Tuple3<String, String, String> key, HashMap<String, Object> resultMap) {
        Row row = new Row(4);
        row.setField(0, resultMap.get("rowType"));
        row.setField(1, key.getField(0).toString());
        row.setField(2, key.getField(1).toString());
        row.setField(3, 0);
        return row;
    }

}
