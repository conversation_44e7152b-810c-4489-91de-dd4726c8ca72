package com.geeksec.nebula.handler.aggr;

import java.util.HashMap;
import org.apache.flink.types.Row;

/**
 * <AUTHOR>
 * @Description：
 */
public class NebulaTagAggrHandler {
    public static HashMap<String, Object> handleTagAddFiled(HashMap<String, Object> acc, Row row) {
        String rowType = row.getFieldAs(0);
        acc.put("rowType", rowType);
        switch (rowType) {
            case "IP_TAG":
                acc = handleIpTagAdd(acc, row);
                break;
            case "DOMAIN_TAG":
                acc = handleDomainTagAdd(acc, row);
                break;
            case "MAC_TAG":
                acc = handleMacTagAdd(acc, row);
                break;
            case "APP_TAG":
                acc = handleAppTagAdd(acc, row);
                break;
            case "FDOMAIN_TAG":
                acc = handleFDomainTagAdd(acc, row);
                break;
            case "SSL_FINGER_TAG":
                acc = handleFingerTagAdd(acc, row);
                break;
            case "UA_TAG":
                acc = handleUATagAdd(acc, row);
                break;
            case "DEVICE_TAG":
                acc = handleDeviceTagAdd(acc, row);
                break;
            case "OS_TAG":
                acc = handleOSTagAdd(acc, row);
                break;
            case "APPTYPE_TAG":
                acc = handleApptypeTag(acc, row);
                break;
            case "APK_TAG":
                acc = handleApkTag(acc, row);
                break;
            default:
                return acc;
        }
        return acc;
    }

    private static HashMap<String, Object> handleApkTag(HashMap<String, Object> acc, Row row) {
        Integer currentTime = (Integer) row.getField(5);
        updateSeenTime(acc, currentTime);

        acc.put("apk_id", row.getField(1));
        acc.put("appName", row.getField(2));
        acc.put("appVersion", row.getField(3));
        return acc;

    }

    public static HashMap<String, Object> handleTagMergeFiled(HashMap<String, Object> acc, HashMap<String, Object> acc1) {
        String rowType = (String) acc.get("rowType");
        acc.put("rowType", rowType);

        switch (rowType) {
            case "IP_TAG":
                acc = handleIpTagMerge(acc, acc1);
                break;
            case "DOMAIN_TAG":
                acc = handleDomainTagMerge(acc, acc1);
                break;
            case "MAC_TAG":
                acc = handleMacTagMerge(acc, acc1);
            default:
                return acc;

        }
        return acc;
    }

    private static HashMap<String, Object> handleIpTagAdd(HashMap<String, Object> acc, Row row) {
        commonHandleAdd(acc, row);
        acc.put("ipAddr", row.getField(5));
        acc.put("ipKey", row.getField(6));
        acc.put("version", row.getField(7));
        acc.put("city", row.getField(8));
        acc.put("country", row.getField(9));
        acc.put("blackList", row.getField(12));
        acc.put("whiteList", row.getField(13));
        acc.put("remark", row.getField(14));
        acc.put("task_id",row.getField(18));

        // 发送流量
        Long sendBytes = (Long) row.getFieldAs(15);
        if (sendBytes != null) {
            if (!acc.containsKey("sendBytes")) {
                acc.put("sendBytes", 0L);
            }
            Long existSendBytes = (Long) acc.get("sendBytes");
            if (existSendBytes != 0L) {
                acc.put("sendBytes", existSendBytes + sendBytes);
            } else {
                acc.put("sendBytes", sendBytes);
            }
        }

        // 接受流量
        Long recvBytes = (Long) row.getField(16);
        if (recvBytes != null) {
            if (!acc.containsKey("recvBytes")) {
                acc.put("recvBytes", 0L);
            }
            Long existRecvBytes = (Long) acc.get("recvBytes");
            if (existRecvBytes != 0L) {
                acc.put("recvBytes", existRecvBytes + recvBytes);
            } else {
                acc.put("recvBytes", recvBytes);
            }
        }

        // 总流量
        Long bytes = (Long) row.getField(10);
        if (bytes != null) {
            if (!acc.containsKey("bytes")) {
                acc.put("bytes", 0L);
            }
            Long existBytes = (Long) acc.get("bytes");
            if (existBytes != 0L) {
                acc.put("bytes", bytes + existBytes);
            } else {
                acc.put("bytes", bytes);
            }
        }

        // 总包数
        Integer packets = (Integer) row.getField(11);
        if (packets != null) {
            if (!acc.containsKey("packets")) {
                acc.put("packets", 0);
            }
            Integer existPackets = (Integer) acc.get("packets");
            if (existPackets != 0) {
                acc.put("packets", packets + existPackets);
            } else {
                acc.put("packets", packets);
            }
        }
        return acc;
    }

    private static HashMap<String, Object> handleIpTagMerge(HashMap<String, Object> acc, HashMap<String, Object> acc1) {

        commonHandleMerge(acc, acc1);
        Long sendBytes = (Long) acc.get("sendBytes");
        Long sendBytes1 = (Long) acc1.get("sendBytes");
        acc.put("sendBytes", sendBytes + sendBytes1);

        Long recvBytes = (Long) acc.get("recvBytes");
        Long recvBytes1 = (Long) acc1.get("recvBytes");
        acc.put("recvBytes", recvBytes + recvBytes1);

        Long bytes = (Long) acc.get("bytes");
        Long bytes1 = (Long) acc1.get("bytes");
        acc.put("bytes", bytes + bytes1);

        Integer packets = (Integer) acc.get("packets");
        Integer packets1 = (Integer) acc1.get("packets");
        acc.put("packets", packets + packets1);

        return acc;
    }

    private static HashMap<String, Object> handleDomainTagAdd(HashMap<String, Object> acc, Row row) {
        commonHandleAdd(acc, row);
        acc.put("domainAddr", row.getFieldAs(1));
        acc.put("blackList", row.getFieldAs(4));
        acc.put("whiteList", row.getField(5));
        acc.put("remark", row.getField(6));
        acc.put("alexaRank", row.getField(7));
        acc.put("whois", row.getField(10));
        acc.put("task_id",row.getField(11));
        acc.put("domain_id",row.getField(12));

        Long bytes = (Long) row.getField(8);
        if (bytes != null) {
            if (!acc.containsKey("bytes")) {
                acc.put("bytes", 0L);
            }
            Long existBytes = (Long) acc.get("bytes");
            if (existBytes != 0L) {
                acc.put("bytes", bytes + existBytes);
            } else {
                acc.put("bytes", bytes);
            }
        }

        return acc;
    }

    private static HashMap<String, Object> handleDomainTagMerge(HashMap<String, Object> acc, HashMap<String, Object> acc1) {
        commonHandleMerge(acc, acc1);
        Long bytes = (Long) acc.get("bytes");
        Long bytes1 = (Long) acc1.get("bytes");
        acc.put("bytes", bytes + bytes1);
        return acc;
    }

    // 对最早出现时间，最晚出现时间，出现次数进行统一聚合
    private static HashMap<String, Object> commonHandleAdd(HashMap<String, Object> acc, Row row) {
        Integer currentTime = (Integer) row.getField(2);

        updateSeenTime(acc, currentTime);

        // 出现次数
        Long times = (Long) acc.get("times");
        acc.put("times", times + 1);

        return acc;
    }

    private static HashMap<String, Object> handleMacTagAdd(HashMap<String, Object> acc, Row row) {
        Integer currentTime = (Integer) row.getField(6);
        updateSeenTime(acc, currentTime);

        Long bytes = (Long) row.getField(3);
        if (bytes != null) {
            if (!acc.containsKey("bytes")) {
                acc.put("bytes", 0L);
            }
            Long existBytes = (Long) acc.get("bytes");
            if (existBytes != 0L) {
                acc.put("bytes", bytes + existBytes);
            } else {
                acc.put("bytes", bytes);
            }
        }
        Long times = (Long) acc.get("times");


        acc.put("mac", row.getField(1));
        acc.put("times", times + 1);
        acc.put("vlan_info", row.getField(5));
        acc.put("black_list", row.getField(8));
        acc.put("white_list", row.getField(9));
        acc.put("remark", row.getField(10));
        acc.put("task_id",row.getField(11));
        return acc;
    }

    private static void updateSeenTime(HashMap<String, Object> acc, Integer currentTime) {
        Integer firstTime = (Integer) acc.getOrDefault("firstSeen",0);
        Integer lastTime = (Integer) acc.getOrDefault("lastSeen", 0);
        if (currentTime < firstTime || firstTime == 0) {
            acc.put("firstSeen", currentTime);
        }
        if (currentTime > lastTime || lastTime == 0) {
            acc.put("lastSeen", currentTime);
        }
    }

    private static HashMap<String, Object> handleMacTagMerge(HashMap<String, Object> acc, HashMap<String, Object> acc1) {
        commonHandleMerge(acc, acc1);
        Long bytes = (Long) acc.get("bytes");
        Long bytes1 = (Long) acc1.get("bytes");
        acc.put("bytes", bytes + bytes1);
        return acc;
    }

    private static HashMap<String, Object> handleAppTagAdd(HashMap<String, Object> acc, Row row) {
        Integer currentTime = (Integer) row.getField(6);
        updateSeenTime(acc, currentTime);
        acc.put("service_key", row.getField(1));
        acc.put("ip_addr", row.getField(2));
        acc.put("app_name", row.getField(3));
        acc.put("port", row.getField(4));
        acc.put("ip_pro", row.getField(5));
        return acc;
    }

    private static HashMap<String, Object> handleFDomainTagAdd(HashMap<String, Object> acc, Row row) {
        Integer currentTime = (Integer) row.getField(3);
        updateSeenTime(acc, currentTime);
        acc.put("fdomain", row.getField(1));
        acc.put("fdomain_id",row.getField(2));
        return acc;
    }

    private static HashMap<String, Object> handleFingerTagAdd(HashMap<String, Object> acc, Row row) {
        Integer currentTime = (Integer) row.getField(5);
        updateSeenTime(acc, currentTime);
        acc.put("finger_id", row.getField(1));
        acc.put("ja3_hash", row.getField(2));
        acc.put("desc", row.getField(3));
        acc.put("type", row.getField(4));
        return acc;
    }

    private static HashMap<String, Object> handleUATagAdd(HashMap<String, Object> acc, Row row) {
        Integer currentTime = (Integer) row.getField(4);
        updateSeenTime(acc, currentTime);
        acc.put("ua_id", row.getField(1));
        acc.put("ua_str", row.getField(2));
        acc.put("desc", row.getField(3));
        return acc;
    }

    private static HashMap<String, Object> handleDeviceTagAdd(HashMap<String, Object> acc, Row row) {
        Integer currentTime = (Integer) row.getField(2);
        updateSeenTime(acc, currentTime);
        acc.put("device_name", row.getField(1));
        return acc;
    }

    private static HashMap<String, Object> handleOSTagAdd(HashMap<String, Object> acc, Row row) {
        Integer currentTime = (Integer) row.getField(2);
        updateSeenTime(acc, currentTime);
        acc.put("os_name", row.getField(1));
        return acc;
    }

    private static HashMap<String, Object> handleApptypeTag(HashMap<String, Object> acc, Row row) {
        Integer currentTime = (Integer) row.getField(6);
        updateSeenTime(acc, currentTime);
        acc.put("application_name", row.getField(1));
        return acc;
    }


    private static HashMap<String, Object> commonHandleMerge(HashMap<String, Object> acc, HashMap<String, Object> acc1) {
        Integer firstTime = (Integer) acc.get("firstSeen");
        Integer lastTime = (Integer) acc.get("lastSeen");

        Integer firstTime1 = (Integer) acc1.get("firstSeen");
        Integer lastTime1 = (Integer) acc1.get("lastSeen");

        if (firstTime > firstTime1) {
            acc.put("firstSeen", firstTime1);
        }

        if (lastTime < lastTime1) {
            acc.put("lastSeen", lastTime1);
        }

        Long times = (Long) acc.get("times");
        Long times1 = (Long) acc.get("times");
        acc.put("time", times + times1);
        return acc;
    }

}
