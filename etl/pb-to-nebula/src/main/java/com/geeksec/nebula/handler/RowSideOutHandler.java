package com.geeksec.nebula.handler;

import com.geeksec.common.OutPutTagConstant;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 * @Description：Row 分流选择器
 */
public class RowSideOutHandler {
    public static SingleOutputStreamOperator<Row> handleRowSideOutPutTag(DataStream<Row> rowDataStream) {
        return rowDataStream.process(new ProcessFunction<Row, Row>() {
            @Override
            public void processElement(Row row, Context ctx, Collector<Row> collector) throws Exception {
                // 通过ROW的第一个字段来判断是什么类型的数据
                assert row.getField(0) != null;
                String type = row.getField(0).toString(); // 获取sink Row 类型
                if ("LABEL_TAG".equals(type)) {
                    ctx.output(OutPutTagConstant.LABEL_TAG, row);
                }
                if (type.endsWith("TAG")) {
                    // 分到总tag流中
                    ctx.output(OutPutTagConstant.VERTEX_ALL_ROW_TAG, row);
                } else if (type.endsWith("EDGE")) {
                    ctx.output(OutPutTagConstant.EDGE_ALL_ROW_TAG, row);
                }
            }
        }).setParallelism(8).name("对总流进行Edge和Tag分流");
    }

    public static SingleOutputStreamOperator<Row> handleRowSinkOutPutTag(DataStream<Row> rowDataStream) {
        return rowDataStream.process(new ProcessFunction<Row, Row>() {
            @Override
            public void processElement(Row row, ProcessFunction<Row, Row>.Context ctx, Collector<Row> collector) throws Exception {
                if (row != null && row.getArity() != 0) {
                    assert row.getField(0) != null;
                    String type = row.getField(0).toString(); // 获取sink Row 类型
                    switch (type) {
                        case "IP_TAG":
                            ctx.output(OutPutTagConstant.IP_ROW_TAG, row);
                            break;
                        case "MAC_TAG":
                            ctx.output(OutPutTagConstant.MAC_ROW_TAG, row);
                            break;
                        case "APP_TAG":
                            ctx.output(OutPutTagConstant.APP_TAG, row);
                            break;
                        case "DOMAIN_TAG":
                            ctx.output(OutPutTagConstant.DOMAIN_TAG, row);
                            break;
                        case "FDOMAIN_TAG":
                            ctx.output(OutPutTagConstant.FDOMAIN_TAG, row);
                            break;
                       case "SSL_FINGER_TAG":
                            ctx.output(OutPutTagConstant.SSL_FINGER_TAG, row);
                            break;
                        case "LABEL_TAG":
                            ctx.output(OutPutTagConstant.LABEL_TAG, row);
                            break;
                        case "UA_TAG":
                            ctx.output(OutPutTagConstant.UA_TAG, row);
                            break;
                        case "DEVICE_TAG":
                            ctx.output(OutPutTagConstant.DEVICE_TAG, row);
                            break;
                        case "OS_TAG":
                            ctx.output(OutPutTagConstant.OS_TAG, row);
                            break;
                        case "ORG_TAG":
                            ctx.output(OutPutTagConstant.ORG_TAG, row);
                            break;
                        case "SRC_BIND_EDGE":
                            ctx.output(OutPutTagConstant.SRC_BIND_EDGE, row);
                            break;
                        case "DST_BIND_EDGE":
                            ctx.output(OutPutTagConstant.DST_BIND_EDGE, row);
                            break;
                        case "IP_CONNECT_EDGE":
                            ctx.output(OutPutTagConstant.IP_CONNECT_EDGE, row);
                            break;
                        case "MAC_CONNECT_EDGE":
                            ctx.output(OutPutTagConstant.MAC_CONNECT_EDGE, row);
                            break;
                        case "FDOMAIN_BELONG_EDGE":
                            ctx.output(OutPutTagConstant.FDOMAIN_BELONG_EDGE, row);
                            break;
                        case "CLIENT_APP_EDGE":
                            ctx.output(OutPutTagConstant.CLIENT_APP_EDGE, row);
                            break;
                        case "APP_SERVER_EDGE":
                            ctx.output(OutPutTagConstant.APP_SERVER_EDGE, row);
                            break;
                        case "CLIENT_HTTP_CONNECT_DOMAIN_EDGE":
                            ctx.output(OutPutTagConstant.CLIENT_HTTP_CONNECT_DOMAIN_EDGE, row);
                            break;
                        case "SERVER_HTTP_CONNECT_DOMAIN_EDGE":
                            ctx.output(OutPutTagConstant.SERVER_HTTP_CONNECT_DOMAIN_EDGE, row);
                            break;
                        case "CLIENT_SSL_CONNECT_DOMAIN_EDGE":
                            ctx.output(OutPutTagConstant.CLIENT_SSL_CONNECT_DOMAIN_EDGE, row);
                            break;
                        case "SERVER_SSL_CONNECT_DOMAIN_EDGE":
                            ctx.output(OutPutTagConstant.SERVER_SSL_CONNECT_DOMAIN_EDGE, row);
                            break;
                        case "CLIENT_USE_CERT_EDGE":
                            ctx.output(OutPutTagConstant.CLIENT_USE_CERT_EDGE, row);
                            break;
                        case "SERVER_USE_CERT_EDGE":
                            ctx.output(OutPutTagConstant.SERVER_USE_CERT_EDGE, row);
                            break;
                        case "CLIENT_CONNECT_CERT_EDGE":
                            ctx.output(OutPutTagConstant.CLIENT_CONNECT_CERT_EDGE, row);
                            break;
                        case "CLIENT_USE_SSLFINGER_EDGE":
                            ctx.output(OutPutTagConstant.CLIENT_USE_SSLFINGER_EDGE, row);
                            break;
                        case "SERVER_USE_SSLFINGER_EDGE":
                            ctx.output(OutPutTagConstant.SERVER_USE_SSLFINGER_EDGE, row);
                            break;
                        case "SNI_BIND_EDGE":
                            ctx.output(OutPutTagConstant.SNI_BIND_EDGE, row);
                            break;
                        case "SSLFINGER_CONNECT_CERT_EDGE":
                            ctx.output(OutPutTagConstant.SSLFINGER_CONNECT_CERT_EDGE, row);
                            break;
                        case "SSLFINGER_CONNECT_DOMAIN_EDGE":
                            ctx.output(OutPutTagConstant.SSLFINGER_CONNECT_DOMAIN_EDGE, row);
                            break;
                        case "CLIENT_QUERY_DOMAIN_EDGE":
                            ctx.output(OutPutTagConstant.CLIENT_QUERY_DOMAIN_EDGE, row);
                            break;
                        case "CLIENT_QUERY_DNS_SERVER_EDGE":
                            ctx.output(OutPutTagConstant.CLIENT_QUERY_DNS_SERVER_EDGE, row);
                            break;
                        case "DNS_SERVER_DOMAIN_EDGE":
                            ctx.output(OutPutTagConstant.DNS_SERVER_DOMAIN_EDGE, row);
                            break;
                        case "DNS_PARSE_TO_EDGE":
                            ctx.output(OutPutTagConstant.DNS_PARSE_TO_EDGE, row);
                            break;
                        case "CNAME_EDGE":
                            ctx.output(OutPutTagConstant.CNAME_EDGE, row);
                            break;
                        case "CNAME_RESULT_EDGE":
                            ctx.output(OutPutTagConstant.CNAME_RESULT_EDGE, row);
                            break;
                        case "UA_CONNECT_DOMAIN_EDGE":
                            ctx.output(OutPutTagConstant.UA_CONNECT_DOMAIN_EDGE, row);
                            break;
                        case "CLIENT_USE_UA_EDGE":
                            ctx.output(OutPutTagConstant.CLIENT_USE_UA_EDGE, row);
                            break;
                        case "UA_BELONG_DEVICE_EDGE":
                            ctx.output(OutPutTagConstant.UA_BELONG_DEVICE_EDGE, row);
                            break;
                        case "UA_BELONG_OS_EDGE":
                            ctx.output(OutPutTagConstant.UA_BELONG_OS_EDGE, row);
                            break;
                        case "APK_TAG":
                            ctx.output(OutPutTagConstant.APK_TAG, row);
                            break;
                        case "SNI_FDOMAIN_EDGE":
                            ctx.output(OutPutTagConstant.SNI_FDOMAIN_EDGE, row);
                            break;
                        case "CERT_SERVICE_EDGE":
                            ctx.output(OutPutTagConstant.CERT_SERVICE_EDGE, row);
                            break;
                        case "CERT_BELONG_APP_EDGE":
                            ctx.output(OutPutTagConstant.CERT_BELONG_APP_EDGE, row);
                            break;
                        case "DOMAIN_BELONG_APP_EDGE":
                            ctx.output(OutPutTagConstant.DOMAIN_BELONG_APP_EDGE, row);
                            break;
                        case "IP_BELONG_ORG_EDGE":
                            ctx.output(OutPutTagConstant.IP_BELONG_ORG_EDGE, row);
                            break;
                        case "UA_BELONG_APP_EDGE":
                            ctx.output(OutPutTagConstant.UA_BELONG_APP_EDGE, row);
                            break;
                        case "DOMAIN_BELONG_ORG_EDGE":
                            ctx.output(OutPutTagConstant.DOMAIN_BELONG_ORG_EDGE, row);
                            break;
                        case "IP_APP_EDGE":
                            ctx.output(OutPutTagConstant.IP_APP_EDGE, row);
                            break;
                        case "DSSLFINGER_APP_EDGE":
                            ctx.output(OutPutTagConstant.DSSLFINGER_APP_EDGE, row);
                            break;
                        default:
                            break;
                    }
                }
            }


        }).name("将所有异步处理后的数据进行不同点边类型的sink分类").setParallelism(4);
    }
}
