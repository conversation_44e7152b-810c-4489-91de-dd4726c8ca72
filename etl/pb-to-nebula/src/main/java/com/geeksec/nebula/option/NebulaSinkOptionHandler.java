package com.geeksec.nebula.option;

import com.geeksec.common.OutPutTagConstant;
import com.geeksec.common.loader.PropertiesLoader;
import com.geeksec.nebula.handler.RowSideOutHandler;
import com.geeksec.nebula.handler.WindowFunctionHandler;
import com.geeksec.nebula.handler.aggr.NebulaEdgeAggrHandler;
import com.geeksec.nebula.handler.aggr.NebulaTagAggrHandler;
import com.geeksec.nebula.handler.async.Redis2NebulaAsyncFunction;
import com.geeksec.nebulaFunction.key.EdgeKeySelector;
import java.util.HashMap;
import java.util.Iterator;
import java.util.concurrent.TimeUnit;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.AggregateFunction;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.connector.nebula.connection.NebulaClientOptions;
import org.apache.flink.connector.nebula.connection.NebulaGraphConnectionProvider;
import org.apache.flink.connector.nebula.connection.NebulaMetaConnectionProvider;
import org.apache.flink.connector.nebula.sink.NebulaSinkFunction;
import org.apache.flink.streaming.api.datastream.AsyncDataStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.windowing.WindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingEventTimeWindows;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @Description：Nebula Sink入库配置入口
 */
public class NebulaSinkOptionHandler {

    private static PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");
    public static final String NEBULA_GRAPH_ADDR = propertiesLoader.getProperty("nebula.graph.addr");
    public static final String NEBULA_META_ADDR = propertiesLoader.getProperty("nebula.meta.addr");
    public static final String NEBULA_GRAPH_SPACE = propertiesLoader.getProperty("nebula.space.name");

    private static Integer capacity = 1000;

    public static NebulaGraphConnectionProvider graphConnectionProvider = null;
    public static NebulaMetaConnectionProvider metaConnectionProvider = null;

    static {
        // Nebula Conn通用配置
        NebulaClientOptions nebulaClientOptions = new NebulaClientOptions.NebulaClientOptionsBuilder()
                .setGraphAddress(NEBULA_GRAPH_ADDR)
                .setMetaAddress(NEBULA_META_ADDR)
                .build();
        graphConnectionProvider = new NebulaGraphConnectionProvider(nebulaClientOptions);
        metaConnectionProvider = new NebulaMetaConnectionProvider(nebulaClientOptions);
    }


    public static void handleDataType(SingleOutputStreamOperator<Row> stream, String streamType) {
        switch (streamType) {
            case "label":
                labelSink(stream);
                break;
            default:
                break;
        }
    }

    private static void labelSink(SingleOutputStreamOperator<Row> stream) {
        stream.getSideOutput(OutPutTagConstant.LABEL_TAG).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleTagFormat("label_tag"))).name("LABEL 点插入").setParallelism(1);
    }

    public static void orgSink(SingleOutputStreamOperator<Row> stream) {
        stream.addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleTagFormat("org_tag"))).name("ORG 点插入").setParallelism(1);
    }

    /**
     * 按照300s为水位线进行IP tag点信息聚合
     */
    private static DataStream<Row> filterTagRowByAggr(DataStream<Row> tagRowStream) {
        DataStream<Row> aggregateRowStream = tagRowStream.assignTimestampsAndWatermarks(
                        WatermarkStrategy.forMonotonousTimestamps()
                ).keyBy(new KeySelector<Row, String>() {
                    @Override
                    public String getKey(Row row) throws Exception {
                        // 通过ipAddr/Mac作为VID进行keyBy
                        String key = (String) row.getField(1);
                        return key;
                    }
                }).window(TumblingProcessingTimeWindows.of(Time.seconds(300)))
                .aggregate(new AggregateFunction<Row, HashMap<String, Object>, HashMap<String, Object>>() {
                    @Override
                    public HashMap<String, Object> createAccumulator() {
                        // IP tag需要聚合的字段
                        HashMap<String, Object> acc = new HashMap<>();
                        acc.put("bytes", 0L);
                        acc.put("packets", 0);
                        acc.put("firstSeen", 0);
                        acc.put("lastSeen", 0);
                        acc.put("times", 0L);
                        return acc;
                    }

                    @Override
                    public HashMap<String, Object> add(Row row, HashMap<String, Object> acc) {
                        acc = NebulaTagAggrHandler.handleTagAddFiled(acc, row);
                        return acc;
                    }

                    @Override
                    public HashMap<String, Object> getResult(HashMap<String, Object> acc) {
                        return acc;
                    }

                    @Override
                    public HashMap<String, Object> merge(HashMap<String, Object> acc, HashMap<String, Object> acc1) {
                        acc = NebulaTagAggrHandler.handleTagMergeFiled(acc, acc1);
                        return acc;
                    }
                }, new WindowFunction<HashMap<String, Object>, Row, String, TimeWindow>() {
                    @Override
                    public void apply(String key, TimeWindow timeWindow, Iterable<HashMap<String, Object>> iterable, Collector<Row> collector) throws Exception {
                        // 通过WindowFuntion将聚合后的最终数据进行转换
                        Iterator<HashMap<String, Object>> iterator = iterable.iterator();
                        while (iterator.hasNext()) {
                            HashMap<String, Object> resultMap = iterator.next();
                            Row resultRow = WindowFunctionHandler.handleTagRowByRowType(key, resultMap);
                            if (!ObjectUtils.isEmpty(resultRow)) {
                                collector.collect(resultRow);
                            }
                        }
                    }
                }).name("window function 聚合最终数据统一转换").setParallelism(8);
        return aggregateRowStream;

    }


    /**
     * 按照300s为水位线进行边流窗口聚合
     *
     * @param singleEdgeRowStream 以当前边种类进行单独处理
     * @return
     */
    private static DataStream<Row> filterEdgeRowByAggr(DataStream<Row> singleEdgeRowStream) {
        DataStream<Row> aggregateRowStream = singleEdgeRowStream.assignTimestampsAndWatermarks(
                        WatermarkStrategy.forMonotonousTimestamps()
                ).keyBy(new EdgeKeySelector())
                .window(TumblingProcessingTimeWindows.of(Time.seconds(300)))
                .aggregate(
                        new AggregateFunction<Row, HashMap<String, Object>, HashMap<String, Object>>() {
                            @Override
                            public HashMap<String, Object> createAccumulator() {
                                // 统一字段 最初出现时间，最晚出现时间，总数
                                HashMap<String, Object> acc = new HashMap<>();
                                acc.put("firstTime", 0);
                                acc.put("lastTime", 0);
                                acc.put("sessionCnt", 0L);
                                return acc;
                            }

                            @Override
                            public HashMap<String, Object> add(Row row, HashMap<String, Object> acc) {
                                // 根据Type种类不同，对其acc累加器进行key-value添加
                                acc = NebulaEdgeAggrHandler.handleEdgeAddFiled(acc, row);
                                return acc;
                            }

                            @Override
                            public HashMap<String, Object> getResult(HashMap<String, Object> acc) {
                                return acc;
                            }

                            @Override
                            public HashMap<String, Object> merge(HashMap<String, Object> acc, HashMap<String, Object> acc1) {
                                acc = NebulaEdgeAggrHandler.handleEdgeMergeFiled(acc, acc1);
                                return acc;
                            }
                        }, new WindowFunction<HashMap<String, Object>, Row, Tuple3<String, String, String>, TimeWindow>() {
                            @Override
                            public void apply(Tuple3<String, String, String> key, TimeWindow timeWindow, Iterable<HashMap<String, Object>> iterable, Collector<Row> collector) throws Exception {
                                // 通过WindowFunction将聚合后的最终数据进行转换
                                Iterator<HashMap<String, Object>> iterator = iterable.iterator();
                                while (iterator.hasNext()) {
                                    HashMap<String, Object> resultMap = iterator.next();
                                    Row resultRow = WindowFunctionHandler.handleEdgeRowByRowType(key, resultMap);
                                    if (!ObjectUtils.isEmpty(resultRow)) {
                                        collector.collect(resultRow);
                                    }
                                }
                            }
                        }).name("window function 聚合最终数据统一转换").setParallelism(8);
        return aggregateRowStream;
    }

    /**
     * 分成Tag和Edge进行总流操作，随即进行异步查询与窗口聚合
     *
     */
    public static SingleOutputStreamOperator<Row> execAsyncFunction(SingleOutputStreamOperator<Row> stream) {
        DataStream<Row> tagAsyncDataStream = filterTagRowByAggr(stream.getSideOutput(OutPutTagConstant.VERTEX_ALL_ROW_TAG));
        DataStream<Row> edgeAsyncDataStream = filterEdgeRowByAggr(stream.getSideOutput(OutPutTagConstant.EDGE_ALL_ROW_TAG));

        DataStream<Row> asyncDataStream = tagAsyncDataStream.union(edgeAsyncDataStream);

        SingleOutputStreamOperator<Row> asyncQueryDataResult = AsyncDataStream
                .unorderedWait(asyncDataStream,
                        new Redis2NebulaAsyncFunction(),
                        60,
                        TimeUnit.SECONDS,
                        capacity).name("统一进行异步查询").setParallelism(128);

        // 将不同中点、边分别输出到他们自身的OUTTAG上
        return RowSideOutHandler.handleRowSinkOutPutTag(asyncQueryDataResult);

    }

    /**
     * 统一分配sink nebula入库配置
     *
     */
    public static void handleSinkDataByType(SingleOutputStreamOperator<Row> stream) {
        // Vertex部分
        stream.getSideOutput(OutPutTagConstant.IP_ROW_TAG).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleTagFormat("ip_tag"))).name("IP点插入").setParallelism(8);
        stream.getSideOutput(OutPutTagConstant.MAC_ROW_TAG).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleTagFormat("mac_tag"))).name("MAC点插入").setParallelism(2);
        stream.getSideOutput(OutPutTagConstant.APP_TAG).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleTagFormat("app_tag"))).name("APP点插入").setParallelism(2);
        stream.getSideOutput(OutPutTagConstant.DOMAIN_TAG).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleTagFormat("domain_tag"))).name("DOMAIN点插入").setParallelism(8);
        stream.getSideOutput(OutPutTagConstant.FDOMAIN_TAG).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleTagFormat("fdomain_tag"))).name("FDOMAIN 点插入").setParallelism(4);
        stream.getSideOutput(OutPutTagConstant.SSL_FINGER_TAG).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleTagFormat("ssl_finger_tag"))).name("SSL_FINGER 点插入").setParallelism(1);
        stream.getSideOutput(OutPutTagConstant.UA_TAG).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleTagFormat("ua_tag"))).name("UA 点插入").setParallelism(1);
        stream.getSideOutput(OutPutTagConstant.DEVICE_TAG).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleTagFormat("device_tag"))).name("DEVICE 点插入").setParallelism(1);
        stream.getSideOutput(OutPutTagConstant.OS_TAG).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleTagFormat("os_tag"))).name("OS 点插入").setParallelism(1);
        stream.getSideOutput(OutPutTagConstant.ORG_TAG).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleTagFormat("org_tag"))).name("ORG 点插入").setParallelism(1);
        // 新增apk_tag
        stream.getSideOutput(OutPutTagConstant.APK_TAG).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleTagFormat("apk_tag"))).name("APK 点插入").setParallelism(1);

        // Edge部分
        stream.getSideOutput(OutPutTagConstant.FDOMAIN_BELONG_EDGE).addSink(new NebulaSinkFunction<>(NebulaSinkExecutionOptions.handleEdgeFormat("fdomain_belong_edge"))).name("FDOMAIN_BELONG 边插入").setParallelism(2);
        stream.getSideOutput(OutPutTagConstant.SRC_BIND_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("src_bind_edge"))).name("SRC_BIND 边插入").setParallelism(4);
        stream.getSideOutput(OutPutTagConstant.DST_BIND_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("dst_bind_edge"))).name("DST_BIND_EDGE 边插入").setParallelism(4);
        stream.getSideOutput(OutPutTagConstant.CLIENT_APP_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("client_app_edge"))).name("CLIENT_APP 边插入").setParallelism(4);
        stream.getSideOutput(OutPutTagConstant.APP_SERVER_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("app_server_edge"))).name("APP_SERVER 边插入").setParallelism(4);
        stream.getSideOutput(OutPutTagConstant.CLIENT_HTTP_CONNECT_DOMAIN_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("client_http_connect_edge"))).name("CLIENT_HTTP_CONNECT 边插入").setParallelism(1);
        stream.getSideOutput(OutPutTagConstant.SERVER_HTTP_CONNECT_DOMAIN_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("server_http_connect_edge"))).name("SERVER_HTTP_CONNECT 边插入").setParallelism(1);
        stream.getSideOutput(OutPutTagConstant.CLIENT_SSL_CONNECT_DOMAIN_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("client_ssl_connect_domain_edge"))).name("CLIENT_SSL_CONNECT_DOMAIN_EDGE 边插入").setParallelism(1);
        stream.getSideOutput(OutPutTagConstant.SERVER_SSL_CONNECT_DOMAIN_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("server_ssl_connect_domain_edge"))).name("SERVER_SSL_CONNECT_DOMAIN_EDGE 边插入").setParallelism(1);
        stream.getSideOutput(OutPutTagConstant.CLIENT_USE_CERT_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("client_use_cert_edge"))).name("CLIENT_USE_CERT 边插入").setParallelism(2);
        stream.getSideOutput(OutPutTagConstant.SERVER_USE_CERT_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("server_use_cert_edge"))).name("SERVER_USE_CERT 边插入").setParallelism(1);
        stream.getSideOutput(OutPutTagConstant.CLIENT_CONNECT_CERT_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("client_connect_cert_edge"))).name("CLIENT_CONNECT_CERT 边插入").setParallelism(1);
        stream.getSideOutput(OutPutTagConstant.CLIENT_USE_SSLFINGER_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("client_use_sslfinger_edge"))).name("CLIENT_USE_SSLFINGER_EDGE 边插入").setParallelism(1);
        stream.getSideOutput(OutPutTagConstant.SERVER_USE_SSLFINGER_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("server_use_sslfinger_edge"))).name("SERVER_USE_SSLFINGER_EDGE 边插入").setParallelism(1);
        stream.getSideOutput(OutPutTagConstant.SNI_BIND_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("sni_bind_edge"))).name("SNI_BIND_EDGE 边插入").setParallelism(1);
        stream.getSideOutput(OutPutTagConstant.SSLFINGER_CONNECT_CERT_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("sslfinger_connect_cert"))).name("SSLFINGER_CONNECT_CERT_EDGE 边插入").setParallelism(1);
        stream.getSideOutput(OutPutTagConstant.SSLFINGER_CONNECT_DOMAIN_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("sslfinger_connect_domain"))).name("SSLFINGER_CONNECT_DOMAIN_EDGE 边插入").setParallelism(1);
        stream.getSideOutput(OutPutTagConstant.CLIENT_QUERY_DOMAIN_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("client_query_domain_edge"))).name("CLIENT_QUERY_DOMAIN_EDGE 边插入").setParallelism(4);
        stream.getSideOutput(OutPutTagConstant.CLIENT_QUERY_DNS_SERVER_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("client_query_dns_server_edge"))).name("CLIENT_QUERY_DNS_SERVER_EDGE 边插入").setParallelism(2);
        stream.getSideOutput(OutPutTagConstant.DNS_SERVER_DOMAIN_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("dns_server_domain_edge"))).name("DNS_SERVER_DOMAIN_EDGE 边插入").setParallelism(2);
        stream.getSideOutput(OutPutTagConstant.DNS_PARSE_TO_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("dns_parse_to_edge"))).name("DNS_PARSE_TO_EDGE 边插入").setParallelism(2);
        stream.getSideOutput(OutPutTagConstant.CNAME_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("cname_edge"))).name("CNAME_EDGE 边插入").setParallelism(2);
        stream.getSideOutput(OutPutTagConstant.CNAME_RESULT_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("cname_result_edge"))).name("CNAME_RESULT_EDGE 边插入").setParallelism(2);
        stream.getSideOutput(OutPutTagConstant.UA_CONNECT_DOMAIN_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("ua_connect_domain_edge"))).name("UA_CONNECT_DOMAIN_EDGE 边插入").setParallelism(2);
        stream.getSideOutput(OutPutTagConstant.CLIENT_USE_UA_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("client_use_ua_edge"))).name("CLIENT_USE_UA_EDGE 边插入").setParallelism(2);
        stream.getSideOutput(OutPutTagConstant.UA_BELONG_DEVICE_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("ua_belong_device_edge"))).name("UA_BELONG_DEVICE_EDGE 边插入").setParallelism(2);
        stream.getSideOutput(OutPutTagConstant.UA_BELONG_OS_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("ua_belong_os_edge"))).name("UA_BELONG_OS_EDGE 边插入").setParallelism(2);
        // connect -> ip mac
//        stream.getSideOutput(OutPutTagConstant.CONNECT_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("connect_edge"))).name("CONNECT 边插入").setParallelism(4);

        stream.getSideOutput(OutPutTagConstant.IP_CONNECT_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("ip_connect_edge"))).name("IP CONNECT 边插入").setParallelism(4);
        stream.getSideOutput(OutPutTagConstant.MAC_CONNECT_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("mac_connect_edge"))).name("MAC CONNECT 边插入").setParallelism(4);

        // 新增
        stream.getSideOutput(OutPutTagConstant.SNI_FDOMAIN_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("sni_fdomain_edge"))).name("SNI_FDOMAIN_EDGE 边插入").setParallelism(2);
        stream.getSideOutput(OutPutTagConstant.CERT_SERVICE_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("cert_service_edge"))).name("CERT_SERVICE_EDGE 边插入").setParallelism(2);

        stream.getSideOutput(OutPutTagConstant.CERT_BELONG_APP_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("cert_belong_app_edge"))).name("CERT_BELONG_APP_EDGE 边插入").setParallelism(2);
        stream.getSideOutput(OutPutTagConstant.DOMAIN_BELONG_APP_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("domain_belong_app_edge"))).name("DOMAIN_BELONG_APP_EDGE 边插入").setParallelism(2);
        stream.getSideOutput(OutPutTagConstant.UA_BELONG_APP_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("ua_belong_app_edge"))).name("UA_BELONG_APP_EDGE 边插入").setParallelism(2);
        stream.getSideOutput(OutPutTagConstant.IP_BELONG_ORG_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("ip_belong_org_edge"))).name("IP_BELONG_ORG_EDGE 边插入").setParallelism(2);
        stream.getSideOutput(OutPutTagConstant.DOMAIN_BELONG_ORG_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("domain_belong_org_edge"))).name("DOMAIN_BELONG_ORG_EDGE 边插入").setParallelism(2);
        stream.getSideOutput(OutPutTagConstant.IP_APP_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("ip_belong_app_edge"))).name("IP_BELONG_APP_EDGE 边插入").setParallelism(2);
        stream.getSideOutput(OutPutTagConstant.DSSLFINGER_APP_EDGE).addSink(new NebulaSinkFunction(NebulaSinkExecutionOptions.handleEdgeFormat("sslfinger_belong_app_edge"))).name("SSLFINGER_BELONG_APP_EDGE 边插入").setParallelism(2);

    }
}
