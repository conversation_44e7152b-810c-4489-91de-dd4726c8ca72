package com.geeksec.common;

import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * <AUTHOR>
 * @Description：Flink 数据分流OutPutTag分流仓库
 */
public class OutPutTagConstant {

    public final static OutputTag<Map<String, Object>> CONNECTINFO_PBMAP_TAG_OUTPUT = new OutputTag("connectinfo_tag", TypeInformation.of(Map.class));
    public final static OutputTag<Map<String, Object>> CONNECTINFO_PBMAP_EDGE_OUTPUT = new OutputTag("connectinfo_edge", TypeInformation.of(Map.class));

    public static final OutputTag<Map<String, Object>> DNS_PBMAP_TAG_OUTPUT = new OutputTag("dns_tag", TypeInformation.of(Map.class));
    public static final OutputTag<Map<String, Object>> DNS_PBMAP_EDGE_OUTPUT = new OutputTag("dns_edge", TypeInformation.of(Map.class));

    public final static OutputTag<Map<String, Object>> SSL_PBMAP_TAG_OUTPUT = new OutputTag("ssl_tag", TypeInformation.of(Map.class));
    public final static OutputTag<Map<String, Object>> SSL_PBMAP_EDGE_OUTPUT = new OutputTag("ssl_edge", TypeInformation.of(Map.class));

    public final static OutputTag<Map<String, Object>> HTTP_PBMAP_TAG_OUTPUT = new OutputTag("http_tag", TypeInformation.of(Map.class));
    public final static OutputTag<Map<String, Object>> HTTP_PBMAP_EDGE_OUTPUT = new OutputTag("http_edge", TypeInformation.of(Map.class));


    // 点Row Tag
    public static final OutputTag<Row> IP_ROW_TAG = new OutputTag<>("IP_TAG", TypeInformation.of(Row.class));
    public static final OutputTag<Row> MAC_ROW_TAG = new OutputTag<>("MAC_TAG", TypeInformation.of(Row.class));
    public static final OutputTag<Row> APP_TAG = new OutputTag<>("APP_TAG", TypeInformation.of(Row.class));
    public static final OutputTag<Row> DOMAIN_TAG = new OutputTag<>("DOMAIN_TAG", TypeInformation.of(Row.class));
    public static final OutputTag<Row> FDOMAIN_TAG = new OutputTag<>("FDOMAIN_TAG", TypeInformation.of(Row.class));
    public static final OutputTag<Row> CERT_TAG = new OutputTag<>("CERT_TAG", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SSL_FINGER_TAG = new OutputTag<>("SSL_FINGER_TAG", TypeInformation.of(Row.class));
    public static final OutputTag<Row> UA_TAG = new OutputTag<>("UA_TAG", TypeInformation.of(Row.class));
    public static final OutputTag<Row> DEVICE_TAG = new OutputTag<>("DEVICE_TAG", TypeInformation.of(Row.class));
    public static final OutputTag<Row> OS_TAG = new OutputTag<>("OS_TAG", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ORG_TAG = new OutputTag<>("ORG_TAG", TypeInformation.of(Row.class));

    public static final OutputTag<Row> LABEL_TAG = new OutputTag<>("LABEL_TAG", TypeInformation.of(Row.class));

    // 总Row 流
    public static final OutputTag<Row> VERTEX_ALL_ROW_TAG = new OutputTag<>("VERTEX_ALL_ROW_TAG", TypeInformation.of(Row.class));
    public static final OutputTag<Row> EDGE_ALL_ROW_TAG = new OutputTag<>("EDGE_ALL_ROW_TAG", TypeInformation.of(Row.class));


    // 边Row Tag
    // 会话部分
    public static final OutputTag<Row> SRC_BIND_EDGE = new OutputTag<>("SRC_BIND_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> DST_BIND_EDGE = new OutputTag<>("DST_BIND_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> FDOMAIN_BELONG_EDGE = new OutputTag<>("FDOMAIN_BELONG_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> CLIENT_APP_EDGE = new OutputTag<>("CLIENT_APP_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> APP_SERVER_EDGE = new OutputTag<>("APP_SERVER_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> CLIENT_HTTP_CONNECT_DOMAIN_EDGE = new OutputTag<>("CLIENT_HTTP_CONNECT_DOMAIN_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SERVER_HTTP_CONNECT_DOMAIN_EDGE = new OutputTag<>("SERVER_HTTP_CONNECT_DOMAIN_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> IP_CONNECT_EDGE = new OutputTag<>("IP_CONNECT_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> MAC_CONNECT_EDGE = new OutputTag<>("MAC_CONNECT_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> CERT_SERVICE_EDGE = new OutputTag<>("CERT_SERVICE_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> CERT_BELONG_APP_EDGE = new OutputTag<>("CERT_BELONG_APP_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> DOMAIN_BELONG_APP_EDGE = new OutputTag<>("DOMAIN_BELONG_APP_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> UA_BELONG_APP_EDGE = new OutputTag<>("UA_BELONG_APP_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> IP_BELONG_ORG_EDGE = new OutputTag<>("IP_BELONG_ORG_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> DOMAIN_BELONG_ORG_EDGE = new OutputTag<>("DOMAIN_BELONG_ORG_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> IP_APP_EDGE = new OutputTag<>("IP_APP_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> DSSLFINGER_APP_EDGE = new OutputTag<>("DSSLFINGER_APP_EDGE", TypeInformation.of(Row.class));

    // SSL部分
    public static final OutputTag<Row> CLIENT_SSL_CONNECT_DOMAIN_EDGE = new OutputTag<>("CLIENT_SSL_CONNECT_DOMAIN_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SERVER_SSL_CONNECT_DOMAIN_EDGE = new OutputTag<>("SERVER_SSL_CONNECT_DOMAIN_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> CLIENT_USE_CERT_EDGE = new OutputTag<>("CLIENT_USE_CERT_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SERVER_USE_CERT_EDGE = new OutputTag<>("SERVER_USE_CERT_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> CLIENT_CONNECT_CERT_EDGE = new OutputTag<>("CLIENT_CONNECT_CERT_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> CLIENT_USE_SSLFINGER_EDGE = new OutputTag<>("CLIENT_USE_SSLFINGER_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SERVER_USE_SSLFINGER_EDGE = new OutputTag<>("SERVER_USE_SSLFINGER_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SNI_BIND_EDGE = new OutputTag<>("SNI_BIND_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SSLFINGER_CONNECT_CERT_EDGE = new OutputTag<>("SSLFINGER_CONNECT_CERT_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SSLFINGER_CONNECT_DOMAIN_EDGE = new OutputTag<>("SSLFINGER_CONNECT_DOMAIN_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> SNI_FDOMAIN_EDGE = new OutputTag<>("SNI_FDOMAIN_EDGE", TypeInformation.of(Row.class));

    // DNS部分
    public static final OutputTag<Row> CLIENT_QUERY_DOMAIN_EDGE = new OutputTag<>("CLIENT_QUERY_DOMAIN_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> CLIENT_QUERY_DNS_SERVER_EDGE = new OutputTag<>("CLIENT_QUERY_DNS_SERVER_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> DNS_SERVER_DOMAIN_EDGE = new OutputTag<>("DNS_SERVER_DOMAIN_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> DNS_PARSE_TO_EDGE = new OutputTag<>("DNS_PARSE_TO_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> CNAME_EDGE = new OutputTag<>("CNAME_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> CNAME_RESULT_EDGE = new OutputTag<>("CNAME_RESULT_EDGE", TypeInformation.of(Row.class));

    // HTTP 部分
    public static final OutputTag<Row> UA_CONNECT_DOMAIN_EDGE = new OutputTag<>("UA_CONNECT_DOMAIN_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> CLIENT_USE_UA_EDGE = new OutputTag<>("CLIENT_USE_UA_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> UA_BELONG_DEVICE_EDGE = new OutputTag<>("UA_BELONG_DEVICE_EDGE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> UA_BELONG_OS_EDGE = new OutputTag<>("UA_BELONG_OS_EDGE", TypeInformation.of(Row.class));

    // 检测部分
    public static final OutputTag<Row> APK_TAG = new OutputTag<>("APK_TAG", TypeInformation.of(Row.class));
}
