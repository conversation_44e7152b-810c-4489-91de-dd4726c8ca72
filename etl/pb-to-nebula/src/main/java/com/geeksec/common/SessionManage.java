package com.geeksec.common;

import com.geeksec.common.loader.PropertiesLoader;
import com.geeksec.common.utils.NebulaUtils;
import com.vesoft.nebula.client.graph.NebulaPoolConfig;
import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.net.NebulaPool;
import com.vesoft.nebula.client.graph.net.Session;
import java.util.concurrent.LinkedBlockingQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Description：
 */
public class SessionManage {

    private static final Logger logger = LoggerFactory.getLogger(SessionManage.class);

    private static PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");

    // Nebula Configs
    private static final int nebulaPoolMaxConnSize = 1000;
    private static final int nebulaPoolMinConnSize = 50;
    private static final int nebulaPoolIdleTime = 180000;
    private static final int nebulaPoolTimeout = 300000;
    private static String nebulaCluster = propertiesLoader.getProperty("nebula.graph.addr");
    private static String userName = propertiesLoader.getProperty("nebula.graph.username");
    private static String password = propertiesLoader.getProperty("nebula.graph.password");

    private static Integer sessionQueueSize = propertiesLoader.getInteger("nebula.session.size");

    // 空间表名
    private static String space = propertiesLoader.getProperty("nebula.space.name");

    public static NebulaPoolConfig nebulaPoolConfig() {
        NebulaPoolConfig nebulaPoolConfig = new NebulaPoolConfig();
        nebulaPoolConfig.setMaxConnSize(nebulaPoolMaxConnSize);
        nebulaPoolConfig.setMinConnSize(nebulaPoolMinConnSize);
        nebulaPoolConfig.setIdleTime(nebulaPoolIdleTime);
        nebulaPoolConfig.setTimeout(nebulaPoolTimeout);
        return nebulaPoolConfig;
    }

    private static transient LinkedBlockingQueue<Session> sessionQueue = new LinkedBlockingQueue();
    private static transient NebulaPool nebulaPool = null;

    public static void initSessionQueue() {
        try {
            if (nebulaPool == null) {
                nebulaPool = NebulaUtils.getNebulaPool(nebulaPoolConfig());
            }
            logger.info("开始初始化Session使用连接池！连接队列长度设置：{}", sessionQueueSize);
            sessionQueue.clear();
            for (int i = 0; i < sessionQueueSize; i++) {
                sessionQueue.put(nebulaPool.getSession(userName, password, false));
            }
            logger.info("初始化session队列成功! 队列长度: {}", sessionQueue.size());
        } catch (Exception e) {
            logger.error("初始化Session队列失败,error:{}", e);
        }
    }

    public static ResultSet executeNGQL(String query) {
        Session session = getSession();
        if (session == null) {
            return null;
        }
        try {
            logger.info("获取session成功！session is {},active num is {}",session,sessionQueue.size());
            ResultSet rs = session.execute(query);
            releaseSession(session);
            if (!rs.isSucceeded()) {
                logger.error(String.format("Execute: `%s', failed: %s", query, rs.getErrorMessage()));
            }
            return rs;
        } catch (Exception e) {
            logger.error("查询Nebula现存数据库信息失败,error->", e);
            return null;
        }
    }

    public static Session getSession() {
        try {
            Session session = sessionQueue.take();
//            logger.info("获取空闲Session队列中Session ,sessionQueue size is {}",sessionQueue.size());
            return session;
        } catch (Exception e) {
            logger.error("获取Nebula Session 失败！error :{}", e);
            return null;
        }
    }

    public static void releaseSession(Session session) {
        try{
            sessionQueue.put(session);
            logger.info("将session返回队列中,移除结果 sessionQueue size:{}", sessionQueue.size());

        }catch (Exception e){
            logger.error("释放Nebula Session 链接失败！error :{}", e);
        }
    }

    public static void closePool() {
        if (nebulaPool != null) {
            nebulaPool.close();
        }
    }
}
