package com.geeksec.nebulaEntity.edge.dns;

import com.geeksec.nebulaEntity.edge.BaseEdge;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class DnsParseToEdge extends BaseEdge {
    /**
     *  DNS解析服务器IP
     */
    private String dnsServer;

    /**
     * 是否为最终解析
     */
    private Boolean finalParse;

    /**
     * 最大有效期
     */
    private Integer maxTTL;

    /**
     * 最小有效期
     */
    private Integer minTTL;
}
