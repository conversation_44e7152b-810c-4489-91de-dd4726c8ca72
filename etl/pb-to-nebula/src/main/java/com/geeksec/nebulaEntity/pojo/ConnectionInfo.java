package com.geeksec.nebulaEntity.pojo;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class ConnectionInfo {

    /**
     * 源IP
     */
    private String sIp;

    /**
     * 源端口
     */
    private Integer sPort;

    /**
     * 目的IP
     */
    private String dIp;

    /**
     * 目的端口
     */
    private Integer dPort;

    /**
     * IP协议号
     */
    private Integer IPPro;

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 应用协议
     */
    private String appName;

    /**
     * 标签数组 [35102,18273,19271,20132]
     */
    private List<Object> labels;

    /**
     * 会话ID 全局唯一
     */
    private String sessionId;

    /**
     * 线程ID
     */
    private Integer threadId;

    /**
     * 会话持续时间-秒
     */
    private Integer duration;

    /**
     * 会话起始时间-秒
     */
    private Integer startTime;

    /**
     * 会话结束时间-秒
     */
    private Integer endTime;

    /**
     * 首层协议ID
     */
    private Integer firstProto;

    /**
     * 上下行流量比
     */
    private Integer sbytesDbytesDivi;

    /**
     * 源Mac地址
     */
    private String sMac;

    /**
     * 客户端SSL指纹
     */
    private Long sSSLFinger;

    /**
     * 客户端HTTP指纹
     */
    private Long sHTTPFinger;

    /**
     * 目的MAC
     */
    private String dMac;

    /**
     * 服务端SSL地址
     */
    private Long dSSLFinger;

    /**
     * 服务端HTTP指纹
     */
    private Long dHTTPFinger;

    /**
     * 源IP地址所在城市
     */
    private String sIpCity;

    /**
     * 源IP地址所在国家
     */
    private String sIpCountry;

    /**
     * 源IP地址经度
     */
    private Double sIpLongitude;

    /**
     * 源IP地址纬度
     */
    private Double sIpLatitude;

    /**
     * 目的IP地址所在城市
     */
    private String dIpCity;

    /**
     * 目的IP所在国家
     */
    private String dIpCountry;

    /**
     * 目的IP地址经度
     */
    private Double dIpLongitude;

    /**
     * 目的IP地址纬度
     */
    private Double dIpLatitude;

    /**
     * HTTP协议扩展字段
     */
    private List<singleHttp> Http;

    /**
     * 域名扩展字段
     */
    private List<singleDns> Dns;

    /**
     * 上下行流量统计
     */
    private ssPkt ssPkt;

    /**
     * SSL扩展字段
     */
    private List<singleSsl> Ssl;

    /**
     * 源端发送原始TTL
     */
    private Integer sInitialTTL;

    /**
     * 源端发送TTL最大距离
     */
    private Integer sTTLMax;

    /**
     * 源端发送TTL最小距离
     */
    private Integer sTTLMin;

    private Integer dMaxTTL;

    private Integer dBaseTTL;

    private Integer dInitialTTL;

    /**
     * 网络协议名
     */
    private String proName;

    private String esKey;

    private Integer type;

    @Data
    public static class singleHttp {
        String url;

        String act;

        String host;

        String Response;
    }

    @Data
    public static class singleDns {
        String domain;

        String domainIp;
    }

    @Data
    public static class singleSsl {
        String CH_Ciphersuit;

        Integer CH_CiphersuitNum;

        String CH_ServerName;

        List<Object> CH_ALPN;

        List<Object> sCertHash;

        List<Object> dCertHash;
    }

    @Data
    public static class ssPkt{
        Integer sPayLoadNum;

        Long sPayloadBytes;

        Integer dPayloadNum;

        Long dPayloadBytes;
    }


}
