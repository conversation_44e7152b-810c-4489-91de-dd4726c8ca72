package com.geeksec.nebulaEntity.vertex;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class SSLFingerTagInfo extends BaseVertex {

    /**
     * 指纹ID
     */
    private String fingerId;

    /**
     * Ja3指纹Hash
     */
    private String ja3Hash;

    /**
     * 指纹说明
     */
    private String desc;

    /**
     * 客户端/服务端
     */
    private String type;

    /**
     * 源IP
     */
    private String sIp;

    /**
     * 目的IP
     */
    private String dIp;

    /**
     * 服务器域名
     */
    private String server_domain;

    /**
     * 所属SessionId
     */
    private String SessionId;
}
