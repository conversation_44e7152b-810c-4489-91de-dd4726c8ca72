package com.geeksec.nebulaFunction.transfer.filter;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Description：过滤多重边处理Process
 */
public class EdgeFilterProcessFunction extends ProcessWindowFunction<Row, Row, Boolean, TimeWindow> {

    private static final Logger logger = LoggerFactory.getLogger(EdgeFilterProcessFunction.class);

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void process(Boolean aBoolean, Context context, Iterable<Row> iterable, Collector<Row> collector) throws Exception {

    }
}
