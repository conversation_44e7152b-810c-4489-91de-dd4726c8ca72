package com.geeksec.nebulaFunction.transfer.row.connection;


import com.geeksec.nebulaEntity.edge.BaseEdge;
import com.geeksec.nebulaEntity.edge.TaskBelongToEdge;
import com.geeksec.nebulaEntity.edge.app.RelatedIpAppEdge;
import com.geeksec.nebulaEntity.edge.connection.BindEdge;
import com.geeksec.nebulaEntity.edge.connection.ConnectEdge;
import com.geeksec.nebulaFunction.transfer.row.dns.DnsInfoEdgeRowFlatMap;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description：
 */
public class ConnectInfoEdgeRowFlatMap extends RichFlatMapFunction<Map<String, Object>, Row> {

    private Map<String, String> BASE_EDGE_OBJECT_MAPPING = new HashMap<>();
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        BASE_EDGE_OBJECT_MAPPING.put("ip2OrgEdgeList","IP_BELONG_ORG_EDGE");
        BASE_EDGE_OBJECT_MAPPING.put("domain2OrgEdgeList","DOMAIN_BELONG_ORG_EDGE");
        BASE_EDGE_OBJECT_MAPPING.put("ua2AppEdgeList","UA_BELONG_APP_EDGE");
        BASE_EDGE_OBJECT_MAPPING.put("certBelongAppTagEdgeList","CERT_BELONG_APP_EDGE");
        BASE_EDGE_OBJECT_MAPPING.put("domainBelongAppTagEdgeList","DOMAIN_BELONG_APP_EDGE");
        BASE_EDGE_OBJECT_MAPPING.put("ip2AppEdge","IP_APP_EDGE");
        BASE_EDGE_OBJECT_MAPPING.put("dSslFinger2AppEdge","DSSLFINGER_APP_EDGE");
    }

    @Override
    public void flatMap(Map<String, Object> edgeMap, Collector<Row> collector) throws Exception {
        // 会话部分
        BindEdge srcBindEdge = (BindEdge) edgeMap.get("srcBindEdge");
        if (!ObjectUtils.isEmpty(srcBindEdge)) {
            Row srcBindEdgeRow = new Row(9);
            srcBindEdgeRow.setField(0, "SRC_BIND_EDGE");
            srcBindEdgeRow.setField(1, srcBindEdge.getSrcId());
            srcBindEdgeRow.setField(2, srcBindEdge.getDstId());
            srcBindEdgeRow.setField(3, 0L);
            srcBindEdgeRow.setField(4, srcBindEdge.getSessionCnt());
            srcBindEdgeRow.setField(5, srcBindEdge.getIpAddr());
            srcBindEdgeRow.setField(6, srcBindEdge.getMac());
            srcBindEdgeRow.setField(7, srcBindEdge.getBytes());
            srcBindEdgeRow.setField(8, srcBindEdge.getPackets());
            collector.collect(srcBindEdgeRow);
        }

        BindEdge dstBindEdge = (BindEdge) edgeMap.get("dstBindEdge");
        if (!ObjectUtils.isEmpty(dstBindEdge)) {
            Row dstBindEdgeRow = new Row(9);
            dstBindEdgeRow.setField(0, "DST_BIND_EDGE");
            dstBindEdgeRow.setField(1, dstBindEdge.getSrcId());
            dstBindEdgeRow.setField(2, dstBindEdge.getDstId());
            dstBindEdgeRow.setField(3, 0L);
            dstBindEdgeRow.setField(4, dstBindEdge.getSessionCnt());
            dstBindEdgeRow.setField(5, dstBindEdge.getIpAddr());
            dstBindEdgeRow.setField(6, dstBindEdge.getMac());
            dstBindEdgeRow.setField(7, dstBindEdge.getBytes());
            dstBindEdgeRow.setField(8, dstBindEdge.getPackets());
            collector.collect(dstBindEdgeRow);
        }


        ConnectEdge ipConnectEdge = (ConnectEdge) edgeMap.get("ipConnectEdge");
        if (!ObjectUtils.isEmpty(ipConnectEdge)) {
            Row ipConnectEdgeRow = baseEdge2Row(13, "IP_CONNECT_EDGE", ipConnectEdge);
            ipConnectEdgeRow.setField(4, ipConnectEdge.getSessionCnt());
            ipConnectEdgeRow.setField(5, ipConnectEdge.getAppName());
            ipConnectEdgeRow.setField(6, ipConnectEdge.getDPort());
            ipConnectEdgeRow.setField(7, ipConnectEdge.getBytes());
            ipConnectEdgeRow.setField(8, ipConnectEdge.getPackets());
            ipConnectEdgeRow.setField(9, ipConnectEdge.getSendBytes());
            ipConnectEdgeRow.setField(10, ipConnectEdge.getRecvBytes());
            ipConnectEdgeRow.setField(11, ipConnectEdge.getSendPackets());
            ipConnectEdgeRow.setField(12, ipConnectEdge.getRecvPackets());
            collector.collect(ipConnectEdgeRow);
        }


        ConnectEdge macConnectEdge = (ConnectEdge) edgeMap.get("macConnectEdge");
        if (!ObjectUtils.isEmpty(macConnectEdge)) {
            Row macConnectEdgeRow = baseEdge2Row(13, "MAC_CONNECT_EDGE", macConnectEdge);
            macConnectEdgeRow.setField(4, macConnectEdge.getSessionCnt());
            macConnectEdgeRow.setField(5, macConnectEdge.getAppName());
            macConnectEdgeRow.setField(6, macConnectEdge.getDPort());
            macConnectEdgeRow.setField(7, macConnectEdge.getBytes());
            macConnectEdgeRow.setField(8, macConnectEdge.getPackets());
            macConnectEdgeRow.setField(9, macConnectEdge.getSendBytes());
            macConnectEdgeRow.setField(10, macConnectEdge.getRecvBytes());
            macConnectEdgeRow.setField(11, macConnectEdge.getSendPackets());
            macConnectEdgeRow.setField(12, macConnectEdge.getRecvPackets());
            collector.collect(macConnectEdgeRow);
        }


        List<BaseEdge> domain2FDomainEdgeList = (List<BaseEdge>) edgeMap.get("domain2FdomainEdgeList");
        if (!CollectionUtils.isEmpty(domain2FDomainEdgeList)) {
            for (BaseEdge domain2FDomainEdge : domain2FDomainEdgeList) {
                Row domain2FDomainEdgeRow = baseEdge2Row(4, "FDOMAIN_BELONG_EDGE", domain2FDomainEdge);
                collector.collect(domain2FDomainEdgeRow);
            }
        }

        RelatedIpAppEdge clientIpAppEdge = (RelatedIpAppEdge) edgeMap.get("clientAppEdge");
        if (!ObjectUtils.isEmpty(clientIpAppEdge)) {
            Row clientIpAppEdgeRow = baseEdge2Row(7, "CLIENT_APP_EDGE", clientIpAppEdge);
            clientIpAppEdgeRow.setField(4, clientIpAppEdge.getSessionCnt());
            clientIpAppEdgeRow.setField(5, clientIpAppEdge.getIpAddr());
            clientIpAppEdgeRow.setField(6, clientIpAppEdge.getAppName());
            collector.collect(clientIpAppEdgeRow);
        }

        RelatedIpAppEdge serverAppEdge = (RelatedIpAppEdge) edgeMap.get("serverAppEdge");
        if (!ObjectUtils.isEmpty(serverAppEdge)) {
            Row serverIpAppEdgeRow = baseEdge2Row(7, "APP_SERVER_EDGE", serverAppEdge);
            serverIpAppEdgeRow.setField(4, serverAppEdge.getSessionCnt());
            serverIpAppEdgeRow.setField(5, serverAppEdge.getIpAddr());
            serverIpAppEdgeRow.setField(6, serverAppEdge.getAppName());
            collector.collect(serverIpAppEdgeRow);
        }

        BaseEdge clientIpConnectDomaineEdge = (BaseEdge) edgeMap.get("clientIpConnectDomainEdge");
        if (!ObjectUtils.isEmpty(clientIpConnectDomaineEdge)) {
            Row clientIpConnectDomaineEdgeRow = baseEdge2Row(5, "CLIENT_HTTP_CONNECT_DOMAIN_EDGE", clientIpConnectDomaineEdge);
            clientIpConnectDomaineEdgeRow.setField(4, clientIpConnectDomaineEdge.getSessionCnt());
            collector.collect(clientIpConnectDomaineEdgeRow);
        }

        BaseEdge serverIpConnectDomainEdge = (BaseEdge) edgeMap.get("serverIpConnectDomainEdge");
        if (!ObjectUtils.isEmpty(serverIpConnectDomainEdge)) {
            Row serverIpConnectDomaineEdgeRow = baseEdge2Row(5, "SERVER_HTTP_CONNECT_DOMAIN_EDGE", serverIpConnectDomainEdge);
            serverIpConnectDomaineEdgeRow.setField(4, serverIpConnectDomainEdge.getSessionCnt());

            collector.collect(serverIpConnectDomaineEdgeRow);
        }

        List<BaseEdge> certConnectAppTagEdgeList = (List<BaseEdge>) edgeMap.get("certConnectAppTagEdgeList");
        if (!CollectionUtils.isEmpty(certConnectAppTagEdgeList)) {
            for (BaseEdge certConnectAppTagEdge : certConnectAppTagEdgeList) {
                Row certConnectAppTagEdgeRow = baseEdge2Row(4, "CERT_SERVICE_EDGE", certConnectAppTagEdge);
                collector.collect(certConnectAppTagEdgeRow);
            }
        }
        DnsInfoEdgeRowFlatMap.extractRowFromEdgeMap(edgeMap, collector, BASE_EDGE_OBJECT_MAPPING);
    }

    public static Row baseEdge2Row(int arity, String entry, BaseEdge edge) {
        Row edgeRow = new Row(arity);
        // 无通用属性
        edgeRow.setField(0, entry);
        edgeRow.setField(1, edge.getSrcId());
        edgeRow.setField(2, edge.getDstId());
        edgeRow.setField(3, 0);
        return edgeRow;
    }
}
