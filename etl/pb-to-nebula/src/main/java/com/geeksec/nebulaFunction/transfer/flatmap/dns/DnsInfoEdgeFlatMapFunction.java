package com.geeksec.nebulaFunction.transfer.flatmap.dns;

import com.geeksec.common.utils.*;
import com.geeksec.nebulaEntity.edge.BaseEdge;
import com.geeksec.nebulaEntity.edge.TaskBelongToEdge;
import com.geeksec.nebulaEntity.edge.dns.DnsParseToEdge;
import com.geeksec.nebulaEntity.edge.dns.DnsQueryEdge;
import com.geeksec.nebulaEntity.vertex.DomainTagInfo;
import com.geeksec.nebulaEntity.vertex.IpTagInfo;
import com.geeksec.nebulaEntity.vertex.TaskTagInfo;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixList;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixListFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import ua_parser.Parser;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.*;

import static com.geeksec.common.utils.ProNameForMysql.PASSWORD;
import static com.geeksec.common.utils.ProNameForMysql.USERNAME;
import static com.geeksec.nebulaFunction.transfer.flatmap.connection.ConnectInfoEdgeFlatMapFunction.*;

/**
 * <AUTHOR>
 * @Description：
 */
public class DnsInfoEdgeFlatMapFunction extends RichFlatMapFunction<Map<String, Object>, Map<String, Object>> {

    private final static Logger logger = LoggerFactory.getLogger(DnsInfoEdgeFlatMapFunction.class);
    private static PublicSuffixListFactory factory = null;
    public static PublicSuffixList suffixList = null;
    private transient Connection connection;
    public transient ValueState<Map<String, Object>> valueState;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        factory = new PublicSuffixListFactory();
        Properties properties = factory.getDefaults();
        properties.setProperty(PublicSuffixListFactory.PROPERTY_LIST_FILE, "/effective_tld_names.dat");
        suffixList = factory.build();
        Class.forName("com.mysql.jdbc.Driver").newInstance();
        connection = DriverManager.getConnection(KNOWLEDGE_HOST, USERNAME, PASSWORD);
    }

    @Override
    public void close() throws Exception {
        if (connection != null){
            connection.close();
        }
    }
    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<Map<String, Object>> collector) throws Exception {
        // 由于三种边类型所含属性相同，使用统一实体类DnsQueryEdge
        // 1.ClientIp客户端IP ---> Domain 域名 client_query_domain
        DnsQueryEdge clientQueryDomainEdge = getClientQueryDomainEdge(pbMap);

        // 2.ClientIP 客户端 ---> DNSServerIP DNS服务器IP client_query_dns_server
        DnsQueryEdge clientQueryDnsServerEdge = getClientQueryDnsServerEdge(pbMap);

        // 3.DNSServerIP DNS服务器IP ---> Domain dns_server_domain
        DnsQueryEdge dnsServerDomainEdge = getDnsServerDomainEdge(pbMap);

        // 4.Domain ---> ServerIP ---> parse_to
        List<DnsParseToEdge> dnsParseToEdgeList = getDnsParseToEdge(pbMap);

        // 5.domain ---> cname 或 cname ---> cname --->cname
        List<BaseEdge> cnameEdgeList = getCnameEdgeList(pbMap);

        // 6.CNAME ---> ServerIp ---> cname_result
        List<BaseEdge> cnameResultEdgeList = getCnameResultEdge(pbMap);

        // 7.DOMAIN ---> FDOMAIN ---> domain_belong_to
        List<BaseEdge> domainBelongToEdgeList = getDomain2FDomainEdge(pbMap);

        // 8. DOMAIN --> ORG
        List<BaseEdge> domain2OrgEdgeList = getDomain2OrgEdgeList(pbMap);
        // 9. IP --> ORG
        List<BaseEdge> ip2OrgEdgeList = getIP2OrgEdgeList(pbMap);

        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("clientQueryDomainEdge", clientQueryDomainEdge);
        resultMap.put("clientQueryDnsServerEdge", clientQueryDnsServerEdge);
        resultMap.put("dnsServerDomainEdge", dnsServerDomainEdge);
        resultMap.put("dnsParseToEdgeList", dnsParseToEdgeList);
        resultMap.put("cnameEdgeList", cnameEdgeList);
        resultMap.put("cnameResultEdgeList", cnameResultEdgeList);
        resultMap.put("domainBelongToEdgeList", domainBelongToEdgeList);

        resultMap.put("domain2OrgEdgeList", domain2OrgEdgeList);
        resultMap.put("ip2OrgEdgeList", ip2OrgEdgeList);

        collector.collect(resultMap);

    }

    private List<BaseEdge> getIP2OrgEdgeList(Map<String, Object> pbMap) {
        Set<String> ips = getIps(pbMap);
        if (CollectionUtils.isEmpty(ips)) {
            return null;
        }
        List<BaseEdge> ip2OrgEdgeList = new ArrayList<>();
        orgKnowledgeHit(connection, pbMap, ips, ip2OrgEdgeList, "ip_enterprise");
        return ip2OrgEdgeList;
    }

    private List<BaseEdge> getDomain2OrgEdgeList(Map<String, Object> pbMap) {
        Set<String> domains = getDomains(pbMap);
        if (CollectionUtils.isEmpty(domains)) {
            return null;
        }
        List<BaseEdge> domain2OrgEdgeList = new ArrayList<>();
        orgDomainKnowledgeHit(connection, pbMap, domains, domain2OrgEdgeList, "tb_domain_whois");
        return domain2OrgEdgeList;
    }

    private Set<String> getDomains(Map<String, Object> pbMap) {
        Set<String> domains = new HashSet<>();

        // 1.Domain字段
        String basicDomainAddr = (String) pbMap.get("Domain");
        if (DomainUtils.isValidDomain(basicDomainAddr)) {
            domains.add(basicDomainAddr.length() > 200 ? Md5Util.Md5(basicDomainAddr) : basicDomainAddr);
        }

        // 2.CNAME 字段
        Integer ansNum = (Integer) pbMap.get("Ans");
        if (ansNum != 0) {
            List<Map<String, Object>> answerMapList = (List<Map<String, Object>>) pbMap.get("Answer");
            for (Map<String, Object> answerMap : answerMapList) {
                Integer type = (Integer) answerMap.get("type");
                String domainAddr = StringUtils.EMPTY;
                // type 为 5时解析出CNAME
                if (type == 5) {
                    domainAddr = (String) answerMap.get("value");
                } else if (type == 1 || type == 28) {
                    // 对于解析完成后的结果，怕会有遗漏，在name字段获取域名的信息
                    domainAddr = (String) answerMap.get("name");
                }
                if (DomainUtils.isValidDomain(domainAddr)) {
                    domains.add(domainAddr.length() > 200 ? Md5Util.Md5(domainAddr) : domainAddr);
                }
            }
        }

        return domains;
    }
    private Set<String> getIps(Map<String, Object> pbMap){
        Set<String> ipSet = new HashSet<>();
        String Hkey = (String) pbMap.get("Hkey");

        String dIp = (String) pbMap.get("dIp");
        if (IpNetUtils.judgeInternal(dIp)) {
            dIp = Hkey.split("_")[1] + "-" + dIp;
        }
        ipSet.add(dIp);

        String sIp = (String) pbMap.get("sIp");
        if (IpNetUtils.judgeInternal(sIp)) {
            sIp = Hkey.split("_")[1] + "-" + sIp;
        }
        ipSet.add(sIp);

        String domainIpStr = (String) pbMap.get("DomainIp");
        ipSet.add(domainIpStr);
        List<String> domainIpList = new ArrayList<>();
        if (domainIpStr.contains("|")) {
            domainIpList = Arrays.asList(domainIpStr.split("\\|").clone());
        } else {
            domainIpList.add(domainIpStr);
        }
        if (!domainIpList.isEmpty()) {
            for (String domainIp : domainIpList) {
                if (!IpUtils.isIpv4Str(domainIp) && !IpUtils.isIpv6Str(domainIp)) {
                    continue;
                }
                if (IpNetUtils.isValidIPV4(domainIp)) {
                    // 判断是否为内网地址
                    if (IpNetUtils.judgeInternal(domainIp)) {
                        ipSet.add(Hkey.split("_")[1] + "-" + domainIp);
                    } else {
                        ipSet.add(domainIp);
                    }
                }
            }
        }

        return ipSet;
    }
    private DnsQueryEdge getClientQueryDomainEdge(Map<String, Object> pbMap) {
        DnsQueryEdge edge = new DnsQueryEdge();
        String clientIp = (String) pbMap.get("sIp");
        String domainAddr = (String) pbMap.get("Domain");
        List<Map<String, Object>> queryMapList = (List<Map<String, Object>>) pbMap.get("Query");
        Integer queryType = (Integer) queryMapList.get(0).get("type");
        if (DomainUtils.isValidDomain(domainAddr) && queryType != null) {
            edge.setSrcId(clientIp);
            if (domainAddr.length() > 200) {
                edge.setDstId(Md5Util.Md5(domainAddr));
            } else {
                edge.setDstId(domainAddr);
            }
            edge.setQueryType(queryType);
            edge.setAnswerType(judgeAnswerType(pbMap));
            edge.setSessionCnt(0L);
            return edge;
        }
        return null;
    }

    private DnsQueryEdge getClientQueryDnsServerEdge(Map<String, Object> pbMap) {
        DnsQueryEdge edge = new DnsQueryEdge();
        String clientIp = (String) pbMap.get("sIp");
        String serverIp = (String) pbMap.get("dIp");
        edge.setSrcId(clientIp);
        edge.setDstId(serverIp);

        List<Map<String, Object>> queryMapList = (List<Map<String, Object>>) pbMap.get("Query");
        Integer queryType = (Integer) queryMapList.get(0).get("type");
        if (queryType != null) {
            edge.setQueryType(queryType);
            edge.setAnswerType(judgeAnswerType(pbMap));
            edge.setSessionCnt(0L);
            return edge;
        } else {
            return null;
        }

    }

    private DnsQueryEdge getDnsServerDomainEdge(Map<String, Object> pbMap) {
        DnsQueryEdge edge = new DnsQueryEdge();
        String serverIp = (String) pbMap.get("dIp");
        String domainAddr = (String) pbMap.get("Domain");
        List<Map<String, Object>> queryMapList = (List<Map<String, Object>>) pbMap.get("Query");
        Integer queryType = (Integer) queryMapList.get(0).get("type");
        if (DomainUtils.isValidDomain(domainAddr) && queryType != null) {
            edge.setSrcId(serverIp);
            if (domainAddr.length() > 200) {
                edge.setDstId(Md5Util.Md5(domainAddr));
            } else {
                edge.setDstId(domainAddr);
            }
            edge.setQueryType(queryType);
            edge.setAnswerType(judgeAnswerType(pbMap));
            edge.setSessionCnt(0L);
            return edge;
        }
        return null;
    }


    private List<DnsParseToEdge> getDnsParseToEdge(Map<String, Object> pbMap) {
        List<DnsParseToEdge> dnsParseToEdgeList = new ArrayList<>();
        List<Map<String, Object>> answerMapList = (List<Map<String, Object>>) pbMap.get("Answer");
        String domainAddr = (String) pbMap.get("Domain");
        if (CollectionUtils.isEmpty(answerMapList)) {
            return null;
        }
        if (!DomainUtils.isValidDomain(domainAddr)) {
            return null;
        }
        for (Map<String, Object> answerMap : answerMapList) {
            Integer type = (Integer) answerMap.get("type");
            if (type == 1 || type == 28) {
                DnsParseToEdge edge = new DnsParseToEdge();
                if (domainAddr.length() > 200) {
                    edge.setSrcId(Md5Util.Md5(domainAddr));
                } else {
                    edge.setSrcId(domainAddr);
                }
                edge.setDstId((String) answerMap.get("value"));
                edge.setDnsServer(domainAddr);
                edge.setFinalParse(true);
                edge.setMaxTTL((Integer) answerMap.get("ttl"));
                edge.setMinTTL((Integer) answerMap.get("ttl"));
                edge.setSessionCnt(0L);
                dnsParseToEdgeList.add(edge);
            } else if (type == 5) {
                DnsParseToEdge edge = new DnsParseToEdge();
                if (domainAddr.length() > 200) {
                    edge.setSrcId(Md5Util.Md5(domainAddr));
                } else {
                    edge.setSrcId(domainAddr);
                }
                edge.setDstId((String) answerMap.get("value"));
                edge.setDnsServer(domainAddr);
                edge.setFinalParse(false);
                edge.setMaxTTL((Integer) answerMap.get("ttl"));
                edge.setMinTTL((Integer) answerMap.get("ttl"));
                edge.setSessionCnt(0L);
                dnsParseToEdgeList.add(edge);
            } else {
                continue;
            }
        }

        return dnsParseToEdgeList;
    }


    private List<BaseEdge> getCnameEdgeList(Map<String, Object> pbMap) {

        List<BaseEdge> cnameEdgeList = new ArrayList<>();

        // 判断Answer是否存在
        List<Map<String, Object>> answerMapList = (List<Map<String, Object>>) pbMap.get("Answer");
        if (CollectionUtils.isEmpty(answerMapList)) {
            return null;
        }
        for (Map<String, Object> answerMap : answerMapList) {
            Integer type = (Integer) answerMap.get("type");
            if (type == 5) {
                BaseEdge edge = new BaseEdge();
                String srcName = (String) answerMap.get("name");
                String dstName = (String) answerMap.get("value");
                // cname value -> domain
                if (!DomainUtils.isValidDomain(dstName)) {
                    return null;
                }
                if (srcName.length() > 200) {
                    edge.setSrcId(Md5Util.Md5(srcName));
                } else {
                    edge.setSrcId(srcName);
                }
                if (dstName.length() > 200) {
                    edge.setDstId(Md5Util.Md5(dstName));
                } else {
                    edge.setDstId(dstName);
                }
                edge.setDstId(dstName);
                edge.setSessionCnt(0L);
                cnameEdgeList.add(edge);
            }
        }
        return cnameEdgeList;
    }

    private List<BaseEdge> getCnameResultEdge(Map<String, Object> pbMap) {
        List<BaseEdge> cnameEdgeList = new ArrayList<>();
        List<Map<String, Object>> answerMapList = (List<Map<String, Object>>) pbMap.get("Answer");
        if (CollectionUtils.isEmpty(answerMapList)) {
            return null;
        }
        // 先判断是否有CNAME且有最终解析结果
        if (!judgeCnameResult(answerMapList)) {
            return null;
        } else {
            for (Map<String, Object> answerMap : answerMapList) {
                Integer type = (Integer) answerMap.get("type");
                if (type == 1 || type == 28) {
                    BaseEdge edge = new BaseEdge();
                    String domainAddr = (String) answerMap.get("name");
                    if (domainAddr.length() > 200) {
                        edge.setSrcId(Md5Util.Md5(domainAddr));
                    } else {
                        edge.setSrcId(domainAddr);
                    }
                    edge.setDstId((String) answerMap.get("value"));
                    edge.setSessionCnt(0L);
                    cnameEdgeList.add(edge);
                } else {
                    continue;
                }
            }
        }
        return cnameEdgeList;
    }


    private List<BaseEdge> getDomain2FDomainEdge(Map<String, Object> pbMap) {
        List<BaseEdge> edgeList = new ArrayList<>();

        // 基础DNS解析域名
        String basicDomainAddr = (String) pbMap.get("Domain");
        if (DomainUtils.isValidDomain(basicDomainAddr)) {
            String fBasicDomainAddr = StringUtil.EMPTY_STRING;
            try {
                fBasicDomainAddr = "*." + suffixList.getRegistrableDomain(basicDomainAddr);
            } catch (Exception e) {
                logger.warn("获取锚域名失败,basicDomainAddr--->{},error--->", basicDomainAddr, e);
            }
            if (!StringUtil.isNullOrEmpty(fBasicDomainAddr)) {
                BaseEdge edge = new BaseEdge();
                edge.setSrcId(basicDomainAddr);
                edge.setDstId(fBasicDomainAddr);
                edge.setSessionCnt(0L);
                edgeList.add(edge);
            }
        }

        // 2.CNAME 字段
        Integer ansNum = (Integer) pbMap.get("Ans");
        if (ansNum != 0) {
            List<Map<String, Object>> answerMapList = (List<Map<String, Object>>) pbMap.get("Answer");
            for (Map<String, Object> answerMap : answerMapList) {
                Integer type = (Integer) answerMap.get("type");
                // type 为 5时解析出CNAME
                if (type == 5) {
                    String domainAddr = (String) answerMap.get("value");
                    if (DomainUtils.isValidDomain(domainAddr)) {
                        String fDomainAddr = StringUtil.EMPTY_STRING;
                        try {
                            fDomainAddr = "*" + suffixList.getRegistrableDomain(basicDomainAddr);
                        } catch (Exception e) {
                            logger.warn("获取锚域名失败,basicDomainAddr--->{},error--->", basicDomainAddr, e);
                        }
                        if (!StringUtil.isNullOrEmpty(fDomainAddr)) {
                            BaseEdge edge = new BaseEdge();
                            edge.setSrcId(basicDomainAddr);
                            edge.setDstId(fDomainAddr);
                            edge.setSessionCnt(0L);
                            edgeList.add(edge);
                        }
                    }
                }
            }
        }
        // 处理锚域名MD5
        for (BaseEdge edge : edgeList) {
            String domain = edge.getSrcId();
            String fDomain = edge.getDstId();
            if (domain.length() > 200) {
                edge.setSrcId(Md5Util.Md5(domain));
            }
            if (fDomain.length() > 200) {
                edge.setDstId(Md5Util.Md5(fDomain));
            }
        }

        return edgeList;
    }

    private boolean judgeCnameResult(List<Map<String, Object>> answerMapList) {
        if (answerMapList.size() == 1) {
            return false;
        }
        for (Map<String, Object> answerMap : answerMapList) {
            Integer type = (Integer) answerMap.get("type");
            if (type == 1) {
                return true;
            } else if (type == 5) {
                continue;
            }
        }
        return false;
    }

    // 判断DNS请求返回类型
    private Integer judgeAnswerType(Map<String, Object> pbMap) {
        List<Integer> successAnsType = new ArrayList<>(Arrays.asList(1, 5, 28));
        List<Map<String, Object>> answerMapList = (List<Map<String, Object>>) pbMap.get("Answer");
        // 若Answer中为空，则为不存在
        if (CollectionUtils.isEmpty(answerMapList)) {
            return 2;
        }
        // 若Answer中type有1、5、28的形式，则判定为成功
        for (Map<String, Object> answerMap : answerMapList) {
            Integer type = (Integer) answerMap.get("type");
            if (successAnsType.contains(type)) {
                return 1;
            } else {
                continue;
            }
        }
        // 若上述都不包含，则为错误信息
        return 0;
    }


}
