package com.geeksec.nebulaFunction.transfer.flatmap.ssl;

import static com.geeksec.nebulaFunction.transfer.flatmap.dns.DnsInfoEdgeFlatMapFunction.suffixList;

import cn.hutool.core.util.StrUtil;
import com.geeksec.common.utils.DomainUtils;
import com.geeksec.common.utils.Md5Util;
import com.geeksec.nebulaEntity.edge.BaseEdge;
import com.geeksec.nebulaEntity.edge.TaskBelongToEdge;
import com.geeksec.nebulaEntity.edge.ssl.ClientUseCertEdge;
import com.geeksec.nebulaEntity.edge.ssl.SSLFingerConnectCertEdge;
import com.geeksec.nebulaEntity.edge.ssl.ServerUseCertEdge;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.util.Collector;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @Description：
 */
public class SslInfoEdgeFlatMapFunction extends RichFlatMapFunction<Map<String, Object>, Map<String, Object>> {

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<Map<String, Object>> collector) throws Exception {

        // 1. SSL 访问 & SSL 域名
        BaseEdge clientSslConnectDomainEdge = getSslConnectDomainEdge(pbMap, "client");
        BaseEdge serverSslConnectDomainEdge = getSslConnectDomainEdge(pbMap, "server");
        // 2.客户端使用证书 & 服务端使用证书 & 客户端所使用服务端证书
        ClientUseCertEdge clientUseCertEdge = getClientUseCertEdge(pbMap, "client");
        ServerUseCertEdge serverUseCertEdge = getServerUseCertEdge(pbMap, "server");
        ClientUseCertEdge clientUseServerCertEdge = getClientUseCertEdge(pbMap, "server");

        // 3.使用SSL指纹 & 应用SSL指纹
        BaseEdge clientSslFingerEdge = getSslFingerEdge(pbMap, "client");
        BaseEdge serverSslFingerEdge = getSslFingerEdge(pbMap, "server");

        // 4.域名SNI--->服务端证书关联
        BaseEdge domainServerCertEdge = getDomainCertEdge(pbMap);

        // 5.client SSL指纹--->服务端证书CertId
        SSLFingerConnectCertEdge sSSLConnectCertEdge = getSSLFingerConnectCertEdge(pbMap);

        // 6.client SSL指纹--->域名SNI 指纹访问域名
        BaseEdge sSSLConnectDomainEdge = getSSLFingerConnectDomainEdge(pbMap);

        // 8.域名SNI--->fDomain书关联
        BaseEdge domainServerFDomainEdge = getDomainServerFDomainEdge(pbMap);

        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("clientSslConnectDomainEdge", clientSslConnectDomainEdge);
        resultMap.put("serverSslConnectDomainEdge", serverSslConnectDomainEdge);
        resultMap.put("clientUseCertEdge", clientUseCertEdge);
        resultMap.put("serverUseCertEdge", serverUseCertEdge);
        resultMap.put("clientUseServerCertEdge", clientUseServerCertEdge);
        resultMap.put("clientSslFingerEdge", clientSslFingerEdge);
        resultMap.put("serverSslFingerEdge", serverSslFingerEdge);
        resultMap.put("domainServerCertEdge", domainServerCertEdge);
        resultMap.put("sSSLConnectCertEdge", sSSLConnectCertEdge);
        resultMap.put("sSSLConnectDomainEdge", sSSLConnectDomainEdge);
        resultMap.put("domainServerFDomainEdge", domainServerFDomainEdge);

        collector.collect(resultMap);
    }

    /**
     * SNI 归属于锚域名的边
     * sni_bind_fdomain
     *
     * @param pbMap
     * @param
     * @return
     */
    private BaseEdge getDomainServerFDomainEdge(Map<String, Object> pbMap) {
        BaseEdge domainServerFDomainEdge = new BaseEdge();
        String sslDomainAddr = (String) pbMap.get("CH_ServerName");
        if (DomainUtils.isValidDomain(sslDomainAddr)) {
            if (sslDomainAddr.contains(":")) {
                sslDomainAddr = sslDomainAddr.split("\\:")[0];
            }
            domainServerFDomainEdge.setSrcId(sslDomainAddr);
            String fDomainAddr = StringUtil.EMPTY_STRING;
            try {
                fDomainAddr = suffixList.getRegistrableDomain(sslDomainAddr);
            } catch (Exception e) {
                fDomainAddr = DomainUtils.extractRootDomain(sslDomainAddr);
            }
            domainServerFDomainEdge.setDstId("*." + fDomainAddr);
            domainServerFDomainEdge.setSessionCnt(0L);
            return domainServerFDomainEdge;
        }
        return null;
    }

    /**
     * SSL 访问&应用边
     * client_ssl_connect_domain
     * server_ssl_connect_domain
     *
     * @param pbMap
     * @param
     * @return
     */
    private BaseEdge getSslConnectDomainEdge(Map<String, Object> pbMap, String type) {
        String clientIp = (String) pbMap.get("sIp");
        String serverIp = (String) pbMap.get("dIp");
        String sni = (String) pbMap.get("CH_ServerName");

        BaseEdge baseEdge = new BaseEdge();

        if (DomainUtils.isValidDomain(sni) && !sni.equals(serverIp)) {
            if ("client".equals(type)) {
                baseEdge.setSrcId(clientIp);
            } else {
                baseEdge.setSrcId(serverIp);
            }
            if (sni.length() > 200) {
                baseEdge.setDstId(Md5Util.Md5(sni));
            } else {
                baseEdge.setDstId(sni);
            }
            baseEdge.setSessionCnt(0L);
        } else {
            return null;
        }
        return baseEdge;
    }

    /**
     * 客户端使用证书
     * client_use_cert
     * 客户端访问证书
     * client_connect_cert
     *
     * @param pbMap
     * @return
     */
    private ClientUseCertEdge getClientUseCertEdge(Map<String, Object> pbMap, String type) {

        ClientUseCertEdge edge = new ClientUseCertEdge();

        List<String> sCertHashList = (List<String>) pbMap.get("sCertHash");
        List<String> dCertHashList = (List<String>) pbMap.get("dCertHash");
        String sni = (String) pbMap.get("CH_ServerName");

        String clientIp = (String) pbMap.get("sIp");
        String serverIp = (String) pbMap.get("dIp");
        if ("client".equals(type)) {
            // 获取客户端使用证书
            if (CollectionUtils.isEmpty(sCertHashList)) {
                return null;
            } else {
                String certHash = sCertHashList.get(0);
                edge.setSrcId(clientIp);
                edge.setDstId(certHash);
                edge.setSni(sni);
                edge.setSessionCnt(0L);
                // 需要判定域名是否符合域名格式
                // 若SSL：SNI为IP且与服务器IP相同，不记录
                if (DomainUtils.sniValidDomain(sni, serverIp)) {
                    edge.setSni(sni);
                } else {
                    edge.setSni(StrUtil.EMPTY);
                }
            }
        } else if ("server".equals(type)) {
            if (CollectionUtils.isEmpty(dCertHashList)) {
                return null;
            } else {
                String certHash = dCertHashList.get(0);
                edge.setSrcId(clientIp);
                edge.setDstId(certHash);
                edge.setSni(sni);
                edge.setSessionCnt(0L);
                // 需要判定域名是否符合域名格式
                // 若SSL：SNI为IP且与服务器IP相同，不记录
                if (DomainUtils.sniValidDomain(sni, serverIp)) {
                    edge.setSni(sni);
                } else {
                    edge.setSni(StrUtil.EMPTY);
                }
            }
        }

        return edge;
    }

    /**
     * 服务端使用证书 server_use_cert
     *
     * @param pbMap
     * @param
     * @return
     */
    private ServerUseCertEdge getServerUseCertEdge(Map<String, Object> pbMap, String type) {
        ServerUseCertEdge edge = new ServerUseCertEdge();
        List<String> dCertHashList = (List<String>) pbMap.get("dCertHash");
        String clientIp = (String) pbMap.get("sIp");
        String serverIp = (String) pbMap.get("dIp");

        if (CollectionUtils.isEmpty(dCertHashList)) {
            return null;
        } else {
            // 取除叶子节点外的集合
            String sni = (String) pbMap.get("CH_ServerName");
            String certHash = dCertHashList.get(0);
            edge.setSrcId(serverIp);
            edge.setDstId(certHash);
            List<String> subCertListStr = dCertHashList.subList(1, dCertHashList.size());
            edge.setSubCert(subCertListStr.stream().map(String::valueOf).collect(Collectors.joining(",")));
            edge.setSessionCnt(0L);
            if (DomainUtils.sniValidDomain(sni, serverIp)) {
                edge.setSni(sni);
            } else {
                edge.setSni(StrUtil.EMPTY);
            }
        }
        return edge;
    }

    /**
     * 使用指纹关系
     * client_use_sslfinger
     * server_use_sslfinger
     *
     * @param pbMap
     * @param
     * @return
     */
    private BaseEdge getSslFingerEdge(Map<String, Object> pbMap, String type) {
        BaseEdge edge = new BaseEdge();
        if (type.equals("client")) {
            String sIp = (String) pbMap.get("sIp");
            String sSSLFinger = (String) pbMap.get("sSSLFinger");
            if (ObjectUtils.isEmpty(sSSLFinger) || sSSLFinger.equals("0")) {
                return null;
            }
            edge.setSrcId(sIp);
            edge.setDstId(sSSLFinger);
        } else if (type.equals("server")) {
            String dIp = (String) pbMap.get("dIp");
            String dSSLFinger = (String) pbMap.get("dSSLFinger");
            if (ObjectUtils.isEmpty(dSSLFinger) || dSSLFinger.equals("0")) {
                return null;
            }
            edge.setSrcId(dIp);
            edge.setDstId(dSSLFinger);
        }
        edge.setSessionCnt(0L);
        return edge;
    }

    /**
     * SNI关联证书
     * sni_bind
     *
     * @param pbMap
     * @return
     */
    private BaseEdge getDomainCertEdge(Map<String, Object> pbMap) {
        BaseEdge edge = new BaseEdge();
        String sni = (String) pbMap.get("CH_ServerName");
        String dIp = (String) pbMap.get("dIp");
        List<String> dCertHashList = (List<String>) pbMap.get("dCertHash");
        if (CollectionUtils.isEmpty(dCertHashList)) {
            return null;
        }

        if (DomainUtils.isValidDomain(sni) && !sni.equals(dIp)) {
            if (sni.length() > 200) {
                edge.setSrcId(Md5Util.Md5(sni));
            } else {
                edge.setSrcId(sni);
            }
            edge.setDstId(dCertHashList.get(0));
            edge.setSessionCnt(0L);
        } else {
            return null;
        }
        return edge;
    }

    /**
     * 客户端指纹访问服务器证书
     * sslfinger_connect_cert
     *
     * @param pbMap
     * @return
     */
    private SSLFingerConnectCertEdge getSSLFingerConnectCertEdge(Map<String, Object> pbMap) {

        SSLFingerConnectCertEdge edge = new SSLFingerConnectCertEdge();
        String sSSLFinger = (String) pbMap.get("sSSLFinger");
        String dIp = (String) pbMap.get("dIp");

        if (ObjectUtils.isEmpty(sSSLFinger) || sSSLFinger.equals("0")) {
            return null;
        }
        List<String> dCertHashList = (List<String>) pbMap.get("dCertHash");
        if (CollectionUtils.isEmpty(dCertHashList)) {
            return null;
        }

        String sni = (String) pbMap.get("CH_ServerName");
        if (DomainUtils.isValidDomain(sni) || sni.equals(dIp)) {
            edge.setSrcId(sSSLFinger);
            edge.setDstId(dCertHashList.get(0));
            edge.setSni(sni);
            edge.setSessionCnt(0L);
        } else {
            return null;
        }

        return edge;

    }

    /**
     * 客户端指纹访问域名
     * sslfinger_connect_domain
     *
     * @param pbMap
     * @return
     */
    private BaseEdge getSSLFingerConnectDomainEdge(Map<String, Object> pbMap) {
        BaseEdge edge = new BaseEdge();
        String sSSLFinger = (String) pbMap.get("sSSLFinger");
        String dIp = (String) pbMap.get("dIp");
        if (ObjectUtils.isEmpty(sSSLFinger) || sSSLFinger.equals("0")) {
            return null;
        }
        String sni = (String) pbMap.get("CH_ServerName");
        if (DomainUtils.isValidDomain(sni) || sni.equals(dIp)) {
            edge.setSrcId(sSSLFinger);
            if (sni.length() > 200) {
                edge.setDstId(Md5Util.Md5(sni));
            } else {
                edge.setDstId(sni);
            }
            edge.setSessionCnt(0L);
        } else {
            return null;
        }
        return edge;
    }

}
