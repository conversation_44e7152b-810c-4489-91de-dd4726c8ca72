package com.geeksec.nebulaFunction.transfer.row.ssl;

import com.geeksec.nebulaEntity.vertex.CertTagInfo;
import com.geeksec.nebulaEntity.vertex.SSLFingerTagInfo;
import java.util.List;
import java.util.Map;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 * @Description：
 */
public class SslInfoTagRowFlatMap extends RichFlatMapFunction<Map<String,Object>, Row> {

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void flatMap(Map<String, Object> tagMap, Collector<Row> collector) throws Exception {
        SSLFingerTagInfo sSSLFingerTagInfo = (SSLFingerTagInfo) tagMap.get("sSSLFingerInfoTag");
        Row sSSLFingerTagRow = new Row(7);
        if (sSSLFingerTagInfo != null){
            sSSLFingerTagRow.setField(0,"SSL_FINGER_TAG");
            sSSLFingerTagRow.setField(1,sSSLFingerTagInfo.getFingerId());
            sSSLFingerTagRow.setField(2,sSSLFingerTagInfo.getJa3Hash());
            sSSLFingerTagRow.setField(3,sSSLFingerTagInfo.getDesc());
            sSSLFingerTagRow.setField(4,sSSLFingerTagInfo.getType());
            sSSLFingerTagRow.setField(5,sSSLFingerTagInfo.getFirstSeen());
            sSSLFingerTagRow.setField(6,sSSLFingerTagInfo.getLastSeen());
            collector.collect(sSSLFingerTagRow);
        }

        SSLFingerTagInfo dSSLFingerTagInfo = (SSLFingerTagInfo) tagMap.get("dSSLFingerInfoTag");
        Row dSSLFingerTagRow = new Row(7);
        if (sSSLFingerTagInfo != null){
            dSSLFingerTagRow.setField(0,"SSL_FINGER_TAG");
            dSSLFingerTagRow.setField(1,dSSLFingerTagInfo.getFingerId());
            dSSLFingerTagRow.setField(2,dSSLFingerTagInfo.getJa3Hash());
            dSSLFingerTagRow.setField(3,dSSLFingerTagInfo.getDesc());
            dSSLFingerTagRow.setField(4,dSSLFingerTagInfo.getType());
            dSSLFingerTagRow.setField(5,dSSLFingerTagInfo.getFirstSeen());
            dSSLFingerTagRow.setField(6,dSSLFingerTagInfo.getLastSeen());
            collector.collect(dSSLFingerTagRow);
        }
    }
}
