package com.geeksec.nebulaFunction.transfer.row.task;

import com.geeksec.nebulaEntity.vertex.TaskTagInfo;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @Description：
 */
public class TaskInfoTagRowFlatMap extends RichFlatMapFunction<TaskTagInfo, Row> {

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void flatMap(TaskTagInfo taskTagInfo, Collector<Row> collector) throws Exception {
        if(!ObjectUtils.isEmpty(taskTagInfo)){
            Row taskRow = new Row(8);
            taskRow.setField(0,"TASK_TAG");
            taskRow.setField(1,taskTagInfo.getTaskId());
            taskRow.setField(2,taskTagInfo.getBatchId());
            taskRow.setField(3,taskTagInfo.getTaskName());
            taskRow.setField(4,taskTagInfo.getTaskRemark());
            taskRow.setField(5,taskTagInfo.getCreatedTime());
            taskRow.setField(6,taskTagInfo.getState());
            taskRow.setField(7,taskTagInfo.getTaskId() + "_" + taskTagInfo.getTaskName());
            collector.collect(taskRow);
        }
    }
}
