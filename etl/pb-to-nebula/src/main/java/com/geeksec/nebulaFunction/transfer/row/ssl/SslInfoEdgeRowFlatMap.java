package com.geeksec.nebulaFunction.transfer.row.ssl;

import com.geeksec.nebulaEntity.edge.BaseEdge;
import com.geeksec.nebulaEntity.edge.TaskBelongToEdge;
import com.geeksec.nebulaEntity.edge.ssl.ClientUseCertEdge;
import com.geeksec.nebulaEntity.edge.ssl.SSLFingerConnectCertEdge;
import com.geeksec.nebulaEntity.edge.ssl.ServerUseCertEdge;
import java.util.List;
import java.util.Map;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @Description：
 */
public class SslInfoEdgeRowFlatMap extends RichFlatMapFunction<Map<String, Object>, Row> {

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void flatMap(Map<String, Object> edgeMap, Collector<Row> collector) throws Exception {

        // ClientIP ---> DOMAIN: client_ssl_connect_domain
        BaseEdge clientSslConnectDomainEdge = (BaseEdge) edgeMap.get("clientSslConnectDomainEdge");
        if (clientSslConnectDomainEdge != null) {
            Row clientSslConnectDomainEdgeRow = new Row(5);
            clientSslConnectDomainEdgeRow.setField(0, "CLIENT_SSL_CONNECT_DOMAIN_EDGE");
            clientSslConnectDomainEdgeRow.setField(1, clientSslConnectDomainEdge.getSrcId());
            clientSslConnectDomainEdgeRow.setField(2, clientSslConnectDomainEdge.getDstId());
            clientSslConnectDomainEdgeRow.setField(3, 0);
            clientSslConnectDomainEdgeRow.setField(4, clientSslConnectDomainEdge.getSessionCnt());

            collector.collect(clientSslConnectDomainEdgeRow);
        }

        // ServerIP ---> DOMAIN: server_ssl_connect_domain
        BaseEdge serverSslConnectDomainEdge = (BaseEdge) edgeMap.get("serverSslConnectDomainEdge");
        if (serverSslConnectDomainEdge != null) {
            Row serverSslConnectDomainEdgeRow = new Row(5);
            serverSslConnectDomainEdgeRow.setField(0, "SERVER_SSL_CONNECT_DOMAIN_EDGE");
            serverSslConnectDomainEdgeRow.setField(1, serverSslConnectDomainEdge.getSrcId());
            serverSslConnectDomainEdgeRow.setField(2, serverSslConnectDomainEdge.getDstId());
            serverSslConnectDomainEdgeRow.setField(3, 0);
            serverSslConnectDomainEdgeRow.setField(4, serverSslConnectDomainEdge.getSessionCnt());
            collector.collect(serverSslConnectDomainEdgeRow);
        }

        // ClientIP ---> Client_CERT :client_use_cert
        ClientUseCertEdge clientUseCertEdge = (ClientUseCertEdge) edgeMap.get("clientUseCertEdge");
        if (clientUseCertEdge != null) {
            Row clientUseCertEdgeRow = new Row(6);
            clientUseCertEdgeRow.setField(0, "CLIENT_USE_CERT_EDGE");
            clientUseCertEdgeRow.setField(1, clientUseCertEdge.getSrcId());
            clientUseCertEdgeRow.setField(2, clientUseCertEdge.getDstId());
            clientUseCertEdgeRow.setField(3, 0);
            clientUseCertEdgeRow.setField(4, clientUseCertEdge.getSessionCnt());
            clientUseCertEdgeRow.setField(5, clientUseCertEdge.getSni());

            collector.collect(clientUseCertEdgeRow);
        }

        // ServerIP ---> Server_CERT: server_use_cert
        ServerUseCertEdge serverUseCertEdge = (ServerUseCertEdge) edgeMap.get("serverUseCertEdge");
        if (serverUseCertEdge != null) {
            Row serverUseCertEdgeRow = new Row(9);
            serverUseCertEdgeRow.setField(0, "SERVER_USE_CERT_EDGE");
            serverUseCertEdgeRow.setField(1, serverUseCertEdge.getSrcId());
            serverUseCertEdgeRow.setField(2, serverUseCertEdge.getDstId());
            serverUseCertEdgeRow.setField(3, 0);
            serverUseCertEdgeRow.setField(4, serverUseCertEdge.getSessionCnt());
            serverUseCertEdgeRow.setField(5, serverUseCertEdge.getSni());
            serverUseCertEdgeRow.setField(6, serverUseCertEdge.getSubCert());

            collector.collect(serverUseCertEdgeRow);
        }

        // ClientIP ---> Server_CERT: client_connect_cert
        ClientUseCertEdge clientUseServerCertEdge = (ClientUseCertEdge) edgeMap.get("clientUseServerCertEdge");
        if (clientUseServerCertEdge != null) {
            Row clientConnectCertRow = new Row(6);
            clientConnectCertRow.setField(0, "CLIENT_CONNECT_CERT_EDGE");
            clientConnectCertRow.setField(1, clientUseServerCertEdge.getSrcId());
            clientConnectCertRow.setField(2, clientUseServerCertEdge.getDstId());
            clientConnectCertRow.setField(3, 0);
            clientConnectCertRow.setField(4, clientUseServerCertEdge.getSessionCnt());
            clientConnectCertRow.setField(5, clientUseServerCertEdge.getSni());
            collector.collect(clientConnectCertRow);
        }

        // ClientIP ---> Client_SSLFINGER :client_use_sslfinger
        BaseEdge clientSslFingerEdge = (BaseEdge) edgeMap.get("clientSslFingerEdge");
        if (clientSslFingerEdge != null) {
            Row clientSslFingerEdgeRow = new Row(5);
            clientSslFingerEdgeRow.setField(0, "CLIENT_USE_SSLFINGER_EDGE");
            clientSslFingerEdgeRow.setField(1, clientSslFingerEdge.getSrcId());
            clientSslFingerEdgeRow.setField(2, clientSslFingerEdge.getDstId());
            clientSslFingerEdgeRow.setField(3, 0);
            clientSslFingerEdgeRow.setField(4, clientSslFingerEdge.getSessionCnt());

            collector.collect(clientSslFingerEdgeRow);
        }

        // ServerIP ---> Server_SSLFINGER :server_use_sslfinger
        BaseEdge serverSslFingerEdge = (BaseEdge) edgeMap.get("serverSslFingerEdge");
        if (serverSslFingerEdge != null) {
            Row serverSslFingerEdgeRow = new Row(5);
            serverSslFingerEdgeRow.setField(0, "SERVER_USE_SSLFINGER_EDGE");
            serverSslFingerEdgeRow.setField(1, serverSslFingerEdge.getSrcId());
            serverSslFingerEdgeRow.setField(2, serverSslFingerEdge.getDstId());
            serverSslFingerEdgeRow.setField(3, 0);
            serverSslFingerEdgeRow.setField(4, serverSslFingerEdge.getSessionCnt());

            collector.collect(serverSslFingerEdgeRow);
        }

        // DOMAIN ---> Server_CERT: sni_bind
        BaseEdge domainServerCertEdge = (BaseEdge) edgeMap.get("domainServerCertEdge");
        if (domainServerCertEdge != null) {
            Row domainServerCertEdgeRow = new Row(5);
            domainServerCertEdgeRow.setField(0, "SNI_BIND_EDGE");
            domainServerCertEdgeRow.setField(1, domainServerCertEdge.getSrcId());
            domainServerCertEdgeRow.setField(2, domainServerCertEdge.getDstId());
            domainServerCertEdgeRow.setField(3, 0);
            domainServerCertEdgeRow.setField(4, domainServerCertEdge.getSessionCnt());

            collector.collect(domainServerCertEdgeRow);
        }

        // Client_SSLFINGER --> DOMAIN : sslfinger_connect_domain
        BaseEdge sSSLConnectDomainEdge = (BaseEdge) edgeMap.get("sSSLConnectDomainEdge");
        if (sSSLConnectDomainEdge != null) {
            Row sSSLConnectDomainEdgeRow = new Row(5);
            sSSLConnectDomainEdgeRow.setField(0, "SSLFINGER_CONNECT_DOMAIN_EDGE");
            sSSLConnectDomainEdgeRow.setField(1, sSSLConnectDomainEdge.getSrcId());
            sSSLConnectDomainEdgeRow.setField(2, sSSLConnectDomainEdge.getDstId());
            sSSLConnectDomainEdgeRow.setField(3, 0);
            sSSLConnectDomainEdgeRow.setField(4, sSSLConnectDomainEdge.getSessionCnt());
            collector.collect(sSSLConnectDomainEdgeRow);
        }


        // Client_SSLFINGER --> Server_CERT: sslfinger_connect_cert
        SSLFingerConnectCertEdge sSSLConnectCertEdge = (SSLFingerConnectCertEdge) edgeMap.get("sSSLConnectCertEdge");
        if (sSSLConnectCertEdge != null) {
            Row sSSLConnectCertEdgeRow = new Row(6);
            sSSLConnectCertEdgeRow.setField(0, "SSLFINGER_CONNECT_CERT_EDGE");
            sSSLConnectCertEdgeRow.setField(1, sSSLConnectCertEdge.getSrcId());
            sSSLConnectCertEdgeRow.setField(2, sSSLConnectCertEdge.getDstId());
            sSSLConnectCertEdgeRow.setField(3, 0);
            sSSLConnectCertEdgeRow.setField(4, sSSLConnectCertEdge.getSessionCnt());
            sSSLConnectCertEdgeRow.setField(5, sSSLConnectCertEdge.getSni());


            collector.collect(sSSLConnectCertEdgeRow);
        }

        BaseEdge domainServerFDomainEdge = (BaseEdge) edgeMap.get("domainServerFDomainEdge");
        if (domainServerFDomainEdge != null) {
            Row domainServerFDomainEdgeRow = new Row(4);
            // 无通用属性
            domainServerFDomainEdgeRow.setField(0, "SNI_FDOMAIN_EDGE");
            domainServerFDomainEdgeRow.setField(1, domainServerFDomainEdge.getSrcId());
            domainServerFDomainEdgeRow.setField(2, domainServerFDomainEdge.getDstId());
            domainServerFDomainEdgeRow.setField(3, 0);
            collector.collect(domainServerFDomainEdgeRow);
        }

    }
}
