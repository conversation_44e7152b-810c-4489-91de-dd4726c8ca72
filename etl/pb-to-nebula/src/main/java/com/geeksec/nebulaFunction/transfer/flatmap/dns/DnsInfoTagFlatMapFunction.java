package com.geeksec.nebulaFunction.transfer.flatmap.dns;

import com.geeksec.common.utils.*;
import com.geeksec.nebulaEntity.vertex.DomainTagInfo;
import com.geeksec.nebulaEntity.vertex.FDomainTagInfo;
import com.geeksec.nebulaEntity.vertex.IpTagInfo;
import com.maxmind.geoip2.model.CityResponse;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixList;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixListFactory;
import java.util.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @Description：
 */
public class DnsInfoTagFlatMapFunction extends RichFlatMapFunction<Map<String, Object>, Map<String, Object>> {

    private final static Logger logger = LoggerFactory.getLogger(DnsInfoTagFlatMapFunction.class);
    private static PublicSuffixListFactory factory = null;
    private static PublicSuffixList suffixList = null;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        factory = new PublicSuffixListFactory();
        Properties properties = factory.getDefaults();
        properties.setProperty(PublicSuffixListFactory.PROPERTY_LIST_FILE, "/effective_tld_names.dat");
        suffixList = factory.build();
    }

    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<Map<String, Object>> collector) throws Exception {

        // 1.获取DNS信息中的域名信息
        List<DomainTagInfo> domainTagInfoList = getDomainTagList(pbMap);

        // 2.获取DNS信息中的锚域名信息
        List<FDomainTagInfo> fDomainInfoTagList = new ArrayList<>();
        if (!domainTagInfoList.isEmpty()) {
            for (DomainTagInfo domainTagInfo : domainTagInfoList) {
                String fDomainAddr = StringUtil.EMPTY_STRING;
                try {
                    fDomainAddr = "*." + suffixList.getRegistrableDomain(domainTagInfo.getDomainAddr());
                } catch (Exception e) {
                    logger.warn("获取锚域名失败,domainAddr--->{},error--->", domainTagInfo.getDomainAddr(), e);
                }
                if (!StringUtil.isNullOrEmpty(fDomainAddr)) {
                    FDomainTagInfo fDomainTagInfo = new FDomainTagInfo();
                    fDomainTagInfo.setDomainAddr(fDomainAddr);
                    fDomainTagInfo.setFirstSeen((Integer) pbMap.getOrDefault("StartTime", 0));
                    fDomainTagInfo.setFirstSeen((Integer) pbMap.getOrDefault("StartTime", 0));
                    fDomainInfoTagList.add(fDomainTagInfo);
                }
            }
        }

        // 3.获取DNS信息中IP节点信息
        List<IpTagInfo> ipTagInfoList = getIpTagInfo(pbMap);

        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("domainTagList", domainTagInfoList);
        resultMap.put("fDomainTagList", fDomainInfoTagList);
        resultMap.put("ipTagInfoList", ipTagInfoList);
        collector.collect(resultMap);
    }

    /**
     * 抽取DNS中所有的域名信息(不计平均流量)
     *
     * @param pbMap
     * @return
     */
    private List<DomainTagInfo> getDomainTagList(Map<String, Object> pbMap) {

        List<DomainTagInfo> resultList = new ArrayList<>();

        String Hkey = (String) pbMap.get("Hkey");

        // 1.Domain字段
        DomainTagInfo basicDomainTagInfo = new DomainTagInfo();
        String basicDomainAddr = (String) pbMap.get("Domain");
        if (DomainUtils.isValidDomain(basicDomainAddr)) {
            basicDomainTagInfo.setDomainAddr((String) pbMap.get("Domain"));
            basicDomainTagInfo.setAlexaRank(ProNameForMysql.getInstance().getDomainAlexaRank(basicDomainAddr));
            basicDomainTagInfo.setBlackList(0);
            basicDomainTagInfo.setWhiteList(0);
            basicDomainTagInfo.setFirstSeen((Integer) pbMap.get("StartTime"));
            basicDomainTagInfo.setLastSeen((Integer) pbMap.get("StartTime"));
            basicDomainTagInfo.setRemark(StringUtil.EMPTY_STRING);
            basicDomainTagInfo.setWhois(ProNameForMysql.getInstance().getDomainWhois(basicDomainAddr));
            basicDomainTagInfo.setTaskId(Integer.valueOf(Hkey.split("_")[1]));
            resultList.add(basicDomainTagInfo);
        }

        // 2.CNAME 字段
        Integer ansNum = (Integer) pbMap.get("Ans");
        if (ansNum != 0) {
            List<Map<String, Object>> answerMapList = (List<Map<String, Object>>) pbMap.get("Answer");
            for (Map<String, Object> answerMap : answerMapList) {
                Integer type = (Integer) answerMap.get("type");
                DomainTagInfo cnameDomainTagInfo = new DomainTagInfo();
                String domainAddr = StringUtils.EMPTY;
                // type 为 5时解析出CNAME
                if (type == 5) {
                    domainAddr = (String) answerMap.get("value");
                } else if (type == 1 || type == 28) {
                    // 对于解析完成后的结果，怕会有遗漏，在name字段获取域名的信息
                    domainAddr = (String) answerMap.get("name");
                }
                if (DomainUtils.isValidDomain(domainAddr)) {
                    cnameDomainTagInfo.setDomainAddr(domainAddr);
                    cnameDomainTagInfo.setAlexaRank(ProNameForMysql.getInstance().getDomainAlexaRank(domainAddr));
                    cnameDomainTagInfo.setBlackList(0);
                    cnameDomainTagInfo.setWhiteList(0);
                    cnameDomainTagInfo.setFirstSeen((Integer) pbMap.get("StartTime"));
                    cnameDomainTagInfo.setLastSeen((Integer) pbMap.get("StartTime"));
                    cnameDomainTagInfo.setRemark(StringUtil.EMPTY_STRING);
                    cnameDomainTagInfo.setWhois(ProNameForMysql.getInstance().getDomainWhois(basicDomainAddr));
                    cnameDomainTagInfo.setTaskId(Integer.valueOf(Hkey.split("_")[1]));
                    resultList.add(cnameDomainTagInfo);
                }
            }
        }

        for (DomainTagInfo tagInfo : resultList) {
            String domainAddr = tagInfo.getDomainAddr();
            if (domainAddr.length() > 200) {
                tagInfo.setDomainId(Md5Util.Md5(domainAddr));
            } else {
                tagInfo.setDomainId(domainAddr);
            }
        }

        return resultList;
    }

    /**
     * 抽取DNS元数据中所有IP节点的信息 (SIP + DIP（DNS解析服务器IP） + DomainIp（解析后服务器IP）)
     *
     * @param pbMap
     * @return
     */
    private List<IpTagInfo> getIpTagInfo(Map<String, Object> pbMap) {
        List<IpTagInfo> ipTagInfoList = new ArrayList<>();

        String Hkey = (String) pbMap.get("Hkey");
        //1.ClientIp
        IpTagInfo clientIpTagInfo = new IpTagInfo();
        String sIp = (String) pbMap.get("sIp");
        if (IpNetUtils.isValidIPV4(sIp)) {
            clientIpTagInfo.setVersion("V4");
            // 判断是否为内网地址
            if (IpNetUtils.judgeInternal(sIp)) {
                clientIpTagInfo.setIpKey(Hkey.split("_")[1] + "-" + sIp);
            } else {
                clientIpTagInfo.setIpKey(sIp);
            }
        } else {
            // IPV6
            clientIpTagInfo.setVersion("V6");
            clientIpTagInfo.setIpKey(sIp);
        }
        clientIpTagInfo.setIpAddr(sIp);
        clientIpTagInfo.setFirstSeen((Integer) pbMap.get("StartTime"));
        clientIpTagInfo.setLastSeen((Integer) pbMap.get("StartTime"));
        clientIpTagInfo.setTimes(1L);
        CityResponse sIpRes = IP2Addr.getInstance().getAddrInfo(sIp);
        if (!ObjectUtils.isEmpty(sIpRes)) {
            clientIpTagInfo.setCountry(IP2Addr.getInstance().getCountry(sIpRes));
            clientIpTagInfo.setCity(IP2Addr.getInstance().getCity(sIpRes));
        } else {
            clientIpTagInfo.setCountry(StringUtil.EMPTY_STRING);
            clientIpTagInfo.setCity(StringUtil.EMPTY_STRING);
        }

        clientIpTagInfo.setWhiteList(0);
        clientIpTagInfo.setBlackList(0);
        clientIpTagInfo.setRemark(StringUtil.EMPTY_STRING);
        clientIpTagInfo.setBytes(0L);
        clientIpTagInfo.setPackets(0);
        clientIpTagInfo.setSendBytes(0L);
        clientIpTagInfo.setRecvBytes(0L);
        clientIpTagInfo.setAverageBps(0L);
        clientIpTagInfo.setTaskId(Integer.valueOf(Hkey.split("_")[1]));
        ipTagInfoList.add(clientIpTagInfo);

        //2.ServerIp
        IpTagInfo serverIpTagInfo = new IpTagInfo();
        String dIp = (String) pbMap.get("dIp");
        if (IpNetUtils.isValidIPV4(dIp)) {
            serverIpTagInfo.setVersion("V4");
            // 判断是否为内网地址
            if (IpNetUtils.judgeInternal(dIp)) {
                serverIpTagInfo.setIpKey(Hkey.split("_")[1] + "-" + dIp);
            } else {
                serverIpTagInfo.setIpKey(dIp);
            }
        } else {
            // IPV6
            serverIpTagInfo.setVersion("V6");
            serverIpTagInfo.setIpKey(dIp);
        }
        serverIpTagInfo.setIpAddr(dIp);
        serverIpTagInfo.setFirstSeen((Integer) pbMap.get("StartTime"));
        serverIpTagInfo.setLastSeen((Integer) pbMap.get("StartTime"));
        serverIpTagInfo.setTimes(1L);
        CityResponse dIpRes = IP2Addr.getInstance().getAddrInfo(dIp);
        if (!ObjectUtils.isEmpty(dIpRes)) {
            serverIpTagInfo.setCountry(IP2Addr.getInstance().getCountry(dIpRes));
            serverIpTagInfo.setCity(IP2Addr.getInstance().getCity(dIpRes));
        } else {
            serverIpTagInfo.setCountry(StringUtil.EMPTY_STRING);
            serverIpTagInfo.setCity(StringUtil.EMPTY_STRING);
        }

        serverIpTagInfo.setWhiteList(0);
        serverIpTagInfo.setBlackList(0);
        serverIpTagInfo.setRemark(StringUtil.EMPTY_STRING);
        serverIpTagInfo.setBytes(0L);
        serverIpTagInfo.setPackets(0);
        serverIpTagInfo.setSendBytes(0L);
        serverIpTagInfo.setRecvBytes(0L);
        serverIpTagInfo.setAverageBps(0L);
        serverIpTagInfo.setTaskId(Integer.valueOf(Hkey.split("_")[1]));
        ipTagInfoList.add(serverIpTagInfo);

        // DNS解析出的IP
        String domainIpStr = (String) pbMap.get("DomainIp");
        try {
            if (!StringUtil.isNullOrEmpty(domainIpStr)) {
                List<String> domainIpList = new ArrayList<>();
                if (domainIpStr.contains("|")) {
                    domainIpList = Arrays.asList(domainIpStr.split("\\|").clone());
                } else {
                    domainIpList.add(domainIpStr);
                }
                if (!domainIpList.isEmpty()) {
                    for (String domainIp : domainIpList) {
                        IpTagInfo domainIpTagInfo = new IpTagInfo();
                        if (!IpUtils.isIpv4Str(domainIp) && !IpUtils.isIpv6Str(domainIp)) {
                            continue;
                        }
                        if (IpNetUtils.isValidIPV4(domainIp)) {
                            domainIpTagInfo.setVersion("V4");
                            // 判断是否为内网地址
                            if (IpNetUtils.judgeInternal(domainIp)) {
                                domainIpTagInfo.setIpKey(Hkey.split("_")[1] + "-" + domainIp);
                            } else {
                                domainIpTagInfo.setIpKey(domainIp);
                            }
                        } else {
                            // IPV6
                            domainIpTagInfo.setVersion("V6");
                            domainIpTagInfo.setIpKey(dIp);
                        }
                        domainIpTagInfo.setIpAddr(domainIp);
                        domainIpTagInfo.setFirstSeen((Integer) pbMap.get("StartTime"));
                        domainIpTagInfo.setLastSeen((Integer) pbMap.get("StartTime"));
                        domainIpTagInfo.setTimes(1L);
                        domainIpTagInfo.setWhiteList(0);
                        domainIpTagInfo.setBlackList(0);
                        CityResponse domainIpRes = IP2Addr.getInstance().getAddrInfo(domainIp);
                        if (!ObjectUtils.isEmpty(domainIpRes)) {
                            domainIpTagInfo.setCountry(IP2Addr.getInstance().getCountry(domainIpRes));
                            domainIpTagInfo.setCity(IP2Addr.getInstance().getCity(domainIpRes));
                        } else {
                            domainIpTagInfo.setCountry(StringUtil.EMPTY_STRING);
                            domainIpTagInfo.setCity(StringUtil.EMPTY_STRING);
                        }
                        domainIpTagInfo.setRemark(StringUtil.EMPTY_STRING);
                        domainIpTagInfo.setBytes(0L);
                        domainIpTagInfo.setPackets(0);
                        domainIpTagInfo.setSendBytes(0L);
                        domainIpTagInfo.setRecvBytes(0L);
                        domainIpTagInfo.setAverageBps(0L);
                        domainIpTagInfo.setTaskId(Integer.valueOf(Hkey.split("_")[1]));
                        ipTagInfoList.add(domainIpTagInfo);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("get domain list from dns info failed ,domainIpStr : {},error:", domainIpStr, e);
        }

        return ipTagInfoList;
    }

}
