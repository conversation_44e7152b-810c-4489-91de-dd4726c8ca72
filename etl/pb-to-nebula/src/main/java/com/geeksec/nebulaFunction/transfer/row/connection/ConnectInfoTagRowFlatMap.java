package com.geeksec.nebulaFunction.transfer.row.connection;

import com.geeksec.common.utils.Md5Util;
import com.geeksec.nebulaEntity.vertex.*;
import java.util.List;
import java.util.Map;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 * @Description：
 */
public class ConnectInfoTagRowFlatMap extends RichFlatMapFunction<Map<String, Object>, Row> {

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    /**
     * 开始生成多个Tag Row
     *
     * @param tagMap
     * @param collector
     * @throws Exception
     */
    @Override
    public void flatMap(Map<String, Object> tagMap, Collector<Row> collector) throws Exception {

        // clientIp Tag
        IpTagInfo clientIpTagInfo = (IpTagInfo) tagMap.get("clientIpTag");
        Row clientIpTagRow = new Row(19);
        clientIpTagRow.setField(0, "IP_TAG");
        clientIpTagRow.setField(1, clientIpTagInfo.getIpAddr());// vid
        clientIpTagRow.setField(2, clientIpTagInfo.getFirstSeen());
        clientIpTagRow.setField(3, clientIpTagInfo.getLastSeen());
        clientIpTagRow.setField(4, clientIpTagInfo.getTimes());
        clientIpTagRow.setField(5, clientIpTagInfo.getIpAddr());
        clientIpTagRow.setField(6, clientIpTagInfo.getIpKey());
        clientIpTagRow.setField(7, clientIpTagInfo.getVersion());
        clientIpTagRow.setField(8, clientIpTagInfo.getCity());
        clientIpTagRow.setField(9, clientIpTagInfo.getCountry());
        clientIpTagRow.setField(10, clientIpTagInfo.getBytes());
        clientIpTagRow.setField(11, clientIpTagInfo.getPackets());
        clientIpTagRow.setField(12, clientIpTagInfo.getBlackList());
        clientIpTagRow.setField(13, clientIpTagInfo.getWhiteList());
        clientIpTagRow.setField(14, clientIpTagInfo.getRemark());
        clientIpTagRow.setField(15, clientIpTagInfo.getSendBytes());
        clientIpTagRow.setField(16, clientIpTagInfo.getRecvBytes());
        clientIpTagRow.setField(17, clientIpTagInfo.getAverageBps());
        clientIpTagRow.setField(18, clientIpTagInfo.getTaskId());
        collector.collect(clientIpTagRow);

        // serverIp Tag
        IpTagInfo serverIpTagInfo = (IpTagInfo) tagMap.get("serverIpTag");
        Row serverIpTagRow = new Row(19);
        serverIpTagRow.setField(0, "IP_TAG");
        serverIpTagRow.setField(1, serverIpTagInfo.getIpAddr());// vid
        serverIpTagRow.setField(2, serverIpTagInfo.getFirstSeen());
        serverIpTagRow.setField(3, serverIpTagInfo.getLastSeen());
        serverIpTagRow.setField(4, serverIpTagInfo.getTimes());
        serverIpTagRow.setField(5, serverIpTagInfo.getIpAddr());
        serverIpTagRow.setField(6, serverIpTagInfo.getIpKey());
        serverIpTagRow.setField(7, serverIpTagInfo.getVersion());
        serverIpTagRow.setField(8, serverIpTagInfo.getCity());
        serverIpTagRow.setField(9, serverIpTagInfo.getCountry());
        serverIpTagRow.setField(10, serverIpTagInfo.getBytes());
        serverIpTagRow.setField(11, serverIpTagInfo.getPackets());
        serverIpTagRow.setField(12, serverIpTagInfo.getBlackList());
        serverIpTagRow.setField(13, serverIpTagInfo.getWhiteList());
        serverIpTagRow.setField(14, serverIpTagInfo.getRemark());
        serverIpTagRow.setField(15, serverIpTagInfo.getSendBytes());
        serverIpTagRow.setField(16, serverIpTagInfo.getRecvBytes());
        serverIpTagRow.setField(17, serverIpTagInfo.getAverageBps());
        serverIpTagRow.setField(18, serverIpTagInfo.getTaskId());
        collector.collect(serverIpTagRow);

        // clientMac Tag
        MacTagInfo clientMacTagInfo = (MacTagInfo) tagMap.get("clientMacTag");
        Row clientMacRow = new Row(12);
        clientMacRow.setField(0, "MAC_TAG");
        clientMacRow.setField(1, clientMacTagInfo.getMac()); // vid
        clientMacRow.setField(2, clientMacTagInfo.getTimes());
        clientMacRow.setField(3, clientMacTagInfo.getBytes());
        clientMacRow.setField(4, clientMacTagInfo.getAveragePbs());
        clientMacRow.setField(5, clientMacTagInfo.getVlanInfo());
        clientMacRow.setField(6, clientMacTagInfo.getFirstSeen());
        clientMacRow.setField(7, clientMacTagInfo.getLastSeen());
        clientMacRow.setField(8, clientMacTagInfo.getBlackList());
        clientMacRow.setField(9, clientMacTagInfo.getWhiteList());
        clientMacRow.setField(10, clientMacTagInfo.getRemark());
        clientMacRow.setField(11, clientIpTagInfo.getTaskId());
        collector.collect(clientMacRow);

        // serverMac Tag
        MacTagInfo serverMacTagInfo = (MacTagInfo) tagMap.get("serverMacTag");
        if (serverMacTagInfo != null) {
            Row serverMacTagRow = new Row(12);
            serverMacTagRow.setField(0, "MAC_TAG");
            serverMacTagRow.setField(1, serverMacTagInfo.getMac()); // vid
            serverMacTagRow.setField(2, serverMacTagInfo.getTimes());
            serverMacTagRow.setField(3, serverMacTagInfo.getBytes());
            serverMacTagRow.setField(4, serverMacTagInfo.getAveragePbs());
            serverMacTagRow.setField(5, serverMacTagInfo.getVlanInfo());
            serverMacTagRow.setField(6, serverMacTagInfo.getFirstSeen());
            serverMacTagRow.setField(7, serverMacTagInfo.getLastSeen());
            serverMacTagRow.setField(8, serverMacTagInfo.getBlackList());
            serverMacTagRow.setField(9, serverMacTagInfo.getWhiteList());
            serverMacTagRow.setField(10, serverMacTagInfo.getRemark());
            serverMacTagRow.setField(11, serverMacTagInfo.getTaskId());
            collector.collect(serverMacTagRow);
        }

        // AppService Tag
        AppTagInfo appTagInfo = (AppTagInfo) tagMap.get("appTag");
        if (appTagInfo != null) {
            Row appTagRow = new Row(8);
            appTagRow.setField(0, "APP_TAG");
            appTagRow.setField(1, appTagInfo.getServiceKey()); // vid
            appTagRow.setField(2, appTagInfo.getIpAddr());
            appTagRow.setField(3, appTagInfo.getAppName());
            appTagRow.setField(4, appTagInfo.getDPort());
            appTagRow.setField(5, appTagInfo.getIPPro());
            appTagRow.setField(6, appTagInfo.getFirstSeen());
            appTagRow.setField(7, appTagInfo.getLastSeen());

            collector.collect(appTagRow);
        }
        // App Tag
        ApkTagInfo apkTagInfo = (ApkTagInfo) tagMap.get("apkTag");
        if (apkTagInfo != null) {
            Row apkTagRow = new Row(6);
            apkTagRow.setField(0, "APK_TAG");
            apkTagRow.setField(1, apkTagInfo.getAppVid()); // vid
            apkTagRow.setField(2, apkTagInfo.getAppName());
            apkTagRow.setField(3, apkTagInfo.getAppVersion());
            apkTagRow.setField(4, apkTagInfo.getFirstSeen());
            apkTagRow.setField(5, apkTagInfo.getLastSeen());
            collector.collect(apkTagRow);
        }

        // 域名Domain Tag
        List<DomainTagInfo> domainTagInfoList = (List<DomainTagInfo>) tagMap.get("domainTagList");
        if (!domainTagInfoList.isEmpty()) {
            for (DomainTagInfo domainTagInfo : domainTagInfoList) {
                Row domainTagRow = new Row(13);
                domainTagRow.setField(0, "DOMAIN_TAG");
                domainTagRow.setField(1, domainTagInfo.getDomainAddr()); //vid
                domainTagRow.setField(2, domainTagInfo.getFirstSeen());
                domainTagRow.setField(3, domainTagInfo.getLastSeen());
                domainTagRow.setField(4, domainTagInfo.getBlackList());
                domainTagRow.setField(5, domainTagInfo.getWhiteList());
                domainTagRow.setField(6, domainTagInfo.getRemark());
                domainTagRow.setField(7, domainTagInfo.getAlexaRank());
                domainTagRow.setField(8, domainTagInfo.getBytes());
                domainTagRow.setField(9, domainTagInfo.getAveragePbs());
                domainTagRow.setField(10, domainTagInfo.getWhois());
                domainTagRow.setField(11, domainTagInfo.getTaskId());
                domainTagRow.setField(12, domainTagInfo.getDomainId());
                collector.collect(domainTagRow);
            }
        }

        // 锚域名 FDOMAIN Tag
        List<FDomainTagInfo> fDomainInfoTagList = (List<FDomainTagInfo>) tagMap.get("fDomainTagList");
        if (!fDomainInfoTagList.isEmpty()) {
            for (FDomainTagInfo fDomainTagInfo : fDomainInfoTagList) {
                Row fDomainTagRow = new Row(5);
                fDomainTagRow.setField(0, "FDOMAIN_TAG");
                String fDomain = fDomainTagInfo.getDomainAddr();
                fDomainTagRow.setField(1, fDomain);
                if (fDomain.length() > 200) {
                    fDomainTagRow.setField(2, Md5Util.Md5(fDomain));
                } else {
                    fDomainTagRow.setField(2, fDomain);
                }
                fDomainTagRow.setField(3, fDomainTagInfo.getFirstSeen());
                fDomainTagRow.setField(4, fDomainTagInfo.getLastSeen());
                collector.collect(fDomainTagRow);
            }
        }

        // 证书 CERT TAG
        List<CertTagInfo> certTagInfoList = (List<CertTagInfo>) tagMap.get("certTagList");
        if (!certTagInfoList.isEmpty()) {
            for (CertTagInfo certTagInfo : certTagInfoList) {
                Row certTagRow = new Row(7);
                certTagRow.setField(0, "CERT_TAG");
                certTagRow.setField(1, certTagInfo.getCertId());
                certTagRow.setField(2, certTagInfo.getFirstSeen());
                certTagRow.setField(3, certTagInfo.getLastSeen());
                certTagRow.setField(4, certTagInfo.getBlackList());
                certTagRow.setField(5, certTagInfo.getWhiteList());
                certTagRow.setField(6, certTagInfo.getRemark());
                collector.collect(certTagRow);
            }
        }
    }

}
