package com.geeksec.nebulaFunction.transfer.row.dns;

import com.geeksec.nebulaEntity.edge.BaseEdge;
import com.geeksec.nebulaEntity.edge.dns.DnsParseToEdge;
import com.geeksec.nebulaEntity.edge.dns.DnsQueryEdge;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.geeksec.nebulaFunction.transfer.row.connection.ConnectInfoEdgeRowFlatMap.baseEdge2Row;

/**
 * <AUTHOR>
 * @Description：
 */
public class DnsInfoEdgeRowFlatMap extends RichFlatMapFunction<Map<String, Object>, Row> {
    private Map<String, String> BASE_EDGE_OBJECT_MAPPING = new HashMap<>();

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        BASE_EDGE_OBJECT_MAPPING.put("ip2OrgEdgeList","IP_BELONG_ORG_EDGE");
        BASE_EDGE_OBJECT_MAPPING.put("domain2OrgEdgeList","DOMAIN_BELONG_ORG_EDGE");
    }

    @Override
    public void flatMap(Map<String, Object> edgeMap, Collector<Row> collector) throws Exception {
        // 1.ClientIp客户端IP ---> Domain 域名 client_query_domain
        DnsQueryEdge clientQueryDomainEdge = (DnsQueryEdge) edgeMap.get("clientQueryDomainEdge");
        if (clientQueryDomainEdge != null) {
            Row clientQueryDomainRow = new Row(7);
            clientQueryDomainRow.setField(0, "CLIENT_QUERY_DOMAIN_EDGE");
            clientQueryDomainRow.setField(1, clientQueryDomainEdge.getSrcId());
            clientQueryDomainRow.setField(2, clientQueryDomainEdge.getDstId());
            clientQueryDomainRow.setField(3, 0);
            clientQueryDomainRow.setField(4, clientQueryDomainEdge.getSessionCnt());
            clientQueryDomainRow.setField(5, clientQueryDomainEdge.getQueryType());
            clientQueryDomainRow.setField(6, clientQueryDomainEdge.getAnswerType());
            collector.collect(clientQueryDomainRow);
        }

        // 2.ClientIP 客户端 ---> DNSServerIP DNS服务器IP client_query_dns_server
        DnsQueryEdge clientQueryDnsServerEdge = (DnsQueryEdge) edgeMap.get("clientQueryDnsServerEdge");
        if (clientQueryDnsServerEdge != null) {
            Row clientQueryDnsServerRow = new Row(7);
            clientQueryDnsServerRow.setField(0, "CLIENT_QUERY_DNS_SERVER_EDGE");
            clientQueryDnsServerRow.setField(1, clientQueryDnsServerEdge.getSrcId());
            clientQueryDnsServerRow.setField(2, clientQueryDnsServerEdge.getDstId());
            clientQueryDnsServerRow.setField(3, 0);
            clientQueryDnsServerRow.setField(4, clientQueryDnsServerEdge.getSessionCnt());
            clientQueryDnsServerRow.setField(5, clientQueryDnsServerEdge.getQueryType());
            clientQueryDnsServerRow.setField(6, clientQueryDnsServerEdge.getAnswerType());
            collector.collect(clientQueryDnsServerRow);
        }

        // 3.DNSServerIP DNS服务器IP ---> Domain dns_server_domain
        DnsQueryEdge dnsServerDomainEdge = (DnsQueryEdge) edgeMap.get("dnsServerDomainEdge");
        if (dnsServerDomainEdge != null) {
            Row dnsServerDomainRow = new Row(7);
            dnsServerDomainRow.setField(0, "DNS_SERVER_DOMAIN_EDGE");
            dnsServerDomainRow.setField(1, dnsServerDomainEdge.getSrcId());
            dnsServerDomainRow.setField(2, dnsServerDomainEdge.getDstId());
            dnsServerDomainRow.setField(3, 0);
            dnsServerDomainRow.setField(4, dnsServerDomainEdge.getSessionCnt());
            dnsServerDomainRow.setField(5, dnsServerDomainEdge.getQueryType());
            dnsServerDomainRow.setField(6, dnsServerDomainEdge.getAnswerType());
            collector.collect(dnsServerDomainRow);
        }

        // 4.Domain ---> ServerIP ---> parse_to
        List<DnsParseToEdge> dnsParseToEdgeList = (List<DnsParseToEdge>) edgeMap.get("dnsParseToEdgeList");
        if (!CollectionUtils.isEmpty(dnsParseToEdgeList)) {
            for (DnsParseToEdge edge : dnsParseToEdgeList) {
                Row dnsParseToEdgeRow = new Row(9);
                dnsParseToEdgeRow.setField(0, "DNS_PARSE_TO_EDGE");
                dnsParseToEdgeRow.setField(1, edge.getSrcId());
                dnsParseToEdgeRow.setField(2, edge.getDstId());
                dnsParseToEdgeRow.setField(3, 0);
                dnsParseToEdgeRow.setField(4, edge.getSessionCnt());
                dnsParseToEdgeRow.setField(5, edge.getDnsServer());
                dnsParseToEdgeRow.setField(6, edge.getFinalParse());
                dnsParseToEdgeRow.setField(7, edge.getMaxTTL());
                dnsParseToEdgeRow.setField(8, edge.getMinTTL());
                collector.collect(dnsParseToEdgeRow);
            }
        }

        // 5.domain ---> cname 或 cname ---> cname --->cname
        List<BaseEdge> cnameEdgeList = (List<BaseEdge>) edgeMap.get("cnameEdgeList");
        if (!CollectionUtils.isEmpty(cnameEdgeList)) {
            for (BaseEdge edge : cnameEdgeList) {
                Row cnameRow = new Row(5);
                cnameRow.setField(0, "CNAME_EDGE");
                cnameRow.setField(1, edge.getSrcId());
                cnameRow.setField(2, edge.getDstId());
                cnameRow.setField(3, 0);
                cnameRow.setField(4, edge.getSessionCnt());
                collector.collect(cnameRow);
            }
        }

        // 6.CNAME ---> ServerIp ---> cname_result
        List<BaseEdge> cnameResultList = (List<BaseEdge>) edgeMap.get("cnameResultEdgeList");
        if (!CollectionUtils.isEmpty(cnameResultList)) {
            for (BaseEdge edge : cnameResultList) {
                Row cnameResultRow = new Row(5);
                cnameResultRow.setField(0, "CNAME_RESULT_EDGE");
                cnameResultRow.setField(1, edge.getSrcId());
                cnameResultRow.setField(2, edge.getDstId());
                cnameResultRow.setField(3, 0);
                cnameResultRow.setField(4, edge.getSessionCnt());
                collector.collect(cnameResultRow);
            }
        }

        // 7.DOMAIN ---> FDOMAIN ---> domain_belong_to
        List<BaseEdge> domainBelongToEdgeList = (List<BaseEdge>) edgeMap.get("domainBelongToEdgeList");
        if (!CollectionUtils.isEmpty(domainBelongToEdgeList)) {
            for (BaseEdge domain2FDomainEdge : domainBelongToEdgeList) {
                Row domain2FDomainEdgeRow = new Row(4);
                // 无通用属性
                domain2FDomainEdgeRow.setField(0, "FDOMAIN_BELONG_EDGE");
                domain2FDomainEdgeRow.setField(1, domain2FDomainEdge.getSrcId());
                domain2FDomainEdgeRow.setField(2, domain2FDomainEdge.getDstId());
                domain2FDomainEdgeRow.setField(3, 0);
                collector.collect(domain2FDomainEdgeRow);
            }
        }

        extractRowFromEdgeMap(edgeMap, collector, BASE_EDGE_OBJECT_MAPPING);
    }

    public static void extractRowFromEdgeMap(Map<String, Object> edgeMap, Collector<Row> collector, Map<String, String> baseEdgeObjectMapping) {
        baseEdgeObjectMapping.forEach((key, value) -> {
            if (key.endsWith("List")) {
                List<BaseEdge> edgeList = (List<BaseEdge>) edgeMap.get(key);
                if (!CollectionUtils.isEmpty(edgeList)) {
                    for (BaseEdge edge : edgeList) {
                        Row edgeRow = baseEdge2Row(4, value, edge);
                        collector.collect(edgeRow);
                    }
                }
            } else {
                BaseEdge edge = (BaseEdge) edgeMap.get(key);
                if (edge != null) {
                    Row edgeRow = baseEdge2Row(4, value, edge);
                    collector.collect(edgeRow);
                }
            }
        });
    }
}
