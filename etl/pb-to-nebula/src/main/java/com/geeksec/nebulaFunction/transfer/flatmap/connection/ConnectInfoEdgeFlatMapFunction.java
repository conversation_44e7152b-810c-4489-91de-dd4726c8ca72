package com.geeksec.nebulaFunction.transfer.flatmap.connection;

import cn.hutool.core.collection.CollectionUtil;
import com.geeksec.common.loader.PropertiesLoader;
import com.geeksec.common.utils.DomainUtils;
import com.geeksec.common.utils.IpNetUtils;
import com.geeksec.common.utils.Md5Util;
import com.geeksec.nebulaEntity.edge.BaseEdge;
import com.geeksec.nebulaEntity.edge.TaskBelongToEdge;
import com.geeksec.nebulaEntity.edge.app.RelatedIpAppEdge;
import com.geeksec.nebulaEntity.edge.connection.BindEdge;
import com.geeksec.nebulaEntity.edge.connection.ConnectEdge;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixList;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixListFactory;
import nl.basjes.parse.useragent.UserAgent;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.*;

import ua_parser.Parser;

import static com.geeksec.common.utils.ProNameForMysql.*;
import static com.geeksec.nebulaFunction.transfer.flatmap.http.HttpInfoEdgeFlatMapFunction.analyzer;

/**
 * <AUTHOR>
 * @Description：
 */

public class ConnectInfoEdgeFlatMapFunction extends RichFlatMapFunction<Map<String, Object>, Map<String, Object>> {

    private final static Logger logger = LoggerFactory.getLogger(ConnectInfoEdgeFlatMapFunction.class);
    private static PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");
    public static String KNOWLEDGE_HOST = propertiesLoader.getProperty("mysql.knowledge.database.host");
    public static final String APP_DEFAULT_VERSION = "1.0";
    private static PublicSuffixListFactory factory = null;
    private static PublicSuffixList suffixList = null;
    private static Parser uaParser = null;
    private transient Connection connection;
    private static final String BROADCAST_MAC = "ff:ff:ff:ff:ff:ff";

    // 过滤APP_NAME
    List<String> ERR_DIRECT_SERVICE = Arrays.asList("TCP_QueryOnly", "No_Payload", "TCP_NoPayload", "UDP_NoPayload", "TCP_PortClose", "APP_ICMP_v4", "UDP_Unknown", "APP_ICMP_v6", "APP_IPMessage");

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        factory = new PublicSuffixListFactory();
        Properties properties = factory.getDefaults();
        properties.setProperty(PublicSuffixListFactory.PROPERTY_LIST_FILE, "/effective_tld_names.dat");
        suffixList = factory.build();
        uaParser = new Parser();
        Class.forName("com.mysql.jdbc.Driver").newInstance();
        connection = DriverManager.getConnection(KNOWLEDGE_HOST, USERNAME, PASSWORD);

    }

    @Override
    public void close() throws Exception {
        if (connection != null) {
            connection.close();
        }
    }

    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<Map<String, Object>> collector) throws Exception {

        // 获取PKT发送字节/包信息
        Map<String, Object> pktMap = (Map<String, Object>) pbMap.get("pkt");

        // Edge部分
        // 会话部分
        // 1. 源IP--->源MAC 发方关联 & 目的MAC---->目的IP 收方关联
        BindEdge srcBindEdge = getBindEdge(pbMap, "client");
        BindEdge dstBindEdge = getBindEdge(pbMap, "server");

        // 2.connect 链接信息 源IP--->目的IP & 源MAC--->目的MAC
        ConnectEdge ipConnectEdge = getConnectEdge(pbMap, "ip");
        ConnectEdge macConnectEdge = getConnectEdge(pbMap, "mac");

        // 3.域名--->锚域名 归属域名
        List<BaseEdge> domain2FDomainEdgeList = getDomain2FDomainEdge(pbMap);

        // 4.Client --->应用服务 客户端IP访问服务
        RelatedIpAppEdge clientAppEdge = getRelatedIpAppEdge(pbMap, "client");
        RelatedIpAppEdge serverAppEdge = getRelatedIpAppEdge(pbMap, "server");

        // HTTP部分
        // 5.ClientIp--->Domain Http访问 & ServerIp--->Doamin Http应用
        BaseEdge clientIpConnectDomain = getIpConnectDomain(pbMap, "client");
        BaseEdge serverIpConnectDomain = getIpConnectDomain(pbMap, "server");

        // 6.cert->appServiceTag,证书部署的特定业务端口的服务发现, special_business_port_service
        List<BaseEdge> certConnectAppTagEdgeList = getCertConnectAppTagEdgeList(pbMap);

        // 7. cert -> app
        List<BaseEdge> certBelongAppTagEdgeList = getCertBelongAppTagEdgeList(pbMap);

        // 8. domain -> app
        List<BaseEdge> domainBelongAppTagEdgeList = getDomain2AppEdge(pbMap);

        // 9. ua -> app
        List<BaseEdge> ua2AppEdgeList = getUa2AppTagEdge(pbMap);

        // 10. domain -> org
        List<BaseEdge> domain2OrgEdgeList = getDomain2OrgEdge(pbMap);

        // 11. ip -> org
        List<BaseEdge> ip2OrgEdgeList = getIP2OrgEdge(pbMap);

        // 12. ip -> app
        BaseEdge ip2AppEdge = getIP2AppEdge(pbMap);

        // 13. sslfinger -> app
        BaseEdge dSslFinger2AppEdge = getDsslFinger2AppEdge(pbMap);


        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("srcBindEdge", srcBindEdge);
        resultMap.put("dstBindEdge", dstBindEdge);
        resultMap.put("ipConnectEdge", ipConnectEdge);
        resultMap.put("macConnectEdge", macConnectEdge);
        resultMap.put("domain2FdomainEdgeList", domain2FDomainEdgeList);
        resultMap.put("clientAppEdge", clientAppEdge);
        resultMap.put("serverAppEdge", serverAppEdge);
        resultMap.put("clientIpConnectDomainEdge", clientIpConnectDomain);
        resultMap.put("serverIpConnectDomainEdge", serverIpConnectDomain);
        resultMap.put("certConnectAppTagEdgeList", certConnectAppTagEdgeList);

        resultMap.put("certBelongAppTagEdgeList", certBelongAppTagEdgeList);
        resultMap.put("domainBelongAppTagEdgeList", domainBelongAppTagEdgeList);
        resultMap.put("ua2AppEdgeList", ua2AppEdgeList);
        resultMap.put("domain2OrgEdgeList", domain2OrgEdgeList);
        resultMap.put("ip2OrgEdgeList", ip2OrgEdgeList);
        resultMap.put("ip2AppEdge", ip2AppEdge);
        resultMap.put("dSslFinger2AppEdge", dSslFinger2AppEdge);

        collector.collect(resultMap);
    }

    private BaseEdge getDsslFinger2AppEdge(Map<String, Object> pbMap) {
        BaseEdge dSslFinger2AppEdge = new BaseEdge();
        String appVidKey;
        String appName = (String) pbMap.get("AppName");
        // 过滤不是App应用的会话
        if (appName.contains("_")) {
            return null;
        } else {
            // 处理App vid
            appVidKey = appName + "_" + APP_DEFAULT_VERSION;
        }
        dSslFinger2AppEdge.setSrcId(pbMap.get("dSSLFinger").toString());
        dSslFinger2AppEdge.setDstId(appVidKey);
        dSslFinger2AppEdge.setSessionCnt(0L);
        return dSslFinger2AppEdge;
    }

    /**
     * Ua 关联 App应用
     * ua_belong_app
     *
     * @param pbMap
     * @return
     */

    private List<BaseEdge> getUa2AppTagEdge(Map<String, Object> pbMap) {
        List<BaseEdge> ua2AppEdgeList = new ArrayList<>();
        String appVidKey;
        String appName = (String) pbMap.get("AppName");
        // 过滤不是App应用的会话
        if (appName.contains("_")) {
            return null;
        } else {
            // 处理App vid
            appVidKey = appName + "_" + APP_DEFAULT_VERSION;
        }
        List<String> ua2App = getUaList(pbMap);
        for (String ua : ua2App) {
            BaseEdge ua2AppEdge = new BaseEdge();
            ua2AppEdge.setSrcId(ua);
            ua2AppEdge.setDstId(appVidKey);
            ua2AppEdge.setSessionCnt(0L);
            ua2AppEdgeList.add(ua2AppEdge);
        }
        return ua2AppEdgeList;
    }

    private static List<String> getUaList(Map<String, Object> pbMap) {
        List<HashMap<String, Object>> httpInfoList = (List<HashMap<String, Object>>) pbMap.get("HTTP");
        List<String> uaStr = new ArrayList<>();
        for (HashMap<String, Object> httpInfo : httpInfoList) {
            // 解析User-Agent
            String userAgent = (String) httpInfo.get("User-Agent");
            if (!userAgent.isEmpty()){
                UserAgent agent = analyzer.parse(userAgent);

                // 获取解析结果
                String osName = agent.getValue("OperatingSystemName"); // 操作系统
                String deviceName = agent.getValue("DeviceName"); // 设备名称
                String applicationName = agent.getValue("AgentName"); // 浏览器/应用名称
//            String browserVersion = agent.getValue("AgentVersion"); // 浏览器版本
                String userAgentKey = applicationName + "_" + osName + "_" + deviceName;
                if (!uaStr.contains(userAgentKey)) {
                    uaStr.add(userAgentKey);
                }
            }
        }
        return uaStr;
    }

    /**
     * Cert 关联的特定业务端口的服务
     * special_business_port_service
     *
     * @param pbMap
     * @param
     * @return
     */
    private List<BaseEdge> getCertConnectAppTagEdgeList(Map<String, Object> pbMap) {

        List<BaseEdge> CertConnectAppTagEdgeList = new ArrayList<>();

        String serviceKey = "";
        List<String> serverCertList = new ArrayList<>();

        String appName = (String) pbMap.get("AppName");
        // 过滤无效服务
        if (ERR_DIRECT_SERVICE.contains(appName) || !appName.contains("_")) {
            return null;
        } else {
            // 处理Service_Key
            String dIp = (String) pbMap.get("dIp");
            Integer dPort = (Integer) pbMap.get("dPort");
            String ipKey = null;

            if (IpNetUtils.isValidIPV4(dIp)) {
                if (IpNetUtils.judgeInternal(dIp)) {
                    ipKey = pbMap.get("task_id") + "-" + dIp;
                } else {
                    ipKey = dIp;
                }
            } else {
                ipKey = dIp;
            }
            serviceKey = ipKey + "_" + dPort.toString() + "_" + appName;
        }

        List<HashMap<String, Object>> sslInfoList = (List<HashMap<String, Object>>) pbMap.get("SSL");

        if (!CollectionUtil.isEmpty(sslInfoList)) {
            for (HashMap<String, Object> sslMap : sslInfoList) {
                List<String> certIds = (List<String>) sslMap.get("dCertHash");
                for (String certId : certIds) {
                    if (!serverCertList.contains(certId)) {
                        serverCertList.add(certId);
                    }
                }
            }
        }

        if (!"".equals(serviceKey) && serverCertList.size() != 0) {
            for (String certId : serverCertList) {
                BaseEdge CertConnectAppTagEdge = new BaseEdge();
                CertConnectAppTagEdge.setSrcId(certId);
                CertConnectAppTagEdge.setDstId(serviceKey);
                CertConnectAppTagEdge.setSessionCnt(0L);
                CertConnectAppTagEdgeList.add(CertConnectAppTagEdge);
            }
            return CertConnectAppTagEdgeList;
        }
        return null;
    }

    /**
     * Cert 关联 APP
     * cert_belong_app
     *
     * @param pbMap
     * @param
     * @return
     */
    private List<BaseEdge> getCertBelongAppTagEdgeList(Map<String, Object> pbMap) {

        List<BaseEdge> CertConnectAppTagEdgeList = new ArrayList<>();

        String appVidKey = "";
        List<String> serverCertList = new ArrayList<>();

        String appName = (String) pbMap.get("AppName");
        // 过滤不是App应用的会话
        if (appName.contains("_")) {
            return null;
        } else {
            // 处理App vid
            appVidKey = appName + "_" + APP_DEFAULT_VERSION;
        }

        List<HashMap<String, Object>> sslInfoList = (List<HashMap<String, Object>>) pbMap.get("SSL");

        if (!CollectionUtil.isEmpty(sslInfoList)) {
            for (HashMap<String, Object> sslMap : sslInfoList) {
                List<String> certIds = (List<String>) sslMap.get("dCertHash");
                for (String certId : certIds) {
                    if (!serverCertList.contains(certId)) {
                        serverCertList.add(certId);
                    }
                }
            }
        }

        if (!"".equals(appVidKey) && serverCertList.size() != 0) {
            for (String certId : serverCertList) {
                BaseEdge CertConnectAppTagEdge = new BaseEdge();
                CertConnectAppTagEdge.setSrcId(certId);
                CertConnectAppTagEdge.setDstId(appVidKey);
                CertConnectAppTagEdge.setSessionCnt(0L);
                CertConnectAppTagEdgeList.add(CertConnectAppTagEdge);
            }
            return CertConnectAppTagEdgeList;
        }
        return null;
    }

    /**
     * 链路、IP访问关系 Connect
     *
     * @param pbMap
     * @param type
     * @return
     */
    private ConnectEdge getConnectEdge(Map<String, Object> pbMap, String type) {

        Map<String, Object> pktMap = (Map<String, Object>) pbMap.get("pkt");

        ConnectEdge connectEdge = new ConnectEdge();
        if (pbMap.get("dMac").equals(BROADCAST_MAC)) {
            return null;
        }

        if (type.equals("ip")) {
            connectEdge.setSrcId((String) pbMap.get("sIp"));
            connectEdge.setDstId((String) pbMap.get("dIp"));
        } else if (type.equals("mac")) {
            connectEdge.setSrcId((String) pbMap.get("sMac"));
            connectEdge.setDstId((String) pbMap.get("dMac"));
        }

        connectEdge.setAppName((String) pbMap.get("AppName"));
        connectEdge.setDPort((Integer) pbMap.get("dPort"));
        connectEdge.setBytes(Long.parseLong(pktMap.get("dPayloadBytes").toString()) + Long.parseLong(pktMap.get("sPayloadBytes").toString()));
        connectEdge.setPackets((Integer) pktMap.get("sPayloadNum") + (Integer) pktMap.get("dPayloadNum"));
        connectEdge.setSendBytes(Long.parseLong(pktMap.get("sPayloadBytes").toString()));
        connectEdge.setRecvBytes(Long.parseLong(pktMap.get("dPayloadBytes").toString()));
        connectEdge.setSendPackets((Integer) pktMap.get("sPayloadNum"));
        connectEdge.setRecvPackets((Integer) pktMap.get("dPayloadNum"));
        connectEdge.setSessionCnt(0L);
        return connectEdge;
    }

    /**
     * 收方/发送方关联 Bind
     *
     * @param pbMap
     * @param type
     * @return
     */
    private BindEdge getBindEdge(Map<String, Object> pbMap, String type) {
        Map<String, Object> pktMap = (Map<String, Object>) pbMap.get("pkt");

        BindEdge bindEdge = new BindEdge();
        if (type.equals("client")) {
            bindEdge.setSrcId((String) pbMap.get("sIp"));
            bindEdge.setDstId((String) pbMap.get("sMac"));
            bindEdge.setIpAddr((String) pbMap.get("sIp"));
            bindEdge.setMac((String) pbMap.get("sMac"));
            bindEdge.setBytes(Long.parseLong(pktMap.get("sPayloadBytes").toString()));
            bindEdge.setPackets((Integer) pktMap.get("sPayloadNum"));
        } else if (type.equals("server")) {
            bindEdge.setSrcId((String) pbMap.get("dIp"));
            bindEdge.setDstId((String) pbMap.get("dMac"));
            bindEdge.setIpAddr((String) pbMap.get("dIp"));
            bindEdge.setMac((String) pbMap.get("dMac"));
            bindEdge.setBytes(Long.parseLong(pktMap.get("dPayloadBytes").toString()));
            bindEdge.setPackets((Integer) pktMap.get("dPayloadNum"));
        }

        bindEdge.setSessionCnt(0L);
        return bindEdge;
    }

    /**
     * 生成IP与HTTP域名之间的边联系
     *
     * @param pbMap
     * @param type
     * @return
     */
    private BaseEdge getIpConnectDomain(Map<String, Object> pbMap, String type) {
        List<HashMap<String, Object>> httpInfoList = (List<HashMap<String, Object>>) pbMap.get("HTTP");
        BaseEdge ipConnectDomainEdge = new BaseEdge();
        if (httpInfoList.size() != 0) {
            for (HashMap<String, Object> httpMap : httpInfoList) {
                String response = (String) httpMap.get("Response");
                if (!StringUtil.isNullOrEmpty(response) && response.contains("200")) {
                    String httpDomainAddr = (String) httpMap.get("Host");
                    String serverIp = (String) pbMap.get("dIp");
                    if (DomainUtils.isValidDomain(httpDomainAddr) && !httpDomainAddr.equals(serverIp)) {
                        if (type.equals("client")) {
                            ipConnectDomainEdge.setSrcId((String) pbMap.get("sIp"));
                        } else if (type.equals("server")) {
                            ipConnectDomainEdge.setSrcId((String) pbMap.get("dIp"));
                        }
                        if (httpDomainAddr.length() > 200) {
                            ipConnectDomainEdge.setDstId(Md5Util.Md5(httpDomainAddr));
                        } else {
                            ipConnectDomainEdge.setDstId(httpDomainAddr);
                        }
                        ipConnectDomainEdge.setSessionCnt(0L);
                    } else {
                        return null;
                    }
                } else {
                    return null;
                }
            }
        } else {
            return null;
        }
        return ipConnectDomainEdge;
    }

    /**
     * 获取域名--->锚域名边关系集合
     *
     * @param pbMap
     * @return
     */
    private List<BaseEdge> getDomain2FDomainEdge(Map<String, Object> pbMap) {
        List<BaseEdge> domain2FDomainEdgeList = new ArrayList<>();

        // 获取会话信息中的域名信息
        List<HashMap<String, Object>> httpInfoList = (List<HashMap<String, Object>>) pbMap.get("HTTP");
        List<HashMap<String, Object>> sslInfoList = (List<HashMap<String, Object>>) pbMap.get("SSL");
        List<HashMap<String, Object>> dnsInfoList = (List<HashMap<String, Object>>) pbMap.get("DNS");

        if (httpInfoList.size() != 0) {
            for (HashMap<String, Object> httpMap : httpInfoList) {
                String response = (String) httpMap.get("Response");
                if (!StringUtil.isNullOrEmpty(response) && response.contains("200")) {
                    String httpDomainAddr = (String) httpMap.get("Host");
                    BaseEdge httpDomain2FDomainEdge = new BaseEdge();
                    if (DomainUtils.isValidDomain(httpDomainAddr)) {
                        if (httpDomainAddr.contains(":")) {
                            httpDomainAddr = httpDomainAddr.split("\\:")[0];
                        }
                        httpDomain2FDomainEdge.setSrcId(httpDomainAddr);
                        String fDomainAddr = StringUtil.EMPTY_STRING;
                        try {
                            fDomainAddr = suffixList.getRegistrableDomain(httpDomainAddr);
                        } catch (Exception e) {
                            logger.warn("获取锚域名失败,http domainAddr--->{},error--->{}", httpDomainAddr, e);
                        }
                        httpDomain2FDomainEdge.setDstId("*." + fDomainAddr);
                        httpDomain2FDomainEdge.setSessionCnt(0L);
                        domain2FDomainEdgeList.add(httpDomain2FDomainEdge);
                    }
                }
            }
        }

        if (httpInfoList.size() != 0) {
            for (HashMap<String, Object> httpMap : httpInfoList) {
                List<String> domainList = new ArrayList<>();
                BaseEdge dnsDomain2FDomainEdge = new BaseEdge();
                try {
                    for (HashMap<String, Object> domainMap : dnsInfoList) {
                        String dnsDomainAddr = (String) domainMap.get("Domain");
                        if (domainList.contains(dnsDomainAddr)) {
                            continue;
                        } else {
                            if (DomainUtils.isValidDomain(dnsDomainAddr)) {
                                if (dnsDomainAddr.contains(":")) {
                                    dnsDomainAddr = dnsDomainAddr.split("\\:")[0];
                                }
                                dnsDomain2FDomainEdge.setSrcId(dnsDomainAddr);
                                String fDomainAddr = StringUtil.EMPTY_STRING;
                                try {
                                    fDomainAddr = suffixList.getRegistrableDomain(dnsDomainAddr);
                                } catch (Exception e) {
                                    logger.warn("获取锚域名失败,dns domainAddr--->{},error--->{}", dnsDomainAddr, e);
                                }
                                dnsDomain2FDomainEdge.setDstId("*." + fDomainAddr);
                                dnsDomain2FDomainEdge.setSessionCnt(0L);
                                domainList.add(dnsDomainAddr);
                                domain2FDomainEdgeList.add(dnsDomain2FDomainEdge);
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.error("转义Domain失败,dnsInfoList--->{}", httpInfoList, e);
                }
            }
        }

        if (sslInfoList.size() != 0) {
            HashMap<String, Object> sslMap = sslInfoList.get(0);
            BaseEdge sslDomain2FDomainEdge = new BaseEdge();
            String sslDomainAddr = (String) sslMap.get("CH_ServerName");
            if (DomainUtils.isValidDomain(sslDomainAddr)) {
                if (sslDomainAddr.contains(":")) {
                    sslDomainAddr = sslDomainAddr.split("\\:")[0];
                }
                sslDomain2FDomainEdge.setSrcId(sslDomainAddr);
                String fDomainAddr = StringUtil.EMPTY_STRING;
                try {
                    fDomainAddr = suffixList.getRegistrableDomain(sslDomainAddr);
                } catch (Exception e) {
                    logger.warn("获取锚域名失败,ssl domainAddr--->{},error--->{}", sslDomainAddr, e);
                }
                sslDomain2FDomainEdge.setDstId("*." + fDomainAddr);
                sslDomain2FDomainEdge.setSessionCnt(0L);
                domain2FDomainEdgeList.add(sslDomain2FDomainEdge);
            }
        }

        // 处理锚域名MD5
        for (BaseEdge edge : domain2FDomainEdgeList) {
            String domain = edge.getSrcId();
            String fDomain = edge.getDstId();
            if (domain.length() > 200) {
                edge.setSrcId(Md5Util.Md5(domain));
            }
            if (fDomain.length() > 200) {
                edge.setDstId(Md5Util.Md5(fDomain));
            }
        }

        return domain2FDomainEdgeList;
    }

    /**
     * 获取域名--->锚域名边关系集合
     *
     * @param pbMap
     * @return
     */
    private List<BaseEdge> getDomain2AppEdge(Map<String, Object> pbMap) {
        List<BaseEdge> domain2AppEdgeList = new ArrayList<>();
        String appName = pbMap.get("AppName").toString();
        if (appName.contains("_")) {
            return null;
        }
        String appVidKey = appName + "_" + APP_DEFAULT_VERSION;
        Set<String> domainList = getDomainList(pbMap);
        if (domainList.isEmpty()) {
            return null;
        }
        // 处理域名列表进行关联
        for (String domain : domainList) {
            BaseEdge domain2AppEdge = new BaseEdge();
            domain2AppEdge.setDstId(appVidKey);
            if (domain.length() > 200) {
                domain2AppEdge.setSrcId(Md5Util.Md5(domain));
            } else {
                domain2AppEdge.setSrcId(domain);
            }
            domain2AppEdge.setSessionCnt(0L);
            domain2AppEdgeList.add(domain2AppEdge);
        }

        return domain2AppEdgeList;
    }

    /**
     * 获取域名--->组织，通过数据库碰撞
     *
     * @param pbMap
     * @return
     */
    private List<BaseEdge> getDomain2OrgEdge(Map<String, Object> pbMap) {
        List<BaseEdge> domain2OrgEdgeList = new ArrayList<>();
        Set<String> domainList = getDomainList(pbMap);
        if (domainList.isEmpty()) {
            return null;
        }
        orgDomainKnowledgeHit(connection, pbMap, domainList, domain2OrgEdgeList, "tb_domain_whois");

        return domain2OrgEdgeList;
    }

    public static void orgKnowledgeHit(Connection connection, Map<String, Object> pbMap, Set<String> keys, List<BaseEdge> outputs, String tableName) {
        for (String key : keys) {
            String preparedSql = "select organization from " + tableName + " where prefix = ? limit 1";
            try (PreparedStatement ps = connection.prepareStatement(preparedSql)) {
                ps.setString(1, key);
                ResultSet rs = ps.executeQuery();
                if (rs.next()) {
                    BaseEdge edge = new BaseEdge();
                    edge.setDstId(DigestUtils.md5Hex(rs.getString("organization")));
                    if (key.length() > 200) {
                        edge.setSrcId(Md5Util.Md5(key));
                    } else {
                        edge.setSrcId(key);
                    }
                    edge.setSessionCnt(0L);
                    outputs.add(edge);
                }
            } catch (SQLException e) {
                logger.info("select org_name with key failed {}", e.getMessage());
            }
        }
    }

    public static void orgDomainKnowledgeHit(Connection connection, Map<String, Object> pbMap, Set<String> keys, List<BaseEdge> outputs, String tableName) {
        for (String key : keys) {
            String preparedSql = "select whois from " + tableName + " where domain = ? limit 1";
            try (PreparedStatement ps = connection.prepareStatement(preparedSql)) {
                ps.setString(1, key);
                ResultSet rs = ps.executeQuery();
                if (rs.next()) {
                    BaseEdge edge = new BaseEdge();
                    edge.setDstId(DigestUtils.md5Hex(rs.getString("whois")));
                    if (key.length() > 200) {
                        edge.setSrcId(Md5Util.Md5(key));
                    } else {
                        edge.setSrcId(key);
                    }
                    edge.setSessionCnt(0L);
                    outputs.add(edge);
                }
            } catch (SQLException e) {
                logger.info("select org_name with key failed {}", e.getMessage());
            }
        }
    }

    /**
     * 获取IP--->组织，通过数据库碰撞
     *
     * @param pbMap
     * @return
     */
    private List<BaseEdge> getIP2OrgEdge(Map<String, Object> pbMap) {
        List<BaseEdge> ip2OrgEdgeList = new ArrayList<>();
        Set<String> ipList = new HashSet<>();
        ipList.add((String) pbMap.get("sIp"));
        ipList.add((String) pbMap.get("dIp"));

        orgKnowledgeHit(connection, pbMap, ipList, ip2OrgEdgeList, "ip_enterprise");

        return ip2OrgEdgeList;
    }

    /**
     * 获取sIP--->APP
     *
     * @param pbMap
     * @return
     */
    private BaseEdge getIP2AppEdge(Map<String, Object> pbMap) {
        String appName = pbMap.get("AppName").toString();
        if (appName.contains("_")) {
            return null;
        }
        String appVidKey = appName + "_" + APP_DEFAULT_VERSION;
        BaseEdge ip2AppEdge = new BaseEdge();
        ip2AppEdge.setSrcId((String) pbMap.get("dIp"));
        ip2AppEdge.setDstId(appVidKey);
        ip2AppEdge.setSessionCnt(0L);
        return ip2AppEdge;
    }

    private static Set<String> getDomainList(Map<String, Object> pbMap) {
        Set<String> domainList = new HashSet<>();
        // 获取会话信息中的域名信息
        List<HashMap<String, Object>> httpInfoList = (List<HashMap<String, Object>>) pbMap.get("HTTP");
        List<HashMap<String, Object>> sslInfoList = (List<HashMap<String, Object>>) pbMap.get("SSL");
        List<HashMap<String, Object>> dnsInfoList = (List<HashMap<String, Object>>) pbMap.get("DNS");

        if (!httpInfoList.isEmpty()) {
            for (HashMap<String, Object> httpMap : httpInfoList) {
                String response = (String) httpMap.get("Response");
                if (!StringUtil.isNullOrEmpty(response) && response.contains("200")) {
                    String httpDomainAddr = (String) httpMap.get("Host");
                    if (DomainUtils.isValidDomain(httpDomainAddr)) {
                        if (httpDomainAddr.contains(":")) {
                            httpDomainAddr = httpDomainAddr.split("\\:")[0];
                        }
                        domainList.add(httpDomainAddr);
                    }
                }
            }
        }

        if (!dnsInfoList.isEmpty()) {
            try {
                for (HashMap<String, Object> domainMap : dnsInfoList) {
                    String dnsDomainAddr = (String) domainMap.get("Domain");
                    if (DomainUtils.isValidDomain(dnsDomainAddr)) {
                        if (dnsDomainAddr.contains(":")) {
                            dnsDomainAddr = dnsDomainAddr.split("\\:")[0];
                        }
                        domainList.add(dnsDomainAddr);
                    }
                }
            } catch (Exception e) {
                logger.error("转义Domain失败,dnsInfoList--->{}", dnsInfoList, e);
            }
        }

        if (!sslInfoList.isEmpty()) {
            HashMap<String, Object> sslMap = sslInfoList.get(0);
            String sslDomainAddr = (String) sslMap.get("CH_ServerName");
            if (DomainUtils.isValidDomain(sslDomainAddr)) {
                if (sslDomainAddr.contains(":")) {
                    sslDomainAddr = sslDomainAddr.split("\\:")[0];
                }
                domainList.add(sslDomainAddr);
            }
        }
        return domainList;
    }

    // 创建TaskBelongToEdge对象的辅助方法
    private TaskBelongToEdge createTaskBelongToEdge(String srcId, String dstId, Integer taskId, String taskName) {
        TaskBelongToEdge taskBelongToEdge = new TaskBelongToEdge();
        taskBelongToEdge.setSrcId(srcId);
        taskBelongToEdge.setDstId(dstId);
        taskBelongToEdge.setTaskId(taskId);
        taskBelongToEdge.setTaskName(taskName);
        return taskBelongToEdge;
    }

    /**
     * 生成IP与APPSERVICE的服务相关边联系
     *
     * @param pbMap
     * @param type
     * @return
     */
    private RelatedIpAppEdge getRelatedIpAppEdge(Map<String, Object> pbMap, String type) {
        String appName = (String) pbMap.get("AppName");
        RelatedIpAppEdge edge = new RelatedIpAppEdge();
        String Hkey = (String) pbMap.get("Hkey");
        // 处理Service_Key
        String dIp = (String) pbMap.get("dIp");
        Integer dPort = (Integer) pbMap.get("dPort");
        String ipKey = null;
        if (IpNetUtils.isValidIPV4(dIp)) {
            if (IpNetUtils.judgeInternal(dIp)) {
                ipKey = Hkey.split("_")[1] + "-" + dIp;
            } else {
                ipKey = dIp;
            }
        } else {
            ipKey = dIp;
        }

        if (!ERR_DIRECT_SERVICE.contains(appName)) {
            if (type.equals("client")) {
                edge.setIpAddr((String) pbMap.get("sIp"));
                edge.setSrcId((String) pbMap.get("sIp"));
                edge.setDstId(ipKey + "_" + dPort.toString() + "_" + appName);
            } else if (type.equals("server")) {
                edge.setIpAddr((String) pbMap.get("dIp"));
                edge.setSrcId(ipKey + "_" + dPort.toString() + "_" + appName);
                edge.setDstId((String) pbMap.get("dIp"));
            }
            edge.setAppName(appName);
            edge.setSessionCnt(0L);
        } else {
            return null;
        }
        return edge;
    }

}
