{"88": {"protocol_remark": "EIGRP ", "protocol_type": "EIGRP"}, "89": {"protocol_remark": "OSPFIGP ", "protocol_type": "OSPFIGP"}, "110": {"protocol_remark": "Compaq对等协议 ", "protocol_type": "Compaq-Peer"}, "111": {"protocol_remark": "IP中的IPX ", "protocol_type": "IPX-in-IP"}, "112": {"protocol_remark": "虚拟路由器冗余协议 ", "protocol_type": "VRRP"}, "113": {"protocol_remark": "PGM可靠传输协议 ", "protocol_type": "PGM"}, "114": {"protocol_remark": "任意0跳协议 ", "protocol_type": ""}, "115": {"protocol_remark": "第二层隧道协议 ", "protocol_type": "L2TP"}, "116": {"protocol_remark": "D-II数据交换(DDX) ", "protocol_type": "DDX"}, "90": {"protocol_remark": "Sprite RPC协议 ", "protocol_type": "Sprite-RPC"}, "117": {"protocol_remark": "交互式代理传输协议 ", "protocol_type": "IATP"}, "91": {"protocol_remark": "轨迹地址解析协议 ", "protocol_type": "LARP"}, "118": {"protocol_remark": "计划传输协议 ", "protocol_type": "STP"}, "92": {"protocol_remark": "多播传输协议 ", "protocol_type": "MTP"}, "119": {"protocol_remark": "SpectraLink无线协议 ", "protocol_type": "SRP"}, "93": {"protocol_remark": "AX.25帧 ", "protocol_type": "AX.25"}, "94": {"protocol_remark": "IP中的IP封装协议 ", "protocol_type": "IPIP"}, "95": {"protocol_remark": "移动互联控制协议 ", "protocol_type": "MICP"}, "96": {"protocol_remark": "信号通讯安全协议 ", "protocol_type": "SCC-SP"}, "97": {"protocol_remark": "IP中的以太网封装 ", "protocol_type": "ETHERIP"}, "10": {"protocol_remark": "BBN RCC监视 ", "protocol_type": "BBN-RCC-MON"}, "98": {"protocol_remark": "封装标头 ", "protocol_type": "ENCAP"}, "11": {"protocol_remark": "网络语音协议 ", "protocol_type": "NVP-II"}, "99": {"protocol_remark": "任意专用加密方案 ", "protocol_type": ""}, "12": {"protocol_remark": "PUP ", "protocol_type": "PUP"}, "13": {"protocol_remark": "ARGUS ", "protocol_type": "ARGUS"}, "14": {"protocol_remark": "EMCON ", "protocol_type": "EMCON"}, "15": {"protocol_remark": "跨网调试器 ", "protocol_type": "XNET"}, "16": {"protocol_remark": "Chaos ", "protocol_type": "CHAOS"}, "17": {"protocol_remark": "用户数据报 ", "protocol_type": "UDP"}, "18": {"protocol_remark": "多路复用 ", "protocol_type": "MUX"}, "19": {"protocol_remark": "DCN测量子系统 ", "protocol_type": "DCN-MEAS"}, "120": {"protocol_remark": "UTI ", "protocol_type": "UTI"}, "0": {"protocol_remark": "IPv6逐跳选项 ", "protocol_type": "HOPOPT"}, "121": {"protocol_remark": "简单邮件协议 ", "protocol_type": "SMP"}, "1": {"protocol_remark": "Internet控制消息 ", "protocol_type": "ICMP"}, "122": {"protocol_remark": "SM ", "protocol_type": "SM"}, "2": {"protocol_remark": "Internet组管理 ", "protocol_type": "IGMP"}, "123": {"protocol_remark": "性能透明协议 ", "protocol_type": "PTP"}, "3": {"protocol_remark": "网关对网关 ", "protocol_type": "GGP"}, "124": {"protocol_remark": "Over IPv4 ", "protocol_type": "ISIS"}, "4": {"protocol_remark": "IP中的IP（封装） ", "protocol_type": "IP"}, "125": {"protocol_remark": "", "protocol_type": "FIRE "}, "5": {"protocol_remark": "流  ", "protocol_type": "ST"}, "126": {"protocol_remark": "Combat无线传输协议 ", "protocol_type": "CRTP"}, "6": {"protocol_remark": "传输控制 ", "protocol_type": "TCP"}, "127": {"protocol_remark": "Combat无线用户数据报 ", "protocol_type": "CRUDP"}, "7": {"protocol_remark": "CBT ", "protocol_type": "CBT"}, "128": {"protocol_remark": "", "protocol_type": "SSCOPMCE "}, "8": {"protocol_remark": "外部网关协议 ", "protocol_type": "EGP"}, "129": {"protocol_remark": "", "protocol_type": "IPLT "}, "9": {"protocol_remark": "任何专用内部网关（Cisco将其用于IGRP）", "protocol_type": " IGP"}, "20": {"protocol_remark": "主机监视 ", "protocol_type": "HMP"}, "21": {"protocol_remark": "数据包无线测量 ", "protocol_type": "PRM"}, "22": {"protocol_remark": "XEROX NS IDP ", "protocol_type": "XNS-IDP"}, "23": {"protocol_remark": "第1主干 ", "protocol_type": "TRUNK-1"}, "24": {"protocol_remark": "第2主干 ", "protocol_type": "TRUNK-2"}, "25": {"protocol_remark": "第1叶 ", "protocol_type": "LEAF-1"}, "26": {"protocol_remark": "第2叶 ", "protocol_type": "LEAF-2"}, "27": {"protocol_remark": "可靠数据协议 ", "protocol_type": "RDP"}, "28": {"protocol_remark": "Internet可靠事务 ", "protocol_type": "IRTP"}, "29": {"protocol_remark": "ISO传输协议第4类 ", "protocol_type": "ISO-TP4"}, "130": {"protocol_remark": "安全数据包防护 ", "protocol_type": "SPS"}, "131": {"protocol_remark": "IP中的专用IP封装 ", "protocol_type": "PIPE"}, "132": {"protocol_remark": "流控制传输协议 ", "protocol_type": "SCTP"}, "133": {"protocol_remark": "光纤通道 ", "protocol_type": "FC"}, "255": {"protocol_remark": "保留", "protocol_type": ""}, "134-254": {"protocol_remark": "未分配", "protocol_type": ""}, "30": {"protocol_remark": "批量数据传输协议 ", "protocol_type": "NETBLT"}, "31": {"protocol_remark": "MFE网络服务协议 ", "protocol_type": "MFE-NSP"}, "32": {"protocol_remark": "MERIT节点间协议 ", "protocol_type": "MERIT-INP"}, "33": {"protocol_remark": "顺序交换协议 ", "protocol_type": "SEP"}, "34": {"protocol_remark": "第三方连接协议 ", "protocol_type": "3PC"}, "35": {"protocol_remark": "域间策略路由协议", "protocol_type": "IDPR"}, "36": {"protocol_remark": "XTP ", "protocol_type": "XTP"}, "37": {"protocol_remark": "数据报传送协议 ", "protocol_type": "DDP"}, "38": {"protocol_remark": "IDPR控制消息传输协议", "protocol_type": "IDPR-CMTP"}, "39": {"protocol_remark": "TP++传输协议 ", "protocol_type": "TP++"}, "40": {"protocol_remark": "IL传输协议 ", "protocol_type": "IL"}, "41": {"protocol_remark": "Ipv6 ", "protocol_type": "IPv6"}, "42": {"protocol_remark": "源要求路由协议 ", "protocol_type": "SDRP"}, "43": {"protocol_remark": "IPv6的路由标头 ", "protocol_type": "IPv6-Route"}, "44": {"protocol_remark": "IPv6的片断标头 ", "protocol_type": "IPv6-Frag"}, "45": {"protocol_remark": "域间路由协议 ", "protocol_type": "IDRP"}, "46": {"protocol_remark": "保留协议 ", "protocol_type": "RSVP"}, "47": {"protocol_remark": "通用路由封装 ", "protocol_type": "GRE"}, "48": {"protocol_remark": "移动主机路由协议", "protocol_type": "MHRP"}, "49": {"protocol_remark": "BNA ", "protocol_type": "BNA"}, "50": {"protocol_remark": "IPv6的封装安全负载 ", "protocol_type": "ESP"}, "51": {"protocol_remark": "IPv6的身份验证标头 ", "protocol_type": "AH"}, "52": {"protocol_remark": "集成网络层安全性TUBA ", "protocol_type": "I-NLSP"}, "53": {"protocol_remark": "采用加密的IP ", "protocol_type": "SWIPE"}, "54": {"protocol_remark": "NBMA地址解析协议 ", "protocol_type": "NARP"}, "55": {"protocol_remark": "IP移动性 ", "protocol_type": "MOBILE"}, "56": {"protocol_remark": "传输层安全协议，使用Kryptonet密钥管理", "protocol_type": "TLSP"}, "57": {"protocol_remark": "SKIP ", "protocol_type": "SKIP"}, "58": {"protocol_remark": "用于IPv6的ICMP ", "protocol_type": "IPv6-ICMP"}, "59": {"protocol_remark": "用于IPv6的无下一个标头 ", "protocol_type": "IPv6-NoNxt"}, "60": {"protocol_remark": "IPv6的目标选项 ", "protocol_type": "IPv6-Opts"}, "61": {"protocol_remark": "任意主机内部协议 ", "protocol_type": ""}, "62": {"protocol_remark": "CFTP ", "protocol_type": "CFTP"}, "63": {"protocol_remark": "任意本地网络 ", "protocol_type": ""}, "64": {"protocol_remark": "SATNET与后台EXPAK ", "protocol_type": "SAT-EXPAK"}, "65": {"protocol_remark": "Kryptolan ", "protocol_type": "KRYPTOLAN"}, "66": {"protocol_remark": "MIT远程虚拟磁盘协议 ", "protocol_type": "RVD"}, "67": {"protocol_remark": "Internet luribus数据包核心 ", "protocol_type": "IPPC"}, "68": {"protocol_remark": "任意分布式文件系统 ", "protocol_type": ""}, "69": {"protocol_remark": "SATNET监视 ", "protocol_type": "SAT-MON"}, "70": {"protocol_remark": "VISA协议 ", "protocol_type": "VISA"}, "71": {"protocol_remark": "Internet数据包核心工具 ", "protocol_type": "IPCV"}, "72": {"protocol_remark": "计算机协议网络管理 ", "protocol_type": "CPNX"}, "73": {"protocol_remark": "计算机协议检测信号 ", "protocol_type": "CPHB"}, "74": {"protocol_remark": "王安电脑网络 ", "protocol_type": "WSN"}, "75": {"protocol_remark": "数据包视频协议 ", "protocol_type": "PVP"}, "76": {"protocol_remark": "后台SATNET监视 ", "protocol_type": "BR-SAT-MON"}, "77": {"protocol_remark": "SUN-ND PROTOCOL-Temporary ", "protocol_type": "SUN-ND"}, "78": {"protocol_remark": "WIDEBAND监视 ", "protocol_type": "WB-MON"}, "79": {"protocol_remark": "WIDEBAND EXPAK ", "protocol_type": "WB-EXPAK"}, "100": {"protocol_remark": "GMTP ", "protocol_type": "GMTP"}, "101": {"protocol_remark": "Ipsilon流量管理协议 ", "protocol_type": "IFMP"}, "102": {"protocol_remark": "IP上的PNNI ", "protocol_type": "PNNI"}, "103": {"protocol_remark": "独立于协议的多播 ", "protocol_type": "PIM"}, "104": {"protocol_remark": "ARIS ", "protocol_type": "ARIS"}, "105": {"protocol_remark": "SCPS ", "protocol_type": "SCPS"}, "106": {"protocol_remark": "QNX ", "protocol_type": "QNX"}, "80": {"protocol_remark": "ISO Internet 协议 ", "protocol_type": "ISO-IP"}, "107": {"protocol_remark": "活动网络 ", "protocol_type": "A/N"}, "81": {"protocol_remark": "VMTP ", "protocol_type": "VMTP"}, "108": {"protocol_remark": "IP负载压缩协议 ", "protocol_type": "IPComp"}, "82": {"protocol_remark": "SECURE-VMTP ", "protocol_type": "SECURE-VMTP"}, "109": {"protocol_remark": "Sitara网络协议 ", "protocol_type": "SNP"}, "83": {"protocol_remark": "VINES ", "protocol_type": "VINES"}, "84": {"protocol_remark": "TTP ", "protocol_type": "TTP"}, "85": {"protocol_remark": "NSFNET-IGP ", "protocol_type": "NSFNET-IGP"}, "86": {"protocol_remark": "异类网关协议 ", "protocol_type": "DGP"}, "87": {"protocol_remark": "TCF ", "protocol_type": "TCF"}}