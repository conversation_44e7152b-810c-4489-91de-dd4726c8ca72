package com.geeksec.common.utils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class IndexContext {
    //private static Long AccCount_task_0 = 0L;
    //private static Long AccCount_task_1 = 0L;
    private static IndexContext instance = null;
    private  static RedisUtils Redis = new RedisUtils();
    private static Map<String,Long>  IndexNameMap =  new HashMap<String,Long>();
    private static final Logger LOG = LoggerFactory.getLogger(IndexContext.class);

    private IndexContext() {
        //Redis = new RedisUtils("host.docker.internal",6379);
        Redis.initJedisPool();
    }

    public static IndexContext getInstance() {
        if (instance == null) {
            synchronized (IndexContext.class) {
                instance = new IndexContext();
            }
        }
        return instance;
    }


    public String IndexToNum(String Type, String es_key) {
        String Index = es_key;
        Long AccCount_task_0 = 0L;
        List<String> Tlist = Arrays.asList(es_key.split("_"));
        String TaskId = Tlist.get(1);
        String  redis_key =  "flinkgetkey"+Tlist.get(1) + Tlist.get(2)+Tlist.get(3);
        //LOG.info("redis_key ==== "+redis_key);
        if (IndexNameMap.containsKey(redis_key)) {
            AccCount_task_0 = IndexNameMap.get(redis_key);

        } else {
            String value =  Redis.getValue(redis_key) ;
            if (value != null) {
                AccCount_task_0 =  Long.valueOf(value);
            }else {
                AccCount_task_0 = 0L;
            }
            IndexNameMap.put(redis_key,AccCount_task_0);
        }
        Long Num = 100000001L;
        if (Type.contains("3009")) {
            AccCount_task_0 += 1L;
            IndexNameMap.put(redis_key,AccCount_task_0);
             //  提交数据//
             if (AccCount_task_0 > 0 &&  AccCount_task_0%10000000 == 0 ) {
                String value =  Redis.getValue(redis_key) ;
                 //LOG.info("value ==== "+value);
                if (value != null) {
                    if (Long.valueOf(value)  > AccCount_task_0) {
                        AccCount_task_0 = Long.valueOf(value) ;
                    }
                }
            //存储3天
             Redis.setValue(es_key,String.valueOf(AccCount_task_0),3600*24*3L);
            }
        }
        Num += AccCount_task_0 / Num;
        return Index + "_" + String.valueOf(Num);//+IndexNameMap.get(Num);
    }
    public  void IndexRefresh() {

    }
    public  void close() {
        //Redis.colseRedis();
    }
}
