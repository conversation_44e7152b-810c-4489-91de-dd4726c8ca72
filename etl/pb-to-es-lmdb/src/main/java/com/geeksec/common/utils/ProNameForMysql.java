package com.geeksec.common.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ProNameForMysql {

    private static final Logger logger = LoggerFactory.getLogger(ProNameForMysql.class);

    private static ProNameForMysql instance = null;
    private static Map<Integer, String> ProNameMap = new HashMap<Integer, String>();

    private static  String readInputStream(InputStream inputStream) throws IOException {
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
        String tmp;
        StringBuilder sb = new StringBuilder();
        while ((tmp = reader.readLine()) != null) {
            sb.append(tmp).append("\n");
        }
        if (sb.length() > 0 && sb.charAt(sb.length() - 1) == '\n') {
            sb.setLength(sb.length() - 1);
        }
        reader.close();
        return sb.toString();
    }

    private ProNameForMysql() {
        try {
            InputStream in = this.getClass().getResourceAsStream("/proto_type.json");

            JSONObject jsonObject = new JSONObject(readInputStream(in));
            Iterator sIterator = jsonObject.keys();
            //Iterator<String> sIterator = jsonObject.keys();

            while(sIterator.hasNext()){
                String key =(String) sIterator.next();
                JSONObject tvalue = jsonObject.getJSONObject(key);
                String ProName = tvalue.getString("protocol_type");
//                System.out.println("key ==== "+key);
//                System.out.println("ProName ==== "+ProName);
                ProNameMap.put(Integer.getInteger(key),ProName);
            }

            ProNameMap.put(88,"EIGRP");
            ProNameMap.put(89,"OSPFIGP");
            ProNameMap.put(110,"Compaq-Peer");
            ProNameMap.put(111,"IPX-in-IP");
            ProNameMap.put(112,"VRRP");
            ProNameMap.put(113,"PGM");
            ProNameMap.put(114,"");
            ProNameMap.put(115,"L2TP");
            ProNameMap.put(116,"DDX");
            ProNameMap.put(90,"Sprite-RPC");
            ProNameMap.put(117,"IATP");
            ProNameMap.put(91,"LARP");
            ProNameMap.put(118,"STP");
            ProNameMap.put(92,"MTP");
            ProNameMap.put(119,"SRP");
            ProNameMap.put(93,"AX.25");
            ProNameMap.put(94,"IPIP");
            ProNameMap.put(95,"MICP");
            ProNameMap.put(96,"SCC-SP");
            ProNameMap.put(97,"ETHERIP");
            ProNameMap.put(10,"BBN-RCC-MON");
            ProNameMap.put(98,"ENCAP");
            ProNameMap.put(11,"NVP-II");
            ProNameMap.put(99,"");
            ProNameMap.put(12,"PUP");
            ProNameMap.put(13,"ARGUS");
            ProNameMap.put(14,"EMCON");
            ProNameMap.put(15,"XNET");
            ProNameMap.put(16,"CHAOS");
            ProNameMap.put(17,"UDP");
            ProNameMap.put(18,"MUX");
            ProNameMap.put(19,"DCN-MEAS");
            ProNameMap.put(120,"UTI");
            ProNameMap.put(0,"HOPOPT");
            ProNameMap.put(121,"SMP");
            ProNameMap.put(1,"ICMP");
            ProNameMap.put(122,"SM");
            ProNameMap.put(2,"IGMP");
            ProNameMap.put(123,"PTP");
            ProNameMap.put(3,"GGP");
            ProNameMap.put(124,"ISIS");
            ProNameMap.put(4,"IP");
            ProNameMap.put(125,"FIRE ");
            ProNameMap.put(5,"ST");
            ProNameMap.put(126,"CRTP");
            ProNameMap.put(6,"TCP");
            ProNameMap.put(127,"CRUDP");
            ProNameMap.put(7,"CBT");
            ProNameMap.put(128,"SSCOPMCE ");
            ProNameMap.put(8,"EGP");
            ProNameMap.put(129,"IPLT ");
            ProNameMap.put(9," IGP");
            ProNameMap.put(20,"HMP");
            ProNameMap.put(21,"PRM");
            ProNameMap.put(22,"XNS-IDP");
            ProNameMap.put(23,"TRUNK-1");
            ProNameMap.put(24,"TRUNK-2");
            ProNameMap.put(25,"LEAF-1");
            ProNameMap.put(26,"LEAF-2");
            ProNameMap.put(27,"RDP");
            ProNameMap.put(28,"IRTP");
            ProNameMap.put(29,"ISO-TP4");
            ProNameMap.put(130,"SPS");
            ProNameMap.put(131,"PIPE");
            ProNameMap.put(132,"SCTP");
            ProNameMap.put(133,"FC");
            ProNameMap.put(30,"NETBLT");
            ProNameMap.put(31,"MFE-NSP");
            ProNameMap.put(32,"MERIT-INP");
            ProNameMap.put(33,"SEP");
            ProNameMap.put(34,"3PC");
            ProNameMap.put(35,"IDPR");
            ProNameMap.put(36,"XTP");
            ProNameMap.put(37,"DDP");
            ProNameMap.put(38,"IDPR-CMTP");
            ProNameMap.put(39,"TP++");
            ProNameMap.put(40,"IL");
            ProNameMap.put(41,"IPv6");
            ProNameMap.put(42,"SDRP");
            ProNameMap.put(43,"IPv6-Route");
            ProNameMap.put(44,"IPv6-Frag");
            ProNameMap.put(45,"IDRP");
            ProNameMap.put(46,"RSVP");
            ProNameMap.put(47,"GRE");
            ProNameMap.put(48,"MHRP");
            ProNameMap.put(49,"BNA");
            ProNameMap.put(50,"ESP");
            ProNameMap.put(51,"AH");
            ProNameMap.put(52,"I-NLSP");
            ProNameMap.put(53,"SWIPE");
            ProNameMap.put(54,"NARP");
            ProNameMap.put(55,"MOBILE");
            ProNameMap.put(56,"TLSP");
            ProNameMap.put(57,"SKIP");
            ProNameMap.put(58,"IPv6-ICMP");
            ProNameMap.put(59,"IPv6-NoNxt");
            ProNameMap.put(60,"IPv6-Opts");
            ProNameMap.put(61,"");
            ProNameMap.put(62,"CFTP");
            ProNameMap.put(63,"");
            ProNameMap.put(64,"SAT-EXPAK");
            ProNameMap.put(65,"KRYPTOLAN");
            ProNameMap.put(66,"RVD");
            ProNameMap.put(67,"IPPC");
            ProNameMap.put(68,"");
            ProNameMap.put(69,"SAT-MON");
            ProNameMap.put(70,"VISA");
            ProNameMap.put(71,"IPCV");
            ProNameMap.put(72,"CPNX");
            ProNameMap.put(73,"CPHB");
            ProNameMap.put(74,"WSN");
            ProNameMap.put(75,"PVP");
            ProNameMap.put(76,"BR-SAT-MON");
            ProNameMap.put(77,"SUN-ND");
            ProNameMap.put(78,"WB-MON");
            ProNameMap.put(79,"WB-EXPAK");
            ProNameMap.put(100,"GMTP");
            ProNameMap.put(101,"IFMP");
            ProNameMap.put(102,"PNNI");
            ProNameMap.put(103,"PIM");
            ProNameMap.put(104,"ARIS");
            ProNameMap.put(105,"SCPS");
            ProNameMap.put(106,"QNX");
            ProNameMap.put(80,"ISO-IP");
            ProNameMap.put(107,"A/N");
            ProNameMap.put(81,"VMTP");
            ProNameMap.put(108,"IPComp");
            ProNameMap.put(82,"SECURE-VMTP");
            ProNameMap.put(109,"SNP");
            ProNameMap.put(83,"VINES");
            ProNameMap.put(84,"TTP");
            ProNameMap.put(85,"NSFNET-IGP");
            ProNameMap.put(86,"DGP");
            ProNameMap.put(87,"TCF");

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static ProNameForMysql getInstance() {
        if (instance == null) {
            synchronized (ProNameForMysql.class) {
                instance = new ProNameForMysql();
            }
        }
        return instance;
    }

    public static String  GetProName(int pro_id) {
        if (ProNameMap.containsKey(pro_id)) {
            return  ProNameMap.get(pro_id);
        } else {
            return String.valueOf(pro_id);
        }
    }

}
