package com.geeksec.task.JsonSerializationSchema;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import org.apache.flink.api.common.serialization.SerializationSchema;

/**
 * <AUTHOR>
 * @Date 2024/9/20
 */

public class JsonKeySerializationSchema implements SerializationSchema<Map<String, Object>> {
    @Override
    public byte[] serialize(Map<String, Object> stringObjectMap) {
        String Hkey = (String) stringObjectMap.get("Hkey");
        byte[] HkeyByte = Hkey.getBytes(StandardCharsets.UTF_8);
        return HkeyByte;
    }
}
