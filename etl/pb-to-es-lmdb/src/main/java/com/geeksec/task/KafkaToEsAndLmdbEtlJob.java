package com.geeksec.task;

import static com.geeksec.common.function.SplitRawStreamProcessFunction.*;

import com.geeksec.common.function.SessionDataEnrichmentFunction;
import com.geeksec.common.function.SplitRawStreamProcessFunction;
import com.geeksec.common.loader.PropertiesLoader;
import com.geeksec.common.utils.DateUtils;
import com.geeksec.proto.ZMPNMsg;
import com.geeksec.proto.output.*;
import com.geeksec.task.JsonSerializationSchema.JsonKeySerializationSchema;
import com.geeksec.task.JsonSerializationSchema.JsonValueSerializationSchema;
import com.geeksec.task.MyKeySerializer.MyKeySerializationSchema;
import com.geeksec.task.MyKeySerializer.MyValueSerializationSchema;
import com.geeksec.task.sink.ElasticsearchSinkConnector;
import com.geeksec.task.sink.Pb2LmdbSink;
import com.geeksec.task.sink.PbInfoRedisSink;
import com.twitter.chill.protobuf.ProtobufSerializer;
import java.util.Collections;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

import com.geeksec.proto.output.*;
import com.geeksec.task.sink.AppNebulaSinkFunction;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.geeksec.common.function.SessionDataEnrichmentFunction.APP_TAG_OUTPUT;
import static com.geeksec.common.function.SessionDataEnrichmentFunction.DATAMAP_OUTPUT;

public class KafkaToEsAndLmdbEtlJob {

        private static final Logger logger = LoggerFactory.getLogger(KafkaToEsAndLmdbEtlJob.class);

        // 获取当前环境配置
        private static PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");
        public static final String BROKER_LIST = propertiesLoader.getProperty("kafka.broker.list");
        public static final String GROUP_ID = propertiesLoader.getProperty("kafka.group.id");
        public static final String TOPIC = propertiesLoader.getProperty("kafka.topic");
        public static final String ES_HOST = propertiesLoader.getProperty("elasticsearch.conn.host");
        public static final Integer ES_PORT = propertiesLoader.getInteger("elasticsearch.conn.port");
        public static final String LMDB_TOPIC = propertiesLoader.getProperty("kafka.lmdb_topic");
        public static final String JSON_TOPIC = propertiesLoader.getProperty("kafka.json_topic");
        // lmdb配置
        public static final Long LMDB_MAXSIZE = Long.valueOf(propertiesLoader.getProperty("lmdb.maxsize"));
        public static final String lmdbParentDir = propertiesLoader.getProperty("lmdb.path");

        // 并行度配置
        public static final Integer kafkaSourceParallelism = propertiesLoader.getInteger("parallelism.kafka_source");
        public static final Integer parsingParallelism = propertiesLoader.getInteger("parallelism.parsing");
        public static final Integer esSinkParallelism = propertiesLoader.getInteger("parallelism.es_sink");
        public static final Integer kafkaSinkParallelism = propertiesLoader.getInteger("parallelism.kafka_sink");
        public static final Integer kafkaJsonSinkParallelism = propertiesLoader
                        .getInteger("parallelism.kafka_json_sink");
        public static final Integer sessionDataEnrichmentParallelism = propertiesLoader
                        .getInteger("parallelism.update_connect_info");
        public static final Integer qjPbInfoSinkParallelism = propertiesLoader
                        .getInteger("parallelism.qj_pb_info_sink");

        public static void main(String[] args) throws Exception {
                // 设置flink相关配置
                final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
                env.getConfig().registerTypeWithKryoSerializer(ZMPNMsg.JKNmsg.class, ProtobufSerializer.class);
                env.getConfig().registerTypeWithKryoSerializer(DnsInfoOuterClass.DnsInfo.class, ProtobufSerializer.class);
                env.getConfig().registerTypeWithKryoSerializer(HttpInfoOuterClass.HttpInfo.class, ProtobufSerializer.class);
                env.getConfig().registerTypeWithKryoSerializer(NtpInfoOuterClass.NtpInfo.class, ProtobufSerializer.class);
                env.getConfig().registerTypeWithKryoSerializer(IcmpInfoOuterClass.IcmpInfo.class, ProtobufSerializer.class);
                env.getConfig().registerTypeWithKryoSerializer(ProtocolMetadata.ProtocolInfo.class, ProtobufSerializer.class);
                env.getConfig().registerTypeWithKryoSerializer(ProtocolMetadata.MetaInfo.class, ProtobufSerializer.class);
                env.getConfig().registerTypeWithKryoSerializer(SslTlsInfo.Ssl_TlsInfo.class, ProtobufSerializer.class);
                env.getConfig().registerTypeWithKryoSerializer(SshInfoOuterClass.SshInfo.class, ProtobufSerializer.class);
                env.getConfig().registerTypeWithKryoSerializer(X509CerInfoOuterClass.X509CerInfo.class, ProtobufSerializer.class);
                env.setRestartStrategy(RestartStrategies.failureRateRestart(
                                // 最大失败次数
                                3,
                                // 衡量失败次数的是时间段
                                Time.of(1, TimeUnit.MINUTES),
                                // 间隔
                                Time.of(30, TimeUnit.SECONDS)));

                // 设置链式任务和重置时间点
                env.disableOperatorChaining();
                // env.enableCheckpointing(60000L);
                env.enableCheckpointing(10000) // checkpoint every 10000 msecs
                                .getCheckpointConfig()
                                .setCheckpointingMode(CheckpointingMode.AT_LEAST_ONCE);

                // 创建Kafka properties
                Properties properties = new Properties();
                properties.put("bootstrap.servers", BROKER_LIST);
                // 优化参数
                properties.put(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG, "60000");
                properties.put("batch.size", 409600);
                properties.put("linger.ms", 300);
                properties.put("buffer.memory", 256 * 1024 * 1024);
                properties.put("max.request.size", 10 * 1024 * 1024);
                properties.setProperty("group.id", GROUP_ID);
                properties.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, 5000);
                logger.info("当前kafka地址:{},group id:{}", BROKER_LIST, GROUP_ID);

                KafkaSource<ZMPNMsg.JKNmsg> consumer = KafkaSource.<ZMPNMsg.JKNmsg>builder()
                                .setBootstrapServers(properties.getProperty("bootstrap.servers"))
                                .setTopics(Collections.singletonList(TOPIC))
                                .setStartingOffsets(OffsetsInitializer.latest())// 从最新的地方开始取
                                .setDeserializer(new KafkaProtoMetricDeserializer())
                                .setGroupId(properties.getProperty("group.id"))
                                .setProperties(properties)
                                .build();

                // 1.开始读取Kafka PB数据流至DataStream(进行transfer转换为JKNmsg对象实体)
                DataStream<ZMPNMsg.JKNmsg> rawStream = env.fromSource(
                                consumer,
                                WatermarkStrategy.noWatermarks(),
                                "Kafka-Source",
                                TypeInformation.of(ZMPNMsg.JKNmsg.class))
                                .setParallelism(kafkaSourceParallelism).name("Probe Kafka Topic meta");

                // 2.转化pb数据
                SingleOutputStreamOperator<Object> sideOutStream = rawStream
                                .process(new SplitRawStreamProcessFunction())
                                .setParallelism(parsingParallelism).name("Parsing Data");

                SingleOutputStreamOperator<Map<String, Object>> enrichedAllStream = sideOutStream.getSideOutput(ES_STREAM)
                                .process(new SessionDataEnrichmentFunction())
                                .name("Session Metadata Enrichment").setParallelism(sessionDataEnrichmentParallelism);
                DataStream<Map<String, Object>> enrichedStream = enrichedAllStream.getSideOutput(DATAMAP_OUTPUT);
                DataStream<Map<String, Object>> appTagStream = enrichedAllStream.getSideOutput(APP_TAG_OUTPUT);
                AppNebulaSinkFunction.insertAppHasLabel(appTagStream);
                // ES sink
                ElasticsearchSinkConnector.configurePcapSink(enrichedStream);

                // redis缓存外发的元数据，做30分钟缓存
                enrichedStream.addSink(new PbInfoRedisSink()).name("Pb Info Sink To Redis").setParallelism(qjPbInfoSinkParallelism);

                // Kafka Json Sink to kafka
                KafkaSink<Map<String, Object>> kafkaJsonSink = KafkaSink.<Map<String, Object>>builder()
                                .setBootstrapServers(properties.getProperty("bootstrap.servers"))
                                .setRecordSerializer(KafkaRecordSerializationSchema.<Map<String, Object>>builder()
                                                .setTopic(JSON_TOPIC)
                                                .setKeySerializationSchema(new JsonKeySerializationSchema())
                                                .setValueSerializationSchema(new JsonValueSerializationSchema())
                                                .build())
                                .build();

                // Kafka Json sink
                enrichedStream.sinkTo(kafkaJsonSink).name("Kafka JSON Sink")
                                .setParallelism(kafkaJsonSinkParallelism);

                // Kafka PB Sink to LMDB topic
                KafkaSink<Tuple2<byte[], byte[]>> kafkaSink = KafkaSink.<Tuple2<byte[], byte[]>>builder()
                                .setBootstrapServers(properties.getProperty("bootstrap.servers"))
                                .setRecordSerializer(KafkaRecordSerializationSchema.<Tuple2<byte[], byte[]>>builder()
                                                .setTopic(LMDB_TOPIC)
                                                .setKeySerializationSchema(new MyKeySerializationSchema())
                                                .setValueSerializationSchema(new MyValueSerializationSchema())
                                                .build())
                                .build();

                sideOutStream.getSideOutput(KAFKA_LMDB_STREAM).sinkTo(kafkaSink).name("Kafka LMDB Sink")
                                .setParallelism(kafkaSinkParallelism);
                // Sink to Lmdb
                sideOutStream.getSideOutput(KAFKA_LMDB_STREAM).addSink(new Pb2LmdbSink()).name("LMDB Sink").setParallelism(1);
                logger.info("Start ETL FROM KAFKA TO ES & LMDB TOPIC , current time ---> {}",
                                DateUtils.getDateString());
                env.execute("GSF02-ES元数据入库-LMDB-Topic输出-Json-Topic输出");
        }
}
