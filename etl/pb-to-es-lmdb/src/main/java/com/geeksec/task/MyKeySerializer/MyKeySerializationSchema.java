package com.geeksec.task.MyKeySerializer;

import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.api.java.tuple.Tuple2;

/**
 * <AUTHOR>
 * @Date 2023/4/17
 */

public class MyKeySerializationSchema implements SerializationSchema<Tuple2<byte[], byte[]>> {
    @Override
    public byte[] serialize(Tuple2<byte[], byte[]> tuple2) {
        return tuple2.f0;
    }
}
