package com.geeksec.task.sink;

import static com.geeksec.task.KafkaToEsAndLmdbEtlJob.esSinkParallelism;

import com.geeksec.common.loader.PropertiesLoader;
import com.geeksec.common.utils.IndexContext;
import com.geeksec.common.utils.LabelRedisUtils;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.connectors.elasticsearch.ElasticsearchSinkFunction;
import org.apache.flink.streaming.connectors.elasticsearch.RequestIndexer;
import org.apache.flink.streaming.connectors.elasticsearch.util.RetryRejectedExecutionFailureHandler;
import org.apache.flink.streaming.connectors.elasticsearch7.ElasticsearchSink;
import org.apache.flink.streaming.connectors.elasticsearch7.RestClientFactory;
import org.apache.http.HttpHost;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.Requests;
import org.elasticsearch.client.RestClientBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * <AUTHOR>
 * @Date 2024/1/10
 */

public class ElasticsearchSinkConnector {
    private static final Logger logger = LoggerFactory.getLogger(ElasticsearchSinkConnector.class);
    private static PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");
    public static final String ES_HOST = propertiesLoader.getProperty("elasticsearch.conn.host");
    public static final Integer ES_PORT = propertiesLoader.getInteger("elasticsearch.conn.port");
    public static final String REDIS_HTTP_HEADER = "HTTP_HEADER";

    public static void configurePcapSink(DataStream<Map<String, Object>> pcapStream) {
        ElasticsearchSink.Builder<Map<String, Object>> sinkBuilder = createSinkBuilder();
        configureSinkProperties(sinkBuilder);
        configureRestClient(sinkBuilder);

        pcapStream.addSink(sinkBuilder.build())
                .name("Elasticsearch Sink")
                .setParallelism(esSinkParallelism);
    }

    private static ElasticsearchSink.Builder<Map<String, Object>> createSinkBuilder() {
        List<HttpHost> httpHosts = Collections.singletonList(
                new HttpHost(ES_HOST, ES_PORT, "http"));

        return new ElasticsearchSink.Builder<>(httpHosts, new PcapElasticsearchSinkFunction());
    }

    private static void configureSinkProperties(ElasticsearchSink.Builder<Map<String, Object>> builder) {
        builder.setBulkFlushInterval(2500);
        builder.setBulkFlushMaxSizeMb(100);
        builder.setBulkFlushMaxActions(500);
        builder.setBulkFlushBackoff(true);
        builder.setBulkFlushBackoffRetries(2);
        builder.setFailureHandler(new RetryRejectedExecutionFailureHandler());
    }

    private static void configureRestClient(ElasticsearchSink.Builder<Map<String, Object>> builder) {
        builder.setRestClientFactory(new RestClientFactory() {
            @Override
            public void configureRestClientBuilder(RestClientBuilder restClientBuilder) {
                restClientBuilder.setHttpClientConfigCallback(httpClientBuilder -> {
                    httpClientBuilder.disableAuthCaching();
                    httpClientBuilder.setKeepAliveStrategy(
                            (response, context) -> TimeUnit.MINUTES.toMillis(5));
                    return httpClientBuilder;
                });

                restClientBuilder.setRequestConfigCallback(
                        requestConfigBuilder -> requestConfigBuilder.setConnectionRequestTimeout(1000 * 60 * 2)
                                .setSocketTimeout(1000 * 60 * 2));
            }
        });
    }

    private static class PcapElasticsearchSinkFunction
            implements ElasticsearchSinkFunction<Map<String, Object>> {

        private static final String CONNECT_INFO_PREFIX = "connectinfo_";
        private static final String HTTP_PREFIX = "http_";
        private static final String SSL_PREFIX = "ssl_";
        private static final String DEFAULT_TYPE = "148";
        private static final String CONNECT_INFO_TYPE = "3009";

        private static transient JedisPool jedisPool = null;

        @Override
        public void open() throws Exception {
            // redis池 初始化.
            jedisPool = LabelRedisUtils.initJedisPool();
            logger.info("生成jedisPool成功! {}", jedisPool.getNumIdle(),jedisPool.hashCode());
            ElasticsearchSinkFunction.super.open();
        }

        @Override
        public void close() throws Exception {
            ElasticsearchSinkFunction.super.close();
            if(!jedisPool.isClosed()){
                jedisPool.close();
            }
        }

        @Override
        public void process(Map<String, Object> data, RuntimeContext context,
                RequestIndexer indexer) {
            indexer.add(createIndexRequest(data));
        }

        private IndexRequest createIndexRequest(Map<String, Object> data) {
            String esKey = String.valueOf(data.get("es_key"));
            String type = determineDocumentType(esKey, data);
            String index = IndexContext.getInstance().IndexToNum(type, esKey);

            try {
                return Requests.indexRequest()
                        .index(index)
                        .source(data);
            } catch (Exception e) {
                logger.error("Failed to write to ES, index: " + index, e);
                return null;
            }
        }

        private String determineDocumentType(String esKey, Map<String, Object> data) {
            if (esKey.startsWith(CONNECT_INFO_PREFIX)) {
                cleanupConnectInfo(data);
                return CONNECT_INFO_TYPE;
            }
            if (esKey.startsWith(SSL_PREFIX)) {
                cleanupSsl(data);
            }
            if (esKey.startsWith(HTTP_PREFIX)) {
                Jedis jedis = null;
                try{
                    jedis = LabelRedisUtils.getJedis(jedisPool);
                    // 从db7获取pcap文件路径
                    jedis.select(9);
                    updateHttpHeaders(data,jedis);
                }catch (Exception e){
                    logger.error("alert log构建读取协议元数据失败，读取redis失败，error:——{}——",e.toString());
                }finally {
                    if (jedis != null){
                        jedis.close();
                    }
                }
            }
            return DEFAULT_TYPE;
        }

        private void cleanupSsl(Map<String, Object> data) {
            data.remove("ClientCertList");
            data.remove("ServerCertList");
        }

        private void cleanupConnectInfo(Map<String, Object> data) {
            data.remove("Server4PayloadList");
            data.remove("Client4PayloadList");
            data.remove("pktInfo");
        }

        private void updateHttpHeaders(Map<String, Object> data, Jedis jedis) {
            data.put("Client", HttpHeaderProcessor.extractClientHeaders(data,jedis));
            data.put("Server", HttpHeaderProcessor.extractServerHeaders(data,jedis));
        }
    }
}
