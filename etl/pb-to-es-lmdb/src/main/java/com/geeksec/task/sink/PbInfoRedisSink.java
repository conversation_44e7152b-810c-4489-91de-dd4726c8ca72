package com.geeksec.task.sink;

import com.geeksec.common.utils.LabelRedisUtils;
import com.geeksec.proto.output.*;
import com.google.protobuf.ByteString;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * <AUTHOR>
 */
public class PbInfoRedisSink extends RichSinkFunction<Map<String, Object>> {

    private static transient JedisPool jedisPool = null;
    private final static Logger logger = LoggerFactory.getLogger(PbInfoRedisSink.class);
    private static Map<Integer,String> QUERY_TYPE_MAP = new HashMap<Integer,String>(){{
        put(1,"A");
        put(28,"AAAA");
        put(5,"CNAME");
        put(15,"MX");
        put(47,"NSEC");
        put(12,"PTR");
        put(16,"TXT");
        put(2,"NS");
    }};
    private static final Integer REDIS_PCAP_FILE_EXPIRE_TIME = 10800;
    private static final Integer REDIS_PROTOCOL_PB_TRANS_INFO_EXPIRE_TIME = 3600;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // redis池 初始化.
        jedisPool = LabelRedisUtils.initJedisPool();
        logger.info("生成jedisPool成功! {}", jedisPool.getNumIdle(),jedisPool.hashCode());
    }

    @Override
    public void close() throws Exception {
        super.close();
        if(!jedisPool.isClosed()){
            jedisPool.close();
        }
    }

    @Override
    public void invoke(Map<String, Object> pbMap, Context context) throws Exception {

        // 会话及协议元数据转化
        String type = (String) pbMap.get("type");
        String sessionId = (String) pbMap.get("SessionId");
        try (Jedis jedis = LabelRedisUtils.getJedis(jedisPool)) {
            // ConnectionInfo 抽取pcap路径信息，sessionId信息
            if ("connect".equals(type)) {
                jedis.select(7);
                String pcapFile = (String) pbMap.getOrDefault("PcapFile", "");
                if (!pcapFile.isEmpty()) {
                    jedis.sadd(sessionId, pcapFile);
                    jedis.expire(sessionId, REDIS_PCAP_FILE_EXPIRE_TIME);
                } else {
                    logger.error("会话ID为：:{}的会话元数据没有PcapFile", sessionId, type);
                }

            }
            // 协议元数据按照sessionId缓存,使用byte[]存储
            else {
                jedis.select(7);
                String pcapFile = (String) pbMap.getOrDefault("PcapFile", "");
                if (!pcapFile.isEmpty()) {
                    jedis.sadd(sessionId, pcapFile);
                    jedis.expire(sessionId, REDIS_PCAP_FILE_EXPIRE_TIME);
                } else {
                    logger.error("会话ID为：:{}的{}协议元数据没有PcapFile", sessionId, type);
                }

                jedis.select(8);
                sinkMapToPb(pbMap, jedis, sessionId);
            }
        } catch (Exception e) {
            logger.error("PB信息写入redis失败，写入type：——{}——，,写入信息：——{}——，error：——{}——", type, pbMap, e.toString());
        }
    }

    private void sinkMapToPb(Map<String, Object> pbMap, Jedis jedis, String sessionId) {
        String type = (String) pbMap.get("type");
        switch (type) {
            case "http":
                String httpKey = sessionId + "_"  + "http";
                byte[] httpPbInfo = tranHttpToPb(pbMap);
                // Http 抽取 sessionId:PB 信息
                jedis.lpush(httpKey.getBytes(StandardCharsets.UTF_8),httpPbInfo);
                jedis.expire(httpKey.getBytes(StandardCharsets.UTF_8),REDIS_PROTOCOL_PB_TRANS_INFO_EXPIRE_TIME);
            case "dns":
                // DNS 抽取 sessionId:PB 信息
                String dnsKey = sessionId + "_"  + "dns";
                byte[] dnsPbInfo = tranDnsToPb(pbMap);
                // Http 抽取 sessionId:PB 信息
                jedis.lpush(dnsKey.getBytes(StandardCharsets.UTF_8),dnsPbInfo);
                jedis.expire(dnsKey.getBytes(StandardCharsets.UTF_8),REDIS_PROTOCOL_PB_TRANS_INFO_EXPIRE_TIME);
            case "ssl":
                // SSL 抽取 sessionId:PB 信息
                String sslKey = sessionId + "_"  + "ssl";
                byte[] sslPbInfo = tranSslToPb(pbMap);
                // Http 抽取 sessionId:PB 信息
                jedis.lpush(sslKey.getBytes(StandardCharsets.UTF_8),sslPbInfo);
                jedis.expire(sslKey.getBytes(StandardCharsets.UTF_8),REDIS_PROTOCOL_PB_TRANS_INFO_EXPIRE_TIME);

                List<Map<String,Object>> sCertHashList = (List<Map<String, Object>>) pbMap.getOrDefault("ClientCertList",new ArrayList<>());
                List<Map<String,Object>> dCertHashList = (List<Map<String, Object>>) pbMap.getOrDefault("ServerCertList",new ArrayList<>());
                if (!sCertHashList.isEmpty()){
                    tranX509ToPb(sCertHashList,"clientCert",jedis,sessionId);
                }
                if(!dCertHashList.isEmpty()){
                    tranX509ToPb(dCertHashList,"serverCert",jedis,sessionId);
                }
            case "ntp":
                // ntp 抽取 sessionId:PB 信息
                String ntpKey = sessionId + "_"  + "ntp";
                byte[] ntpPbInfo = tranNtpToPb(pbMap);
                // Http 抽取 sessionId:PB 信息
                jedis.lpush(ntpKey.getBytes(StandardCharsets.UTF_8),ntpPbInfo);
                jedis.expire(ntpKey.getBytes(StandardCharsets.UTF_8),REDIS_PROTOCOL_PB_TRANS_INFO_EXPIRE_TIME);
            case "icmp":
                // icmp 抽取 sessionId:PB 信息
                String icmpKey = sessionId + "_"  + "icmp";
                byte[] icmpPbInfo = tranIcmpToPb(pbMap);
                // Http 抽取 sessionId:PB 信息
                jedis.lpush(icmpKey.getBytes(StandardCharsets.UTF_8),icmpPbInfo);
                jedis.expire(icmpKey.getBytes(StandardCharsets.UTF_8),REDIS_PROTOCOL_PB_TRANS_INFO_EXPIRE_TIME);
            case "ssh":
                // icmp 抽取 sessionId:PB 信息
                String sshKey = sessionId + "_"  + "ssh";
                byte[] sshPbInfo = tranSshToPb(pbMap);
                // Http 抽取 sessionId:PB 信息
                jedis.lpush(sshKey.getBytes(StandardCharsets.UTF_8),sshPbInfo);
                jedis.expire(sshKey.getBytes(StandardCharsets.UTF_8),REDIS_PROTOCOL_PB_TRANS_INFO_EXPIRE_TIME);
            default:
                logger.info("type不存在，或者不需要处理");
        }
    }

    private void tranX509ToPb(List<Map<String,Object>> certInfoList, String dir, Jedis jedis, String sessionId) {
        int certDir = 0;
        String key;
        if ("clientCert".equals(dir)){
            key = sessionId + "_"  + "clientCert";
        }else {
            certDir = 1;
            key = sessionId + "_"  + "serverCert";
        }
        for (Map<String,Object> certInfo : certInfoList) {
            X509CerInfoOuterClass.X509CerInfo.Builder certBuilder = X509CerInfoOuterClass.X509CerInfo.newBuilder();
            String hash = (String) certInfo.getOrDefault("hash","");
            String issuer = (String) certInfo.getOrDefault("issuer","");
            String subject = (String) certInfo.getOrDefault("subject","");
            certBuilder.setHash(ByteString.copyFrom(hash,StandardCharsets.UTF_8));
            certBuilder.setFpAlg(ByteString.copyFrom("SHA1",StandardCharsets.UTF_8));
            certBuilder.setSource(certDir);
            certBuilder.setIssuer(ByteString.copyFrom(issuer,StandardCharsets.UTF_8));
            certBuilder.setSubject(ByteString.copyFrom(subject,StandardCharsets.UTF_8));

            jedis.lpush(key.getBytes(StandardCharsets.UTF_8),certBuilder.build().toByteArray());
            jedis.expire(key.getBytes(StandardCharsets.UTF_8),REDIS_PROTOCOL_PB_TRANS_INFO_EXPIRE_TIME);
        }


    }

    private byte[] tranSshToPb(Map<String, Object> pbMap) {
        SshInfoOuterClass.SshInfo.Builder sshInfo= SshInfoOuterClass.SshInfo.newBuilder();
        Map<String, Object> clientMap = (Map<String, Object>) pbMap.get("Client");
        Map<String, Object> serverMap = (Map<String, Object>) pbMap.get("Server");

        String cliVer = (String) clientMap.getOrDefault("Protocol","");
        String cliCookie = (String) clientMap.getOrDefault("Cookie","");
        String cliKeyExcAndAutMet = (String) clientMap.getOrDefault("KexAlgorithms","");
        String cliHostKeyAlg = (String) clientMap.getOrDefault("ServerHostKeyAlgorithms","");
        String cliEncryAlg = (String) clientMap.getOrDefault("EncryptionAlgorithmsC2S","");
        String cliComprAlg = (String) clientMap.getOrDefault("CompressionAlgorithmsC2S","");

        String srvVer = (String) serverMap.getOrDefault("Protocol","");
        String srvCookie = (String) serverMap.getOrDefault("Cookie","");
        String srvKeyExcAndAutMet = (String) serverMap.getOrDefault("KexAlgorithms","");
        String srvHostKeyAlg = (String) serverMap.getOrDefault("ServerHostKeyAlgorithms","");
        String srvEncryAlg = (String) serverMap.getOrDefault("EncryptionAlgorithmsS2C","");
        String srvComprAlg = (String) serverMap.getOrDefault("CompressionAlgorithmsS2C","");

        sshInfo.setCliVer(ByteString.copyFrom(cliVer,StandardCharsets.UTF_8));
        sshInfo.setCliCookie(ByteString.copyFrom(cliCookie,StandardCharsets.UTF_8));
        sshInfo.setCliKeyExcAndAutMet(ByteString.copyFrom(cliKeyExcAndAutMet,StandardCharsets.UTF_8));
        sshInfo.setCliHostKeyAlg(ByteString.copyFrom(cliHostKeyAlg,StandardCharsets.UTF_8));
        sshInfo.setCliEncryAlg(ByteString.copyFrom(cliEncryAlg,StandardCharsets.UTF_8));
        sshInfo.setCliComprAlg(ByteString.copyFrom(cliComprAlg,StandardCharsets.UTF_8));
        sshInfo.setSrvVer(ByteString.copyFrom(srvVer,StandardCharsets.UTF_8));
        sshInfo.setSrvCookie(ByteString.copyFrom(srvCookie,StandardCharsets.UTF_8));
        sshInfo.setSrvKeyExcAndAuthMet(ByteString.copyFrom(srvKeyExcAndAutMet,StandardCharsets.UTF_8));
        sshInfo.setSrvHostKeyAlg(ByteString.copyFrom(srvHostKeyAlg,StandardCharsets.UTF_8));
        sshInfo.setSrvEncryAlg(ByteString.copyFrom(srvEncryAlg,StandardCharsets.UTF_8));
        sshInfo.setSrvComprAlg(ByteString.copyFrom(srvComprAlg,StandardCharsets.UTF_8));

        return sshInfo.build().toByteArray();
    }

    private byte[] tranIcmpToPb(Map<String, Object> pbMap) {
        IcmpInfoOuterClass.IcmpInfo.Builder icmpInfo= IcmpInfoOuterClass.IcmpInfo.newBuilder();

        Integer msgType = (Integer) pbMap.getOrDefault("msgType",0);
        Integer infoCode = (Integer) pbMap.getOrDefault("infoCode",0);
        Integer echoSeqNum = (Integer) pbMap.getOrDefault("echoSeqNum",0);
        String dataCon = (String) pbMap.getOrDefault("dataCon","");
        String unrSrcAddr = (String) pbMap.getOrDefault("unrSrcAddr","");
        String unrDstAddr = (String) pbMap.getOrDefault("unrDstAddr","");
        Integer unrProt = (Integer) pbMap.getOrDefault("unrProt",0);
        Integer uncTTL = (Integer) pbMap.getOrDefault("uncTTL",0);
        Integer ver = (Integer) pbMap.getOrDefault("ver",0);
        Long origTimeStamp = (Long) pbMap.getOrDefault("origTimeStamp",0L);
        Long recvTimeStamp = (Long) pbMap.getOrDefault("recvTimeStamp",0L);
        Long transTimeStamp = (Long) pbMap.getOrDefault("transTimeStamp",0L);
        Integer mask = (Integer) pbMap.getOrDefault("mask",0);
        Integer subNetId = (Integer) pbMap.getOrDefault("subNetId",0);
        Integer rtrTimeOut = (Integer) pbMap.getOrDefault("rtrTimeOut",0);
        String excSrcAddr = (String) pbMap.getOrDefault("excSrcAddr","");
        String excDstAddr = (String) pbMap.getOrDefault("excDstAddr","");
        Integer excProt = (Integer) pbMap.getOrDefault("excProt",0);
        Integer excSrcPort = (Integer) pbMap.getOrDefault("excSrcPort",0);
        Integer excDstPort = (Integer) pbMap.getOrDefault("excDstPort",0);
        String gwAddr = (String) pbMap.getOrDefault("gwAddr","");
        Integer ttl = (Integer) pbMap.getOrDefault("ttl",0);
        Integer repTtl = (Integer) pbMap.getOrDefault("repTtl",0);
        Integer qurType = (Integer) pbMap.getOrDefault("qurType",0);
        String qurIpv6Addr = (String) pbMap.getOrDefault("qurIpv6Addr","");
        String qurIpv4Addr = (String) pbMap.getOrDefault("qurIpv4Addr","");
        String qurDNS = (String) pbMap.getOrDefault("qurDNS","");
        Integer ndpLifeTime = (Integer) pbMap.getOrDefault("ndpLifeTime",0);
        String ndpLinkAddr = (String) pbMap.getOrDefault("ndpLinkAddr","");
        Integer ndpPreLen = (Integer) pbMap.getOrDefault("ndpPreLen",0);
        String ndpPreFix = (String) pbMap.getOrDefault("ndpPreFix","");
        Integer ndpValLifeTime = (Integer) pbMap.getOrDefault("ndpValLifeTime",0);
        Integer ndpCurMtu = (Integer) pbMap.getOrDefault("ndpCurMtu",0);
        String ndpTarAddr = (String) pbMap.getOrDefault("ndpTarAddr","");
        Integer nextHopMtu = (Integer) pbMap.getOrDefault("nextHopMtu",0);
        Integer excPointer = (Integer) pbMap.getOrDefault("excPointer",0);
        String mulCastAddr = (String) pbMap.getOrDefault("mulCastAddr","");
        Integer checkSum = (Integer) pbMap.getOrDefault("checkSum",0);
        Integer checkSumReply = (Integer) pbMap.getOrDefault("checkSumReply",0);
        Integer rtraddr = (Integer) pbMap.getOrDefault("rtraddr",0);
        Long resTime = (Long) pbMap.getOrDefault("resTime",0L);
        Integer excTTL = (Integer) pbMap.getOrDefault("excTTL",0);
        Long ResponseTime = (Long) pbMap.getOrDefault("ResponseTime",0L);
        Integer unreachableSourcePort = (Integer) pbMap.getOrDefault("unreachableSourcePort",0);
        Integer unreachableDestinationPort = (Integer) pbMap.getOrDefault("unreachableDestinationPort",0);


        icmpInfo.setMsgType(msgType);
        icmpInfo.setInfoCode(infoCode);
        icmpInfo.setEchoSeqNum(echoSeqNum);
        icmpInfo.setDataCon(ByteString.copyFrom(dataCon,StandardCharsets.UTF_8));
        icmpInfo.setUnrSrcAddr(ByteString.copyFrom(unrSrcAddr,StandardCharsets.UTF_8));
        icmpInfo.setUnrDstAddr(ByteString.copyFrom(unrDstAddr,StandardCharsets.UTF_8));
        icmpInfo.setUnrProt(unrProt);
        icmpInfo.setUncTTL(uncTTL);
        icmpInfo.setVer(ver);
        icmpInfo.setOrigTimeStamp(origTimeStamp);
        icmpInfo.setRecvTimeStamp(recvTimeStamp);
        icmpInfo.setTransTimeStamp(transTimeStamp);
        icmpInfo.setMask(mask);
        icmpInfo.setSubNetId(subNetId);
        icmpInfo.setRtrTimeOut(rtrTimeOut);
        icmpInfo.setExcSrcAddr(ByteString.copyFrom(excSrcAddr,StandardCharsets.UTF_8));
        icmpInfo.setExcDstAddr(ByteString.copyFrom(excDstAddr,StandardCharsets.UTF_8));
        icmpInfo.setExcProt(excProt);
        icmpInfo.setExcSrcPort(excSrcPort);
        icmpInfo.setExcDstPort(excDstPort);
        icmpInfo.setGwAddr(ByteString.copyFrom(gwAddr,StandardCharsets.UTF_8));
        icmpInfo.setTtl(ttl);
        icmpInfo.setRepTtl(repTtl);
        icmpInfo.setQurType(qurType);
        icmpInfo.setQurIpv6Addr(ByteString.copyFrom(qurIpv6Addr,StandardCharsets.UTF_8));
        icmpInfo.setQurIpv4Addr(ByteString.copyFrom(qurIpv4Addr,StandardCharsets.UTF_8));
        icmpInfo.setQurDNS(ByteString.copyFrom(qurDNS,StandardCharsets.UTF_8));
        icmpInfo.setNdpLifeTime(ndpLifeTime);
        icmpInfo.setNdpLinkAddr(ByteString.copyFrom(ndpLinkAddr,StandardCharsets.UTF_8));
        icmpInfo.setNdpPreLen(ndpPreLen);
        icmpInfo.setNdpPreFix(ByteString.copyFrom(ndpPreFix,StandardCharsets.UTF_8));
        icmpInfo.setNdpValLifeTime(ndpValLifeTime);
        icmpInfo.setNdpCurMtu(ndpCurMtu);
        icmpInfo.setNdpTarAddr(ByteString.copyFrom(ndpTarAddr,StandardCharsets.UTF_8));
        icmpInfo.setNextHopMtu(nextHopMtu);
        icmpInfo.setExcPointer(excPointer);
        icmpInfo.setMulCastAddr(ByteString.copyFrom(mulCastAddr,StandardCharsets.UTF_8));
        icmpInfo.setCheckSum(checkSum);
        icmpInfo.setCheckSumReply(checkSumReply);
        icmpInfo.setRtraddr(rtraddr);
        icmpInfo.setResTime(resTime);
        icmpInfo.setExcTTL(excTTL);
        icmpInfo.setResponseTime(ResponseTime);
        icmpInfo.setUnreachableSourcePort(unreachableSourcePort);
        icmpInfo.setUnreachableDestinationPort(unreachableDestinationPort);

        return icmpInfo.build().toByteArray();
    }

    private byte[] tranNtpToPb(Map<String, Object> pbMap) {
        NtpInfoOuterClass.NtpInfo.Builder ntpInfo= NtpInfoOuterClass.NtpInfo.newBuilder();
        Map<String,Object> client = (Map<String, Object>) pbMap.getOrDefault("Client",new HashMap<String, Object>());
        Integer version = (Integer) pbMap.getOrDefault("Version",0);
        Integer Stratum = (Integer) client.getOrDefault("Stratum",0);
        Double RootDelay = (Double) client.getOrDefault("RootDelay",0D);
        Double RootDispersion = (Double) client.getOrDefault("RootDispersion",0D);
        String ReferID = (String) client.getOrDefault("ReferID","");
        Integer ReferTsSec = (Integer) client.getOrDefault("ReferTsSec",0);
        Integer OriginTsSec = (Integer) client.getOrDefault("OriginTsSec",0);
        Integer RecvTsSec = (Integer) client.getOrDefault("RecvTsSec",0);

        ntpInfo.setVer(version);
        ntpInfo.setStratum(Stratum);
        ntpInfo.setRooDelay(RootDelay.intValue());
        ntpInfo.setRooDis(RootDispersion.intValue());
        ntpInfo.setRefID(ByteString.copyFrom(ReferID.toString(),StandardCharsets.UTF_8));
        ntpInfo.setRefTime(Long.valueOf(ReferTsSec.toString()));
        ntpInfo.setOrgTimeStamp(Long.valueOf(OriginTsSec.toString()));
        ntpInfo.setRecvTimeStamp(Long.valueOf(RecvTsSec.toString()));

        return ntpInfo.build().toByteArray();
    }

    private byte[] tranSslToPb(Map<String, Object> pbMap) {
        SslTlsInfo.Ssl_TlsInfo.Builder sslTlsInfo= SslTlsInfo.Ssl_TlsInfo.newBuilder();

        Integer cSSLVersion = (Integer) pbMap.getOrDefault("cSSLVersion",0);
        Integer CH_Version = (Integer) pbMap.getOrDefault("CH_Version",0);
        Integer CH_Time = (Integer) pbMap.getOrDefault("CH_Time",0);
        Integer CH_SessionIDLen = (Integer) pbMap.getOrDefault("CH_SessionIDLen",0);
        Integer CH_CiphersuitNum = (Integer) pbMap.getOrDefault("CH_CiphersuitNum",0);
        Integer CH_CompressionMethodLen = (Integer) pbMap.getOrDefault("CH_CompressionMethodLen",0);
        Integer CH_ExtentionNum = (Integer) pbMap.getOrDefault("CH_ExtentionNum",0);
        Integer CH_ServerNameType = (Integer) pbMap.getOrDefault("CH_ServerNameType",0);
        Integer sCertNum = (Integer) pbMap.getOrDefault("sCertNum",0);
        Integer sKeyExchangeLen = (Integer) pbMap.getOrDefault("sKeyExchangeLen",0);
        Integer sSSLVersion = (Integer) pbMap.getOrDefault("sSSLVersion",0);
        Integer SH_Version = (Integer) pbMap.getOrDefault("SH_Version",0);
        Integer SH_Time = (Integer) pbMap.getOrDefault("SH_Time",0);
        Integer SH_SessionIdLen = (Integer) pbMap.getOrDefault("SH_SessionIdLen",0);
        Integer dCertNum = (Integer) pbMap.getOrDefault("dCertNum",0);
        Integer dKeyExchangelen = (Integer) pbMap.getOrDefault("dKeyExchangelen",0);
        Integer dNewSessionTicket_TicketLen = (Integer) pbMap.getOrDefault("dNewSessionTicket_TicketLen",0);
        Integer dNewSessionTicket_LifeTime = (Integer) pbMap.getOrDefault("dNewSessionTicket_LifeTime","");


        String CH_Ciphersuit = (String) pbMap.getOrDefault("CH_Ciphersuit","");
        String CH_CompressionMethod = (String) pbMap.getOrDefault("CH_CompressionMethod","");
        List<Object> CH_Extention = (List<Object>) pbMap.getOrDefault("CH_Extention",new ArrayList<>());
        String CH_ServerName = (String) pbMap.getOrDefault("CH_ServerName","");
        String CH_SessionTicket = (String) pbMap.getOrDefault("CH_SessionTicket","");
        List<String> CH_ALPN = (List<String>) pbMap.getOrDefault("CH_ALPN",new ArrayList<>());
        List<String> sCertHash = (List<String>) pbMap.getOrDefault("sCertHash",new ArrayList<>());
        String sKeyExchange = (String) pbMap.getOrDefault("sKeyExchange","");
        String sSSLFinger = (String) pbMap.getOrDefault("sSSLFinger","");
        String SH_Random = (String) pbMap.getOrDefault("SH_Random","");
        String SH_SessionId = (String) pbMap.getOrDefault("SH_SessionId","");
        String SH_Cipersuite = (String) pbMap.getOrDefault("SH_Cipersuite","");
        String SH_CompressionMethod = (String) pbMap.getOrDefault("SH_CompressionMethod","");
        String SH_SessionTicket = (String) pbMap.getOrDefault("SH_SessionTicket","");
        String SH_Alpn = (String) pbMap.getOrDefault("SH_Alpn","");
        List<Object> SH_Extention = (List<Object>) pbMap.getOrDefault("SH_Extention",new ArrayList<>());
        List<String> dCertHash = (List<String>) pbMap.getOrDefault("dCertHash",new ArrayList<>());
        String dKeyExchange = (String) pbMap.getOrDefault("dKeyExchange","");
        String dNewSessionTicket_Ticket = (String) pbMap.getOrDefault("dNewSessionTicket_Ticket","");
        String dSSLFinger = (String) pbMap.getOrDefault("dSSLFinger","");

        sslTlsInfo.setCliCertCnt(sCertNum);
        sslTlsInfo.setCliCertHashes(ByteString.copyFrom(sCertHash.toString(),StandardCharsets.UTF_8));
        sslTlsInfo.setSrvCertCnt(dCertNum);
        sslTlsInfo.setSrvCertHashes(ByteString.copyFrom(dCertHash.toString(),StandardCharsets.UTF_8));
        if (sCertHash.size()>0 || dCertHash.size()>0) {
            sslTlsInfo.setCertExist(1);
        }else {
            sslTlsInfo.setCertExist(0);
        }
        sslTlsInfo.setCliExtCnt(CH_Extention.size());
        sslTlsInfo.setSrvExtCnt(SH_Extention.size());
        sslTlsInfo.setCliExt(ByteString.copyFrom(CH_Extention.toString(),StandardCharsets.UTF_8));
        sslTlsInfo.setSrvExt(ByteString.copyFrom(SH_Extention.toString(),StandardCharsets.UTF_8));
        sslTlsInfo.setCliCipSui(ByteString.copyFrom(CH_Ciphersuit,StandardCharsets.UTF_8));
        sslTlsInfo.setSrvCipSui(ByteString.copyFrom(SH_Cipersuite,StandardCharsets.UTF_8));
        sslTlsInfo.setSrvComprMet(ByteString.copyFrom(CH_CompressionMethod,StandardCharsets.UTF_8));
        sslTlsInfo.setSrvComprMet(ByteString.copyFrom(SH_CompressionMethod,StandardCharsets.UTF_8));
        sslTlsInfo.setCliKeyExcLen(ByteString.copyFrom(sKeyExchangeLen.toString(),StandardCharsets.UTF_8));
        sslTlsInfo.setSrvKeyExcLen(ByteString.copyFrom(dKeyExchangelen.toString(),StandardCharsets.UTF_8));
        sslTlsInfo.setCliSesIDLen(ByteString.copyFrom(CH_SessionIDLen.toString(),StandardCharsets.UTF_8));
        sslTlsInfo.setCliSessTicket(ByteString.copyFrom(CH_SessionTicket,StandardCharsets.UTF_8));
        sslTlsInfo.setSrvSesID(ByteString.copyFrom(SH_SessionId,StandardCharsets.UTF_8));
        sslTlsInfo.setSrvSesIDLen(ByteString.copyFrom(SH_SessionIdLen.toString(),StandardCharsets.UTF_8));
        sslTlsInfo.setSrvSessTicket(ByteString.copyFrom(SH_SessionTicket,StandardCharsets.UTF_8));
        sslTlsInfo.setSrvRand(ByteString.copyFrom(SH_Random,StandardCharsets.UTF_8));
        sslTlsInfo.setCliVer(cSSLVersion);
        sslTlsInfo.setSrvVer(sSSLVersion);
        sslTlsInfo.setCliGMTUniTime(CH_Time);
        sslTlsInfo.setSrvGMTUniTime14(SH_Time);
        sslTlsInfo.setSrvName(ByteString.copyFrom(CH_ServerName,StandardCharsets.UTF_8));
        sslTlsInfo.setSrvNameAttr(CH_ServerNameType);

        return sslTlsInfo.build().toByteArray();
    }

    // DNS
    private byte[] tranDnsToPb(Map<String, Object> pbMap) {
        DnsInfoOuterClass.DnsInfo.Builder dnsInfo= DnsInfoOuterClass.DnsInfo.newBuilder();

        Integer flags = (Integer) pbMap.getOrDefault("Flags",0);
        Integer que = (Integer) pbMap.getOrDefault("Que",0);
        Integer ans = (Integer) pbMap.getOrDefault("Ans",0);
        Integer auth = (Integer) pbMap.getOrDefault("Auth",0);
        Integer add = (Integer) pbMap.getOrDefault("Add",0);
        String domain = (String) pbMap.getOrDefault("Domain","");
        String domainIp = (String) pbMap.getOrDefault("DomainIp","");

        List<Map<String,Object>> query = (List<Map<String, Object>>) pbMap.getOrDefault("Query",new ArrayList<>());
        List<Map<String,Object>> answer = (List<Map<String, Object>>) pbMap.getOrDefault("Answer",new ArrayList<>());

        dnsInfo.setAddCnt(add);
        dnsInfo.setAutCnt(auth);
        dnsInfo.setAnsCnt(ans);
        dnsInfo.setSrvFlag(flags);
        dnsInfo.setQueName(ByteString.copyFrom(domain,StandardCharsets.UTF_8));
        dnsInfo.setAnsRes(ByteString.copyFrom(domainIp,StandardCharsets.UTF_8));

        List<ByteString> ansTypes = new ArrayList<>();
        for(Map<String,Object> answerMap:answer){
            String type = QUERY_TYPE_MAP.getOrDefault(answerMap.getOrDefault("type",0),"unk");
            ansTypes.add(ByteString.copyFrom(type,StandardCharsets.UTF_8));
        }
        dnsInfo.addAllAnsTypes(ansTypes);

        return dnsInfo.build().toByteArray();
    }

    // HTTP
    private byte[] tranHttpToPb(Map<String, Object> pbMap) {
        HttpInfoOuterClass.HttpInfo.Builder httpInfo= HttpInfoOuterClass.HttpInfo.newBuilder();

        Map<String,Object> client = (Map<String, Object>) pbMap.getOrDefault("Client",new HashMap<>());
        Map<String,Object> server = (Map<String, Object>) pbMap.getOrDefault("Server",new HashMap<>());

        String host = (String) pbMap.getOrDefault("Host","");
        String url = (String) pbMap.getOrDefault("Url","");
        String act = (String) pbMap.getOrDefault("Act","");
        String response = (String) pbMap.getOrDefault("Response","");

        String accept = (String) client.getOrDefault("Accept","");
        String ua = (String) client.getOrDefault("User-Agent","");
        String title = (String) client.getOrDefault("Title","");
        String clientCookie = (String) client.getOrDefault("Cookie","");
        String clientContentType = (String) client.getOrDefault("Content-Type","");

        String serverContentType = (String) server.getOrDefault("Content-Type","");
        String contentEncoding = (String) server.getOrDefault("Content-Encoding","");
        String acceptRanges = (String) server.getOrDefault("Accept-Ranges","");

        httpInfo.setHost(ByteString.copyFrom(host,StandardCharsets.UTF_8));
        httpInfo.setConURL(ByteString.copyFrom(url,StandardCharsets.UTF_8));
        httpInfo.setRespBody(ByteString.copyFrom(response,StandardCharsets.UTF_8));
        httpInfo.setMet(ByteString.copyFrom(act,StandardCharsets.UTF_8));
        httpInfo.setTitle(ByteString.copyFrom(title,StandardCharsets.UTF_8));
        httpInfo.setUsrAge(ByteString.copyFrom(ua,StandardCharsets.UTF_8));
        httpInfo.setAccChaUp(ByteString.copyFrom(accept,StandardCharsets.UTF_8));
        httpInfo.setAccRanDown(ByteString.copyFrom(acceptRanges,StandardCharsets.UTF_8));
        httpInfo.setConType(ByteString.copyFrom(clientContentType,StandardCharsets.UTF_8));
        httpInfo.setConTypDown(ByteString.copyFrom(serverContentType,StandardCharsets.UTF_8));
        httpInfo.setCookie(ByteString.copyFrom(clientCookie,StandardCharsets.UTF_8));
        httpInfo.setConEncByCli(ByteString.copyFrom(contentEncoding,StandardCharsets.UTF_8));

        return httpInfo.build().toByteArray();
    }

}
