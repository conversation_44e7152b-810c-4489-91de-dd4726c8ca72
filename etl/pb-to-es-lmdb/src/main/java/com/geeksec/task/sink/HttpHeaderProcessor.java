package com.geeksec.task.sink;

import static com.geeksec.task.sink.ElasticsearchSinkConnector.REDIS_HTTP_HEADER;

import java.util.HashMap;
import java.util.Map;
import redis.clients.jedis.Jedis;

public class HttpHeaderProcessor {
    private HttpHeaderProcessor() {
    }

    public static Map<String, Object> extractClientHeaders(Map<String, Object> source, Jedis jedis) {
        Map<String,Object> client = (Map<String, Object>) source.get("Client");
        client = extractHeaders(client, HttpHeaderConstants.CLIENT_HEADERS, jedis);
        return client;
    }

    public static Map<String, Object> extractServerHeaders(Map<String, Object> source, Jedis jedis) {
        Map<String,Object> server = (Map<String, Object>) source.get("Server");
        server = extractHeaders(server, HttpHeaderConstants.SERVER_HEADERS, jedis);
        return server;
    }

    private static Map<String, Object> extractHeaders(Map<String, Object> source,
            Map<String, String> headerMapping, Jedis jedis) {
        Map<String, Object> result = new HashMap<>();

        for (Map.Entry<String, Object> entry : source.entrySet()) {
            if (headerMapping.isEmpty() || headerMapping.containsKey(entry.getKey())) {
                result.put(entry.getKey(), entry.getValue());
            }else {
                if (entry.getKey().startsWith("X")){
                    boolean hasField = jedis.sismember(entry.getKey(),REDIS_HTTP_HEADER);
                    Long httpHeaderLen = jedis.scard(REDIS_HTTP_HEADER);
                    if (hasField){
                        result.put(entry.getKey(), entry.getValue());
                    } else if (httpHeaderLen<100) {
                        result.put(entry.getKey(), entry.getValue());
                        jedis.sadd(entry.getKey(), REDIS_HTTP_HEADER);
                    }
                }
            }
        }
        return result;
    }
}
