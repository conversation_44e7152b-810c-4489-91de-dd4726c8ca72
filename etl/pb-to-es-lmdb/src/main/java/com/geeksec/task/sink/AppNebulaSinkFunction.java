package com.geeksec.task.sink;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.connector.nebula.connection.NebulaClientOptions;
import org.apache.flink.connector.nebula.connection.NebulaGraphConnectionProvider;
import org.apache.flink.connector.nebula.connection.NebulaMetaConnectionProvider;
import org.apache.flink.connector.nebula.sink.NebulaBatchOutputFormat;
import org.apache.flink.connector.nebula.sink.NebulaEdgeBatchOutputFormat;
import org.apache.flink.connector.nebula.sink.NebulaSinkFunction;
import org.apache.flink.connector.nebula.statement.EdgeExecutionOptions;
import org.apache.flink.connector.nebula.statement.ExecutionOptions;
import org.apache.flink.connector.nebula.statement.VertexExecutionOptions;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.io.BufferedInputStream;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023/10/16
 */
@Slf4j
public class AppNebulaSinkFunction {

    private static Properties properties = getProperties("/config.properties");
    public static final String NEBULA_GRAPH_ADDR = properties.getProperty("nebula.graph.addr");
    public static final String NEBULA_META_ADDR = properties.getProperty("nebula.meta.addr");
    public static final String NEBULA_GRAPH_SPACE = properties.getProperty("nebula.space.name");
    public static final int BATCH_INTERVAL_MS = 100;


    // Nebula Conn通用配置
    private static NebulaClientOptions nebulaClientOptions = new NebulaClientOptions.NebulaClientOptionsBuilder()
            .setGraphAddress(NEBULA_GRAPH_ADDR)
            .setMetaAddress(NEBULA_META_ADDR)
            .build();
    private static NebulaGraphConnectionProvider graphConnectionProvider = new NebulaGraphConnectionProvider(nebulaClientOptions);
    private static NebulaMetaConnectionProvider metaConnectionProvider = new NebulaMetaConnectionProvider(nebulaClientOptions);

    public static Properties getProperties(String proPath) {
        Properties result = new Properties();
        BufferedInputStream in = null;
        try {
            in = new BufferedInputStream(AppNebulaSinkFunction.class.getResourceAsStream(proPath));
            result.load(in);
        } catch (Exception e) {
            log.error(proPath + " load error.");
        }
        return result;
    }

    /**
     * App标签关系入库
     *
     * @param writeCertModelStream
     */
    public static void insertAppHasLabel(DataStream<Map<String, Object>> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("has_label")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Arrays.asList("analysis_by", "remark"))
                .setPositions(Arrays.asList(3, 4))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.flatMap(new FlatMapFunction<Map<String, Object>, Row>() {
            @Override
            public void flatMap(Map<String, Object> appTagMp, Collector<Row> collector) throws Exception {
                if (appTagMp.size() > 0) {
                    appTagMp.forEach((k, v) -> {
                        Row appTagRow = new Row(5);
                        appTagRow.setField(0, k);
                        appTagRow.setField(1, v);
                        appTagRow.setField(2, 0);
                        appTagRow.setField(3, "Add_Label");
                        appTagRow.setField(4, "");
                        collector.collect(appTagRow);
                    });
                }
            }
        }).name("has_label 边生成").setParallelism(1);
        dataStream.addSink(nebulaSinkFunction).name("has_label 边写入").setParallelism(1);
    }
}
