package com.geeksec.task.sink;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class HttpHeaderConstants {
    private HttpHeaderConstants() {}

    public static final Map<String, String> CLIENT_HEADERS;
    public static final Map<String, String> SERVER_HEADERS;
    
    static {
        Map<String, String> clientHeaders = new HashMap<>();
        clientHeaders.put("Accept", "Accept");
        clientHeaders.put("Host", "Host");
        clientHeaders.put("User-Agent", "User-Agent");
        clientHeaders.put("Title", "Title");
        clientHeaders.put("Cookie", "Cookie");
        clientHeaders.put("Content-Type", "Content-Type");
        CLIENT_HEADERS = Collections.unmodifiableMap(clientHeaders);
        
        Map<String, String> serverHeaders = new HashMap<>();
        serverHeaders.put("Accept-Ranges", "Accept-Ranges");
        serverHeaders.put("Content-Encoding", "Content-Encoding");
        serverHeaders.put("Content-Length", "Content-Length");
        serverHeaders.put("Content-Type", "Content-Type");
        serverHeaders.put("Title", "Title");
        SERVER_HEADERS = Collections.unmodifiableMap(serverHeaders);
    }
}
