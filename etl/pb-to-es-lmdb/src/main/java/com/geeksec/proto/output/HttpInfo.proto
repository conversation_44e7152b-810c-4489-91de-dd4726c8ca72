syntax = "proto2";

message HttpInfo
{
	optional	bytes		host						=	1;
	optional	bytes		uri							=	2;
	optional	bytes		varConEnc					=	3;
	optional	bytes		authInfo					=	4;
	optional	bytes		conEncByCli					=	5;
	optional	bytes		conLan						=	6;
	optional	uint32		conLenByCli					=	7;
	optional	bytes		conURL						=	8;
	optional	bytes		conMD5						=	9;
	optional	bytes		conType						=	10;
	optional	bytes		cookie						=	11;
	optional	bytes		cookie2						=	12;
	optional	bytes		date						=	13;
	optional	bytes		from						=	14;
	optional	bytes		loc							=	15;
	optional	bytes		proAuthen					=	16;
	optional	bytes		proAuthor					=	17;
	optional	bytes		refURL						=	18;
	optional	bytes		srv							=	19;
	optional	uint32		srvCnt						=	20;
	optional	bytes		setCookieKey				=	21;
	optional	bytes		setCookieVal				=	22;
	optional	bytes		traEnc						=	23;
	optional	bytes		usrAge						=	24;
	optional	bytes		via							=	25;
	optional	bytes		xForFor						=	26;
	optional	uint32		statCode					=	27;
	optional	bytes		met							=	28;
	optional	bytes		srvAge						=	29;
	optional	bytes		proAuth						=	30;
	optional	bytes		xPowBy						=	31;
	optional	bytes		extHdrs						=	32;
	optional	bytes		rangeofCli					=	33;
	optional	uint32		viaCnt						=	34;
	optional	uint32		statCodeCnt					=	35;
	optional	bytes		reqVer						=	36;
	optional	bytes		reqHead						=	37;
	optional	uint32		reqHeadMd5					=	38;
	optional	bytes		cacConUp					=	39;
	optional	bytes		conUp						=	40;
	optional	bytes		praUp						=	41;
	optional	bytes		upg							=	42;
	optional	bytes		accChaUp					=	43;
	optional	bytes		acctRanUp					=	44;
	optional	bytes		ifMat						=	45;
	optional	bytes		ifModSin					=	46;
	optional	bytes		ifNonMat					=	47;
	optional	bytes		ifRan						=	48;
	optional	uint64		ifUnModSin					=	49;
	optional	uint32		maxFor						=	50;
	optional	bytes		te							=	51;
	optional	bytes		cacConDown					=	52;
	optional	bytes		conDown						=	53;
	optional	bytes		praDown						=	54;
	optional	bytes		trail						=	55;
	optional	bytes		accRanDown					=	56;
	optional	bytes		eTag						=	57;
	optional	bytes		retAft						=	58;
	optional	bytes		wwwAuth						=	59;
	optional	bytes		refresh						=	60;
	optional	bytes		conTypDown					=	61;
	optional	bytes		allow						=	62;
	optional	uint64		expires						=	63;
	optional	uint64		lasMod						=	64;
	optional	bytes		accChaDown					=	65;
	optional	bytes		httpRelKey					=	66;
	optional	bytes		httpEmbPro					=	67;
	optional	bytes		fullTextHeader				=	68;
	optional	uint32		fullTextLen					=	69;
	optional	bytes		fileName					=	70;
	optional	bytes		contDown					=	71;
	optional	uint32		reqVerCnt					=	72;
	optional	uint32		metCnt						=	73;
	optional	uint32		reqHeadCnt					=	74;
	optional	bytes		accByCli					=	75;
	optional	bytes		accLanByCli					=	76;
	optional	bytes		accEncByCli					=	77;
	optional	uint32		authCnt						=	78;
	optional	uint32		hostCnt						=	79;
	optional	uint32		uriCnt						=	80;
	optional	bytes		uriPath						=	81;
	optional	uint32		uriPathCnt					=	82;
	repeated	bytes		uriKey						=	83;
	optional	uint32		uriKeyCnt					=	84;
	optional	bytes		uriSearch					=	85;
	optional	uint32		usrAgeCnt					=	86;
	optional	bytes		user						=	87;
	optional	uint32		userCnt						=	88;
	optional	bytes		reqBody						=	89;
	optional	bytes		reqBodyN					=	90;
	optional	bytes		conMD5ByCli					=	91;
	repeated	bytes		cookieKey					=	92;
	optional	uint32		cookieKeyCnt				=	93;
	optional	bytes		imei						=	94;
	optional	bytes		imsi						=	95;
	optional	uint32		xForForCnt					=	96;
	optional	bytes		respVer						=	97;
	optional	uint32		respVerCnt					=	98;
	optional	bytes		respHead					=	99;
	optional	bytes		respHeadMd5					=	100;
	optional	uint32		respHeadCnt					=	101;
	optional	bytes		respBody					=	102;
	optional	bytes		respBodyN					=	103;
	optional	bytes		conMD5BySrv					=	104;
	optional	uint32		conEncBySrv					=	105;
	optional	bytes		Location					=	106;
	optional	bytes		xSinHol						=	107;
	optional	uint32		conEncBySrvCnt				=	108;
	optional	uint32		conLenSrv					=	109;
	optional	bytes		conDispUp					=	110;
	optional	bytes		conDispDown					=	111;
	optional	bytes		authUser					=	112;
	optional	uint32		authUserCount				=	113;
	optional	uint32		bodyServerMd5Count			=	114;
	optional	bytes		contentDispositionClient	=	115;
	optional	bytes		contentDispositionServer	=	116;
	optional	bytes		filePath					=	117;
	optional	bytes		setCookie					=	118;
	optional	bytes		title						=	119;

}
