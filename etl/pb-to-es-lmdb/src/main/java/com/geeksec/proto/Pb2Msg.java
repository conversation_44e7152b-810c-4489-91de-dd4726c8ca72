
package com.geeksec.proto;

import com.geeksec.common.utils.IP2Addr;
import com.geeksec.common.utils.ProNameForMysql;
import com.geeksec.common.utils.TTL_Func;
import com.google.protobuf.ByteString;
import com.google.protobuf.ProtocolStringList;
import com.maxmind.geoip2.model.CityResponse;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import org.json.JSONArray;
import org.json.JSONObject;

public class Pb2Msg {
    private Map<String, String> ClientMap = new HashMap<String, String>();
    private Map<String, String> ServerMap = new HashMap<String, String>();
    public Map<String, Object> JKNmsgMap = new HashMap<>();

    public Pb2Msg() {
    }

    public Map<String, Object> Handle(ZMPNMsg.JKNmsg msg) {
        switch (msg.getType()) {
            case 30:
                return HandleAddMsg(msg, Msg3009Handle(msg), 3009);// connect
            case 4:
                return HandleAddMsg(msg, Msg148Handle(msg), 148);// dns
            case 80:
                return HandleAddMsg(msg, Msg3008Handle(msg), 3008);// http
            case 29:
                return HandleAddMsg(msg, Msg145Handle(msg), 145);// ssl
            case 100:
                return HandleAddMsg(msg, Msg100Handle(msg), 100);// RLogin
            case 101:
                return HandleAddMsg(msg, Msg101Handle(msg), 101);// Telnet
            case 28:
                return HandleAddMsg(msg, Msg102Handle(msg), 28);// SSH
            case 639:
                return HandleAddMsg(msg, Msg103Handle(msg), 639);// RDP
            case 104:
                return HandleAddMsg(msg, Msg104Handle(msg), 104);// VNC
            case 105:
                return HandleAddMsg(msg, Msg105Handle(msg), 105);// XDMCP
            case 106:
                return HandleAddMsg(msg, Msg106Handle(msg), 106);// NTP
            case 108:
                return HandleAddMsg(msg, Msg108Handle(msg), 108);// ICMP
        }
        return null;
    }

    // ICMP
    private Map<String, Object> Msg108Handle(ZMPNMsg.JKNmsg msg) {
        JKNmsgMap.put("sIp", msg.getIcmp().getCommMsg().getSrcIp());
        JKNmsgMap.put("sPort", msg.getIcmp().getCommMsg().getSrcPort());
        JKNmsgMap.put("dIp", msg.getIcmp().getCommMsg().getDstIp());
        JKNmsgMap.put("dPort", msg.getIcmp().getCommMsg().getDstPort());
        JKNmsgMap.put("ServerIP", msg.getIcmp().getCommMsg().getServerIp());
        JKNmsgMap.put("SessionId", msg.getIcmp().getCommMsg().getSessionId());
        JKNmsgMap.put("TaskId", msg.getIcmp().getCommMsg().getTaskId());
        JKNmsgMap.put("BatchNum", msg.getIcmp().getCommMsg().getBatchId());
        JKNmsgMap.put("StartTime", msg.getIcmp().getCommMsg().getBeginTime());
        JKNmsgMap.put("StartNSec", msg.getIcmp().getCommMsg().getBeginNsec());
        JKNmsgMap.put("AppName", msg.getIcmp().getCommMsg().getAppName());
        JKNmsgMap.put("AppId",msg.getIcmp().getCommMsg().getAppId());

        JKNmsgMap.put("msgType",msg.getIcmp().getMsgType());
        JKNmsgMap.put("infoCode",msg.getIcmp().getInfoCode());
        JKNmsgMap.put("echoSeqNum",msg.getIcmp().getEchoSeqNum());
        JKNmsgMap.put("dataCon",msg.getIcmp().getDataCon());
        JKNmsgMap.put("unrSrcAddr",msg.getIcmp().getUnrSrcAddr());
        JKNmsgMap.put("unrDstAddr",msg.getIcmp().getUnrDstAddr());
        JKNmsgMap.put("unrProt",msg.getIcmp().getUnrProt());
        JKNmsgMap.put("uncTTL",msg.getIcmp().getUncTtl());
        JKNmsgMap.put("ver",msg.getIcmp().getVer());
        JKNmsgMap.put("origTimeStamp",msg.getIcmp().getOrigTimeStamp());
        JKNmsgMap.put("recvTimeStamp",msg.getIcmp().getRecvTimeStamp());
        JKNmsgMap.put("transTimeStamp",msg.getIcmp().getTransTimeStamp());
        JKNmsgMap.put("mask",msg.getIcmp().getMask());
        JKNmsgMap.put("subNetId",msg.getIcmp().getSubNetId());
        JKNmsgMap.put("rtrTimeOut",msg.getIcmp().getRtrTimeOut());
        JKNmsgMap.put("excSrcAddr",msg.getIcmp().getExcSrcAddr());
        JKNmsgMap.put("excDstAddr",msg.getIcmp().getExcDstAddr());
        JKNmsgMap.put("excProt",msg.getIcmp().getExcProt());
        JKNmsgMap.put("excSrcPort",msg.getIcmp().getExcSrcPort());
        JKNmsgMap.put("excDstPort",msg.getIcmp().getExcDstPort());
        JKNmsgMap.put("gwAddr",msg.getIcmp().getGwAddr());
        JKNmsgMap.put("ttl",msg.getIcmp().getTtl());
        JKNmsgMap.put("repTtl",msg.getIcmp().getRepTtl());
        JKNmsgMap.put("qurType",msg.getIcmp().getQurType());
        JKNmsgMap.put("qurIpv6Addr",msg.getIcmp().getQurIpv6Addr());
        JKNmsgMap.put("qurIpv4Addr",msg.getIcmp().getQurIpv4Addr());
        JKNmsgMap.put("qurDNS",msg.getIcmp().getQurDns());
        JKNmsgMap.put("ndpLifeTime",msg.getIcmp().getNdpLifeTime());
        JKNmsgMap.put("ndpLinkAddr",msg.getIcmp().getNdpLinkAddr());
        JKNmsgMap.put("ndpPreLen",msg.getIcmp().getNdpPreLen());
        JKNmsgMap.put("ndpPreFix",msg.getIcmp().getNdpPreFix());
        JKNmsgMap.put("ndpValLifeTime",msg.getIcmp().getNdpValLifeTime());
        JKNmsgMap.put("ndpCurMtu",msg.getIcmp().getNdpCurMtu());
        JKNmsgMap.put("ndpTarAddr",msg.getIcmp().getNdpTarAddr());
        JKNmsgMap.put("nextHopMtu",msg.getIcmp().getNextHopMtu());
        JKNmsgMap.put("excPointer",msg.getIcmp().getExcPointer());
        JKNmsgMap.put("mulCastAddr",msg.getIcmp().getMulCastAddr());
        JKNmsgMap.put("checkSum",msg.getIcmp().getCheckSum());
        JKNmsgMap.put("checkSumReply",msg.getIcmp().getCheckSumReply());
        JKNmsgMap.put("rtraddr",msg.getIcmp().getRtraddr());
        JKNmsgMap.put("resTime",msg.getIcmp().getResTime());
        JKNmsgMap.put("excTTL",msg.getIcmp().getExcTtl());
        JKNmsgMap.put("ResponseTime",msg.getIcmp().getResponseTime());
        JKNmsgMap.put("unreachableSourcePort",msg.getIcmp().getUnreachableSourcePort());
        JKNmsgMap.put("unreachableDestinationPort",msg.getIcmp().getUnreachableDestinationPort());


        return JKNmsgMap;
    }

    // NTP
    private Map<String, Object> Msg106Handle(ZMPNMsg.JKNmsg msg) {
        JKNmsgMap.put("sIp", msg.getNtp().getCommMsg().getSrcIp());
        JKNmsgMap.put("sPort", msg.getNtp().getCommMsg().getSrcPort());
        JKNmsgMap.put("dIp", msg.getNtp().getCommMsg().getDstIp());
        JKNmsgMap.put("dPort", msg.getNtp().getCommMsg().getDstPort());
        JKNmsgMap.put("ServerIP", msg.getNtp().getCommMsg().getServerIp());
        JKNmsgMap.put("SessionId", msg.getNtp().getCommMsg().getSessionId());
        JKNmsgMap.put("TaskId", msg.getNtp().getCommMsg().getTaskId());
        JKNmsgMap.put("BatchNum", msg.getNtp().getCommMsg().getBatchId());
        JKNmsgMap.put("StartTime", msg.getNtp().getCommMsg().getBeginTime());
        JKNmsgMap.put("StartNSec", msg.getNtp().getCommMsg().getBeginNsec());
        JKNmsgMap.put("AppName", msg.getNtp().getCommMsg().getAppName());
        JKNmsgMap.put("AppId",msg.getNtp().getCommMsg().getAppId());

        Map<String,Object> Client = new HashMap<>();
        Map<String,Object> Server = new HashMap<>();

        Client.put("Stratum",msg.getNtp().getClientMsg().getStratum());
        Client.put("PollIterval",msg.getNtp().getClientMsg().getPollIntervalSec());
        Client.put("ClockPrecision",msg.getNtp().getClientMsg().getClockPrecision());
        Client.put("RootDelay",msg.getNtp().getClientMsg().getRootDelay());
        Client.put("RootDispersion",msg.getNtp().getClientMsg().getRootDispersion());
        Client.put("ReferID",msg.getNtp().getClientMsg().getReferenceIdentifier());
        Client.put("ReferTsSec",msg.getNtp().getClientMsg().getReferTsSec());
        Client.put("ReferTsNSec",msg.getNtp().getClientMsg().getReferTsNsec());
        Client.put("OriginTsSec",msg.getNtp().getClientMsg().getOriginTsSec());
        Client.put("OriginTsNSec",msg.getNtp().getClientMsg().getOriginTsNsec());
        Client.put("RecvTsSec",msg.getNtp().getClientMsg().getRecvTsSec());
        Client.put("RecvTsNSec",msg.getNtp().getClientMsg().getRecvTsNsec());
        Client.put("XmitTsSec",msg.getNtp().getClientMsg().getXmitTsSec());
        Client.put("XmitTsNSec",msg.getNtp().getClientMsg().getXmitTsNsec());

        Server.put("Stratum",msg.getNtp().getServerMsg().getStratum());
        Server.put("PollIterval",msg.getNtp().getServerMsg().getPollIntervalSec());
        Server.put("ClockPrecision",msg.getNtp().getServerMsg().getClockPrecision());
        Server.put("RootDelay",msg.getNtp().getServerMsg().getRootDelay());
        Server.put("RootDispersion",msg.getNtp().getServerMsg().getRootDispersion());
        Server.put("ReferID",msg.getNtp().getServerMsg().getReferenceIdentifier());
        Server.put("ReferTsSec",msg.getNtp().getServerMsg().getReferTsSec());
        Server.put("ReferTsNSec",msg.getNtp().getServerMsg().getReferTsNsec());
        Server.put("OriginTsSec",msg.getNtp().getServerMsg().getOriginTsSec());
        Server.put("OriginTsNSec",msg.getNtp().getServerMsg().getOriginTsNsec());
        Server.put("RecvTsSec",msg.getNtp().getServerMsg().getRecvTsSec());
        Server.put("RecvTsNSec",msg.getNtp().getServerMsg().getRecvTsNsec());
        Server.put("XmitTsSec",msg.getNtp().getServerMsg().getXmitTsSec());
        Server.put("XmitTsNSec",msg.getNtp().getServerMsg().getXmitTsNsec());

        JKNmsgMap.put("Version",msg.getNtp().getVersion());
        JKNmsgMap.put("Client",Client);
        JKNmsgMap.put("Server",Server);

        JKNmsgMap.put("TranProto", "UDP");
        JKNmsgMap.put("AppProto", "NTP");
        return JKNmsgMap;
    }

    // XDMCP
    private Map<String, Object> Msg105Handle(ZMPNMsg.JKNmsg msg) {

        JKNmsgMap.put("sIp", msg.getXdmcp().getCommMsg().getSrcIp());
        JKNmsgMap.put("sPort", msg.getXdmcp().getCommMsg().getSrcPort());
        JKNmsgMap.put("dIp", msg.getXdmcp().getCommMsg().getDstIp());
        JKNmsgMap.put("dPort", msg.getXdmcp().getCommMsg().getDstPort());
        JKNmsgMap.put("ServerIP", msg.getXdmcp().getCommMsg().getServerIp());
        JKNmsgMap.put("SessionId", msg.getXdmcp().getCommMsg().getSessionId());
        JKNmsgMap.put("TaskId", msg.getXdmcp().getCommMsg().getTaskId());
        JKNmsgMap.put("BatchNum", msg.getXdmcp().getCommMsg().getBatchId());
        JKNmsgMap.put("StartTime", msg.getXdmcp().getCommMsg().getBeginTime());
        JKNmsgMap.put("StartNSec", msg.getXdmcp().getCommMsg().getBeginNsec());
        JKNmsgMap.put("AppName", msg.getXdmcp().getCommMsg().getAppName());
        JKNmsgMap.put("AppId",msg.getXdmcp().getCommMsg().getAppId());

        Map<String,Object> Client = new HashMap<>();
        Map<String,Object> Server = new HashMap<>();
        Server.put("HostName",msg.getXdmcp().getHostname());
        ZMPNMsg.xdmcp_auth_msg ServerAuth = msg.getXdmcp().getServerAuth();
        Server.put("AcceptAuthName", protoList2List(ServerAuth.getAuthorizationNamesList()));
        ZMPNMsg.xdmcp_auth_msg ClientAuth = msg.getXdmcp().getClientAuth();
        Client.put("RequestAuthName", protoList2List(ClientAuth.getAuthorizationNamesList()));
        JKNmsgMap.put("Client",Client);
        JKNmsgMap.put("Server",Server);

        JKNmsgMap.put("Version",msg.getXdmcp().getVersion());
        List<ZMPNMsg.xdmcp_connection_msg> connectionList = msg.getXdmcp().getConnectionsList();
        List<Map<String,Object>> connections = new ArrayList<>();
        for (ZMPNMsg.xdmcp_connection_msg connection:connectionList){
            Map<String,Object> connectionMap = new HashMap<>();
            connectionMap.put("Addr", connection.getAddr());
            connectionMap.put("Type", connection.getType());
            connections.add(connectionMap);
        }
        JKNmsgMap.put("Connection",connections);

        JKNmsgMap.put("Status",msg.getXdmcp().getStatus());
        JKNmsgMap.put("XDMCP_SessionID",msg.getXdmcp().getSessionId());
        JKNmsgMap.put("ManufacturerDisplayID",msg.getXdmcp().getManufactureDispId());
        JKNmsgMap.put("DisplayNumber",msg.getXdmcp().getDisplayNumber());
        JKNmsgMap.put("DisplayClass",msg.getXdmcp().getDisplayClass());

        JKNmsgMap.put("TranProto", "TCP");
        JKNmsgMap.put("AppProto", "XDMCP");
        return JKNmsgMap;
    }

    // VNC
    private Map<String, Object> Msg104Handle(ZMPNMsg.JKNmsg msg) {

        JKNmsgMap.put("sIp", msg.getVnc().getCommMsg().getSrcIp());
        JKNmsgMap.put("sPort", msg.getVnc().getCommMsg().getSrcPort());
        JKNmsgMap.put("dIp", msg.getVnc().getCommMsg().getDstIp());
        JKNmsgMap.put("dPort", msg.getVnc().getCommMsg().getDstPort());
        JKNmsgMap.put("ServerIP", msg.getVnc().getCommMsg().getServerIp());
        JKNmsgMap.put("SessionId", msg.getVnc().getCommMsg().getSessionId());
        JKNmsgMap.put("TaskId", msg.getVnc().getCommMsg().getTaskId());
        JKNmsgMap.put("BatchNum", msg.getVnc().getCommMsg().getBatchId());
        JKNmsgMap.put("StartTime", msg.getVnc().getCommMsg().getBeginTime());
        JKNmsgMap.put("StartNSec", msg.getVnc().getCommMsg().getBeginNsec());
        JKNmsgMap.put("AppName", msg.getVnc().getCommMsg().getAppName());
        JKNmsgMap.put("AppId",msg.getVnc().getCommMsg().getAppId());

        Map<String,Object> Client = new HashMap<>();
        Map<String,Object> Server = new HashMap<>();
        Client.put("ProtoVer",msg.getVnc().getClientProtocolVer());
        Client.put("SecureType",Collections.singletonList(msg.getVnc().getClientSecureTypeSelected()));
        Client.put("AuthenticationChallenge",msg.getVnc().getClientAuthenticationResponse());
        Server.put("SecureType",protoList2List(msg.getVnc().getServerSecureTypeSupportedList()));
        Server.put("ProtoVer",msg.getVnc().getServerProtocolVer());
        Server.put("AuthResult",msg.getVnc().getAuthenticationResult());
        Server.put("AuthenticationResponse",msg.getVnc().getServerAuthenticationChallenge());
        JKNmsgMap.put("Client",Client);
        JKNmsgMap.put("Server",Server);

        JKNmsgMap.put("DesktopName",msg.getVnc().getServerFb().getDesktopName());
        JKNmsgMap.put("EncodeType",protoList2List(msg.getVnc().getEncoding().getEncodingTypesList()));
        JKNmsgMap.put("ShareDesktopFlag",msg.getVnc().getShareDsktpFlag());

        Map<String,Object> PixelFormat = new HashMap<>();
        PixelFormat.put("BitPerPixel",msg.getVnc().getPixelFormat().getBitsPerPixel());
        PixelFormat.put("Depth",msg.getVnc().getPixelFormat().getDepth());
        PixelFormat.put("BigEndian",msg.getVnc().getPixelFormat().getBigEndianFlag());
        PixelFormat.put("TrueColor",msg.getVnc().getPixelFormat().getTrueColorFlag());
        JKNmsgMap.put("PixelFormat",PixelFormat);

        JKNmsgMap.put("TranProto", "TCP");
        JKNmsgMap.put("AppProto", "VNC");
        return JKNmsgMap;
    }

    // RDP
    private Map<String, Object> Msg103Handle(ZMPNMsg.JKNmsg msg) {
        JKNmsgMap.put("sIp", msg.getRdp().getCommMsg().getSrcIp());
        JKNmsgMap.put("sPort", msg.getRdp().getCommMsg().getSrcPort());
        JKNmsgMap.put("dIp", msg.getRdp().getCommMsg().getDstIp());
        JKNmsgMap.put("dPort", msg.getRdp().getCommMsg().getDstPort());
        JKNmsgMap.put("ServerIP", msg.getRdp().getCommMsg().getServerIp());
        JKNmsgMap.put("SessionId", msg.getRdp().getCommMsg().getSessionId());
        JKNmsgMap.put("TaskId", msg.getRdp().getCommMsg().getTaskId());
        JKNmsgMap.put("BatchNum", msg.getRdp().getCommMsg().getBatchId());
        JKNmsgMap.put("StartTime", msg.getRdp().getCommMsg().getBeginTime());
        JKNmsgMap.put("StartNSec", msg.getRdp().getCommMsg().getBeginNsec());
        JKNmsgMap.put("AppName", msg.getRdp().getCommMsg().getAppName());
        JKNmsgMap.put("AppId",msg.getRdp().getCommMsg().getAppId());

        Map<String,Object> Client = new HashMap<>();
        Map<String,Object> Server = new HashMap<>();
        Client.put("ProtoFlags",protoList2List(msg.getRdp().getRdpNegotiate().getCFlagProtocolsList()));
        Client.put("RequestedProtocol",protoList2List(msg.getRdp().getRdpNegotiate().getCRequestedProtocolsList()));
        Server.put("ProtoFlags",protoList2List(msg.getRdp().getRdpNegotiate().getSFlagProtocolsList()));
        Server.put("SelectedProtocol", protoList2List(msg.getRdp().getRdpNegotiate().getSSelectedProtocolsList()));

        JKNmsgMap.put("Client",Client);
        JKNmsgMap.put("Server",Server);

        JKNmsgMap.put("TranProto", "TCP");
        JKNmsgMap.put("AppProto", "RDP");
        return JKNmsgMap;
    }
    public static List<String> convertByteStringListToStringList(List<ByteString> byteStringList) {
        List<String> stringList = new ArrayList<>();
        for (ByteString byteString : byteStringList) {
            // 将ByteString转换为String
            String string = byteString.toStringUtf8();
            stringList.add(string);
        }
        return stringList;
    }

    // SSH
    private Map<String, Object> Msg102Handle(ZMPNMsg.JKNmsg msg) {

        List<String> kex_algorithmsListValues = protoList2List(msg.getSsh().getClient().getKexAlgorithmsList());

        List<String> server_host_key_algorithmsListValues = protoList2List(msg.getSsh().getClient().getServerHostKeyAlgorithmsList());

        List<String> encryption_algorithms_client_to_serverListValues = protoList2List(msg.getSsh().getClient().getEncryptionAlgorithmsClientToServerList());

        List<String> encryption_algorithms_server_to_clientListValues = protoList2List(msg.getSsh().getClient().getEncryptionAlgorithmsServerToClientList());

        List<String> mac_algorithms_client_to_serverListValues = protoList2List(msg.getSsh().getClient().getMacAlgorithmsClientToServerList());

        List<String> mac_algorithms_server_to_clientListValues = protoList2List(msg.getSsh().getClient().getMacAlgorithmsServerToClientList());

        List<String> compression_algorithms_client_to_serverListValues = protoList2List(msg.getSsh().getClient().getCompressionAlgorithmsClientToServerList());

        List<String> compression_algorithms_server_to_clientListValues = protoList2List(msg.getSsh().getClient().getCompressionAlgorithmsServerToClientList());

        List<String> kex_algorithmsListValues1 = protoList2List(msg.getSsh().getServer().getKexAlgorithmsList());

        List<String> server_host_key_algorithmsListValues1 = protoList2List(msg.getSsh().getServer().getServerHostKeyAlgorithmsList());

        List<String> encryption_algorithms_client_to_serverListValues1 = protoList2List(msg.getSsh().getServer().getEncryptionAlgorithmsClientToServerList());

        List<String> encryption_algorithms_server_to_clientListValues1 = protoList2List(msg.getSsh().getServer().getEncryptionAlgorithmsServerToClientList());

        List<String> mac_algorithms_client_to_serverListValues1 = protoList2List(msg.getSsh().getServer().getMacAlgorithmsClientToServerList());

        List<String> mac_algorithms_server_to_clientListValues1 = protoList2List(msg.getSsh().getServer().getMacAlgorithmsServerToClientList());

        List<String> compression_algorithms_client_to_serverListValues1 = protoList2List(msg.getSsh().getServer().getCompressionAlgorithmsClientToServerList());

        List<String> compression_algorithms_server_to_clientListValues1 = protoList2List(msg.getSsh().getServer().getCompressionAlgorithmsServerToClientList());

        JKNmsgMap.put("sIp", msg.getSsh().getCommMsg().getSrcIp());
        JKNmsgMap.put("sPort", msg.getSsh().getCommMsg().getSrcPort());
        JKNmsgMap.put("dIp", msg.getSsh().getCommMsg().getDstIp());
        JKNmsgMap.put("dPort", msg.getSsh().getCommMsg().getDstPort());
        JKNmsgMap.put("ServerIP", msg.getSsh().getCommMsg().getServerIp());
        JKNmsgMap.put("SessionId", msg.getSsh().getCommMsg().getSessionId());
        JKNmsgMap.put("TaskId", msg.getSsh().getCommMsg().getTaskId());
        JKNmsgMap.put("BatchNum", msg.getSsh().getCommMsg().getBatchId());
        JKNmsgMap.put("StartTime", msg.getSsh().getCommMsg().getBeginTime());
        JKNmsgMap.put("StartNSec", msg.getSsh().getCommMsg().getBeginNsec());
        JKNmsgMap.put("AppName", msg.getSsh().getCommMsg().getAppName());
        JKNmsgMap.put("AppId",msg.getSsh().getCommMsg().getAppId());
        Map<String, Object> clientMap = new HashMap<>();

        clientMap.put("Protocol", msg.getSsh().getClient().getProtocol());
        clientMap.put("Cookie", msg.getSsh().getClient().getCookie());
        clientMap.put("KexAlgorithms", kex_algorithmsListValues);
        clientMap.put("ServerHostKeyAlgorithms", server_host_key_algorithmsListValues);
        clientMap.put("EncryptionAlgorithmsC2S", encryption_algorithms_client_to_serverListValues);
        clientMap.put("EncryptionAlgorithmsS2C", encryption_algorithms_server_to_clientListValues);
        clientMap.put("MacAlgorithmsC2S", mac_algorithms_client_to_serverListValues);
        clientMap.put("MacAlgorithmsS2C", mac_algorithms_server_to_clientListValues);
        clientMap.put("CompressionAlgorithmsC2S", compression_algorithms_client_to_serverListValues);
        clientMap.put("CompressionAlgorithmsS2C", compression_algorithms_server_to_clientListValues);
        JKNmsgMap.put("Client", clientMap);
        Map<String, Object> serverMap = new HashMap<>();

        serverMap.put("Protocol", msg.getSsh().getServer().getProtocol());
        serverMap.put("Cookie", msg.getSsh().getServer().getCookie());
        serverMap.put("KexAlgorithms", kex_algorithmsListValues1);
        serverMap.put("ServerHostKeyAlgorithms", server_host_key_algorithmsListValues1);
        serverMap.put("EncryptionAlgorithmsC2S", encryption_algorithms_client_to_serverListValues1);
        serverMap.put("EncryptionAlgorithmsS2C", encryption_algorithms_server_to_clientListValues1);
        serverMap.put("MacAlgorithmsC2S", mac_algorithms_client_to_serverListValues1);
        serverMap.put("MacAlgorithmsS2C", mac_algorithms_server_to_clientListValues1);
        serverMap.put("CompressionAlgorithmsC2S", compression_algorithms_client_to_serverListValues1);
        serverMap.put("CompressionAlgorithmsS2C", compression_algorithms_server_to_clientListValues1);
        JKNmsgMap.put("Server", serverMap);

        JKNmsgMap.put("DH_e", msg.getSsh().getDhE());
        JKNmsgMap.put("DH_f", msg.getSsh().getDhF());
        JKNmsgMap.put("DHGEX_Min", msg.getSsh().getDhGexMin());
        JKNmsgMap.put("DHGEX_NBits", msg.getSsh().getDhGexNbits());
        JKNmsgMap.put("DHGEX_Max", msg.getSsh().getDhGexMax());
        JKNmsgMap.put("DHGEX_P", msg.getSsh().getDhGexP());
        JKNmsgMap.put("DHGEX_G", msg.getSsh().getDhGexG());
        JKNmsgMap.put("ECDH_Q_C", msg.getSsh().getEcdhQC());
        JKNmsgMap.put("ECDH_Q_S", msg.getSsh().getEcdhQS());
        JKNmsgMap.put("HostKeyType", msg.getSsh().getHostKeyType());
        JKNmsgMap.put("HostKey_RSA_e", msg.getSsh().getHostKeyRsaE());
        JKNmsgMap.put("HostKey_RSA_N", msg.getSsh().getHostKeyRsaN());
        JKNmsgMap.put("HostKey_ECDSA_ID", msg.getSsh().getHostKeyEcdsaId());
        JKNmsgMap.put("HostKey_ECDSA_Q", msg.getSsh().getHostKeyEcdsaQ());
        JKNmsgMap.put("HostKey_DSA_p", msg.getSsh().getHostKeyDsaP());
        JKNmsgMap.put("HostKey_DSA_q", msg.getSsh().getHostKeyDsaQ());
        JKNmsgMap.put("HostKey_DSA_g", msg.getSsh().getHostKeyDsaG());
        JKNmsgMap.put("HostKey_DSA_y", msg.getSsh().getHostKeyDsaY());
        JKNmsgMap.put("HostKey_EdDSA_Key", msg.getSsh().getHostKeyEddsaKey());
        JKNmsgMap.put("KexHSigType", msg.getSsh().getKexHSigType());
        JKNmsgMap.put("KexHSig", msg.getSsh().getKexHSig());

        JKNmsgMap.put("TranProto", "TCP");
        JKNmsgMap.put("AppProto", "SSH");
        return JKNmsgMap;
    }

    // Telnet
    private Map<String, Object> Msg101Handle(ZMPNMsg.JKNmsg msg) {

        JKNmsgMap.put("sIp", msg.getTelnet().getCommMsg().getSrcIp());
        JKNmsgMap.put("sPort", msg.getTelnet().getCommMsg().getSrcPort());
        JKNmsgMap.put("dIp", msg.getTelnet().getCommMsg().getDstIp());
        JKNmsgMap.put("dPort", msg.getTelnet().getCommMsg().getDstPort());
        JKNmsgMap.put("ServerIP", msg.getTelnet().getCommMsg().getServerIp());
        JKNmsgMap.put("SessionId", msg.getTelnet().getCommMsg().getSessionId());
        JKNmsgMap.put("TaskId", msg.getTelnet().getCommMsg().getTaskId());
        JKNmsgMap.put("BatchNum", msg.getTelnet().getCommMsg().getBatchId());
        JKNmsgMap.put("StartTime", msg.getTelnet().getCommMsg().getBeginTime());
        JKNmsgMap.put("StartNSec", msg.getTelnet().getCommMsg().getBeginNsec());
        JKNmsgMap.put("AppName", msg.getTelnet().getCommMsg().getAppName());
        JKNmsgMap.put("AppId",msg.getTelnet().getCommMsg().getAppId());

        JKNmsgMap.put("TermWidth",msg.getTelnet().getTerminfo().getTermWidth());
        JKNmsgMap.put("TermHeight",msg.getTelnet().getTerminfo().getTermHeight());
        JKNmsgMap.put("TermType",msg.getTelnet().getTerminfo().getTermType());
        JKNmsgMap.put("TermSpeed",msg.getTelnet().getTerminfo().getTermSpeed());
        JKNmsgMap.put("UserName",msg.getTelnet().getUserinfo().getUsername());
        JKNmsgMap.put("Passwd",msg.getTelnet().getUserinfo().getPasswd());

        JKNmsgMap.put("TranProto", "TCP");
        JKNmsgMap.put("AppProto", "Telnet");
        return JKNmsgMap;
    }

    // RLogin
    private Map<String, Object> Msg100Handle(ZMPNMsg.JKNmsg msg) {

        JKNmsgMap.put("sIp", msg.getRlogin().getCommMsg().getSrcIp());
        JKNmsgMap.put("sPort", msg.getRlogin().getCommMsg().getSrcPort());
        JKNmsgMap.put("dIp", msg.getRlogin().getCommMsg().getDstIp());
        JKNmsgMap.put("dPort", msg.getRlogin().getCommMsg().getDstPort());
        JKNmsgMap.put("ServerIP", msg.getRlogin().getCommMsg().getServerIp());
        JKNmsgMap.put("SessionId", msg.getRlogin().getCommMsg().getSessionId());
        JKNmsgMap.put("TaskId", msg.getRlogin().getCommMsg().getTaskId());
        JKNmsgMap.put("BatchNum", msg.getRlogin().getCommMsg().getBatchId());
        JKNmsgMap.put("StartTime", msg.getRlogin().getCommMsg().getBeginTime());
        JKNmsgMap.put("StartNSec", msg.getRlogin().getCommMsg().getBeginNsec());
        JKNmsgMap.put("AppName", msg.getRlogin().getCommMsg().getAppName());
        JKNmsgMap.put("AppId",msg.getRlogin().getCommMsg().getAppId());

        JKNmsgMap.put("Rows",msg.getRlogin().getWininfo().getRows());
        JKNmsgMap.put("Columns",msg.getRlogin().getWininfo().getColumns());
        JKNmsgMap.put("TermType",msg.getRlogin().getWininfo().getTermType());
        JKNmsgMap.put("TermSpeed",msg.getRlogin().getWininfo().getTermSpeed());
        JKNmsgMap.put("ClientUserName",msg.getRlogin().getUserinfo().getClientUsername());
        JKNmsgMap.put("ServerUserName",msg.getRlogin().getUserinfo().getServerUsername());
        JKNmsgMap.put("Passwd",msg.getRlogin().getUserinfo().getPasswd());

        JKNmsgMap.put("TranProto", "TCP");
        JKNmsgMap.put("AppProto", "RLogin");

        return JKNmsgMap;
    }

    public String GetHKey(Map<String, Object> b) {

        if (b.containsKey("es_key") && b.containsKey("SessionId") && b.containsKey("StartTime")) {
            return b.get("es_key").toString() + "_" + b.get("SessionId").toString();
        }
        return null;
    }

    public Map<String, Object> HandleAddMsg(ZMPNMsg.JKNmsg pb_msg, Map<String, Object> Msg, int type) {

        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        Date date = new Date(System.currentTimeMillis());
        String Date_str = formatter.format(date);
        switch (type) {
            case 3009:
                AddConnectHandle(pb_msg, Msg);
                Msg.put("es_key", "connectinfo_" + String.valueOf(pb_msg.getSingleSession().getCommMsg().getTaskId()) + "_" + String.valueOf(pb_msg.getSingleSession().getCommMsg().getBatchId()) + "_" + Date_str);
                Msg.put("type", "connect");
                break;
            case 148:
                Msg.put("es_key", "dns_" + String.valueOf(pb_msg.getDns().getCommMsg().getTaskId()) + "_" + String.valueOf(pb_msg.getDns().getCommMsg().getBatchId()) + "_" + Date_str);
                Msg.put("type", "dns");
                break;
            case 3008:
                AddHttpHandle(pb_msg, Msg);
                Msg.put("es_key", "http_" + String.valueOf(pb_msg.getHttpHeader().getCommMsg().getTaskId()) + "_" + String.valueOf(pb_msg.getHttpHeader().getCommMsg().getBatchId()) + "_" + Date_str);
                Msg.put("type", "http");
                break;
            case 145:
                Msg.put("es_key", "ssl_" + String.valueOf(pb_msg.getSsl().getCommMsg().getTaskId()) + "_" + String.valueOf(pb_msg.getSsl().getCommMsg().getBatchId()) + "_" + Date_str);
                Msg.put("type", "ssl");
                break;
            case 100:
                Msg.put("es_key", "rlogin_" + String.valueOf(pb_msg.getRlogin().getCommMsg().getTaskId()) + "_" + String.valueOf(pb_msg.getRlogin().getCommMsg().getBatchId()) + "_" + Date_str);
                Msg.put("type", "rlogin");
                break;
            case 101:
                Msg.put("es_key", "telnet_" + String.valueOf(pb_msg.getTelnet().getCommMsg().getTaskId()) + "_" + String.valueOf(pb_msg.getTelnet().getCommMsg().getBatchId()) + "_" + Date_str);
                Msg.put("type", "telnet");
                break;
            case 28:
                Msg.put("es_key", "ssh_" + String.valueOf(pb_msg.getSsh().getCommMsg().getTaskId()) + "_" + String.valueOf(pb_msg.getSsh().getCommMsg().getBatchId()) + "_" + Date_str);
                Msg.put("type", "ssh");
                break;
            case 639:
                Msg.put("es_key", "rdp_" + String.valueOf(pb_msg.getRdp().getCommMsg().getTaskId()) + "_" + String.valueOf(pb_msg.getRdp().getCommMsg().getBatchId()) + "_" + Date_str);
                Msg.put("type", "rdp");
                break;
            case 104:
                Msg.put("es_key", "vnc_" + String.valueOf(pb_msg.getVnc().getCommMsg().getTaskId()) + "_" + String.valueOf(pb_msg.getVnc().getCommMsg().getBatchId()) + "_" + Date_str);
                Msg.put("type", "vnc");
                break;
            case 105:
                Msg.put("es_key", "xdmcp_" + String.valueOf(pb_msg.getXdmcp().getCommMsg().getTaskId()) + "_" + String.valueOf(pb_msg.getXdmcp().getCommMsg().getBatchId()) + "_" + Date_str);
                Msg.put("type", "xdmcp");
                break;
            case 106:
                Msg.put("es_key", "ntp_" + String.valueOf(pb_msg.getNtp().getCommMsg().getTaskId()) + "_" + String.valueOf(pb_msg.getNtp().getCommMsg().getBatchId()) + "_" + Date_str);
                Msg.put("type", "ntp");
                break;
            case 108:
                Msg.put("es_key", "icmp_" + String.valueOf(pb_msg.getNtp().getCommMsg().getTaskId()) + "_" + String.valueOf(pb_msg.getNtp().getCommMsg().getBatchId()) + "_" + Date_str);
                Msg.put("type", "icmp");
                break;
            default:
                //Msg.put("es_key",String.valueOf(Msg.get("TaskId")) + "_"+ String.valueOf(Msg.get("BatchNum")));
                break;
        }
        Msg.put("CreateTime", (int) (System.currentTimeMillis() / 1000));
        Msg.put("Hkey", GetHKey(Msg));
        return Msg;
    }

    public Map<String, Object> HttpClient(ZMPNMsg.JKNmsg msg) {
        Map<String, Object> Client = new HashMap<String, Object>();
        List<ZMPNMsg.key_val_msg> ClientList = msg.getHttpHeader().getHttpClientKvList();

        for (ZMPNMsg.key_val_msg tmp : ClientList) {
            if (ClientMap.isEmpty()) {
                Client.put(tmp.getKey(), tmp.getVal());
            } else if (ClientMap.containsKey(tmp.getKey())) {
                Client.put(tmp.getKey(), tmp.getVal());
            }
        }
        return Client;
    }

    public Map<String, Object> HttpServer(ZMPNMsg.JKNmsg msg) {
        Map<String, Object> Server = new HashMap<String, Object>();
        List<ZMPNMsg.key_val_msg> ServerList = msg.getHttpHeader().getHttpServerKvList();

        for (ZMPNMsg.key_val_msg tmp : ServerList) {
            if (ServerMap.isEmpty()) {
                Server.put(tmp.getKey(), tmp.getVal());
            } else if (ServerMap.containsKey(tmp.getKey())) {
                Server.put(tmp.getKey(), tmp.getVal());
            }
        }
        return Server;
    }

    public void AddHttpHandle(ZMPNMsg.JKNmsg msg, Map<String, Object> Map) {
        Map.put("Client", HttpClient(msg));
        Map.put("Server", HttpServer(msg));
    }

    public Map<String, Object> JsonToMap(String sValue) {
        if (sValue == null || sValue.contains("")) {
            JSONObject jo = new JSONObject("[]");
            return jo.toMap();
        } else {
            JSONObject jo = new JSONObject(sValue);
            return jo.toMap();
        }
    }

    public List<Object> JsonArrayToMap(String sValue) {
        if (sValue == null || sValue.length() == 0) {
            JSONArray jo = new JSONArray("[]");
            return jo.toList();
        } else {
            JSONArray jo = new JSONArray(sValue);
            return jo.toList();
        }
    }

    public String IntToString(Integer num) {
        return String.valueOf(num);
    }

    public String LongToString(Long num) {
        return String.valueOf(num);
    }

    public void TTLContCrc(ZMPNMsg.JKNmsg msg, Map<String, Object> Map) {

        int sttl = msg.getSingleSession().getSsPkt().getPktSttlmax();
        int sMaxTTL = 0;
        int sInitialTTL = TTL_Func.getInstance().Judge(sttl, sMaxTTL);
        Map.put("sInitialTTL", sInitialTTL);
        Map.put("sTTLMax", sMaxTTL);
        Map.put("sTTLMin", sttl);
        int dttl = msg.getSingleSession().getSsPkt().getPktDttlmax();
        int dttlmin = msg.getSingleSession().getSsPkt().getPktDttlmin();
        int dMaxTTL = 0;
        int dInitialTTL = TTL_Func.getInstance().Judge(dttl, dMaxTTL);
        Map.put("dMaxTTL", dMaxTTL);
        Map.put("dBaseTTL", dttl);
        Map.put("dInitialTTL", dInitialTTL);
        Map.put("dTTLMax", dttl);
        Map.put("dTTLMin", dttlmin);
    }

    public void AddConnectHandle(ZMPNMsg.JKNmsg msg, Map<String, Object> Map) {
        //  添加地址信息
        TTLContCrc(msg, Map);
        Map.put("ProName", ProNameForMysql.getInstance().GetProName(msg.getSingleSession().getCommMsg().getIppro()));
        String dIp = msg.getSingleSession().getCommMsg().getDstIp();
        String sIp = msg.getSingleSession().getCommMsg().getSrcIp();
        String sMac = msg.getSingleSession().getSsBasic().getSmac();
        String dMac = msg.getSingleSession().getSsBasic().getDmac();
        int sPort = msg.getSingleSession().getCommMsg().getSrcPort();
        int dPort = msg.getSingleSession().getCommMsg().getDstPort();
        int Ippro = msg.getSingleSession().getCommMsg().getIppro();
        int AppId = msg.getSingleSession().getCommMsg().getAppId();
        Map.put("TotalBytes", msg.getSingleSession().getSsPkt().getPktDbytes() + msg.getSingleSession().getSsPkt().getPktSbytes());
        Map.put("TotalPacketNum", msg.getSingleSession().getSsPkt().getPktSnum() + msg.getSingleSession().getSsPkt().getPktDnum());

//        Map.put("sip_appid_dport_dip", sIp + "_" + String.valueOf(Ippro) + "_" + String.valueOf(dPort) + "_" + dIp);
        Map.put("sip_appid_dport_dip", sIp + "_" + String.valueOf(AppId) + "_" + String.valueOf(dPort) + "_" + dIp);
        Map.put("mac2mac", sMac + "_" + dMac);
        Map.put("ip2ip", sIp + "_" + dIp);
        Map.put("ip_port_ippro_appid ", dIp + "_" + String.valueOf(dPort) + "_" + String.valueOf(Ippro) + "_" + String.valueOf(AppId));
        CityResponse dIpRes = IP2Addr.getInstance().getAddrInfo(dIp);
        if (dIpRes != null) {
            Map.put("dIpCity", IP2Addr.getInstance().getCity(dIpRes));
            Map.put("dIpCountry", IP2Addr.getInstance().getCountry(dIpRes));
            Map.put("dIpSubdivisions", IP2Addr.getInstance().getSubdivisions(dIpRes));
            Map.put("dIpLongitude", IP2Addr.getInstance().getLongitude(dIpRes));
            Map.put("dIpLatitude", IP2Addr.getInstance().getLatitude(dIpRes));
        } else {
            Map.put("dIpCity", "");
            Map.put("dIpCountry", "");
            Map.put("dIpSubdivisions", "");
            Map.put("dIpLongitude", 0.0);
            Map.put("dIpLatitude", 0.0);
        }


        CityResponse sIpRes = IP2Addr.getInstance().getAddrInfo(sIp);
        if (sIpRes != null) {
            Map.put("sIpCity", IP2Addr.getInstance().getCity(sIpRes));
            Map.put("sIpCountry", IP2Addr.getInstance().getCountry(sIpRes));
            Map.put("sIpSubdivisions", IP2Addr.getInstance().getSubdivisions(sIpRes));
            Map.put("sIpLongitude", IP2Addr.getInstance().getLongitude(sIpRes));
            Map.put("sIpLatitude", IP2Addr.getInstance().getLatitude(sIpRes));
        } else {
            Map.put("sIpCity", "");
            Map.put("sIpCountry", "");
            Map.put("sIpSubdivisions", "");
            Map.put("sIpLongitude", 0.0);
            Map.put("sIpLatitude", 0.0);
        }
    }

    // Connection 会话元数据
    public Map<String, Object> Msg3009Handle(ZMPNMsg.JKNmsg msg) {

        List<Integer> rule_labelsList = msg.getSingleSession().getRuleLabelsList();
        List<Object> rule_labelsListValues = new ArrayList<>();
        for (Integer tmp : rule_labelsList) {

            rule_labelsListValues.add(tmp);
        }
        List<ZMPNMsg.single_http> httpList = msg.getSingleSession().getHttpList();
        List<Object> httpListValues = new ArrayList<>();
        for (ZMPNMsg.single_http tmp : httpList) {
            Map<String, Object> httpMap = new HashMap<>();
            httpMap.put("Url", tmp.getUrl());
            httpMap.put("Act", tmp.getAct());
            httpMap.put("Host", tmp.getHost());
            httpMap.put("User-Agent", tmp.getUserAgent());
            httpMap.put("Response", tmp.getResponse());

            httpListValues.add(httpMap);
        }
        List<ZMPNMsg.single_dns> dnsList = msg.getSingleSession().getDnsList();
        List<Object> dnsListValues = new ArrayList<>();
        for (ZMPNMsg.single_dns tmp : dnsList) {
            Map<String, Object> dnsMap = new HashMap<>();
            dnsMap.put("Domain", tmp.getDomain());
            dnsMap.put("DomainIp", tmp.getDomainIp());

            dnsListValues.add(dnsMap);
        }
        List<ZMPNMsg.single_ssl> sslList = msg.getSingleSession().getSslList();
        List<Object> sslListValues = new ArrayList<>();
        for (ZMPNMsg.single_ssl tmp : sslList) {
            Map<String, Object> sslMap = new HashMap<>();
            sslMap.put("CH_Ciphersuit", tmp.getChCiphersuit());
            sslMap.put("CH_CiphersuitNum", tmp.getChCiphersuitNum());
            sslMap.put("CH_ServerName", tmp.getChServerName());
            sslMap.put("CH_ALPN", JsonArrayToMap(tmp.getChAlpn()));
            sslMap.put("sCertHash", JsonArrayToMap(tmp.getCCert()));
            sslMap.put("dCertHash", JsonArrayToMap(tmp.getSCert()));

            sslListValues.add(sslMap);
        }
        JKNmsgMap.put("sIp", msg.getSingleSession().getCommMsg().getSrcIp());
        JKNmsgMap.put("sPort", msg.getSingleSession().getCommMsg().getSrcPort());
        JKNmsgMap.put("dIp", msg.getSingleSession().getCommMsg().getDstIp());
        JKNmsgMap.put("dPort", msg.getSingleSession().getCommMsg().getDstPort());
        JKNmsgMap.put("IPPro", msg.getSingleSession().getCommMsg().getIppro());
        JKNmsgMap.put("Labels", rule_labelsListValues);
        JKNmsgMap.put("SessionId", msg.getSingleSession().getCommMsg().getSessionId());
        JKNmsgMap.put("AppName", msg.getSingleSession().getCommMsg().getAppName());
        JKNmsgMap.put("ThreadId", msg.getSingleSession().getCommMsg().getThreadId());
        JKNmsgMap.put("Duration", msg.getSingleSession().getDuration());
        JKNmsgMap.put("StartTime", msg.getSingleSession().getCommMsg().getBeginTime());
        JKNmsgMap.put("EndTime", msg.getSingleSession().getEndTime());
        JKNmsgMap.put("FirstProto", msg.getSingleSession().getFirstProto());
        Map<String, Object> ss_pktMap = new HashMap<>();

        ss_pktMap.put("sPayloadNum", msg.getSingleSession().getSsPkt().getPktSpayloadnum());
        ss_pktMap.put("sPayloadBytes", msg.getSingleSession().getSsPkt().getPktSpayloadbytes());
        ss_pktMap.put("dPayloadNum", msg.getSingleSession().getSsPkt().getPktDpayloadnum());
        ss_pktMap.put("dPayloadBytes", msg.getSingleSession().getSsPkt().getPktDpayloadbytes());

        JKNmsgMap.put("pkt", ss_pktMap);
        JKNmsgMap.put("SbytesDbytesDivi", msg.getSingleSession().getSsPkt().getPktDnum());
        JKNmsgMap.put("sMac", msg.getSingleSession().getSsBasic().getSmac());
        JKNmsgMap.put("sSSLFinger", LongToString(msg.getSingleSession().getSslCFinger()));
        JKNmsgMap.put("sHTTPFinger", LongToString(msg.getSingleSession().getHttpCFinger()));
        JKNmsgMap.put("dMac", msg.getSingleSession().getSsBasic().getDmac());
        JKNmsgMap.put("dSSLFinger", LongToString(msg.getSingleSession().getSslSFinger()));
        JKNmsgMap.put("dHTTPFinger", LongToString(msg.getSingleSession().getHttpCFinger()));
        JKNmsgMap.put("HTTP", httpListValues);
        JKNmsgMap.put("DNS", dnsListValues);
        JKNmsgMap.put("SSL", sslListValues);

        List<String> dPayList = protoList2List(msg.getSingleSession().getSsPkt().getPktDpayloadList());
        List<String> sPayList = protoList2List(msg.getSingleSession().getSsPkt().getPktSpayloadList());

        dPayList = correctPayload(dPayList);
        sPayList = correctPayload(sPayList);

        JKNmsgMap.put("Server4PayloadList",dPayList);
        JKNmsgMap.put("Client4PayloadList",sPayList);
        JKNmsgMap.put("pktInfo",getPktInfo(msg));

        String proListString = msg.getSingleSession().getSsStats().getStatsProlist();
        com.alibaba.fastjson2.JSONObject ProStackJson = com.alibaba.fastjson2.JSON.parseObject(proListString);
        com.alibaba.fastjson2.JSONArray proListArray = (com.alibaba.fastjson2.JSONArray)ProStackJson.getOrDefault("ProStack",new com.alibaba.fastjson2.JSONArray());
        String TranProto = "";
        String AppProto = msg.getSingleSession().getCommMsg().getAppName();
        for (int i = 0; i < proListArray.size(); i++) {
            com.alibaba.fastjson2.JSONObject pro = proListArray.getJSONObject(i);
            if (i==2){
                TranProto = (String) pro.getOrDefault("Pro","");
            }
//            if (i==3){
//                AppProto = (String) pro.getOrDefault("Pro","");
//            }
        }

        // 如果ProList中没有解析到应用层协议，就用AppName替代
//        if ("".equals(AppProto)){
//        AppProto = msg.getSingleSession().getCommMsg().getAppName();
//        }

        JKNmsgMap.put("TranProto", TranProto);
        JKNmsgMap.put("AppProto", AppProto);

        return JKNmsgMap;
    }

    // DNS 域名解析元数据
    public Map<String, Object> Msg148Handle(ZMPNMsg.JKNmsg msg) {

        JKNmsgMap.put("sIp", msg.getDns().getCommMsg().getSrcIp());
        JKNmsgMap.put("sPort", msg.getDns().getCommMsg().getSrcPort());
        JKNmsgMap.put("dIp", msg.getDns().getCommMsg().getDstIp());
        JKNmsgMap.put("dPort", msg.getDns().getCommMsg().getDstPort());
        JKNmsgMap.put("SessionId", msg.getDns().getCommMsg().getSessionId());
        JKNmsgMap.put("AppName", msg.getDns().getCommMsg().getAppName());
        JKNmsgMap.put("TaskId", msg.getDns().getCommMsg().getTaskId());
        JKNmsgMap.put("StartTime", msg.getDns().getCommMsg().getBeginTime());
        JKNmsgMap.put("StartNSec", msg.getDns().getCommMsg().getBeginNsec());
        JKNmsgMap.put("Flags", msg.getDns().getDnsFlags());
        JKNmsgMap.put("Que", msg.getDns().getDnsQue());
        JKNmsgMap.put("Ans", msg.getDns().getDnsAns());
        JKNmsgMap.put("Auth", msg.getDns().getDnsAuth());
        JKNmsgMap.put("Add", msg.getDns().getDnsAdd());
        JKNmsgMap.put("Query", JsonArrayToMap(msg.getDns().getDnsQuery()));
        JKNmsgMap.put("Answer", JsonArrayToMap(msg.getDns().getDnsAnswer()));
        JKNmsgMap.put("Domain", msg.getDns().getDnsDomain());
        JKNmsgMap.put("DomainIp", msg.getDns().getDnsDomainIp());

        JKNmsgMap.put("TranProto", "UDP");
        JKNmsgMap.put("AppProto", "DNS");
        return JKNmsgMap;
    }

    // HTTP协议解析元数据
    public Map<String, Object> Msg3008Handle(ZMPNMsg.JKNmsg msg) {

        JKNmsgMap.put("sIp", msg.getHttpHeader().getCommMsg().getSrcIp());
        JKNmsgMap.put("sPort", msg.getHttpHeader().getCommMsg().getSrcPort());
        JKNmsgMap.put("dIp", msg.getHttpHeader().getCommMsg().getDstIp());
        JKNmsgMap.put("dPort", msg.getHttpHeader().getCommMsg().getDstPort());
        JKNmsgMap.put("ServerIP", msg.getHttpHeader().getCommMsg().getServerIp());
        JKNmsgMap.put("SessionId", msg.getHttpHeader().getCommMsg().getSessionId());
        JKNmsgMap.put("TaskId", msg.getHttpHeader().getCommMsg().getTaskId());
        JKNmsgMap.put("BatchNum", msg.getHttpHeader().getCommMsg().getBatchId());
        JKNmsgMap.put("StartTime", msg.getHttpHeader().getCommMsg().getBeginTime());
        JKNmsgMap.put("StartNSec", msg.getHttpHeader().getCommMsg().getBeginNsec());
        JKNmsgMap.put("Url", msg.getHttpHeader().getUrl());
        JKNmsgMap.put("Act", msg.getHttpHeader().getAct());
        JKNmsgMap.put("Host", msg.getHttpHeader().getHost());
        JKNmsgMap.put("Response", msg.getHttpHeader().getResponse());
        JKNmsgMap.put("sHTTPFinger", LongToString(msg.getHttpHeader().getHttpCFinger()));
        JKNmsgMap.put("dHTTPFinger", LongToString(msg.getHttpHeader().getHttpSFinger()));

        JKNmsgMap.put("TranProto", "TCP");
        JKNmsgMap.put("AppProto", "HTTP");
        return JKNmsgMap;
    }

    // SSL协议解析数据
    public Map<String, Object> Msg145Handle(ZMPNMsg.JKNmsg msg) {

        JKNmsgMap.put("sIp", msg.getSsl().getCommMsg().getSrcIp());
        JKNmsgMap.put("sPort", msg.getSsl().getCommMsg().getSrcPort());
        JKNmsgMap.put("dIp", msg.getSsl().getCommMsg().getDstIp());
        JKNmsgMap.put("dPort", msg.getSsl().getCommMsg().getDstPort());
        JKNmsgMap.put("SessionId", msg.getSsl().getCommMsg().getSessionId());
        JKNmsgMap.put("AppName", msg.getSsl().getCommMsg().getAppName());
        JKNmsgMap.put("TaskId", msg.getSsl().getCommMsg().getTaskId());
        JKNmsgMap.put("BatchNum", msg.getSsl().getCommMsg().getBatchId());
        JKNmsgMap.put("StartTime", msg.getSsl().getCommMsg().getBeginTime());
        JKNmsgMap.put("StartNSec", msg.getSsl().getCommMsg().getBeginNsec());
        JKNmsgMap.put("cSSLVersion", msg.getSsl().getSslCVersion());
        JKNmsgMap.put("CH_Version", msg.getSsl().getSslHelloCVersion());
        JKNmsgMap.put("CH_Time", msg.getSsl().getSslHelloCTime());
        JKNmsgMap.put("CH_SessionIDLen", msg.getSsl().getSslHelloCSessionidlen());
        JKNmsgMap.put("CH_Ciphersuit", msg.getSsl().getSslHelloCCiphersuit());
        JKNmsgMap.put("CH_CiphersuitNum", msg.getSsl().getSslHelloCCiphersuitnum());
        JKNmsgMap.put("CH_CompressionMethod", msg.getSsl().getSslHelloCCompressionmethod());
        JKNmsgMap.put("CH_CompressionMethodLen", msg.getSsl().getSslHelloCCompressionmethodlen());
        JKNmsgMap.put("CH_ExtentionNum", msg.getSsl().getSslHelloCExtentionnum());
        JKNmsgMap.put("CH_Extention", JsonArrayToMap(msg.getSsl().getSslHelloCExtention()));
        JKNmsgMap.put("CH_ServerName", msg.getSsl().getSslHelloCServername());
        JKNmsgMap.put("CH_ServerNameType", msg.getSsl().getSslHelloCServernametype());
        JKNmsgMap.put("CH_SessionTicket", msg.getSsl().getSslHelloCSessionticket());
        JKNmsgMap.put("CH_ALPN", JsonArrayToMap(msg.getSsl().getSslHelloCAlpn()));
        JKNmsgMap.put("sCertHash", JsonArrayToMap(msg.getSsl().getSslCertCHash()));
        JKNmsgMap.put("sCertNum", msg.getSsl().getSslCertCNum());
        JKNmsgMap.put("sKeyExchange", msg.getSsl().getSslCKeyexchange());
        JKNmsgMap.put("sKeyExchangeLen", msg.getSsl().getSslCKeyexchangelen());
        JKNmsgMap.put("sSSLFinger", LongToString(msg.getSsl().getSslCFinger()));
        JKNmsgMap.put("sSSLVersion", msg.getSsl().getSslVersion());
        JKNmsgMap.put("SH_Version", msg.getSsl().getSslHelloSVersion());
        JKNmsgMap.put("SH_Time", msg.getSsl().getSslHelloSTime());
        JKNmsgMap.put("SH_Random", msg.getSsl().getSslHelloSRandom());
        JKNmsgMap.put("SH_SessionId", msg.getSsl().getSslHelloSSessionid());
        JKNmsgMap.put("SH_SessionIdLen", msg.getSsl().getSslHelloSSessionidlen());
        JKNmsgMap.put("SH_Cipersuite", msg.getSsl().getSslHelloSCipersuite());
        JKNmsgMap.put("SH_CompressionMethod", msg.getSsl().getSslHelloSCompressionmethod());
        JKNmsgMap.put("SH_SessionTicket", msg.getSsl().getSslHelloSSessionticket());
        JKNmsgMap.put("SH_Alpn", msg.getSsl().getSslHelloSAlpn());
        JKNmsgMap.put("SH_Extention", JsonArrayToMap(msg.getSsl().getSslHelloSExtention()));
        JKNmsgMap.put("dCertHash", JsonArrayToMap(msg.getSsl().getSslCertSHash()));
        JKNmsgMap.put("dCertHashStr", msg.getSsl().getSslCertSHash());
        JKNmsgMap.put("dCertNum", msg.getSsl().getSslCertSNum());
        JKNmsgMap.put("dKeyExchange", msg.getSsl().getSslSKeyexchange());
        JKNmsgMap.put("dKeyExchangelen", msg.getSsl().getSslSKeyexchangelen());
        JKNmsgMap.put("dNewSessionTicket_LifeTime", msg.getSsl().getSslSNewsessionticketLifetime());
        JKNmsgMap.put("dNewSessionTicket_Ticket", msg.getSsl().getSslSNewsessionticketTicket());
        JKNmsgMap.put("dNewSessionTicket_TicketLen", msg.getSsl().getSslSNewsessionticketTicketlen());
        JKNmsgMap.put("dSSLFinger", LongToString(msg.getSsl().getSslSFinger()));

        JKNmsgMap.put("TranProto", "TCP");
        JKNmsgMap.put("AppProto", "SSL");


        return JKNmsgMap;
    }

    public static List<String> protoList2List(ProtocolStringList protoList){
        List<String> list = protoList.stream()
                .filter(s -> !"None".equals(s))
                .collect(Collectors.toList());
        return list;
    }

    private List<String> correctPayload(List<String> payloads) {
        List<String> resultList = new ArrayList<>();
        for (String payload:payloads){
            char lastChar = payload.charAt(payload.length() - 1);

            // 检查最后一位字符是否是有效的十六进制字符
            if (!isValidHexChar(lastChar)) {
                // 如果不是有效的十六进制字符，去掉它
//                System.out.println("Invalid character removed: " + lastChar);
                payload = payload.substring(0, payload.length() - 1);
            }
            resultList.add(payload);
        }
        return resultList;
    }

    public static boolean isValidHexChar(char c) {
        return Character.digit(c, 16) != -1;
    }

    /**
     * direction:方向 0:客户端到服务端, 1:服务端到客户端
     * byteNum:单包的包数量都是1，此字段为包大小
     * singleTime：本次单包的时间
     * nSingleTime：本次单包的时间,ns
     * count：该包序列的位次
     * */
    public static List<Map<String,Object>> getPktInfo(ZMPNMsg.JKNmsg msg){
        List<Map<String,Object>> pktInfo = new ArrayList<>();
        List<ZMPNMsg.packet_info_msg> packetInfoList = msg.getSingleSession().getSsPkt().getPktInforList();
        for (ZMPNMsg.packet_info_msg packetInfoMsg:packetInfoList){
            Map<String,Object> packetInfo = new HashMap<>();
            int direction = packetInfoMsg.getLen() >= 0 ? 0 : 1;
            packetInfo.put("direction",direction);
            packetInfo.put("singleTime",packetInfoMsg.getSec());
            packetInfo.put("nSingleTime",packetInfoMsg.getNsec());
            packetInfo.put("byteNum",packetInfoMsg.getLen());
            packetInfo.put("count",packetInfoMsg.getCount());
            pktInfo.add(packetInfo);
        }
        return pktInfo;
    }
}