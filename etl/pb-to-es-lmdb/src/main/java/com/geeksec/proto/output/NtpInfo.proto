syntax = "proto2";

message NtpInfo
{
    optional    bytes   NTPMode         = 1;
    optional    uint32  stratum         = 2;
    optional    uint32  precision       = 3;
    optional    uint32  rooDelay        = 4;
    optional    uint32  rooDis          = 5;
    optional    bytes   refID           = 6;
    optional    uint64  refTime         = 7;
    optional    uint32  ver             = 8;
    optional    uint64  orgTimeStamp    = 9;
    optional    uint64  recvTimeStamp   = 10;
    optional    uint64  transTimeStamp  = 11;
    optional    uint32  assId           = 12;
    optional    uint32  monlisremipaddr = 13;
    optional    uint32  monlislocipaddr = 14;
    optional    uint32  monlispor       = 15;
    optional    uint32  monlismod       = 16;
    optional    uint32  monlisntpver    = 17;
    optional    uint32  monlisremipv6   = 18;
    optional    uint32  monlislocipv6   = 19;
    optional    uint32  rspbit          = 20;
    optional    uint32  sta             = 21;
    optional    uint32  opcode          = 22;
	
}
