syntax = "proto2";

message DnsInfo
{
	optional	uint32		addCnt			=	1;
	repeated	bytes		aip				=	2;
	repeated	bytes		aipAsn			=	3;
	optional	uint32		aipCnt			=	4;
	repeated	bytes		aIpv6			=	5;
	optional	uint32		aIpv6Cnt		=	6;
	repeated	bytes		aipCountry		=	7;
	repeated	bytes		ansCname		=	8;
	optional	uint32		ansCnameCnt		=	9;
	optional	uint32		ansCnt			=	10;
	repeated	bytes		ansIPv6			=	11;
	optional	bytes		ansQue			=	12;
	repeated	bytes		ansTypes		=	13;
	optional	uint32		autCnt			=	14;
	repeated	bytes		mxIpAsn			=	15;
	repeated	bytes		mxIpCountry		=	16;
	repeated	bytes		mailSrvHost		=	17;
	optional	uint32		mailSrvHostcnt	=	18;
	repeated	bytes		mailSrvIp		=	19;
	optional	uint32		mailSrvIPCnt	=	20;
	repeated	bytes		nameSrvAsn		=	21;
	repeated	bytes		nameSrvCountry	=	22;
	repeated	bytes		nameSrvHost		=	23;
	optional	uint32		nameSrvHostCnt	=	24;
	repeated	bytes		nsIp			=	25;
	optional	uint32		nsIpCnt			=	26;
	optional	bytes		ansName			=	27;
	optional	uint32		addRrs			=	28;
	optional	bytes		dnsSpf			=	29;
	optional	bytes		dnsTxt			=	30;
	optional	uint32		queType			=	31;
	optional	bytes		queName			=	32;
	optional	uint32		traID			=	33;
	optional	uint32		srvFlag			=	34;
	optional	bytes		ansRes			=	35;
	optional	uint32		authAnsType		=	36;
	optional	bytes		authAnsRes		=	37;
	optional	uint32		addAnsType		=	38;
	optional	bytes		addAnsRes		=	39;
	repeated	bytes		mxIpv6			=	40;
	optional	uint32		mxIpv6Cnt		=	41;
	repeated	bytes		nsIpv6			=	42;
	optional	uint32		nsIpv6Cnt		=	43;
}
