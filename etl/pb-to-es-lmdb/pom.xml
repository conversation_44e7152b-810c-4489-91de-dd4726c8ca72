<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.geeksec</groupId>
		<artifactId>nta-etl</artifactId>
        <version>2.0.3-SNAPSHOT</version>
    </parent>
	
    <artifactId>nta-etl-pb-to-es-lmdb</artifactId>
    <packaging>jar</packaging>

    <build>
        <plugins>
<!--            <plugin>-->
<!--                <groupId>org.sonatype.plugins</groupId>-->
<!--                <artifactId>nexus-staging-maven-plugin</artifactId>-->
<!--                <version>1.5.1</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>default-deploy</id>-->
<!--                        <phase>deploy</phase>-->
<!--                        <goals>-->
<!--                            <goal>deploy</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>1.6.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <executable>maven</executable>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                            <mainClass>com.geeksec.task.KafkaToEsAndLmdbEtlJob</mainClass>
                            <useUniqueVersions>false</useUniqueVersions>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.18.1</version>
                <configuration>
                    <useFile>false</useFile>
                    <disableXmlReport>true</disableXmlReport>
                    <includes>
                        <include>**/*Test.*</include>
                        <include>**/*Suite.*</include>
                    </includes>
                </configuration>
            </plugin>

            <!--<plugin>
                <groupId>org.xolstice.maven.plugins</groupId>
                <artifactId>protobuf-maven-plugin</artifactId>
                <version>0.6.1</version>
                <extensions>true</extensions>
                <configuration>
                    &lt;!&ndash; 工具版本 &ndash;&gt;
&lt;!&ndash;                    <protocArtifact>com.google.protobuf:protoc:0.6.1:exe:${os.detected.classifier}</protocArtifact>&ndash;&gt;
                    &lt;!&ndash;默认值，proto源文件路径&ndash;&gt;
                    <protoSourceRoot>${project.basedir}/src/main/java/com/geeksec/proto</protoSourceRoot>
                    &lt;!&ndash;默认值，proto目标java文件路径&ndash;&gt;
                    <outputDirectory>${project.basedir}/src/main/java/com/geeksec/proto/outpu1</outputDirectory>
                    &lt;!&ndash;设置是否在生成java文件之前清空outputDirectory的文件，默认值为true，设置为false时也会覆盖同名文件&ndash;&gt;
                    <clearOutputDirectory>false</clearOutputDirectory>
                </configuration>
                <executions>
                    <execution>
                        &lt;!&ndash;在执行mvn compile的时候会执行以下操作&ndash;&gt;
                        <phase>compile</phase>
                        <goals>
                            &lt;!&ndash;生成OuterClass类&ndash;&gt;
                            <goal>compile</goal>
                            &lt;!&ndash;生成Grpc类&ndash;&gt;
                            &lt;!&ndash;goal>compile-custom</goal&ndash;&gt;
                        </goals>
                    </execution>
                </executions>
            </plugin>-->

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.2.4</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <artifactSet>
                                <excludes>
                                    <exclude>com.google.code.findbugs:jsr305</exclude>
                                    <exclude>org.slf4j:*</exclude>
                                    <exclude>log4j:*</exclude>
                                </excludes>
                            </artifactSet>
                            <filters>
                                <filter>
                                    <!-- Do not copy the signatures in the META-INF folder.
                                    Otherwise, this might cause SecurityExceptions when using the JAR. -->
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                            <transformers combine.children="append">
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer">
                                </transformer>
                            </transformers>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
