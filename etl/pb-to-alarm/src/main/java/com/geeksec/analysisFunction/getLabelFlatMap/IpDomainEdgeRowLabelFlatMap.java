package com.geeksec.analysisFunction.getLabelFlatMap;

import com.geeksec.SpecProtocolEnum;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixList;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixListFactory;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Properties;

import static com.geeksec.common.LabelUtils.AlarmUtils.getLabelEdgeRow;

/**
 * <AUTHOR>
 * @Date 2022/11/2
 */

public class IpDomainEdgeRowLabelFlatMap extends RichFlatMapFunction<Row, Row> {
    private static final Logger logger = LoggerFactory.getLogger(IpDomainEdgeRowLabelFlatMap.class);
    private List<String> CDN_NAME_LIST = new ArrayList<>();

    public static List<String> MINE_DOMAIN_LIST = new ArrayList<>();
    public static PublicSuffixListFactory factory = null;
    public static PublicSuffixList suffixList = null;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        factory = new PublicSuffixListFactory();
        Properties properties = factory.getDefaults();
        properties.setProperty(PublicSuffixListFactory.PROPERTY_LIST_FILE, "/effective_tld_names.dat");
        suffixList = factory.build();
    }


    @Override
    public void flatMap(Row edgeRow, Collector<Row> collector) throws Exception {

        String InfoType = (String) edgeRow.getField(0);

        if (Objects.equals(InfoType, "DNS_PARSE_TO_EDGE")) {
            String domainAddr = edgeRow.getFieldAs(1).toString();
            String suffixDomain = suffixList.getRegistrableDomain(domainAddr);
            if (CDN_NAME_LIST.contains(suffixDomain)) {
                Row domainLabelRow = getLabelEdgeRow(edgeRow.getField(1), SpecProtocolEnum.CDN_DOMAIN.getCode());//9005CDN域名
                collector.collect(domainLabelRow);
//                logger.info("DOMAIN_IP_LABEL_EDGE 边插入{},9005",edgeRow.getField(1));

                Row ipCdnLabelRow = getLabelEdgeRow(edgeRow.getField(2),SpecProtocolEnum.CDN_IP.getCode());//1008CDN地址
                collector.collect(ipCdnLabelRow);
//                logger.info("DOMAIN_IP_LABEL_EDGE 边插入{},1008",edgeRow.getField(2));
            }//判断是否是DNS请求中的CDN通讯,打上域名CDN标签,对应的IP的CDN标签

            else if (MINE_DOMAIN_LIST.contains(suffixDomain)){
                Row domainMineRow = getLabelEdgeRow(edgeRow.getField(1), SpecProtocolEnum.MINE_DOMAIN.getCode());//9006疑似矿池域名
                collector.collect(domainMineRow);
                logger.info("DOMAIN_IP_LABEL_EDGE 边插入{},9006",edgeRow.getField(1));

                Row ipMineRow = getLabelEdgeRow(edgeRow.getField(2),SpecProtocolEnum.MINE_IP);//9008矿池IP
                collector.collect(ipMineRow);
                logger.info("DOMAIN_IP_LABEL_EDGE 边插入{},9008",edgeRow.getField(2));
            }//判断是否是DNS请求中的挖矿请求，打上挖矿域名标签，矿池IP标签
        } else if (Objects.equals(InfoType, "CLIENT_QUERY_DOMAIN_EDGE")){
            String domainAddr = edgeRow.getFieldAs(2).toString();
            String suffixDomain = suffixList.getRegistrableDomain(domainAddr);
            if (MINE_DOMAIN_LIST.contains(suffixDomain)) {
                Row domainMineRow = getLabelEdgeRow(edgeRow.getField(2),SpecProtocolEnum.MINE_DOMAIN.getCode());//9006疑似挖矿域名
                collector.collect(domainMineRow);
                logger.info("DOMAIN_IP_LABEL_EDGE 边插入{},9006",edgeRow.getField(2));

//                Row ipMinerow = getLabelEdgeRow(edgeRow.getField(1),"中毒客户端IP");//标签待定
//                collector.collect(ipMinerow);
//                logger.info("DOMAIN_IP_LABEL_EDGE 边插入");
            }//判断是否是挖矿的DNS请求，给矿池域名和尝试连接的客户端IP打上标签
        }

        else if (Objects.equals(InfoType, "SERVER_HTTP_CONNECT_DOMAIN_EDGE")){
            String domainAddr = edgeRow.getFieldAs(2).toString();
            String suffixDomain = suffixList.getRegistrableDomain(domainAddr);
            if (CDN_NAME_LIST.contains(suffixDomain)){
                Row domainLabelRow = getLabelEdgeRow(edgeRow.getField(2),SpecProtocolEnum.CDN_DOMAIN.getCode());//9005CDN域名
                collector.collect(domainLabelRow);
//                logger.info("DOMAIN_IP_LABEL_EDGE 边插入{},9005",edgeRow.getField(2));

                Row ipCdnLabelRow = getLabelEdgeRow(edgeRow.getField(1),SpecProtocolEnum.CDN_IP.getCode());//1008CDN地址
                collector.collect(ipCdnLabelRow);
//                logger.info("DOMAIN_IP_LABEL_EDGE 边插入{},1008",edgeRow.getField(1));
            }//判断是否是http会话中的CDN通讯，打上CDN标签

            if (MINE_DOMAIN_LIST.contains(suffixDomain)){
                Row domainMineRow = getLabelEdgeRow(edgeRow.getField(1),SpecProtocolEnum.MINE_DOMAIN.getCode());//9006疑似矿池域名
                collector.collect(domainMineRow);
                logger.info("DOMAIN_IP_LABEL_EDGE 边插入{},9006",edgeRow.getField(1));

                Row ipMineRow = getLabelEdgeRow(edgeRow.getField(2),SpecProtocolEnum.MINE_IP.getCode());//9008矿池IP
                collector.collect(ipMineRow);
                logger.info("DOMAIN_IP_LABEL_EDGE 边插入{},9008",edgeRow.getField(2));
            }//判断是否是DNS请求中的挖矿请求，打上挖矿域名标签，矿池IP标签
        }

        else if (Objects.equals(InfoType, "CLIENT_HTTP_CONNECT_DOMAIN_EDGE")){
            String domainAddr = edgeRow.getFieldAs(2).toString();
            String suffixDomain = suffixList.getRegistrableDomain(domainAddr);
            if (MINE_DOMAIN_LIST.contains(suffixDomain)) {
                Row domainMineRow = getLabelEdgeRow(edgeRow.getField(2),SpecProtocolEnum.MINE_DOMAIN.getCode());//9006疑似挖矿域名
                collector.collect(domainMineRow);
                logger.info("DOMAIN_IP_LABEL_EDGE 边插入{},9006",edgeRow.getField(2));

//                Row ipMinerow = getLabelEdgeRow(edgeRow.getField(1),"中毒客户端IP");//标签待定
//                collector.collect(ipMinerow);
//                logger.info("DOMAIN_IP_LABEL_EDGE 边插入");
            }//判断是否是挖矿的HTTP通讯，给矿池域名和尝试连接的客户端IP打上标签
        }

        else if (Objects.equals(InfoType, "CLIENT_SSL_CONNECT_DOMAIN_EDGE")){
            String domainAddr = edgeRow.getFieldAs(2).toString();
            String suffixDomain = suffixList.getRegistrableDomain(domainAddr);
            if (MINE_DOMAIN_LIST.contains(suffixDomain)) {
                Row domainMineRow = getLabelEdgeRow(edgeRow.getField(2),SpecProtocolEnum.MINE_DOMAIN.getCode());//9006疑似挖矿域名
                collector.collect(domainMineRow);
                logger.info("DOMAIN_IP_LABEL_EDGE 边插入{},9006",edgeRow.getField(2));

//                Row ipMinerow = getLabelEdgeRow(edgeRow.getField(1),"中毒客户端IP");//标签待定
//                collector.collect(ipMinerow);
//                logger.info("DOMAIN_IP_LABEL_EDGE 边插入");
            }//判断是否是挖矿的SSL通讯，给矿池域名和尝试连接的客户端IP打上标签
        }

        else if (Objects.equals(InfoType, "SERVER_SSL_CONNECT_DOMAIN_EDGE")){
            String domainAddr = edgeRow.getFieldAs(2).toString();
            String suffixDomain = suffixList.getRegistrableDomain(domainAddr);
            if (CDN_NAME_LIST.contains(suffixDomain)){
                Row domainLabelRow = getLabelEdgeRow(edgeRow.getField(2),SpecProtocolEnum.CDN_DOMAIN.getCode());//9005CDN域名
                collector.collect(domainLabelRow);
//                logger.info("DOMAIN_IP_LABEL_EDGE 边插入{},9005",edgeRow.getField(2));

                Row ipCdnLabelRow = getLabelEdgeRow(edgeRow.getField(1),SpecProtocolEnum.CDN_IP.getCode());//1008CDN地址
                collector.collect(ipCdnLabelRow);
//                logger.info("DOMAIN_IP_LABEL_EDGE 边插入{},1008",edgeRow.getField(1));
            }//判断是否是SSL会话中的CDN通讯，打上CDN标签，CDN的IP地址标签

            if (MINE_DOMAIN_LIST.contains(suffixDomain)){
                Row domainMineRow = getLabelEdgeRow(edgeRow.getField(1),SpecProtocolEnum.MINE_DOMAIN.getCode());//9006疑似矿池域名
                collector.collect(domainMineRow);
                logger.info("DOMAIN_IP_LABEL_EDGE 边插入{},9006",edgeRow.getField(1));

                Row ipMineRow = getLabelEdgeRow(edgeRow.getField(2),SpecProtocolEnum.MINE_IP.getCode());//9008矿池IP
                collector.collect(ipMineRow);
                logger.info("DOMAIN_IP_LABEL_EDGE 边插入{},9008",edgeRow.getField(2));
            }//判断是否是SSL会话中的挖矿通讯，打上挖矿域名标签，矿池IP标签
        }
    }
}
