package com.geeksec.analysisFunction.getLabelFlatMap;

import static com.geeksec.analysisFunction.getLabelFlatMap.IpDomainEdgeRowLabelFlatMap.suffixList;
import static com.geeksec.analysisFunction.getPbMapInfo.DnsInfoMapFlatMapFunction.BenignDNSServer_Map;
import static com.geeksec.common.LabelUtils.AlarmUtils.getLabelEdgeRow;
import static com.geeksec.task.LabelKafka2ES.MINE_DOMAIN_LIST;

import com.geeksec.SpecProtocolEnum;
import com.geeksec.common.utils.DomainUtils;
import java.util.*;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2022/12/26
 */

public class DnsMineFlatMap extends RichFlatMapFunction<Row,Row> {
    private static final Logger logger = LoggerFactory.getLogger(DnsMineFlatMap.class);

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void flatMap(Row row, Collector<Row> collector) throws Exception {
        List<HashMap<String, Object>> DNS_List = row.getFieldAs(2);
        Set<String> Mine_Domain_Alarm = new HashSet<>();
        Set<String> Mine_Domain_IP_Alarm = new HashSet<>();
        for (HashMap<String, Object> DNS_INFO : DNS_List) {
            String Domain = (String) DNS_INFO.get("Domain");
            String Domain_IP_str = (String) DNS_INFO.get("DomainIp");
            List<String> Domain_IP_List = new ArrayList<>();
            if (Domain_IP_str.contains("|")){
                Domain_IP_List = Arrays.asList(Domain_IP_str.split("\\|").clone());
            }else{
                Domain_IP_List.add(Domain_IP_str);
            }
            if (DomainUtils.isValidDomain(Domain)) {
                String suffix_domain = suffixList.getRegistrableDomain(Domain);
                if (MINE_DOMAIN_LIST.contains(suffix_domain)) {
                    Row domainMineRow = getLabelEdgeRow(Domain, SpecProtocolEnum.MINE_DOMAIN.getCode());//9006疑似挖矿域名
                    collector.collect(domainMineRow);
                    logger.info("MINE_DOMAIN_LABEL_EDGE 边插入{},9006", Domain);
                    Mine_Domain_Alarm.add(Domain);
                    if (Domain_IP_List != null) {
                        for (String mine_IP : Domain_IP_List) {
                            if (!BenignDNSServer_Map.contains(mine_IP) && !mine_IP.equals("")) {
                                Row ipMineRow = getLabelEdgeRow(mine_IP, SpecProtocolEnum.MINE_IP.getCode());//9008矿池IP
                                collector.collect(ipMineRow);
                                logger.info("MINE_IP_LABEL_EDGE 边插入{},9008", mine_IP);
                                Mine_Domain_IP_Alarm.add(mine_IP);
                            }
                        }
                    }
                }
            }
        }
        //产生告警
        if (Mine_Domain_Alarm.size() > 0 && Mine_Domain_IP_Alarm.size() > 0) {
            Row Mine_alarm_row = new Row(8);
            List<String> label_list = Arrays.asList(SpecProtocolEnum.MINE_DOMAIN.getCode(),SpecProtocolEnum.MINE_IP.getCode());
            Mine_alarm_row.setField(0, "尝试挖矿连接");
            Mine_alarm_row.setField(1, row.getField(1));
            Mine_alarm_row.setField(2, Mine_Domain_IP_Alarm);
            Mine_alarm_row.setField(3, Mine_Domain_Alarm);
            Mine_alarm_row.setField(4, label_list);
            Mine_alarm_row.setField(5, row.getField(3));
            Mine_alarm_row.setField(6, row.getField(4));
            Mine_alarm_row.setField(7, row.getField(5));
            logger.info("尝试挖矿连接告警{}",Mine_Domain_Alarm);
            collector.collect(Mine_alarm_row);
        }
    }
}
