package com.geeksec.analysisFunction.analysisEntity.tunnel;

import com.geeksec.analysisFunction.analysisEntity.connectRelatedInfo.ConnectBasicInfo;
import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/7/25
 */

@Data
public class BasicTunnel {

    /**
     *  基础会话信息
     */
    private ConnectBasicInfo connectBasicInfo;

    /**
     *  隐蔽信道类型
     */
    private String tunnelType;

    /**
     * 告警携带信息
     */
    private NeededInfo neededInfo;
}
