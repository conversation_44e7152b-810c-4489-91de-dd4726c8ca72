package com.geeksec.analysisFunction.writeAlarm;

import static com.geeksec.analysisFunction.getRowInfo.SslInfoRowFlatMap.Alarm_Info_Map;
import static com.geeksec.common.LabelUtils.AlarmUtils.get_attack_chain_list;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.connectRelatedInfo.ConnectBasicInfo;
import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import com.geeksec.analysisFunction.analysisEntity.protocolRelatedInfo.SRCPInfo;
import com.geeksec.common.LabelUtils.AlarmUtils;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

/**
 * <AUTHOR>
 * @Date 2024/8/2
 */

public class writeSRCPC2Alarm {
    private static final Logger logger = LoggerFactory.getLogger(writeSRCPC2Alarm.class);
    public static JSONObject getSRCPC2AlarmJson(Row srcpRow, Jedis jedis) throws NoSuchAlgorithmException {

        Row InfoRow = transToInfoRow(srcpRow);

        String alarmType = InfoRow.getFieldAs(0);
        if (!Alarm_Info_Map.keySet().contains(alarmType)) {
            return null;
        }
        Map<String, Object> srcpAlarmJson = AlarmUtils.get_known_alarm_info(InfoRow);
        srcpAlarmJson.putAll(AlarmUtils.get_Default_alarm_map());
        //reason
        List<Map<String,Object>> alarmReason = get_alarm_reason(srcpRow);
        String dIp = InfoRow.getFieldAs(3);
        String sIp = InfoRow.getFieldAs(2);
        //attack_family
        List<Map<String,String>> attackFamily = get_family();
        //targets
        List<Map<String,Object>> targets = get_targets(InfoRow);

        NeededInfo neededInfo = InfoRow.getFieldAs(7);
        srcpAlarmJson.put("vPort",neededInfo.getSPort());
        srcpAlarmJson.put("aPort",neededInfo.getDPort());
        srcpAlarmJson.put("sPort",neededInfo.getSPort());
        srcpAlarmJson.put("dPort",neededInfo.getDPort());
        srcpAlarmJson.put("tranProto",neededInfo.getTranProto());
        srcpAlarmJson.put("appProto",neededInfo.getAppProto());
        srcpAlarmJson.put("httpDomain",neededInfo.getHttpDomain());
        srcpAlarmJson.put("sniDomain",neededInfo.getSniDomain());
        srcpAlarmJson.put("sIp",neededInfo.getSIp());
        srcpAlarmJson.put("dIp",neededInfo.getDIp());

        Set<String> attacker_set = new HashSet<>();
        attacker_set.add(dIp);
        Set<String> victim_set = new HashSet<>();
        victim_set.add(sIp);
        //victim
        List<Map<String,String>> victim = get_victim(victim_set);
        if (!victim_set.contains(neededInfo.getSIp())){
            Map<String,String> victim_map = new HashMap<>();
            victim_map.put("ip",neededInfo.getSIp());
            victim.add(victim_map);
        }
        //attacker
        List<Map<String,String>> attacker = get_attacker(attacker_set);
        if (!attacker_set.contains(neededInfo.getDIp())){
            Map<String,String> victim_map = new HashMap<>();
            victim_map.put("ip",neededInfo.getDIp());
            attacker.add(victim_map);
        }
        List<String> alarmRelatedLabel = new ArrayList<>(InfoRow.getFieldAs(4));
        List<Map<String,String>> attackRoute = get_attack_route();
        Collection<String> alarmSessionList = Collections.singletonList(InfoRow.getFieldAs(5));
        List<String> alarmAttackChainList = get_attack_chain_list(victim,attacker,alarmRelatedLabel, (String) srcpAlarmJson.get("alarm_knowledge_id"));

        srcpAlarmJson.put("alarm_reason",alarmReason);
        srcpAlarmJson.put("attack_family",attackFamily);
        srcpAlarmJson.put("targets",targets);
        String alarmPrinciple = get_alarm_principle(InfoRow);
        srcpAlarmJson.put("alarm_principle",alarmPrinciple);
        String alarmHandleMethod = get_alarm_handle_method();
        srcpAlarmJson.put("alarm_handle_method",alarmHandleMethod);
        srcpAlarmJson.put("alarm_type","模型");
        srcpAlarmJson.put("victim",victim);
        srcpAlarmJson.put("attacker",attacker);
        srcpAlarmJson.put("alarm_related_label",alarmRelatedLabel);
        srcpAlarmJson.put("attack_route",attackRoute);
        srcpAlarmJson.put("alarm_session_list",alarmSessionList.toArray());
        srcpAlarmJson.put("attack_chain_list",alarmAttackChainList);
        String modelId = getModelId(InfoRow);
        srcpAlarmJson.put("model_id",modelId);
        List<String> pcapFileList = AlarmUtils.getPcapFileList(alarmSessionList,jedis);
        srcpAlarmJson.put("PcapFileList",pcapFileList);
        
        String esKey = InfoRow.getFieldAs(6);
        String taskId = esKey.split("_")[1];
        String batchId = esKey.split("_")[2];
        Map<String,Object> send_data = AlarmUtils.get_send_data(srcpAlarmJson,taskId,batchId);
        JSONObject alarm_json = new JSONObject();
        alarm_json.putAll(send_data);
        return alarm_json;
    }

    private static String getModelId(Row infoRow) {
        return "99030";
    }

    private static Row transToInfoRow(Row srcpRow){

        SRCPInfo srcpInfo = (SRCPInfo) srcpRow.getField(1);

        ConnectBasicInfo connectBasicInfo = srcpInfo.getConnectBasicInfo();

        List<String> analysisLabelList = connectBasicInfo.getAnalysisLabelList();

        Row InfoRow =new Row(8);
        InfoRow.setField(0,"标准远程控制协议下的C2行为");
        // 远程控制协议的类型
        InfoRow.setField(1,srcpInfo.getRCPType());
        // sIp
        InfoRow.setField(2,connectBasicInfo.getSIp());
        // dIp
        InfoRow.setField(3,connectBasicInfo.getDIp());
        // 关联新增标签
        InfoRow.setField(4,analysisLabelList);
        InfoRow.setField(5,connectBasicInfo.getSessionId());
        InfoRow.setField(6,connectBasicInfo.getEsKey());
        InfoRow.setField(7,srcpInfo.getNeededInfo());

        return InfoRow;
    }

    private static List<Map<String,Object>> get_alarm_reason(Row row){

        SRCPInfo srcpInfo = row.getFieldAs(1);
        ConnectBasicInfo connectBasicInfo = srcpInfo.getConnectBasicInfo();

        List<Map<String,Object>> alarmReasonList = new ArrayList<>();
        String baselineStr = null;
        if (srcpInfo.isInBaseline()){
            Set<String> dIpdPorts = srcpInfo.getDIpdPorts();
            Set<String> sIpdPorts = srcpInfo.getSIpdPorts();
            baselineStr = String.format("服务端基线：%s；客户端基线：%s",dIpdPorts,sIpdPorts);
        }else {
            baselineStr = "该会话中源目的IP的连接行为未出现在标准远程控制协议下的C2行为基线中";
        }
        // 网络行为基线的检测结果
        Map<String,Object> alarm_reason_protocol = new HashMap<>();
        alarm_reason_protocol.put("key","对该会话的网路行为进行行为基线的检测");
        alarm_reason_protocol.put("actual_value",baselineStr);
        alarmReasonList.add(alarm_reason_protocol);
        // 控制的检测结果
        if(connectBasicInfo.isControl()){
            Map<String,Object> alarm_reason_heartbeat = new HashMap<>();
            alarm_reason_heartbeat.put("key","对该会话的包序列中的包的发送方向及总包大小进行检测");
            alarm_reason_heartbeat.put("actual_value","发现了疑似控制行为的流量通信，工具产生的协议类型为"+srcpInfo.getRCPType());
            alarmReasonList.add(alarm_reason_heartbeat);
        }
        return alarmReasonList;
    }

    private static List<Map<String,String>> get_victim(Set<String> IP_list){
        List<Map<String,String>> result = new ArrayList<>();
        for(String ip:IP_list){
            Map<String,String> victim = new HashMap<>();
            victim.put("ip",ip);
            result.add(victim);
        }
        return result;
    }

    private static List<Map<String,String>> get_attacker(Set<String> IP_list){
        List<Map<String,String>> result = new ArrayList<>();
        for(String ip:IP_list){
            Map<String,String> attacker = new HashMap<>();
            attacker.put("ip",ip);
            result.add(attacker);
        }
        return result;
    }

    private static List<Map<String,Object>> get_targets(Row infoRow){
        String dIp = infoRow.getFieldAs(3);
        List<Map<String,Object>> targets = new ArrayList<>();
        Map<String,Object> target_ip = new HashMap<>();
        target_ip.put("name",dIp);
        target_ip.put("type","ip");
        List<String> labels_ip = new ArrayList<>();
        target_ip.put("labels",labels_ip);
        targets.add(target_ip);
        return targets;
    }

    private static List<Map<String,String>> get_family(){
        return new ArrayList<>();
    }

    private static String get_alarm_principle(Row infoRow){
        String RCPType = infoRow.getFieldAs(1);
        return String.format("基于标准远程控制协议的行为基线C2行为检测模型中发现 %s 协议中出现偏离行为基线的访问，并且在针对控制行为的检测中也发现了可疑的行为",RCPType);
    }

    private static String get_alarm_handle_method(){
        return "首先断开受感染设备的网络连接，防止恶意行为扩散；然后使用可靠的安全软件进行全面扫描和清除；及时更新操作系统和应用程序到最新版本，修补安全漏洞；加强账户密码，避免使用弱密码；备份重要数据，以防万一；最后，提高对可疑邮件和链接的警觉性，避免点击来源不明的链接或下载不明文件。";
    }

    private static List<Map<String,String>> get_attack_route(){
        return new ArrayList<>();
    }
}
