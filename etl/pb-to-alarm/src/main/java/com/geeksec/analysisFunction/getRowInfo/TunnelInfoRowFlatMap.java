package com.geeksec.analysisFunction.getRowInfo;

import com.geeksec.analysisFunction.analysisEntity.tunnel.BasicTunnel;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2024/7/26
 */

public class TunnelInfoRowFlatMap extends RichFlatMapFunction<BasicTunnel, Row> {

    private final static Logger logger = LoggerFactory.getLogger(TunnelInfoRowFlatMap.class);

    @Override
    public void close() throws Exception {
        super.close();
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void flatMap(BasicTunnel basicTunnel, Collector<Row> collector) throws Exception {
        // 隐蔽信道模型根据协议类型分流
        Row basicTunnelRow = new Row(2);
        switch (basicTunnel.getTunnelType()){
            case "ICMP":
                basicTunnelRow.setField(0,"icmp_tunnel_info");
                basicTunnelRow.setField(1,basicTunnel);
                collector.collect(basicTunnelRow);
                break;
            case "NTP":
                basicTunnelRow.setField(0,"ntp_tunnel_info");
                basicTunnelRow.setField(1,basicTunnel);
                collector.collect(basicTunnelRow);
                break;
            case "HTTP":
                basicTunnelRow.setField(0,"http_tunnel_info");
                basicTunnelRow.setField(1,basicTunnel);
                collector.collect(basicTunnelRow);
                break;
            case "TCP":
                basicTunnelRow.setField(0,"tcp_tunnel_info");
                basicTunnelRow.setField(1,basicTunnel);
                collector.collect(basicTunnelRow);
                break;
            case "SSL":
                basicTunnelRow.setField(0,"ssl_tunnel_info");
                basicTunnelRow.setField(1,basicTunnel);
                collector.collect(basicTunnelRow);
                break;
            default:
                logger.error("未知隧道类型：{}",basicTunnel.getTunnelType());
        }
    }
}
