package com.geeksec.analysisFunction.getLabelFlatMap;

import static com.geeksec.analysisFunction.getLabelFlatMap.TunnelProtocolFlatMapFunction.REDIS_SESSION_LABEL_COUNT_KEY;
import static com.geeksec.analysisFunction.getLabelFlatMap.TunnelProtocolFlatMapFunction.REDIS_SESSION_LABEL_KEY;
import static com.geeksec.common.LabelUtils.AlarmUtils.getLabelEdgeRow;

import com.geeksec.SpecProtocolEnum;
import com.geeksec.analysisFunction.analysisEntity.connectRelatedInfo.ConnectBasicInfo;
import com.geeksec.analysisFunction.analysisEntity.connectRelatedInfo.PacketInfo;
import com.geeksec.analysisFunction.analysisEntity.tunnel.*;
import com.geeksec.common.LabelUtils.LabelRedisUtils;
import java.util.*;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * <AUTHOR>
 * @Date 2024/7/26
 */

public class TunnelInfoFeatureFilterFunction extends RichFlatMapFunction<Row, Row> {

    private final static Logger logger = LoggerFactory.getLogger(TunnelInfoFeatureFilterFunction.class);
    private static transient JedisPool jedisPool = null;

    @Override
    public void close() throws Exception {
        super.close();
        if(!jedisPool.isClosed()){
            jedisPool.close();
        }
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // redis池 初始化.
        jedisPool = LabelRedisUtils.initJedisPool();
        logger.info("生成jedisPool成功! {}", jedisPool.getNumIdle(),jedisPool.hashCode());
    }


    /** 对每一个协议的字段中是否存在可疑特征，
     * 以及是否存在心跳，控制，激活等特征，
     * 来为每个可疑数据打上子标签，
     * 并输出到不同的流进行处理*/
    @Override
    public void flatMap(Row row, Collector<Row> collector) throws Exception {

        BasicTunnel basicTunnel = (BasicTunnel) row.getField(1);

        String SessionId = basicTunnel.getConnectBasicInfo().getSessionId();
        List<String> labels = basicTunnel.getConnectBasicInfo().getLabels();

        ConnectBasicInfo connectBasicInfo = basicTunnel.getConnectBasicInfo();

        // 对心跳，控制，激活的检测
        connectBasicInfo.handleHeartBeat();
        connectBasicInfo.handleControl();
        // TODO 是否是激活的检测
        connectBasicInfo.handleActivation();

        String tunnelType = basicTunnel.getTunnelType();
        Row alarmRow = new Row(4);
        Row SessionLabelRow = new Row(4);
        boolean isSuspicious = connectBasicInfo.isActivation() || connectBasicInfo.isControl() || connectBasicInfo.isHeartbeat();

        Jedis jedis = null;
        try{
            jedis = LabelRedisUtils.getJedis(jedisPool);
            jedis.select(6);
            switch (tunnelType) {
                case "TCP":
                    if (labels.contains(TcpTunnel.PROBE_TAG) && isSuspicious){
                        List<String> labelList = connectBasicInfo.getAnalysisLabelList();
                        labelList.add(TcpTunnel.PROBE_TAG);
                        connectBasicInfo.setAnalysisLabelList(labelList);
                        basicTunnel.setConnectBasicInfo(connectBasicInfo);

                        alarmRow.setField(0,"隐蔽隧道");
                        // 隐蔽隧道类型
                        alarmRow.setField(1,tunnelType);
                        // 隐蔽隧道基类
                        alarmRow.setField(2,basicTunnel);
                        //协议异常标签次数
                        alarmRow.setField(3,1);
                        collector.collect(alarmRow);

                        SessionLabelRow.setField(0,"会话打标");
                        SessionLabelRow.setField(1,SessionId);
                        Set<String> connectionLabels = new HashSet<>();
                        connectionLabels.add(TcpTunnel.PROBE_TAG);
                        connectionLabels.add(SpecProtocolEnum.TUNN_TCP.getCode());
                        SessionLabelRow.setField(2,connectionLabels);
                        SessionLabelRow.setField(3,connectBasicInfo.getEsKey());
                        // 给会话打心跳，控制，激活的标签
                        addSessionLabel(collector, basicTunnel, SessionId, SessionLabelRow);
                        collector.collect(SessionLabelRow);
                        Row tunnelToolRow = getLabelEdgeRow(connectBasicInfo.getDIp(),TcpTunnel.PROBE_TAG);//SSL隐蔽信道工具
                        collector.collect(tunnelToolRow);
                    }
                    break;
                case "HTTP":
                    String countHttpKey = REDIS_SESSION_LABEL_COUNT_KEY + "_" + SessionId + "_" + HttpTunnel.ETL_TAG;
                    String labelHttpKey = REDIS_SESSION_LABEL_KEY + "_" + SessionId;
                    if (jedis.exists(labelHttpKey) && jedis.exists(countHttpKey)){
                        Set<String> protocolLabels = jedis.smembers(labelHttpKey);
                        Integer httpLabelCount = Integer.valueOf(jedis.get(countHttpKey));
//                        if (protocolLabels.contains(HttpTunnel.ETL_TAG) && isSuspicious){
                        if (protocolLabels.contains(HttpTunnel.ETL_TAG)){
                            List<String> labelList = connectBasicInfo.getAnalysisLabelList();
                            labelList.add(HttpTunnel.ETL_TAG);
                            connectBasicInfo.setAnalysisLabelList(labelList);
                            basicTunnel.setConnectBasicInfo(connectBasicInfo);

                            alarmRow.setField(0,"隐蔽隧道");
                            // 隐蔽隧道类型
                            alarmRow.setField(1,tunnelType);
                            // 隐蔽隧道基类
                            alarmRow.setField(2,basicTunnel);
                            // 协议异常标签次数
                            alarmRow.setField(3,httpLabelCount);
                            collector.collect(alarmRow);

                            SessionLabelRow.setField(0,"会话打标");
                            SessionLabelRow.setField(1,SessionId);
                            Set<String> connectionLabels = new HashSet<>();
                            connectionLabels.add(HttpTunnel.ETL_TAG);
                            // Http隐蔽隧道
                            connectionLabels.add(SpecProtocolEnum.TUNN_HTTP.getCode());
                            SessionLabelRow.setField(2,connectionLabels);
                            SessionLabelRow.setField(3,connectBasicInfo.getEsKey());

                            // 给会话打心跳，控制，激活的标签
                            addSessionLabel(collector, basicTunnel, SessionId, SessionLabelRow);
                            Row tunnelToolRow = getLabelEdgeRow(connectBasicInfo.getDIp(),HttpTunnel.ETL_TAG);//SSL隐蔽信道工具
                            collector.collect(tunnelToolRow);
                            collector.collect(SessionLabelRow);
                        }
                    }
                    break;
                case "NTP":
                    String labelNtpKey = REDIS_SESSION_LABEL_KEY + "_" + SessionId;
                    String countNtpKey = REDIS_SESSION_LABEL_COUNT_KEY + "_" + SessionId + "_" + NtpTunnel.ETL_TAG;
                    if (jedis.exists(labelNtpKey) && jedis.exists(countNtpKey)) {
                        Set<String> protocolLabels = jedis.smembers(labelNtpKey);
                        Integer ntpLabelCount = Integer.valueOf(jedis.get(countNtpKey));
                        if (protocolLabels.contains(NtpTunnel.ETL_TAG) && isSuspicious){
                            List<String> labelList = connectBasicInfo.getAnalysisLabelList();
                            labelList.add(NtpTunnel.ETL_TAG);
                            connectBasicInfo.setAnalysisLabelList(labelList);
                            basicTunnel.setConnectBasicInfo(connectBasicInfo);

                            alarmRow.setField(0,"隐蔽隧道");
                            // 隐蔽隧道类型
                            alarmRow.setField(1,tunnelType);
                            // 隐蔽隧道基类
                            alarmRow.setField(2,basicTunnel);
                            // 协议异常标签次数
                            alarmRow.setField(3,ntpLabelCount);
                            collector.collect(alarmRow);

                            SessionLabelRow.setField(0,"会话打标");
                            SessionLabelRow.setField(1,SessionId);
                            Set<String> connectionLabels = new HashSet<>();
                            connectionLabels.add(NtpTunnel.ETL_TAG);
                            connectionLabels.add(SpecProtocolEnum.TUNN_NTP.getCode());
                            SessionLabelRow.setField(2,connectionLabels);
                            SessionLabelRow.setField(3,connectBasicInfo.getEsKey());

                            // 给会话打心跳，控制，激活的标签
                            addSessionLabel(collector, basicTunnel, SessionId, SessionLabelRow);

                            Row tunnelToolRow = getLabelEdgeRow(connectBasicInfo.getDIp(),NtpTunnel.ETL_TAG);//SSL隐蔽信道工具
                            collector.collect(tunnelToolRow);
                            collector.collect(SessionLabelRow);
                        }
                    }
                    break;
                case "SSL":
                    String labelTlsKey = REDIS_SESSION_LABEL_KEY + "_" + SessionId;
                    String countTlsKey = REDIS_SESSION_LABEL_COUNT_KEY + "_" + SessionId + "_" + TlsTunnel.ETL_TAG;
                    if (jedis.exists(labelTlsKey) && jedis.exists(countTlsKey)) {
                        Set<String> protocolLabels = jedis.smembers(labelTlsKey);
                        Integer sslLabelCount = Integer.valueOf(jedis.get(countTlsKey));
                        if (protocolLabels.contains(TlsTunnel.ETL_TAG) && isSuspicious && labels.contains(TlsTunnel.PROBE_TAG)){
                            List<String> labelList = connectBasicInfo.getAnalysisLabelList();
                            labelList.add(TlsTunnel.ETL_TAG);
                            labelList.add(TlsTunnel.PROBE_TAG);
                            connectBasicInfo.setAnalysisLabelList(labelList);
                            basicTunnel.setConnectBasicInfo(connectBasicInfo);

                            alarmRow.setField(0,"隐蔽隧道");
                            // 隐蔽隧道类型
                            alarmRow.setField(1,tunnelType);
                            // 隐蔽隧道基类
                            alarmRow.setField(2,basicTunnel);
                            // 协议异常标签次数
                            alarmRow.setField(3,sslLabelCount);
                            collector.collect(alarmRow);

                            SessionLabelRow.setField(0,"会话打标");
                            SessionLabelRow.setField(1,SessionId);
                            Set<String> connectionLabels = new HashSet<>();
                            connectionLabels.add(TlsTunnel.ETL_TAG);
                            connectionLabels.add(SpecProtocolEnum.TUNN_TLS.getCode());
                            SessionLabelRow.setField(2,connectionLabels);
                            SessionLabelRow.setField(3,connectBasicInfo.getEsKey());

                            Row tunnelToolRow = getLabelEdgeRow(connectBasicInfo.getDIp(),TlsTunnel.ETL_TAG);//SSL隐蔽信道工具
                            collector.collect(tunnelToolRow);
                            // 给会话打心跳，控制，激活的标签
                            addSessionLabel(collector, basicTunnel, SessionId, SessionLabelRow);
                            collector.collect(SessionLabelRow);
                        }
                    }
                    break;
                case "ICMP":
                    // 过滤ICMP白负载
                    if (!connectBasicInfo.getLabels().contains(SpecProtocolEnum.ICMP_PAYLOAD.getCode())) {
                        int totalPacketNum = connectBasicInfo.getTotalPacketNum();
                        int duration = connectBasicInfo.getDuration();
                        if (totalPacketNum >= 20 && totalPacketNum / (duration + 0.0001) > 4) {
                            int sMaxlen = connectBasicInfo.getPacketInfoList().stream()
                                    .filter(p -> p.getDirection() == 0)
                                    .mapToInt(PacketInfo::getByteNum)
                                    .max().orElse(-1);
                            int dMaxlen = connectBasicInfo.getPacketInfoList().stream()
                                    .filter(p -> p.getDirection() == 1)
                                    .mapToInt(PacketInfo::getByteNum)
                                    .max().orElse(-1);
                            if (sMaxlen > 100 || dMaxlen > 100){
                                List<String> labelList = connectBasicInfo.getAnalysisLabelList();
                                labelList.add(TlsTunnel.ETL_TAG);
                                labelList.add(TlsTunnel.PROBE_TAG);
                                connectBasicInfo.setAnalysisLabelList(labelList);
                                basicTunnel.setConnectBasicInfo(connectBasicInfo);

                                alarmRow.setField(0,"隐蔽隧道");
                                // 隐蔽隧道类型
                                alarmRow.setField(1,tunnelType);
                                // 隐蔽隧道基类
                                alarmRow.setField(2,basicTunnel);
                                // 协议异常标签次数
                                alarmRow.setField(3,1);
                                collector.collect(alarmRow);

                                SessionLabelRow.setField(0,"会话打标");
                                SessionLabelRow.setField(1,SessionId);
                                Set<String> connectionLabels = new HashSet<>();
                                connectionLabels.add(SpecProtocolEnum.SPOOF_ICMP.getCode());
                                connectionLabels.add(SpecProtocolEnum.TUNN_ICMP.getCode());
                                SessionLabelRow.setField(2,connectionLabels);
                                SessionLabelRow.setField(3,connectBasicInfo.getEsKey());

                                Row tunnelToolRow = getLabelEdgeRow(connectBasicInfo.getDIp(), SpecProtocolEnum.SPOOF_ICMP.getCode());//ICMP蔽信道工具
                                collector.collect(tunnelToolRow);
                                // 给会话打心跳，控制，激活的标签
                                addSessionLabel(collector, basicTunnel, SessionId, SessionLabelRow);
                                collector.collect(SessionLabelRow);
                            }
                        }
                    }
                    break;
                default:
                    logger.info("出现意料之外的隐蔽隧道类型");
                    break;
            }

        }catch (Exception e){
            logger.error("标准远程控制协议下的C2行为攻击告警信息读取redis失败，error:——{}——",e.toString());
        }finally {
            if (jedis != null){
                jedis.close();
            }
        }
    }

    private static void addSessionLabel(Collector<Row> collector, BasicTunnel basicTunnel, String SessionId, Row SessionLabelRow) {
        ConnectBasicInfo connectBasicInfo = basicTunnel.getConnectBasicInfo();
        Set<String> connectLabels = SessionLabelRow.getFieldAs(2);
        if(connectBasicInfo.isHeartbeat()){
            if (connectBasicInfo.getCSuspiciousHeartbeat()!=null){
                connectLabels.add(ConnectBasicInfo.cHeartbeatTag);
            }
            if(connectBasicInfo.getSSuspiciousHeartbeat()!=null){
                connectLabels.add(ConnectBasicInfo.sHeartbeatTag);
            }
        }
        if(connectBasicInfo.isControl()){
            connectLabels.add(ConnectBasicInfo.controlTag);
        }
        if(connectBasicInfo.isActivation()){
            connectLabels.add(ConnectBasicInfo.activationTag);
        }
    }
}
