package com.geeksec.analysisFunction.getLabelFlatMap;

import static com.geeksec.analysisFunction.getLabelFlatMap.WebShellUnEncryptFunction.collectInfo;

import com.geeksec.SpecProtocolEnum;
import com.geeksec.analysisFunction.analysisEntity.protocolRelatedInfo.HttpSimpleInfo;
import com.geeksec.analysisFunction.analysisEntity.webshell.WebshellInfo;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2022/12/6
 * 加密webshell协议识别
 */

public class WebShellEncryptFunction {
    private static final Logger logger = LoggerFactory.getLogger(WebShellEncryptFunction.class);
    public static final String B374K_STR = "(?=.*pass=)(?=.*; s_self=)(?=.*; cwd=)";
    static final String WEBACOO_STR = "(?=.*cm=)(?=.*; cn=)(?=.*; cp=)";
    static final String JSP_MASTER_STR = "\n" +
            "\n" +
            "\n" +
            "\n" +
            "\n" +
            "\n" +
            "\n" +
            "\n" +
            "\n";
    static final String XISE_STR = "xise=%40eval%2F%2A%15%99%D0%21%03%19s%20%0B%CB%A8%DD%E3%A3%C5%C4";

    // 将十六进制字符串转换为字节数组
    public static byte[] hexStringToByteArray(String hexString) {
        try {
            int len = hexString.length();
            byte[] data = new byte[len / 2];
            for (int i = 0; i < len; i += 2) {
                data[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4)
                        + Character.digit(hexString.charAt(i + 1), 16));
            }
            return data;
        } catch (Exception e) {
            System.out.println(hexString);
        }
        return new byte[0];
    }

    /**
     * 根据响应体内容进行解析
     *
     * @param webshellInfoStream
     * @return
     */
    public static DataStream<Row> webshellByPayloadDown(DataStream<Row> webshellInfoStream) {
        return webshellInfoStream.flatMap(new RichFlatMapFunction<Row, Row>() {
            @Override
            public void flatMap(Row row, Collector<Row> collector) throws Exception {
                WebshellInfo webshellInfo = row.getFieldAs(1);
                List<HttpSimpleInfo> httpSimpleInfos = webshellInfo.getHttpSimpleInfos();
                if (detectXise(httpSimpleInfos)){
                    // 是否加密，否; 编码类型，hex; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                    collectInfo(collector, webshellInfo, false, "hex",
                            false, "", SpecProtocolEnum.XISE);
                 }
                if (!httpSimpleInfos.isEmpty() && httpSimpleInfos.get(0).getServerInfoMap().get("Payload") != null) {
                    String payload = String.valueOf(httpSimpleInfos.get(0).getServerInfoMap().get("Payload"));
                    if (!"NoPayload".equals(payload)) {
                        String payloadDown = new String(hexStringToByteArray(payload), StandardCharsets.UTF_8);
                        // jspmaster，响应头内容为.........
                        if (JSP_MASTER_STR.equals(payloadDown)) {
                            logger.info("识别到Jspmaster");
                            // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                            collectInfo(collector, webshellInfo, true, "base64",
                                    false, "aes", SpecProtocolEnum.JSPMASTER);
                        }
                    }
                }
            }
        }).name("根据响应体内容进行解析").setParallelism(2);
    }

    public static boolean detectXise(List<HttpSimpleInfo> httpSimpleInfos) {
        if (!httpSimpleInfos.isEmpty()){
            for (HttpSimpleInfo httpSimpleInfo:httpSimpleInfos){
                if (httpSimpleInfo.getClientInfoMap().get("Payload") != null && !"NoPayload".equals(httpSimpleInfo.getClientInfoMap().get("Payload"))) {
                    // 请求头负载内容
                    String payload = new String(hexStringToByteArray(String.valueOf(httpSimpleInfo.getClientInfoMap().get("Payload"))), StandardCharsets.UTF_8);
                    // 分割负载内容
                    String[] payloadSeq = payload.split("&");
                    // 第一段负载，一般是登录语句和密码
                    String firstSeq = payloadSeq[0];
                    // xise，第一段和第三段为固定内容
                    if (firstSeq.equals(XISE_STR)) {
                        logger.info("识别到Xise");
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 根据请求体特征识别并打标
     *
     * @param webshellInfoStream
     * @return
     */
    public static DataStream<Row> webshellByRequestHead(DataStream<Row> webshellInfoStream) {
        return webshellInfoStream.flatMap(new RichFlatMapFunction<Row, Row>() {
            @Override
            public void flatMap(Row row, Collector<Row> collector) throws Exception {
                WebshellInfo webshellInfo = row.getFieldAs(1);
                // 哥斯拉，cookie内容为分号结尾，请求包中第1位为b4c4e1f6ddd2a488
                for (int i = 0; i < webshellInfo.getHttpSimpleInfos().size(); i++) {
                    String cookie = String.valueOf(webshellInfo.getHttpSimpleInfos().get(i).getClientInfoMap().get("Cookie"));
                    if (cookie.endsWith(";")) {
                        String payloadDownHex = String.valueOf(webshellInfo.getHttpSimpleInfos().get(i).getServerInfoMap().get("Payload"));
                        if (!"null".equals(payloadDownHex) && !"NoPayload".equals(payloadDownHex)) {
                            String payloadDown = new String(hexStringToByteArray(payloadDownHex), StandardCharsets.UTF_8);
                            if (payloadDown.endsWith("6C37")) {
                                logger.info("识别到哥斯拉");
                                // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                                collectInfo(collector, webshellInfo, true, "xor",
                                        false, "des", SpecProtocolEnum.GODZILLA);
                                break;
                            }
                        }
                    }
                }
                List<HttpSimpleInfo> httpSimpleInfos = webshellInfo.getHttpSimpleInfos();
                if (!httpSimpleInfos.isEmpty()) {
                    Map<String, Object> requestHeadMap = httpSimpleInfos.get(0).getClientInfoMap();
                    String contentType = String.valueOf(requestHeadMap.get("Content-Type"));
                    // sharpyshell，请求头中Content-Type为multipart/form-data; boundary=8d8ef8552fca8671052f3044faf663a0，Content-Disposition为form-data; name="data"，响应头Cache-Control为private
                    if (contentType.startsWith("multipart/form-data; boundary=")) {
                        Object payloadHex = webshellInfo.getHttpSimpleInfos().get(0).getClientInfoMap().get("Payload");
                        if (payloadHex != null) {
                            String payload = new String(hexStringToByteArray(String.valueOf(payloadHex)), StandardCharsets.UTF_8);
                            if (payload.endsWith("Content-Disposition: form-da")) {
                                logger.info("识别到SharPyShell");
                                // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                                collectInfo(collector, webshellInfo, true, "base64",
                                        false, "aes", SpecProtocolEnum.SHARPYSHELL);
                            }
                        }
                    }
                    // 天蝎，请求头中Content-Type为application/octet-stream，且所有负载前72位相同
                    else if ("application/octet-stream".equals(contentType) && httpSimpleInfos.size() > 1) {
                        String prefix = new String(hexStringToByteArray(String.valueOf(httpSimpleInfos.get(0).getClientInfoMap().get("Payload"))), StandardCharsets.UTF_8);
                        if (!"NoPayload".equals(prefix) && !"null".equals(prefix)) {
                            if (httpSimpleInfos.stream().allMatch(httpSimpleInfo -> httpSimpleInfo != null
                                    && new String(hexStringToByteArray(String.valueOf(httpSimpleInfo.getClientInfoMap().get("Payload"))), StandardCharsets.UTF_8).startsWith(prefix))) {
                                logger.info("识别到天蝎");
                                // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                                collectInfo(collector, webshellInfo, true, "base64",
                                        false, "aes", SpecProtocolEnum.TIANXIE);
                            }
                        }
                    }
                }
            }
        }).name("根据请求头特征识别").setParallelism(2);
    }

    /**
     * 根据cookie内容匹配
     *
     * @param webshellInfoStream
     * @return DataStream
     */
    public static DataStream<Row> webshellByCookie(DataStream<Row> webshellInfoStream) {
        return webshellInfoStream.flatMap(new RichFlatMapFunction<Row, Row>() {
            @Override
            public void flatMap(Row row, Collector<Row> collector) throws Exception {
                WebshellInfo webshellInfo = row.getFieldAs(1);
                List<HttpSimpleInfo> httpSimpleInfos = webshellInfo.getHttpSimpleInfos();
                if (!httpSimpleInfos.isEmpty()) {
                    if (httpSimpleInfos.get(0).getClientInfoMap().get("Cookie") != null) {
                        collector.collect(row);
                    }
                }
            }
        }).flatMap(new RichFlatMapFunction<Row, Row>() {
            @Override
            public void flatMap(Row row, Collector<Row> collector) throws Exception {
                WebshellInfo webshellInfo = row.getFieldAs(1);
                String cookie = String.valueOf(webshellInfo.getHttpSimpleInfos().get(0).getClientInfoMap().get("Cookie"));
                // 请求头中Cookie格式为  Cookie: cm=; cn=; cp=
                // 其中cm=aXBjb25maWc=;
                if (Pattern.compile(WEBACOO_STR).matcher(cookie).find()) {
                    logger.info("识别到WeBacoo");
                    // 是否加密，否; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，无; 标签，
                    collectInfo(collector, webshellInfo, false, "base64",
                            false, "", SpecProtocolEnum.WEBACOO);
                } // 请求头中Cookie中包含s_self、cwd、pass参数
                else if (Pattern.compile(B374K_STR).matcher(cookie).find()) {
                    logger.info("识别到b374k");
                    // 是否加密，是; 编码类型，base64; 是否自定义协议，否; 是否单向，否; 加密类型，rc4; 标签，
                    collectInfo(collector, webshellInfo, true, "base64",
                            false, "rc4", SpecProtocolEnum.B374K);
                }
            }
        }).name("根据cookie特征识别").setParallelism(2);
    }
}
