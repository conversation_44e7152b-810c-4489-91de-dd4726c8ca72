package com.geeksec.analysisFunction.writeAlarm;

import static com.geeksec.analysisFunction.getRowInfo.SslInfoRowFlatMap.Alarm_Info_Map;
import static com.geeksec.common.LabelUtils.AlarmUtils.get_attack_chain_list;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import com.geeksec.common.LabelUtils.AlarmUtils;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

/**
 * <AUTHOR>
 * @Date 2023/4/25
 */

public class writeEncryptTunnelAlarm {
    private static final Logger logger = LoggerFactory.getLogger(writeFingerDetectAlarm.class);

    public static JSONObject get_encryptTunnelAlarm_Json(Row InfoRow, Jedis jedis) throws NoSuchAlgorithmException {
        String Alarm_type = InfoRow.getFieldAs(0);
        if (!Alarm_Info_Map.keySet().contains(Alarm_type)) {
            return null;
        }
        Map<String, Object> alarmJson = AlarmUtils.get_known_alarm_info(InfoRow);
        alarmJson.putAll(AlarmUtils.get_Default_alarm_map());
        List<Map<String,Object>> alarm_reason = get_alarm_reason(InfoRow);//reason
        List<Map<String,String>> attack_family = new ArrayList<>();//attack_family
        List<Map<String,Object>> targets = get_targets(InfoRow);//targets

        NeededInfo neededInfo = InfoRow.getFieldAs(8);
        alarmJson.put("vPort",neededInfo.getDPort());
        alarmJson.put("aPort",neededInfo.getSPort());
        alarmJson.put("sPort",neededInfo.getSPort());
        alarmJson.put("dPort",neededInfo.getDPort());
        alarmJson.put("tranProto",neededInfo.getTranProto());
        alarmJson.put("appProto",neededInfo.getAppProto());
        alarmJson.put("httpDomain",neededInfo.getHttpDomain());
        alarmJson.put("sniDomain",neededInfo.getSniDomain());
        alarmJson.put("sIp",neededInfo.getSIp());
        alarmJson.put("dIp",neededInfo.getDIp());
        Set<String> dIpSet = new HashSet<>();
        String dIp = InfoRow.getFieldAs(3);
        dIpSet.add(dIp);

        Set<String> sIpSet = new HashSet<>();
        String sIp = InfoRow.getFieldAs(2);
        sIpSet.add(sIp);
        //victim
        List<Map<String,String>> victim = get_victim(dIpSet);
        if (!dIpSet.contains(neededInfo.getDIp())){
            Map<String,String> victim_map = new HashMap<>();
            victim_map.put("ip",neededInfo.getDIp());
            victim.add(victim_map);
        }
        //attacker
        List<Map<String,String>> attacker = get_attacker(sIpSet);
        if (!sIpSet.contains(neededInfo.getSIp())){
            Map<String,String> victim_map = new HashMap<>();
            victim_map.put("ip",neededInfo.getSIp());
            attacker.add(victim_map);
        }
        List<String> alarm_related_label = new ArrayList<>();
        alarm_related_label.add(InfoRow.getFieldAs(5));
        List<Map<String,Object>> attack_route = get_attack_route(InfoRow);
        List<String> alarm_session_list = get_alarm_session_list(InfoRow);
        List<String> alarm_attack_chain_list = get_attack_chain_list(victim,attacker,alarm_related_label, (String) alarmJson.get("alarm_knowledge_id"));

        alarmJson.put("alarm_reason",alarm_reason);
        alarmJson.put("attack_family",attack_family);
        alarmJson.put("targets",targets);
        String alarm_principle = get_alarm_principle(InfoRow);
        alarmJson.put("alarm_principle",alarm_principle);
        String alarm_handle_method = get_alarm_handle_method(InfoRow);
        alarmJson.put("alarm_handle_method",alarm_handle_method);
        alarmJson.put("alarm_type","模型");
        alarmJson.put("victim",victim);
        alarmJson.put("attacker",attacker);
        alarmJson.put("alarm_related_label",alarm_related_label);
        alarmJson.put("attack_route",attack_route);
        alarmJson.put("alarm_session_list",alarm_session_list);
        alarmJson.put("attack_chain_list",alarm_attack_chain_list);
        String modelId = getModelId(InfoRow);
        alarmJson.put("model_id",modelId);
        List<String> pcapFileList = AlarmUtils.getPcapFileList(alarm_session_list,jedis);
        alarmJson.put("PcapFileList",pcapFileList);

        String esKey = InfoRow.getFieldAs(7);
        String taskId = esKey.split("_")[1];
        String batchId = esKey.split("_")[2];
        Map<String,Object> send_data = AlarmUtils.get_send_data(alarmJson,taskId,batchId);
        JSONObject alarm_json = new JSONObject();
        alarm_json.putAll(send_data);
//        JSONObject alarm_data = new JSONObject();
//        List<Map<String,Object>> bulk_list = new ArrayList<>();
//        bulk_list.add(alarm_json);
//        alarm_data.put("type","ALARM_INSERT_ES");
//        alarm_data.put("Bulk",bulk_list);
        return alarm_json;
    }

    private static String getModelId(Row infoRow) {
        String finger_type = infoRow.getFieldAs(1);
        switch (finger_type){
            case "Neoregeo代理隧道":
                return "99020";
            case "GoProxy代理隧道":
                return "99016";
            case "suo5代理隧道":
                return "99026";
            default:
                return "99016";
        }
    }

    private static List<Map<String,Object>> get_alarm_reason(Row InfoRow){
        List<Map<String,Object>> alarm_reason_list = new ArrayList<>();
        Map<String,Object> alarm_reason_fingerInfo = new HashMap<>();
        String TunnelType = InfoRow.getFieldAs(1);
        switch (TunnelType){
            case "Neoregeo代理隧道":
                List<String> alarm_info = (List<String>) InfoRow.getField(4);
                alarm_reason_fingerInfo.put("key","使用了"+InfoRow.getField(1));
                alarm_reason_fingerInfo.put("actual_value",String.format("检测出http头中存在随机生成字段,key：%s，value：%s", alarm_info.get(0), alarm_info.get(1)));
                break;
            case "GoProxy代理隧道":
                alarm_reason_fingerInfo.put("key","使用了"+InfoRow.getField(1));
                alarm_reason_fingerInfo.put("actual_value","检测出GoProxy代理隧道工具指纹"+InfoRow.getField(4));
                break;
            case "suo5代理隧道":
                alarm_reason_fingerInfo.put("key","使用了"+InfoRow.getField(1));
                alarm_reason_fingerInfo.put("actual_value","检测出http请求头中存在多个字段异常：" +
                        "1、请求包和响应包都包含Transfer-Encoding: chunked；" +
                        "2、请求包和响应包都不包含：Content-Length；" +
                        "3、返回包中包含：X-Accel-Buffering: no");
                break;
            default:
                logger.error("知识库中无当前加密隧道告警类型");
                alarm_reason_fingerInfo.put("key","使用了:");
                alarm_reason_fingerInfo.put("actual_value","加密隧道");
                break;
        }

        alarm_reason_list.add(alarm_reason_fingerInfo);
        return alarm_reason_list;
    }

    private static List<Map<String,String>> get_victim(Set<String> InfoRow){
        List<Map<String,String>> result = new ArrayList<>();
        for(String ip:InfoRow){
            Map<String,String> victim = new HashMap<>();
            victim.put("ip",ip);
            result.add(victim);
        }
        return result;
    }

    private static List<Map<String,String>> get_attacker(Set<String> InfoRow){
        List<Map<String,String>> result = new ArrayList<>();
        for(String ip:InfoRow){
            Map<String,String> victim = new HashMap<>();
            victim.put("ip",ip);
            result.add(victim);
        }
        return result;
    }

    private static List<Map<String,Object>> get_targets(Row InfoRow){
        List<Map<String,Object>> targets = new ArrayList<>();
        Map<String,Object> target_tmp = new HashMap<>();
        target_tmp.put("name",InfoRow.getField(2));
        target_tmp.put("type","ip");
        List<String> labels_ip = new ArrayList<>();
        target_tmp.put("labels",labels_ip);
        targets.add(target_tmp);
        return targets;
    }

    private static String get_alarm_principle(Row InfoRow){
        String finger_type = InfoRow.getFieldAs(1);
        switch (finger_type){
            case "Neoregeo代理隧道":
                return "客户端使用Neoregeo代理隧道访问服务器，存在被远控木马操控风险";
            case "GoProxy代理隧道":
                return "客户端使用GoProxy代理隧道访问服务器，存在被远控木马操控风险";
            case "suo5代理隧道":
                return "客户端使用suo5代理隧道访问服务器，存在被远控木马操控风险";
            default:
                logger.error("知识库中无当前指纹告警类型");
                return "客户端访问服务端指纹存在威胁";
        }
    }

    private static String get_alarm_handle_method(Row InfoRow){
        String finger_type = InfoRow.getFieldAs(1);
        switch (finger_type){
            case "Neoregeo代理隧道":
                return "检查服务器是否被Neoregeo代理隧道操控，排查是否存在Neoregeo代理隧道程序的运行，将该客户端IP过滤";
            case "GoProxy代理隧道":
                return "检查服务器是否被GoProxy代理隧道操控，排查是否存在GoProxy代理隧道软件的运行，将该客户端IP过滤";
            case "suo5代理隧道":
                return "检查服务器是否被suo5代理隧道操控，排查是否存在suo5代理隧道软件的运行，将该客户端IP过滤";
            default:
                logger.error("知识库中无当前指纹告警类型");
                return "过滤掉来自该客户端IP的访问";
        }
    }

    private static List<Map<String,Object>> get_attack_route(Row InfoRow){
        List<Map<String,Object>> attack_route = new ArrayList<>();
        String TunnelType = InfoRow.getFieldAs(1);
        switch (TunnelType){
            case "GoProxy代理隧道":
                String fingerID = InfoRow.getFieldAs(4);
                String finger_Label = InfoRow.getFieldAs(5);
                Map<String,Object> attack_route1 = new HashMap<>();
                List<String> labels = new ArrayList<>();
                labels.add(finger_Label);
                attack_route1.put("type","finger");
                attack_route1.put("name",fingerID);
                attack_route1.put("label",labels);
                attack_route.add(attack_route1);
                break;
            default:
                break;
        }
        return attack_route;
    }

    private static List<String> get_alarm_session_list(Row InfoRow){
        List<String> alarm_session_list = new ArrayList<>();
        alarm_session_list.add(InfoRow.getFieldAs(6));
        return alarm_session_list;
    }
}
