package com.geeksec.analysisFunction.infoSink;

import static com.geeksec.task.LabelKafka2ES.PARALLELISM_8;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.dbConnect.poolFactory.ESPoolFactory;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.connectors.elasticsearch.ElasticsearchSinkFunction;
import org.apache.flink.streaming.connectors.elasticsearch.RequestIndexer;
import org.apache.flink.streaming.connectors.elasticsearch.util.RetryRejectedExecutionFailureHandler;
import org.apache.flink.streaming.connectors.elasticsearch7.ElasticsearchSink;
import org.apache.flink.streaming.connectors.elasticsearch7.RestClientFactory;
import org.apache.http.HttpHost;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.Requests;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2024/1/10
 */

public class ESIndexSink {
    private static final Logger logger = LoggerFactory.getLogger(ESIndexSink.class);

    public static void ESAlarmSink(DataStream<JSONObject> pcapStream) {
        // ES Sink
        List<HttpHost> httpHosts = new ArrayList<>();
        httpHosts.add(new HttpHost(ESPoolFactory.esHost, ESPoolFactory.esPort, "http"));
        ElasticsearchSink.Builder<JSONObject> esSinkBuilder = new ElasticsearchSink.Builder<JSONObject>(
                httpHosts,
                new ElasticsearchSinkFunction<JSONObject>() {
                    public IndexRequest createIndexRequest(JSONObject data) {
                        String index = getAlarmIndex(data.getString("TaskId"),data.getString("BatchId"));
                        System.out.println(index);
                        Map<String, Object> alarmMap = (Map<String, Object>) data.get("Alarm");
                        return Requests.indexRequest()
                                .index(index)
                                .source(alarmMap);
                    }
                    @Override
                    public void process(JSONObject data, RuntimeContext runtimeContext, RequestIndexer requestIndexer) {
                        requestIndexer.add(createIndexRequest(data));
                    }
                }
        );

        // 设置批量写数据的缓冲区大小
        // 批量写入的时间间隔，写入的数据条数，写入的缓存空间大小
        esSinkBuilder.setBulkFlushInterval(2500);
        esSinkBuilder.setBulkFlushMaxSizeMb(100);
        esSinkBuilder.setBulkFlushMaxActions(500);
        esSinkBuilder.setBulkFlushBackoff(true);  // 是否开启重试机制
        esSinkBuilder.setBulkFlushBackoffRetries(2);  // 失败重试次数失败策略配置一个即可
        esSinkBuilder.setFailureHandler(new RetryRejectedExecutionFailureHandler());  // 默认失败重试
        esSinkBuilder.setRestClientFactory((RestClientFactory) restClientBuilder -> {
            restClientBuilder.setHttpClientConfigCallback(httpClientBuilder -> {
                httpClientBuilder.disableAuthCaching();
                httpClientBuilder.setKeepAliveStrategy((response, context) -> Duration.ofMinutes(5).toMillis());
                return httpClientBuilder;
            });
            restClientBuilder.setRequestConfigCallback(requestConfigBuilder -> requestConfigBuilder
                    .setConnectionRequestTimeout(1000 * 60 * 2)
                    .setSocketTimeout(1000 * 60 * 2));
        });

        // 入ES
        pcapStream.addSink(esSinkBuilder.build()).name("Elasticsearch Sink").setParallelism(PARALLELISM_8);

    }

    public static String getAlarmIndex(String taskId, String batchId) {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        // 获取当前日期
        Date currentDate = new Date();
        // 使用format方法将日期转换为指定格式
        String dateString = format.format(currentDate);
        return "alarm_"+ taskId +"_"+ batchId +"_"+dateString;
//        return "alarm_"+ taskId +"_10001_"+dateString;
    }
}
