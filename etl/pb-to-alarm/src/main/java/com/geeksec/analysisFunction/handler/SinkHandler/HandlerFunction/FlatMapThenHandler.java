package com.geeksec.analysisFunction.handler.SinkHandler.HandlerFunction;

import com.geeksec.flinkTool.sideOutputTag.SinkOutPutTag.AlarmOutPutTag;
import com.geeksec.flinkTool.sideOutputTag.SinkOutPutTag.NebulaEdgeOutPutTag;
import com.geeksec.flinkTool.sideOutputTag.SinkOutPutTag.SessionOutPutTag;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 * @Date 2022/12/29
 */

public class FlatMapThenHandler {
    public static SingleOutputStreamOperator<Row> getFingerTagInfoRow(SingleOutputStreamOperator<Row> FingerLabelInfoRow) {
        SingleOutputStreamOperator<Row> FingerTagInfoRow = FingerLabelInfoRow.process(new ProcessFunction<Row, Row>() {
            @Override
            public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                String AlarmType = row.getFieldAs(0);
                switch (AlarmType) {
                    case "会话打标":
                        context.output(SessionOutPutTag.Session_RAT_info, row);
                    case "远控木马":
                    case "扫描行为":
                    case "违规外联":
                    case "挖矿病毒":
                    case "加密隐蔽隧道通信":
                    case "黑客工具":
                    case "加密通道攻击行为":
                        context.output(AlarmOutPutTag.Alarm_Finger_Label_EdgeRow, row);
                        break;
                    default:
                        context.output(NebulaEdgeOutPutTag.Nebula_Finger_Label_EdgeRow, row);
                }
            }
        }).
        name("指纹识别，ES告警，会话和Nebula打标分流").setParallelism(1);
        return FingerTagInfoRow;
    }

    public static SingleOutputStreamOperator<Row> getDnsMineInfoRow(SingleOutputStreamOperator<Row> DnsMineLabelInfoRow) {
        SingleOutputStreamOperator<Row> DnsMineInfoRow = DnsMineLabelInfoRow.process(new ProcessFunction<Row, Row>() {
            @Override
            public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                if ("Add_Label".equals(row.getFieldAs(3))) {
                    context.output(NebulaEdgeOutPutTag.Nebula_DNSMine_EdgeRow, row);
                } else {
                    context.output(AlarmOutPutTag.Alarm_DNSMine_EdgeRow, row);
                }
            }
        }).name("ES告警和Nebula打标分流").setParallelism(1);
        return DnsMineInfoRow;
    }

    public static SingleOutputStreamOperator<Row> getHttpWebLoginInfoRow(SingleOutputStreamOperator<Row> HttpWebLoginLabelRow) {
        SingleOutputStreamOperator<Row> HttpWebLoginInfoRow = HttpWebLoginLabelRow.process(new ProcessFunction<Row, Row>() {
            @Override
            public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                if ("会话打标".equals(row.getFieldAs(0))) {
                    context.output(SessionOutPutTag.Session_http_webLogin_info, row);
                } else {
                    context.output(AlarmOutPutTag.Alarm_http_webLogin_info, row);
                }
            }
        }).name("Http登陆爆破，ES告警和ES会话打标分流").setParallelism(1);
        return HttpWebLoginInfoRow;
    }

    public static SingleOutputStreamOperator<Row> getNeoregeoInfoRow(SingleOutputStreamOperator<Row> HttpWebLoginLabelRow) {
        SingleOutputStreamOperator<Row> HttpWebLoginInfoRow = HttpWebLoginLabelRow.process(new ProcessFunction<Row, Row>() {
            @Override
            public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                if ("会话打标".equals(row.getFieldAs(0))) {
                    context.output(SessionOutPutTag.Session_Neoregeo_info, row);
                } else {
                    context.output(AlarmOutPutTag.Alarm_Neoregeo_Row, row);
                }
            }
        }).name("Neoregeo，ES告警和ES会话打标分流").setParallelism(1);
        return HttpWebLoginInfoRow;
    }

    public static SingleOutputStreamOperator<Row> getSuo5InfoRow(SingleOutputStreamOperator<Row> HttpWebLoginLabelRow) {
        SingleOutputStreamOperator<Row> HttpWebLoginInfoRow = HttpWebLoginLabelRow.process(new ProcessFunction<Row, Row>() {
            @Override
            public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                if ("会话打标".equals(row.getFieldAs(0))) {
                    context.output(SessionOutPutTag.Session_suo5_info, row);
                } else {
                    context.output(AlarmOutPutTag.Alarm_suo5_Row, row);
                }
            }
        }).name("Suo5，ES告警和ES会话打标分流").setParallelism(1);
        return HttpWebLoginInfoRow;
    }

    public static SingleOutputStreamOperator<Row> getBeHinderInfoRow(SingleOutputStreamOperator<Row> BeHinderLabelRow) {
        SingleOutputStreamOperator<Row> BeHinderInfoRow = BeHinderLabelRow.process(new ProcessFunction<Row, Row>() {
            @Override
            public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                if ("会话打标".equals(row.getFieldAs(0))) {
                    context.output(SessionOutPutTag.Session_BeHinder_info, row);
                } else {
                    context.output(AlarmOutPutTag.Alarm_BeHinder_Row, row);
                }
            }
        }).name("BeHinder，ES告警和ES会话打标分流").setParallelism(1);
        return BeHinderInfoRow;
    }

    public static SingleOutputStreamOperator<Row> getAntSwordInfoRow(SingleOutputStreamOperator<Row> AntSword_infoLabelRow) {
        SingleOutputStreamOperator<Row> AntSword_infoRow = AntSword_infoLabelRow.process(new ProcessFunction<Row, Row>() {
            @Override
            public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                context.output(AlarmOutPutTag.Alarm_AntSword_Row, row);
            }
        }).name("AntSword，ES告警和ES会话打标分流").setParallelism(1);
        return AntSword_infoRow;
    }

    public static SingleOutputStreamOperator<Row> getTunnelInfoRow(SingleOutputStreamOperator<Row> tunnel_infoLabelRow) {
        SingleOutputStreamOperator<Row> tunnel_infoRow = tunnel_infoLabelRow.process(new ProcessFunction<Row, Row>() {
            @Override
            public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                if ("会话打标".equals(row.getFieldAs(0))) {
                    context.output(SessionOutPutTag.Session_Tunnel_info, row);
                } else if ("Add_Label".equals(row.getFieldAs(3))) {
                    context.output(NebulaEdgeOutPutTag.Nebula_Tunnel_Row, row);
                } else {
                    context.output(AlarmOutPutTag.Alarm_Tunnel_Row, row);
                }
            }
        }).name("隐蔽信道，ES告警和ES会话打标分流").setParallelism(1);
        return tunnel_infoRow;
    }

    public static SingleOutputStreamOperator<Row> getSRCPInfoRow(SingleOutputStreamOperator<Row> SRCPInfoLabelRow) {
        SingleOutputStreamOperator<Row> SRCPInfoRow = SRCPInfoLabelRow.process(new ProcessFunction<Row, Row>() {
            @Override
            public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                if ("会话打标".equals(row.getFieldAs(0))) {
                    context.output(SessionOutPutTag.Session_SRCPC2_info, row);
                } else if ("标准远程控制协议下的C2行为".equals(row.getFieldAs(0))) {
                    context.output(AlarmOutPutTag.Alarm_SRCPC2_Row, row);
                } else {
                    context.output(NebulaEdgeOutPutTag.Nebula_SRCP_Row, row);
                }

            }
        }).name("SRCP C2，ES告警和ES会话打标分流").setParallelism(1);
        return SRCPInfoRow;
    }

    public static SingleOutputStreamOperator<Row> getWebShellInfoRow(DataStream<Row> WebShellInfoLabelRow) {
        SingleOutputStreamOperator<Row> WebShellInfoRow = WebShellInfoLabelRow.process(new ProcessFunction<Row, Row>() {
            @Override
            public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                if ("会话打标".equals(row.getFieldAs(0))) {
                    context.output(SessionOutPutTag.Session_WebShell_info, row);
                } else if ("webShell攻击检测".equals(row.getFieldAs(0))) {
                    context.output(AlarmOutPutTag.Alarm_WebShell_Row, row);
                }else if ("特定协议攻击工具".equals(row.getFieldAs(0))) {
                    context.output(AlarmOutPutTag.Alarm_WebShell_Row, row);
                } else {
                    context.output(NebulaEdgeOutPutTag.Nebula_WebShell_Row, row);
                }

            }
        }).name("WebShell，ES告警和ES会话打标分流").setParallelism(1);
        return WebShellInfoRow;
    }

    public static SingleOutputStreamOperator<Row> getURCPInfoRow(SingleOutputStreamOperator<Row> URCPInfoLabelRow) {
        SingleOutputStreamOperator<Row> URCPInfoRow = URCPInfoLabelRow.process(new ProcessFunction<Row, Row>() {
            @Override
            public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                if ("会话打标".equals(row.getFieldAs(0))) {
                    context.output(SessionOutPutTag.Session_URCP_info, row);
                } else if ("未知远程控制协议".equals(row.getFieldAs(0))){
                    context.output(AlarmOutPutTag.Alarm_URCP_Row, row);
                } else {
                    context.output(NebulaEdgeOutPutTag.Nebula_URCP_Row, row);
                }
            }
        }).name("URCP ES告警和ES会话、dIP打标分流").setParallelism(1);
        return URCPInfoRow;
    }

    public static SingleOutputStreamOperator<Row> getToDeskInfoRow(SingleOutputStreamOperator<Row> toDeskInfoLabelRow) {
        SingleOutputStreamOperator<Row> toDeskInfoRow = toDeskInfoLabelRow.process(new ProcessFunction<Row, Row>() {
            @Override
            public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                if ("会话打标".equals(row.getFieldAs(0))) {
                    context.output(SessionOutPutTag.Session_TODESK_info, row);
                }
            }
        }).name("ToDesk ES会话打标分流").setParallelism(1);
        return toDeskInfoRow;
    }

    public static SingleOutputStreamOperator<Row> getEncryptedToolInfoRow(DataStream<Row> EncryptedToolLabelRow) {
        SingleOutputStreamOperator<Row> WebShellInfoRow = EncryptedToolLabelRow.process(new ProcessFunction<Row, Row>() {
            @Override
            public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
                if ("会话打标".equals(row.getFieldAs(0))) {
                    context.output(SessionOutPutTag.Session_EncryptedTool_info, row);
                } else if("特定协议攻击工具".equals(row.getFieldAs(0))) {
                    context.output(AlarmOutPutTag.Alarm_EncryptedTool_Row, row);
                } else {
                    context.output(NebulaEdgeOutPutTag.Nebula_EncrptedAPT_Row, row);
                }

            }
        }).name("加密流量分析，ES告警和ES会话打标分流").setParallelism(1);
        return WebShellInfoRow;
    }
}
