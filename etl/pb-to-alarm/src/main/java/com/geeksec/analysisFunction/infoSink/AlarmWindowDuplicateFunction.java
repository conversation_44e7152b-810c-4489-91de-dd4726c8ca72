package com.geeksec.analysisFunction.infoSink;

import static com.geeksec.common.LabelUtils.LabelRedisUtils.get_redis_not_alarm;
import static com.geeksec.common.LabelUtils.MysqlUtils.get_mysql_not_white;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.common.LabelUtils.LabelRedisUtils;
import java.util.List;
import java.util.Map;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * <AUTHOR>
 * @Date 2023/5/15
 */

public class AlarmWindowDuplicateFunction extends ProcessFunction<JSONObject,JSONObject>{
    private static final Logger logger = LoggerFactory.getLogger(AlarmWindowDuplicateFunction.class);
    public static final OutputTag<JSONObject> Alarm_Duplicate = new OutputTag<>("Alarm_Duplicate", TypeInformation.of(JSONObject.class));
    private static transient JedisPool jedisPool = null;
    @Override
    public void open(Configuration parameters) throws Exception {
        // redis池 初始化.
        jedisPool = LabelRedisUtils.initJedisPool();
        logger.info("生成jedisPool成功! {}", jedisPool.getNumIdle(),jedisPool.hashCode());
        super.open(parameters);
    }

    @Override
    public void processElement(JSONObject jsonObject, ProcessFunction<JSONObject, JSONObject>.Context context, Collector<JSONObject> collector) throws Exception {
        try{
            Map<String,Object> alarm_json = (Map<String, Object>) jsonObject.get("Alarm");
            List<String> attack_chain_list = (List<String>) alarm_json.get("attack_chain_list");
            boolean redis_not_alarm = false;
            boolean mysql_not_white = false;
            Jedis jedis = null;
            try{
                jedis = LabelRedisUtils.getJedis(jedisPool);
                redis_not_alarm = get_redis_not_alarm(attack_chain_list,jedis);
                mysql_not_white = get_mysql_not_white(attack_chain_list);
                // 统计短时相同告警的最近更新时间和次数
                String sIp = (String) alarm_json.get("sIp");
                String dIp = (String) alarm_json.get("dIp");
                String alarmName = (String) alarm_json.get("alarm_name");
                long createTime = System.currentTimeMillis();
                String alarmCountKey = sIp+dIp+alarmName+":"+createTime;
                jedis.select(3);
                jedis.setex(alarmCountKey,180,String.valueOf(1));
            }catch (Exception e){
                logger.error("告警去重与白名单过滤出错，error:——{}——",e);
            }finally {
                if (jedis != null){
                    jedis.close();
                }
            }
            //redis中没有，且mysql中不是白名单，说明既没有重复也没有被纳入白名单，予以告警
            if (redis_not_alarm && mysql_not_white){
                context.output(Alarm_Duplicate,jsonObject);
                logger.info("成功写入告警，告警不重复，也不是白名单");
            }else {
                logger.info("告警内容是否不重复——{}——，告警内容是否不在白名单——{}——",redis_not_alarm,mysql_not_white);
            }

        }catch (Exception e){
            logger.error("告警去重产生错误——{}——，可能存在告警缺失值现象",e);
        }

    }

    @Override
    public void close() throws Exception {
        super.close();
    }
}
