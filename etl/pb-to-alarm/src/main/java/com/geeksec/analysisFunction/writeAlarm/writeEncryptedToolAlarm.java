package com.geeksec.analysisFunction.writeAlarm;

import static com.geeksec.analysisFunction.getRowInfo.SslInfoRowFlatMap.Alarm_Info_Map;
import static com.geeksec.common.LabelUtils.AlarmUtils.get_attack_chain_list;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.encryptedTool.EncryptedToolInfo;
import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import com.geeksec.common.LabelUtils.AlarmUtils;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

/**
 * <AUTHOR>
 * @Date 2024/9/13
 */

// TODO 全部重写
public class writeEncryptedToolAlarm {
    private static final Logger logger = LoggerFactory.getLogger(writeEncryptedToolAlarm.class);

    public static JSONObject getEncryptedToolAlarmJson(Row InfoRow, Jedis jedis) throws NoSuchAlgorithmException {
        String Alarm_type = InfoRow.getFieldAs(0);
        if (!Alarm_Info_Map.keySet().contains(Alarm_type)) {
            return null;
        }
        Map<String, Object> sslDetectAlarmJson = AlarmUtils.get_known_alarm_info(InfoRow);
        sslDetectAlarmJson.putAll(AlarmUtils.get_Default_alarm_map());
        List<Map<String, Object>> alarmReason = get_alarm_reason(InfoRow);//reason
        EncryptedToolInfo encryptedToolInfo = InfoRow.getFieldAs(1);
        String dIp = encryptedToolInfo.getConnectBasicInfo().getDIp();
        String sIp = encryptedToolInfo.getConnectBasicInfo().getSIp();
        List<Map<String, String>> attackFamily = get_family(InfoRow);//attack_family
        List<Map<String, Object>> targets = get_targets(InfoRow);//targets

        NeededInfo neededInfo = encryptedToolInfo.getNeededInfo();
        sslDetectAlarmJson.put("vPort",neededInfo.getSPort());
        sslDetectAlarmJson.put("aPort",neededInfo.getDPort());
        sslDetectAlarmJson.put("sPort",neededInfo.getSPort());
        sslDetectAlarmJson.put("dPort",neededInfo.getDPort());
        sslDetectAlarmJson.put("tranProto",neededInfo.getTranProto());
        sslDetectAlarmJson.put("appProto",neededInfo.getAppProto());
        sslDetectAlarmJson.put("httpDomain",neededInfo.getHttpDomain());
        sslDetectAlarmJson.put("sniDomain",neededInfo.getSniDomain());
        sslDetectAlarmJson.put("sIp",neededInfo.getSIp());
        sslDetectAlarmJson.put("dIp",neededInfo.getDIp());
        Set<String> sIpSet = new HashSet<>();
        sIpSet.add(sIp);

        Set<String> dIpSet = new HashSet<>();
        dIpSet.add(dIp);

        //victim
        List<Map<String,String>> victim = get_victim(sIpSet);
        if (!sIpSet.contains(neededInfo.getSIp())){
            Map<String,String> victim_map = new HashMap<>();
            victim_map.put("ip",neededInfo.getSIp());
            victim.add(victim_map);
        }
        //attacker
        List<Map<String,String>> attacker = get_attacker(dIpSet);
        if (!dIpSet.contains(neededInfo.getDIp())){
            Map<String,String> victim_map = new HashMap<>();
            victim_map.put("ip",neededInfo.getDIp());
            attacker.add(victim_map);
        }
        List<String> alarmRelatedLabel = new ArrayList<>(encryptedToolInfo.getConnectBasicInfo().getAnalysisLabelList());
        List<Map<String, String>> attackRoute = get_attack_route(InfoRow);
        List<String> alarmSessionList = new ArrayList<>();
        alarmSessionList.add(encryptedToolInfo.getConnectBasicInfo().getSessionId());
        List<String> alarmAttackChainList = get_attack_chain_list(victim, attacker, alarmRelatedLabel, (String) sslDetectAlarmJson.get("alarm_knowledge_id"));

        sslDetectAlarmJson.put("alarm_reason", alarmReason);
        sslDetectAlarmJson.put("attack_family", attackFamily);
        sslDetectAlarmJson.put("targets", targets);
        String alarm_principle = get_alarm_principle(InfoRow);
        sslDetectAlarmJson.put("alarm_principle", alarm_principle);
        String alarmHandleMethod = get_alarm_handle_method(InfoRow);
        sslDetectAlarmJson.put("alarm_handle_method", alarmHandleMethod);
        sslDetectAlarmJson.put("alarm_type", "模型");
        sslDetectAlarmJson.put("victim", victim);
        sslDetectAlarmJson.put("attacker", attacker);
        sslDetectAlarmJson.put("alarm_related_label", alarmRelatedLabel);
        sslDetectAlarmJson.put("attack_route", attackRoute);
        sslDetectAlarmJson.put("alarm_session_list", alarmSessionList);
        sslDetectAlarmJson.put("attack_chain_list", alarmAttackChainList);
        String modelId = getModelId(InfoRow);
        sslDetectAlarmJson.put("model_id",modelId);
        List<String> pcapFileList = AlarmUtils.getPcapFileList(alarmSessionList,jedis);
        sslDetectAlarmJson.put("PcapFileList",pcapFileList);

        String esKey = encryptedToolInfo.getConnectBasicInfo().getEsKey();
        String taskId = esKey.split("_")[1];
        String batchId = esKey.split("_")[2];
        Map<String, Object> sendData = AlarmUtils.get_send_data(sslDetectAlarmJson,taskId,batchId);
        JSONObject sendDataJson = new JSONObject();
        sendDataJson.putAll(sendData);
//        JSONObject alarm_data = new JSONObject();
//        List<Map<String,Object>> bulk_list = new ArrayList<>();
//        bulk_list.add(alarm_json);
//        alarm_data.put("type","ALARM_INSERT_ES");
//        alarm_data.put("Bulk",bulk_list);
        return sendDataJson;
    }

    private static String getModelId(Row infoRow) {
        return "99032";
    }

    private static List<Map<String, Object>> get_alarm_reason(Row InfoRow) {
        List<Map<String, Object>> alarmReasonList = new ArrayList<>();
        Map<String, Object> alarmReasonSslDetect = new HashMap<>();
        EncryptedToolInfo encryptedToolInfo = InfoRow.getFieldAs(1);
        String webshellEncryptionType = encryptedToolInfo.isEncryptedToolEncrypted()?"，加密类型为："+encryptedToolInfo.getEncryptedToolEncryptionType():"，非进行加密";
        String webshellCodingType = "，工具产生的负载编码格式为：" + encryptedToolInfo.getEncryptedToolCodingType();
        String webshellType = encryptedToolInfo.getEncryptedToolType();
        String webshellEncrypted = encryptedToolInfo.isEncryptedToolEncrypted()?"加密通信协议攻击工具":"非加密通信协议攻击工具";
        String encryptedToolCustomProtocol = encryptedToolInfo.isEncryptedToolCustomProtocol() ? "，不存在协议格式" : "，存在协议格式";
        String webshellOneWay = encryptedToolInfo.isEncryptedToolOneWay()?"，会话类型为：单向会话。" : "，会话类型为：非单向会话。";
        String desc = String.format(" %s工具为", webshellType) + webshellEncrypted + webshellEncryptionType + webshellCodingType + encryptedToolCustomProtocol + webshellOneWay;
        switch (encryptedToolInfo.getEncryptedToolType()) {
            case "CobaltStrike":
                alarmReasonSslDetect.put("key", "发现符合CobaltStrike加密通信协议攻击工具的特征：");
                alarmReasonSslDetect.put("actual_value", "证书信息中serialNumber为特定值8bb00ee，" +
                        "issuer.rdnSequence中C、ST、O、OU、CN、L字段内容均为空，" +
                        "subject.rdnSequence中C、ST、O、OU、CN、L字段内容均为空。" +
                        "APT组织: BITTER。 " + desc);
                break;
            case "Metasploit":
                alarmReasonSslDetect.put("key", "发现符合metasploit加密通信协议攻击工具的特征：");
                alarmReasonSslDetect.put("actual_value", "证书信息中检测国家代码为 \"US\"，" +
                        "州代码以及组织单位为常见州代码和组织单位范围，" +
                        "组织名称后缀为\"Inc\"、\"and Sons\"、\"LLC\"或\"Group\"。" +
                        "APT组织: FIN7。 " + desc);
                break;
            case "Koadic":
                alarmReasonSslDetect.put("key", "发现符合Koadic加密通信协议攻击工具的特征：");
                alarmReasonSslDetect.put("actual_value", "请求体中包含固定参数H89A7ARLMT和PF12LRDPWX。" +
                        "APT组织: MuddyWater。 " + desc);
                break;
            case "Empire":
                alarmReasonSslDetect.put("key", "发现符合Empire加密通信协议攻击工具的特征：");
                alarmReasonSslDetect.put("actual_value", "证书信息中serialNumber序列号为特定值8ac729b92c4d6af64225faa43ebf9612238bc97，" +
                        "服务端指纹信息包含特征内容4418072496022778490，客户端指纹信息包含特征内容8829655996777896182。" + desc);
                break;
            case "merlin":
                alarmReasonSslDetect.put("key", "发现符合Merlin加密通信协议攻击工具的特征：");
                alarmReasonSslDetect.put("actual_value", "服务端指纹信息包含特征内容3703381180726290438，客户端指纹信息包含特征内容1142710092471348808，" +
                        "且建立会话后，每30-33s会发送一次心跳包，包长满足特征。" + desc);
                break;
            case "PyFUD":
                alarmReasonSslDetect.put("key", "发现符合PyFUD自定义通信协议攻击工具的特征：");
                alarmReasonSslDetect.put("actual_value", "负载由三个字段组成，且其中包含MAC地址。" + desc);
                break;
            case "Quasar":
                alarmReasonSslDetect.put("key", "发现符合Quasar加密通信协议攻击工具的特征：");
                alarmReasonSslDetect.put("actual_value", "证书信息中subjectCN字段与issuerCN字段为固定值Quasar Server CA，" +
                        "且心跳包负载为1。" +
                        "APT组织: Patchwork。 " + desc);
                break;
            case "NanoCore":
                alarmReasonSslDetect.put("key", "发现符合NanoCore加密通信协议攻击工具的特征：");
                alarmReasonSslDetect.put("actual_value", "从流量行为中分析该工具产生的包序列中检测到心跳包，且从服务端到客户端的心跳包负载为-1234，从客户端到服务端的心跳包负载为12。" + desc);
                break;
            case "Ghost":
                alarmReasonSslDetect.put("key", "发现符合Ghost加密通信协议攻击工具的特征：");
                alarmReasonSslDetect.put("actual_value", "从流量行为中分析该工具产生的包序列中检测到心跳包，且从服务端到客户端的心跳包负载为-1，从客户端到服务端的心跳包负载为1。 " + desc);
                break;
            case "THC-SSL-DOS":
                alarmReasonSslDetect.put("key", "发现符合THC-SSL-DOS加密通信协议攻击工具的特征：");
                alarmReasonSslDetect.put("actual_value", "服务端指纹信息中包含特征内容8033014089335809447，" + desc);
                break;
            case "Remcos":
                alarmReasonSslDetect.put("key", "发现符合Remcos加密通信协议攻击工具的特征：");
                alarmReasonSslDetect.put("actual_value", "证书信息中subjectCN字段为特征内容，issuerCN字段为特征内容Quasar Server CA，" +
                        "服务端指纹信息包含特征内容7498398509702323821，客户端指纹信息包含特征内容7899447458571054003，" + desc);
                break;
            default:
                logger.error("知识库中无当前自定义加密通信协议攻击工具");
                alarmReasonSslDetect.put("key", "");
                alarmReasonSslDetect.put("actual_value", "");
                break;
        }
        alarmReasonList.add(alarmReasonSslDetect);
        return alarmReasonList;
    }

    private static List<Map<String, String>> get_victim(Set<String> dIP) {
        List<Map<String,String>> result = new ArrayList<>();
        for(String ip:dIP){
            Map<String,String> victim = new HashMap<>();
            victim.put("ip",ip);
            result.add(victim);
        }
        return result;
    }

    private static List<Map<String, String>> get_attacker(Set<String> sIP) {
        List<Map<String,String>> result = new ArrayList<>();
        for(String ip:sIP){
            Map<String,String> victim = new HashMap<>();
            victim.put("ip",ip);
            result.add(victim);
        }
        return result;
    }

    private static List<Map<String, Object>> get_targets(Row infoRow) {
        EncryptedToolInfo encryptedToolInfo = infoRow.getFieldAs(1);
        String dIp = encryptedToolInfo.getConnectBasicInfo().getDIp();
        List<Map<String, Object>> targets = new ArrayList<>();
        Map<String, Object> targetIp = new HashMap<>();
        targetIp.put("name", dIp);
        targetIp.put("type", "ip");
        List<String> labelsIp = new ArrayList<>();
        labelsIp.add("1006");//DNS隧道服务器
        targetIp.put("labels", labelsIp);
        targets.add(targetIp);
        return targets;
    }

    private static List<Map<String, String>> get_family(Row InfoRow) {
        List<Map<String, String>> families = new ArrayList<>();
        // key webshell
        // value type
        // Quasar sslDetect、APT
        // key webshell
        // value type
        // org
        Map<String, String> familySslDetect = new HashMap<>();
        EncryptedToolInfo encryptedToolInfo = InfoRow.getFieldAs(1);
        switch (encryptedToolInfo.getEncryptedToolType()) {
            case "CobaltStrike":
                familySslDetect.put("key", "加密通信协议攻击工具");
                familySslDetect.put("value", "CobaltStrike");
                break;
            case "Metasploit":
                familySslDetect.put("key", "加密通信协议攻击工具");
                familySslDetect.put("value", "metsploit");
                break;
            case "Koadic":
                familySslDetect.put("key", "加密通信协议攻击工具");
                familySslDetect.put("value", "Koadic");
                break;
            case "Empire":
                familySslDetect.put("key", "加密通信协议攻击工具");
                familySslDetect.put("value", "Empire");
                break;
            case "merlin":
                familySslDetect.put("key", "加密通信协议攻击工具");
                familySslDetect.put("value", "Merlin");
                break;
            case "PyFUD":
                familySslDetect.put("key", "通信协议攻击工具");
                familySslDetect.put("value", "PyFUD");
                break;
            case "THC-SSL-DOS":
                familySslDetect.put("key", "通信协议攻击工具");
                familySslDetect.put("value", "THC-SSL-DOS");
                break;
            case "NanoCore":
                familySslDetect.put("key", "通信协议攻击工具");
                familySslDetect.put("value", "NanoCore");
                break;
            case "Ghost":
                familySslDetect.put("key", "通信协议攻击工具");
                familySslDetect.put("value", "Ghost");
                break;
            case "Remcos":
                familySslDetect.put("key", "通信协议攻击工具");
                familySslDetect.put("value", "Remcos");
            case "Quasar":
                familySslDetect.put("key", "加密通信协议攻击工具");
                familySslDetect.put("value", "Quasar");
                families.add(new HashMap<String, String>(){{
                    put("key", "APT组织");
                    put("value", "Quasar");
                }});
                break;
            default:
                logger.error("知识库中无当前自定义加密通信协议攻击工具");
                familySslDetect.put("key", "使用了:");
                familySslDetect.put("actual_value", "自定义加密通信协议攻击工具");
                break;
        }
        families.add(familySslDetect);
        return families;
    }

    private static String get_alarm_principle(Row InfoRow) {
        EncryptedToolInfo encryptedToolInfo = InfoRow.getFieldAs(1);
        switch (encryptedToolInfo.getEncryptedToolType()) {
            case "CobaltStrike":
                return "根据证书信息检测到符合CobaltStrike加密通信协议攻击工具的特征。";
            case "Metasploit":
                return "根据证书信息检测到符合metasploit加密通信协议攻击工具的特征。";
            case "Koadic":
                return "根据请求url信息检测到符合Koadic加密通信协议攻击工具的特征。";
            case "Empire":
                return "根据证书和指纹信息检测到符合Empire加密通信协议攻击工具的特征。";
            case "merlin":
                return "根据指纹和心跳包信息检测到符合CobaltStrike加密通信协议攻击工具的特征。";
            case "PyFUD":
                return "根据请求负载信息检测到符合PyFUD加密通信协议攻击工具的特征。";
            case "Quasar":
                return "根据证书和心跳包信息检测到符合Quasar加密通信协议攻击工具的特征。";
            case "THC-SSL-DOS":
                return "根据证书和心跳包信息检测到符合THC-SSL-DOS加密通信协议攻击工具的特征。";
            case "NanoCore":
                return "根据证书和心跳包信息检测到符合NanoCore加密通信协议攻击工具的特征。";
            case "Ghost":
                return "根据证书和心跳包信息检测到符合Ghost加密通信协议攻击工具的特征。";
            case "Remcos":
                return "根据证书和心跳包信息检测到符合Remcos加密通信协议攻击工具的特征。";
            default:
                logger.error("知识库中无当前自定义加密通信协议攻击工具");
                return "";
        }
    }

    private static String get_alarm_handle_method(Row InfoRow) {
        EncryptedToolInfo encryptedToolInfo = InfoRow.getFieldAs(1);
        return String.format("检查服务器是否被%s攻击，监控系统日志审查是否出现异常行为，特别是注意远程登录、文件上传、进程管理等关键操作的日志记录。", encryptedToolInfo.getEncryptedToolType());
    }

    private static List<Map<String, String>> get_attack_route(Row InfoRow) {
        List<Map<String, String>> attack_route = new ArrayList<>();
        return attack_route;
    }
}
