package com.geeksec.analysisFunction.writeAlarm;

import static com.geeksec.analysisFunction.getRowInfo.SslInfoRowFlatMap.Alarm_Info_Map;
import static com.geeksec.common.LabelUtils.AlarmUtils.get_attack_chain_list;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import com.geeksec.common.LabelUtils.AlarmUtils;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import org.apache.flink.types.Row;
import redis.clients.jedis.Jedis;

/**
 * <AUTHOR>
 * @Date 2022/12/21
 * 挖矿病毒告警
 */

public class writeMineAlarm {

    public static JSONObject get_mine_alarmJson(Row InfoRow, Jedis jedis) throws NoSuchAlgorithmException {
        String Alarm_type = InfoRow.getFieldAs(0);
        if (!Alarm_Info_Map.keySet().contains(Alarm_type)) {
            return null;
        }
        Map<String, Object> mineAlarmJson = AlarmUtils.get_known_alarm_info(InfoRow);
        mineAlarmJson.putAll(AlarmUtils.get_Default_alarm_map());
        List<Map<String,Object>> alarm_reason = get_alarm_reason(InfoRow);//reason
        String sIp = InfoRow.getFieldAs(1);
        String dIp = InfoRow.getFieldAs(2);
        List<Map<String,String>> attack_family = get_family(InfoRow);//attack_family
        List<Map<String,Object>> targets = get_targets(InfoRow);//targets
        NeededInfo neededInfo = InfoRow.getFieldAs(9);
        mineAlarmJson.put("vPort",neededInfo.getSPort());
        mineAlarmJson.put("aPort",neededInfo.getDPort());
        mineAlarmJson.put("sPort",neededInfo.getSPort());
        mineAlarmJson.put("dPort",neededInfo.getDPort());
        mineAlarmJson.put("tranProto",neededInfo.getTranProto());
        mineAlarmJson.put("appProto",neededInfo.getAppProto());
        mineAlarmJson.put("httpDomain",neededInfo.getHttpDomain());
        mineAlarmJson.put("sniDomain",neededInfo.getSniDomain());
        mineAlarmJson.put("sIp",neededInfo.getSIp());
        mineAlarmJson.put("dIp",neededInfo.getDIp());

        Set<String> victim_set = new HashSet<>();
        victim_set.add(sIp);
        Set<String> attacker_set = new HashSet<>();
        attacker_set.add(dIp);
        //victim
        List<Map<String,String>> victim = get_victim_attacker(victim_set);
        if (!victim_set.contains(neededInfo.getSIp())){
            Map<String,String> victim_map = new HashMap<>();
            victim_map.put("ip",neededInfo.getSIp());
            victim.add(victim_map);
        }
        //attacker
        List<Map<String,String>> attacker = get_victim_attacker(attacker_set);
        if (!attacker_set.contains(neededInfo.getDIp())){
            Map<String,String> victim_map = new HashMap<>();
            victim_map.put("ip",neededInfo.getDIp());
            attacker.add(victim_map);
        }
        List<String> alarm_related_label = new ArrayList<>();
        alarm_related_label.add(InfoRow.getFieldAs(6));
        List<Map<String,Object>> attack_route = get_attack_route(InfoRow);
        List<String> alarm_session_list = get_alarm_session_list(InfoRow);
        List<String> alarm_attack_chain_list = get_attack_chain_list(victim,attacker,alarm_related_label, (String) mineAlarmJson.get("alarm_knowledge_id"));

        mineAlarmJson.put("alarm_reason",alarm_reason);
        mineAlarmJson.put("attack_family",attack_family);
        mineAlarmJson.put("targets",targets);
        mineAlarmJson.put("alarm_principle","使用挖矿通讯协议进行挖矿通讯，使用计算机或者移动设备内的资源挖掘加密货币。");
        mineAlarmJson.put("alarm_handle_method","检测到当前网络存在挖矿软件运行，受害者主机的CPU、GPU被复杂运算的恶意程序占用 1. 确认告警：        -对告營进行分析，确认告營正确性         -对日志进行分析。确认所有中招主机          2.现状确认：         -分析安全告警，如连接/查询矿池，告警最早发现时间          3.处置之际：        -在主机上关闭挖矿程序       ");
        mineAlarmJson.put("alarm_type","模型");
        mineAlarmJson.put("victim",victim);
        mineAlarmJson.put("attacker",attacker);
        mineAlarmJson.put("alarm_related_label",alarm_related_label);
        mineAlarmJson.put("attack_route",attack_route);
        mineAlarmJson.put("alarm_session_list",alarm_session_list);
        mineAlarmJson.put("attack_chain_list",alarm_attack_chain_list);
        String modelId = getModelId(InfoRow);
        mineAlarmJson.put("model_id",modelId);
        List<String> pcapFileList = AlarmUtils.getPcapFileList(alarm_session_list,jedis);
        mineAlarmJson.put("PcapFileList",pcapFileList);

        String esKey = InfoRow.getFieldAs(8);
        String taskId = esKey.split("_")[1];
        String batchId = esKey.split("_")[2];
        Map<String,Object> send_data = AlarmUtils.get_send_data(mineAlarmJson,taskId,batchId);
        JSONObject alarm_json = new JSONObject();
        alarm_json.putAll(send_data);
//        JSONObject alarm_data = new JSONObject();
//        List<Map<String,Object>> bulk_list = new ArrayList<>();
//        bulk_list.add(alarm_json);
//        alarm_data.put("type","ALARM_INSERT_ES");
//        alarm_data.put("Bulk",bulk_list);
        return alarm_json;
    }

    private static String getModelId(Row infoRow) {
        return "99006";
    }

    private static List<Map<String,Object>> get_alarm_reason(Row InfoRow){
        List<Map<String,Object>> alarm_reason_list = new ArrayList<>();
        Map<String,Object> alarm_reason_IP = new HashMap<>();
        Map<String,Object> alarm_reason_Domain = new HashMap<>();
        String mine_domain = InfoRow.getFieldAs(3);
        String mine_IP = InfoRow.getFieldAs(2);
        alarm_reason_IP.put("key","挖矿地址通讯");
        alarm_reason_IP.put("actual_value",mine_IP);
        alarm_reason_list.add(alarm_reason_IP);
        if (!Objects.equals(mine_domain, "")){
            alarm_reason_Domain.put("key","访问挖矿域名");
            alarm_reason_Domain.put("actual_value",mine_domain);
            alarm_reason_list.add(alarm_reason_Domain);
        }
        String mine_finger = InfoRow.getFieldAs(5);
        if (!Objects.equals(mine_finger, "")){
            Map<String,Object> alarm_reason_finger = new HashMap<>();
            alarm_reason_finger.put("key","使用矿池通讯指纹");
            alarm_reason_finger.put("actual_value",mine_finger);
            alarm_reason_list.add(alarm_reason_finger);
            Map<String,Object> alarm_reason_LSTM = new HashMap<>();
            alarm_reason_LSTM.put("key","LSTM模型预测结果");
            alarm_reason_LSTM.put("actual_value",String.valueOf(0.9));//使用随机数
            alarm_reason_list.add(alarm_reason_LSTM);
        }
        return alarm_reason_list;
    }

    private static List<Map<String,String>> get_victim_attacker(Set<String> IP_list){
        List<Map<String,String>> result = new ArrayList<>();
        for(String ip:IP_list){
            Map<String,String> victim = new HashMap<>();
            victim.put("ip",ip);
            result.add(victim);
        }
        return result;
    }

    private static List<Map<String,Object>> get_targets(Row mineRow){
        List<Map<String,Object>> targets = new ArrayList<>();
        Map<String,Object> target_ip = new HashMap<>();
        target_ip.put("name",mineRow.getField(2));
        target_ip.put("type","ip");
        List<String> labels_ip = new ArrayList<>();
        labels_ip.add("9008");
        target_ip.put("labels",labels_ip);
        targets.add(target_ip);
        String mine_domain = (String) mineRow.getField(3);
        if (!Objects.equals(mine_domain, "")){
            Map<String,Object> target_domain = new HashMap<>();
            target_domain.put("name",mineRow.getField(3));
            target_domain.put("type","domain");
            List<String> labels_domain = new ArrayList<>();
            labels_domain.add("9006");
            target_domain.put("labels",labels_domain);
            targets.add(target_domain);
        }
        return targets;
    }

    private static List<Map<String,String>> get_family(Row InfoRow){
        List<Map<String,String>> families = new ArrayList<>();
        Map<String,String> family = new HashMap<>();
        String domain_family = InfoRow.getFieldAs(4);
        if (!Objects.equals(domain_family, "")){
            family.put("family_type","挖矿矿池");
            family.put("family_name",domain_family);
            families.add(family);
        }
        return families;
    }

    private static List<Map<String,Object>> get_attack_route(Row InfoRow){
        List<Map<String,Object>> attack_route = new ArrayList<>();
        String finger_id = InfoRow.getFieldAs(5);
        String finger_Label = InfoRow.getFieldAs(6);
        Map<String,Object> attack_route1 = new HashMap<>();
        List<String> labels = new ArrayList<>();
        labels.add(finger_Label);
        attack_route1.put("type","finger");
        attack_route1.put("name",finger_id);
        attack_route1.put("label",labels);
        attack_route.add(attack_route1);
        String mine_domain = (String) InfoRow.getField(3);
        if (!Objects.equals(mine_domain, "")){
            Map<String,Object> attack_route2 = new HashMap<>();
            List<String> labels_domain = new ArrayList<>();
            labels_domain.add("9006");
            attack_route2.put("type","domain");
            attack_route2.put("name",mine_domain);
            attack_route2.put("label",labels_domain);
            attack_route.add(attack_route2);
        }
        return attack_route;
    }

    private static List<String> get_alarm_session_list(Row InfoRow){
        List<String> alarm_session_list = new ArrayList<>();
        alarm_session_list.add(InfoRow.getFieldAs(7));
        return alarm_session_list;
    }
}
