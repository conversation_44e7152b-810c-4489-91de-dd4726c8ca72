package com.geeksec.analysisFunction.analysisEntity.connectRelatedInfo;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/6/20
 */

/** 记录一个会话中的一个单包的信息*/
@Data
public class PacketInfo {
    /** 方向：
     * 0:客户端到服务端,
     * 1:服务端到客户端
     * */
    private int direction;

    /** 单包的包数量都是1，
     * 此字段为包大小
     * */
    private int byteNum;

    /** 本次单包的时间
     * */
    private int singleTime;

    /**
     * 本次单包的时间,ns
     * */
    private int nSingleTime;

    /**
     * 该包序列的位次
     * */
    private int count;
}
