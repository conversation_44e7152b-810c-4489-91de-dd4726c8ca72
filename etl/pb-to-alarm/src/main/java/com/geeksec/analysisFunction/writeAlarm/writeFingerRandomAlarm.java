package com.geeksec.analysisFunction.writeAlarm;

import static com.geeksec.analysisFunction.getRowInfo.SslInfoRowFlatMap.Alarm_Info_Map;
import static com.geeksec.common.LabelUtils.AlarmUtils.get_attack_chain_list;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import com.geeksec.common.LabelUtils.AlarmUtils;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import org.apache.flink.types.Row;
import redis.clients.jedis.Jedis;

/**
 * <AUTHOR>
 * @Date 2022/12/16
 * 指纹随机化访问服务端告警
 */

public class writeFingerRandomAlarm {

    public static JSONObject get_FingerRandom_alarmJson(Row InfoRow, Jedis jedis) throws NoSuchAlgorithmException {
        String Alarm_type = InfoRow.getFieldAs(0);
        if (!Alarm_Info_Map.keySet().contains(Alarm_type)){
            return null;
        }
        Map<String, Object> fingerRandomAlarmJson = AlarmUtils.get_known_alarm_info(InfoRow);
        fingerRandomAlarmJson.putAll(AlarmUtils.get_Default_alarm_map());

        List<Map<String,Object>> alarm_reason = get_alarm_reason(InfoRow);//reason
        String sIp = InfoRow.getFieldAs(1);
        String dIp = InfoRow.getFieldAs(2);
        List<Map<String,String>> attack_family = new ArrayList<>();//attack_family
        List<Map<String,Object>> targets = get_targets(InfoRow);
        NeededInfo neededInfo = InfoRow.getFieldAs(7);
        fingerRandomAlarmJson.put("vPort",neededInfo.getDPort());
        fingerRandomAlarmJson.put("aPort",neededInfo.getSPort());
        fingerRandomAlarmJson.put("sPort",neededInfo.getSPort());
        fingerRandomAlarmJson.put("dPort",neededInfo.getDPort());
        fingerRandomAlarmJson.put("tranProto",neededInfo.getTranProto());
        fingerRandomAlarmJson.put("appProto",neededInfo.getAppProto());
        fingerRandomAlarmJson.put("httpDomain",neededInfo.getHttpDomain());
        fingerRandomAlarmJson.put("sniDomain",neededInfo.getSniDomain());
        fingerRandomAlarmJson.put("sIp",neededInfo.getSIp());
        fingerRandomAlarmJson.put("dIp",neededInfo.getDIp());
        Set<String> victim_set = new HashSet<>();
        victim_set.add(dIp);
        Set<String> attacker_set = new HashSet<>();
        attacker_set.add(sIp);
        //victim
        List<Map<String,String>> victim = get_victim_attacker(victim_set);
        if (!victim_set.contains(neededInfo.getDIp())){
            Map<String,String> victim_map = new HashMap<>();
            victim_map.put("ip",neededInfo.getDIp());
            victim.add(victim_map);
        }
        //attacker
        List<Map<String,String>> attacker = get_victim_attacker(attacker_set);
        if (!attacker_set.contains(neededInfo.getSIp())){
            Map<String,String> victim_map = new HashMap<>();
            victim_map.put("ip",neededInfo.getSIp());
            attacker.add(victim_map);
        }
        List<String> alarm_related_label = new ArrayList<>();
        alarm_related_label.add(InfoRow.getFieldAs(4));
        alarm_related_label.add("20056");
        alarm_related_label.add("20057");
        List<Map<String,Object>> attack_route = get_attack_route(InfoRow);
        Collection<String> alarm_session_list = InfoRow.getFieldAs(5);
        List<String> alarm_attack_chain_list = get_attack_chain_list(victim,attacker,alarm_related_label, (String) fingerRandomAlarmJson.get("alarm_knowledge_id"));

        fingerRandomAlarmJson.put("alarm_reason",alarm_reason);
        fingerRandomAlarmJson.put("attack_family",attack_family);
        fingerRandomAlarmJson.put("targets",targets);
        fingerRandomAlarmJson.put("alarm_handle_method","将告警攻击方IP加入黑名单禁止访问");
        fingerRandomAlarmJson.put("alarm_type","模型");
        fingerRandomAlarmJson.put("victim",victim);
        fingerRandomAlarmJson.put("attacker",attacker);
        fingerRandomAlarmJson.put("alarm_related_label",alarm_related_label);
        fingerRandomAlarmJson.put("attack_route",attack_route);
        fingerRandomAlarmJson.put("alarm_session_list",alarm_session_list.toArray());
        fingerRandomAlarmJson.put("attack_chain_list",alarm_attack_chain_list);
        String modelId = getModelId(InfoRow);
        fingerRandomAlarmJson.put("model_id",modelId);
        List<String> pcapFileList = AlarmUtils.getPcapFileList(alarm_session_list,jedis);
        fingerRandomAlarmJson.put("PcapFileList",pcapFileList);
        
        String esKey = InfoRow.getFieldAs(6);
        String taskId = esKey.split("_")[1];
        String batchId = esKey.split("_")[2];
        Map<String,Object> send_data = AlarmUtils.get_send_data(fingerRandomAlarmJson,taskId,batchId);
        JSONObject alarm_json = new JSONObject();
        alarm_json.putAll(send_data);
//        JSONObject alarm_data = new JSONObject();
//        List<Map<String,Object>> bulk_list = new ArrayList<>();
//        bulk_list.add(alarm_json);
//        alarm_data.put("type","ALARM_INSERT_ES");
//        alarm_data.put("Bulk",bulk_list);
        return alarm_json;
    }

    private static String getModelId(Row infoRow) {
        return "99015";
    }

    private static List<Map<String,Object>> get_alarm_reason(Row InfoRow){
        List<Map<String,Object>> alarm_reason_list = new ArrayList<>();
        Map<String,Object> alarm_reason = new HashMap<>();
        Collection<String> finger_set = InfoRow.getFieldAs(3);
        alarm_reason.put("key","使用随机化指纹");
        alarm_reason.put("actual_value",finger_set.toString());
        alarm_reason_list.add(alarm_reason);
        return alarm_reason_list;
    }

    private static List<Map<String,String>> get_victim_attacker(Set<String> IP_list){
        List<Map<String,String>> result = new ArrayList<>();
        for(String ip:IP_list){
            Map<String,String> victim = new HashMap<>();
            victim.put("ip",ip);
            result.add(victim);
        }
        return result;
    }

    private static List<Map<String,Object>> get_targets(Row mineRow){
        List<Map<String,Object>> targets = new ArrayList<>();
        Map<String,Object> target_tmp = new HashMap<>();
        target_tmp.put("name",mineRow.getField(1));
        target_tmp.put("type","ip");
        List<String> labels_ip = new ArrayList<>();
        target_tmp.put("labels",labels_ip);
        targets.add(target_tmp);
        return targets;
    }

    private static List<Map<String,Object>> get_attack_route(Row InfoRow){
        List<Map<String,Object>> attack_route = new ArrayList<>();
        Collection<String> finger_set = InfoRow.getFieldAs(3);
        for (String finger:finger_set){
            Map<String,Object> attack_route1 = new HashMap<>();
            List<String> labels = new ArrayList<>();
            attack_route1.put("type","finger");
            attack_route1.put("name",finger);
            attack_route1.put("label",labels);
            attack_route.add(attack_route1);
        }
        return attack_route;
    }
}
