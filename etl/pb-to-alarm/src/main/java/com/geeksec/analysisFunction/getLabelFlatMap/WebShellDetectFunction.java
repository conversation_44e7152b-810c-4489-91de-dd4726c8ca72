package com.geeksec.analysisFunction.getLabelFlatMap;

import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.types.Row;

/**
 * <AUTHOR>
 * @Date 2024/9/10
 */
public class WebShellDetectFunction{

    public static DataStream<Row> WebShellDetect(DataStream<Row> webShellRow) {
        return WebShellEncryptFunction.webshellByPayloadDown(webShellRow)
                .union(WebShellEncryptFunction.webshellByRequestHead(webShellRow))
                .union(WebShellEncryptFunction.webshellByCookie(webShellRow))
                .union(WebShellUnEncryptFunction.webshellByPayloadDecode(webShellRow))
                .union(WebShellUnEncryptFunction.webshellByUrl(webShellRow))
                .union(WebShellUnEncryptFunction.webshellByPayloadDecode(webShellRow));
    }
}
