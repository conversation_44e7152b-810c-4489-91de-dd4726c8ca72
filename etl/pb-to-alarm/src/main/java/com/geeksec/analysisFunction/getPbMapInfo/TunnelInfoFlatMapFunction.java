package com.geeksec.analysisFunction.getPbMapInfo;

import com.geeksec.analysisFunction.analysisEntity.connectRelatedInfo.ConnectBasicInfo;
import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import com.geeksec.analysisFunction.analysisEntity.tunnel.*;
import java.util.Map;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2024/7/25
 */

public class TunnelInfoFlatMapFunction extends RichFlatMapFunction<Map<String, Object>, BasicTunnel> {

    private final static Logger logger = LoggerFactory.getLogger(TunnelInfoFlatMapFunction.class);

    @Override
    public void close() throws Exception {
        super.close();
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<BasicTunnel> collector) throws Exception {

        ConnectBasicInfo connectBasicInfo = new ConnectBasicInfo(pbMap);
        String appName = (String) pbMap.get("AppProto");

        BasicTunnel basicTunnel = new BasicTunnel();
        basicTunnel.setConnectBasicInfo(connectBasicInfo);

        NeededInfo neededInfo = new NeededInfo(pbMap);
        basicTunnel.setNeededInfo(neededInfo);

        switch (appName){
            case "TCP_Unknow":
            case "TCP_Other":
            case "TCP_QueryOnly":
                basicTunnel.setTunnelType("TCP");
                collector.collect(basicTunnel);
                break;
            case "APP_HTTP":
                basicTunnel.setTunnelType("HTTP");
                collector.collect(basicTunnel);
                break;
            case "APP_NTP":
                basicTunnel.setTunnelType("NTP");
                collector.collect(basicTunnel);
                break;
            case "APP_SSL":
                basicTunnel.setTunnelType("SSL");
                collector.collect(basicTunnel);
                break;
            case "APP_ICMP_v4":
                basicTunnel.setTunnelType("ICMP");
                collector.collect(basicTunnel);
                break;
            default:
//                logger.info("无待提取的隐蔽信道信息");
                break;
        }
    }
}
