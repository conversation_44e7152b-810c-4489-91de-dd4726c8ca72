package com.geeksec.analysisFunction.analysisEntity.neededInfo;

import java.util.Map;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/12/2
 * 57需要的必须信息
 */

@Data
@NoArgsConstructor
public class NeededInfo {
    /**
     * 源IP
     */
    private String sIp;

    /**
     * 目的IP
     */
    private String dIp;

    /**
     * 源端口
     */
    private Integer sPort;

    /**
     * 目的端口
     */
    private Integer dPort;

    /**
     * 传输层协议	TCP、UDP、SCTP
     */
    private String tranProto;

    /**
     * 应用层协议	HTTP、TLS、SSH
     */
    private String appProto;

    /**
     * 原始元数据 产生告警的链接原始元数据，封装为一个字段，参考SDX网防目标元数据存储规范
     */
    private byte[] metaData;

    /**
     * 受害者HTTP域名
     */
    private String httpDomain = "";

    /**
     * 受害者SNI域名
     */
    private String sniDomain = "";

    public NeededInfo(Map<String,Object> pbMap) {
        this.sIp = (String) pbMap.get("sIp");
        this.dIp = (String) pbMap.get("dIp");
        this.sPort = (Integer) pbMap.get("sPort");
        this.dPort = (Integer) pbMap.get("dPort");
        this.appProto = (String) pbMap.getOrDefault("AppProto","");
        this.tranProto = (String) pbMap.getOrDefault("TranProto","");
    }
}
