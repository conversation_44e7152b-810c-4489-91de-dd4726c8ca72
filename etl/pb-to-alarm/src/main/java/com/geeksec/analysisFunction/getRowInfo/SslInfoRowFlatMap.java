package com.geeksec.analysisFunction.getRowInfo;

import com.geeksec.analysisFunction.analysisEntity.nebula.BaseEdge;
import com.geeksec.analysisFunction.analysisEntity.nebula.SSLFingerInfo;
import com.geeksec.common.LabelUtils.FileUtil;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

public class SslInfoRowFlatMap extends RichFlatMapFunction<Map<String,Object>, Row> {

    public static Map<String,Map<String,String>> Alarm_Info_Map = new HashMap<>();
    public static Map<String,Integer> ROW_MODEL_MAP = new HashMap<>();

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        InputStream Alarm_stream = this.getClass().getClassLoader().getResourceAsStream("gk_alarm.csv");
        BufferedReader Alarm_buffer = new BufferedReader(new InputStreamReader(Alarm_stream));
        InputStream ROW_MODEL_stream = this.getClass().getClassLoader().getResourceAsStream("model_info.csv");
        BufferedReader ROW_MODEL_buffer = new BufferedReader(new InputStreamReader(ROW_MODEL_stream));
        //加载配置文件
        try {
            Alarm_Info_Map = FileUtil.load_Alarm_Map(Alarm_buffer);
            ROW_MODEL_MAP = FileUtil.load_ROW_MODEL_Map(ROW_MODEL_buffer);
        } catch (Exception e) {
            throw new IOException("file load fail ==>", e);
        }
    }

    @Override
    public void flatMap(Map<String, Object> InfoMap, Collector<Row> collector) throws Exception {
        SSLFingerInfo sSSLFingerInfo = (SSLFingerInfo) InfoMap.get("sSSLFingerInfoTag");
        Row sSSLFingerTagRow = new Row(11);
        if (sSSLFingerInfo != null){
            sSSLFingerTagRow.setField(0,"SSL_FINGER_INFO");
            sSSLFingerTagRow.setField(1, sSSLFingerInfo.getFingerId());
            sSSLFingerTagRow.setField(2, sSSLFingerInfo.getJa3Hash());
            sSSLFingerTagRow.setField(3, sSSLFingerInfo.getDesc());
            sSSLFingerTagRow.setField(4, sSSLFingerInfo.getType());
            sSSLFingerTagRow.setField(5, sSSLFingerInfo.getSIp());
            sSSLFingerTagRow.setField(6, sSSLFingerInfo.getDIp());
            sSSLFingerTagRow.setField(7, sSSLFingerInfo.getServer_domain());
            sSSLFingerTagRow.setField(8, sSSLFingerInfo.getSessionId());
            sSSLFingerTagRow.setField(9, sSSLFingerInfo.getEsKey());
            sSSLFingerTagRow.setField(10, sSSLFingerInfo.getNeededInfo());
            collector.collect(sSSLFingerTagRow);
        }

        SSLFingerInfo dSSLFingerInfo = (SSLFingerInfo) InfoMap.get("dSSLFingerInfoTag");
        Row dSSLFingerTagRow = new Row(11);
        if (sSSLFingerInfo != null){
            dSSLFingerTagRow.setField(0,"SSL_FINGER_INFO");
            dSSLFingerTagRow.setField(1, dSSLFingerInfo.getFingerId());
            dSSLFingerTagRow.setField(2, dSSLFingerInfo.getJa3Hash());
            dSSLFingerTagRow.setField(3, dSSLFingerInfo.getDesc());
            dSSLFingerTagRow.setField(4, dSSLFingerInfo.getType());
            dSSLFingerTagRow.setField(5, dSSLFingerInfo.getSIp());
            dSSLFingerTagRow.setField(6, dSSLFingerInfo.getDIp());
            dSSLFingerTagRow.setField(7, dSSLFingerInfo.getServer_domain());
            dSSLFingerTagRow.setField(8, dSSLFingerInfo.getSessionId());
            dSSLFingerTagRow.setField(9, dSSLFingerInfo.getEsKey());
            dSSLFingerTagRow.setField(10, dSSLFingerInfo.getNeededInfo());
            collector.collect(dSSLFingerTagRow);
        }

        Row dipdSSLFingerSipRow = (Row) InfoMap.get("SIP_DIP_FINGER_ROW");
        if (dipdSSLFingerSipRow!=null){
            collector.collect(dipdSSLFingerSipRow);
        }

        // ClientIP ---> DOMAIN: client_ssl_connect_domain
        BaseEdge clientSslConnectDomainEdge = (BaseEdge) InfoMap.get("clientSslConnectDomainEdge");
        if (clientSslConnectDomainEdge != null) {
            Row clientSslConnectDomainEdgeRow = new Row(8);
            clientSslConnectDomainEdgeRow.setField(0, "CLIENT_SSL_CONNECT_DOMAIN_EDGE");
            clientSslConnectDomainEdgeRow.setField(1, clientSslConnectDomainEdge.getSrcId());
            clientSslConnectDomainEdgeRow.setField(2, clientSslConnectDomainEdge.getDstId());
            clientSslConnectDomainEdgeRow.setField(3, 0);
            clientSslConnectDomainEdgeRow.setField(4, clientSslConnectDomainEdge.getFirstTime());
            clientSslConnectDomainEdgeRow.setField(5, clientSslConnectDomainEdge.getLastTime());
            clientSslConnectDomainEdgeRow.setField(6, clientSslConnectDomainEdge.getSessionCnt());
            clientSslConnectDomainEdgeRow.setField(7, clientSslConnectDomainEdge.getNeededInfo());

            collector.collect(clientSslConnectDomainEdgeRow);
        }

        // ServerIP ---> DOMAIN: server_ssl_connect_domain
        BaseEdge serverSslConnectDomainEdge = (BaseEdge) InfoMap.get("serverSslConnectDomainEdge");
        if (serverSslConnectDomainEdge != null) {
            Row serverSslConnectDomainEdgeRow = new Row(8);
            serverSslConnectDomainEdgeRow.setField(0, "SERVER_SSL_CONNECT_DOMAIN_EDGE");
            serverSslConnectDomainEdgeRow.setField(1, serverSslConnectDomainEdge.getSrcId());
            serverSslConnectDomainEdgeRow.setField(2, serverSslConnectDomainEdge.getDstId());
            serverSslConnectDomainEdgeRow.setField(3, 0);
            serverSslConnectDomainEdgeRow.setField(4, serverSslConnectDomainEdge.getFirstTime());
            serverSslConnectDomainEdgeRow.setField(5, serverSslConnectDomainEdge.getLastTime());
            serverSslConnectDomainEdgeRow.setField(6, serverSslConnectDomainEdge.getSessionCnt());
            serverSslConnectDomainEdgeRow.setField(7, serverSslConnectDomainEdge.getNeededInfo());
            collector.collect(serverSslConnectDomainEdgeRow);
        }
    }
}
