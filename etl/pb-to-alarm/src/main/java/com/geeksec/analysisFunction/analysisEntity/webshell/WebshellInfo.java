package com.geeksec.analysisFunction.analysisEntity.webshell;

import com.geeksec.analysisFunction.analysisEntity.connectRelatedInfo.ConnectBasicInfo;
import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import com.geeksec.analysisFunction.analysisEntity.protocolRelatedInfo.HttpSimpleInfo;
import com.geeksec.analysisFunction.analysisEntity.protocolRelatedInfo.SslSimpleInfo;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/8/23
 */

@Data
public class WebshellInfo {
    /**
     * 基础会话信息, ConnectBasicInfo
     * */
    private ConnectBasicInfo connectBasicInfo;

    /**
     *  基础ssl元数据信息, List<SslBasicInfo>
     * */
    private List<SslSimpleInfo> sslSimpleInfos = new ArrayList<>();

    /**
     * 基础http信息, List<HttpBasicInfo>
     * */
    private List<HttpSimpleInfo> httpSimpleInfos = new ArrayList<>();

    /**
     * Webshell是否单向, OneWay
     * */
    private boolean webshellOneWay;

    /**
     * Webshell单向流量标签tag号
     * TODO 标签号待定
     * */
    public static String webshellOneWayTag = "xxxxx";

    /**
     * Webshell类型, WebshellType
     * */
    private String webshellType;

    /**
     * 支持的解析的webshell类工具的标签列表
     * 加密webshell：冰蝎、哥斯拉、SharPyShell、Weevely，天蝎和jspmaster
     * 非加密webshell：中国菜刀、cknife、蚁剑、WeBacoo、Webshell-Sniper、
     * 开山斧，以及Altman、QuasiBot、WebshellManager、Xise、wBay、WebKnife、
     * KB飞刀、Hatchet和小李飞刀
     * TODO 标签号待定
     */
    public static List<String> webshellLabelKnowledge = Arrays.asList("1","2","3","4","5","6","7","8","9","10","11","12","13");

    /**
     * Webshell编码类型, WebshellCodingType
     * */
    private String webshellCodingType;
    /**
     * 支持的解析的webshell的编码类型：base64编码、hex编码、xor异或编码
     * TODO 标签号待定
     * */
    public static List<String> webshellCodingKnowledge = Arrays.asList("base64","hex","xor");

    /**
     * Webshell加密算法类型, WebshellEncryptionType
     * */
    private String webshellEncryptionType;

    /**
     * 支持的解析的webshell的加密算法列表：AES，DES，RC4
     * TODO 标签号待定
     * */
    public static List<String> webshellEncryptionKnowledge = Arrays.asList("AES","DES","RC4");

    /**
     * Webshell是否加密, Webshell Encrypted
     * */
    private boolean webshellEncrypted;

    /**
     * Webshell单向流量标签tag号
     * TODO 标签号待定
     * */
    public static String webshellEncryptedTag = "xxxxx";
    /**
     * 是否自定义协议
     */
    private boolean webshellCustomProtocol;

    /**
     * 告警携带信息
     */
    private NeededInfo neededInfo;
}
