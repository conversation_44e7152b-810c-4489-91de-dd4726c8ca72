package com.geeksec.analysisFunction.analysisEntity.tunnel;

import static com.geeksec.analysisFunction.getLabelFlatMap.NeoregeoFlatMap.english_LIST;
import static com.geeksec.analysisFunction.getPbMapInfo.HttpInfoMapFlatMapFunction.get_abnormal_head;

import com.geeksec.SpecProtocolEnum;
import com.geeksec.common.LabelUtils.AlarmUtils;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/7/25
 */

@Data
public class HttpTunnel{

    /**
     * 客户端和服务端的kv信息
     * */
    private Map<String,String> Client_info;
    private Map<String,String> Server_info;

    public static final String ETL_TAG = SpecProtocolEnum.SPOOF_HTTP.getCode();

    private boolean isHttpTunnel = false;

    public HttpTunnel(Map<String, Object> pbMap) {
        this.Client_info = (Map<String, String>) pbMap.getOrDefault("Client",new HashMap<>());
        this.Server_info = (Map<String, String>) pbMap.getOrDefault("Server",new HashMap<>());
    }

    public void handleHTTPTunnelInfo() {
        Map<String,String> Client_info = this.getClient_info();
        Map<String,String> Server_info = this.getServer_info();
        Map<String,String> abnormal_head = get_abnormal_head(Client_info,Server_info);
        for (String key:abnormal_head.keySet()){
            // 判断值的熵，目前弃用
            String val = abnormal_head.get(key);
            if (val.length()>54){
                val = val.substring(0,54);
            }
            Double val_Entropy = AlarmUtils.calculateEntropy(val);
            String field = key.split("-")[1];
            if (!english_LIST.contains(field)){
                // 熵的值：实验室值为4.9
                if (val_Entropy>4.9){
                    this.isHttpTunnel = true;
                    break;
                }
            }
        }
    }
}
