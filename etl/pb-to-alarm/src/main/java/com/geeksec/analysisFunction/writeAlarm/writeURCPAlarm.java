package com.geeksec.analysisFunction.writeAlarm;

import static com.geeksec.analysisFunction.getRowInfo.SslInfoRowFlatMap.Alarm_Info_Map;
import static com.geeksec.common.LabelUtils.AlarmUtils.get_attack_chain_list;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.connectRelatedInfo.ConnectBasicInfo;
import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import com.geeksec.analysisFunction.analysisEntity.protocolRelatedInfo.SRCPInfo;
import com.geeksec.common.LabelUtils.AlarmUtils;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

/**
 * <AUTHOR>
 * @Date 2024/8/2
 */

public class writeURCPAlarm {
    private static final Logger logger = LoggerFactory.getLogger(writeURCPAlarm.class);
    public static JSONObject getURCPAlarmJson(Row urcpRow, Jedis jedis) throws NoSuchAlgorithmException {

        Row InfoRow = transToInfoRow(urcpRow);

        String alarmType = InfoRow.getFieldAs(0);
        if (!Alarm_Info_Map.keySet().contains(alarmType)) {
            return null;
        }
        Map<String, Object> urcpAlarmJson = AlarmUtils.get_known_alarm_info(InfoRow);
        urcpAlarmJson.putAll(AlarmUtils.get_Default_alarm_map());
        //reason
        List<Map<String,Object>> alarmReason = get_alarm_reason(urcpRow);
        String dIp = InfoRow.getFieldAs(3);
        String sIp = InfoRow.getFieldAs(2);
        //attack_family
        List<Map<String,String>> attackFamily = get_family();
        //targets
        List<Map<String,Object>> targets = get_targets(InfoRow);

        NeededInfo neededInfo = InfoRow.getFieldAs(7);
        urcpAlarmJson.put("vPort",neededInfo.getSPort());
        urcpAlarmJson.put("aPort",neededInfo.getDPort());
        urcpAlarmJson.put("sPort",neededInfo.getSPort());
        urcpAlarmJson.put("dPort",neededInfo.getDPort());
        urcpAlarmJson.put("tranProto",neededInfo.getTranProto());
        urcpAlarmJson.put("appProto",neededInfo.getAppProto());
        urcpAlarmJson.put("httpDomain",neededInfo.getHttpDomain());
        urcpAlarmJson.put("sniDomain",neededInfo.getSniDomain());
        urcpAlarmJson.put("sIp",neededInfo.getSIp());
        urcpAlarmJson.put("dIp",neededInfo.getDIp());

        Set<String> attacker_set = new HashSet<>();
        attacker_set.add(dIp);
        Set<String> victim_set = new HashSet<>();
        victim_set.add(sIp);
        //victim
        List<Map<String,String>> victim = get_victim(victim_set);
        if (!victim_set.contains(neededInfo.getSIp())){
            Map<String,String> victim_map = new HashMap<>();
            victim_map.put("ip",neededInfo.getSIp());
            victim.add(victim_map);
        }
        //attacker
        List<Map<String,String>> attacker = get_attacker(attacker_set);
        if (!attacker_set.contains(neededInfo.getDIp())){
            Map<String,String> victim_map = new HashMap<>();
            victim_map.put("ip",neededInfo.getDIp());
            attacker.add(victim_map);
        }
        List<String> alarmRelatedLabel = new ArrayList<>(InfoRow.getFieldAs(4));
        List<Map<String,String>> attackRoute = get_attack_route();
        Collection<String> alarmSessionList = Collections.singletonList(InfoRow.getFieldAs(5));
        List<String> alarmAttackChainList = get_attack_chain_list(victim,attacker,alarmRelatedLabel, (String) urcpAlarmJson.get("alarm_knowledge_id"));

        urcpAlarmJson.put("alarm_reason",alarmReason);
        urcpAlarmJson.put("attack_family",attackFamily);
        urcpAlarmJson.put("targets",targets);
        String alarmPrinciple = get_alarm_principle(InfoRow);
        urcpAlarmJson.put("alarm_principle",alarmPrinciple);
        String alarmHandleMethod = get_alarm_handle_method();
        urcpAlarmJson.put("alarm_handle_method",alarmHandleMethod);
        urcpAlarmJson.put("alarm_type","模型");
        urcpAlarmJson.put("victim",victim);
        urcpAlarmJson.put("attacker",attacker);
        urcpAlarmJson.put("alarm_related_label",alarmRelatedLabel);
        urcpAlarmJson.put("attack_route",attackRoute);
        urcpAlarmJson.put("alarm_session_list",alarmSessionList.toArray());
        urcpAlarmJson.put("attack_chain_list",alarmAttackChainList);
        String modelId = getModelId(InfoRow);
        urcpAlarmJson.put("model_id",modelId);
        List<String> pcapFileList = AlarmUtils.getPcapFileList(alarmSessionList,jedis);
        urcpAlarmJson.put("PcapFileList",pcapFileList);

        String esKey = InfoRow.getFieldAs(6);
        String taskId = esKey.split("_")[1];
        String batchId = esKey.split("_")[2];
        Map<String,Object> send_data = AlarmUtils.get_send_data(urcpAlarmJson,taskId,batchId);
        JSONObject alarm_json = new JSONObject();
        alarm_json.putAll(send_data);
        return alarm_json;
    }

    private static String getModelId(Row infoRow) {
        return "99030";
    }

    private static Row transToInfoRow(Row srcpRow){

        SRCPInfo srcpInfo = (SRCPInfo) srcpRow.getField(1);

        ConnectBasicInfo connectBasicInfo = srcpInfo.getConnectBasicInfo();

        List<String> analysisLabelList = connectBasicInfo.getAnalysisLabelList();

        Row InfoRow =new Row(8);
        InfoRow.setField(0,"未知远程控制协议");
        // 远程控制协议的类型
        InfoRow.setField(1,srcpInfo.getRCPType());
        // sIp
        InfoRow.setField(2,connectBasicInfo.getSIp());
        // dIp
        InfoRow.setField(3,connectBasicInfo.getDIp());
        // 关联新增标签
        InfoRow.setField(4,analysisLabelList);
        InfoRow.setField(5,connectBasicInfo.getSessionId());
        InfoRow.setField(6,connectBasicInfo.getEsKey());
        InfoRow.setField(7,srcpInfo.getNeededInfo());

        return InfoRow;
    }

    private static List<Map<String,Object>> get_alarm_reason(Row row){

        SRCPInfo srcpInfo = row.getFieldAs(1);
        ConnectBasicInfo connectBasicInfo = srcpInfo.getConnectBasicInfo();

        List<Map<String,Object>> alarmReasonList = new ArrayList<>();
        String desc = "该未知远控控制工具产生的流量数据中未对负载进行加密，且不存在协议格式";
        // 网络行为基线的检测结果
        Map<String,Object> alarm_reason_protocol = new HashMap<>();
        alarm_reason_protocol.put("key","对该会话的网路行为进行远程控制行为的检测");
        alarm_reason_protocol.put("actual_value","发现了该会话中存在未知远程控制协议的行为");
        alarmReasonList.add(alarm_reason_protocol);
        // 控制的检测结果
        if(connectBasicInfo.isControl()){
            Map<String,Object> alarm_reason_heartbeat = new HashMap<>();
            alarm_reason_heartbeat.put("key","对该会话的包序列中的包的发送方向及总包大小进行检测");
            alarm_reason_heartbeat.put("actual_value","发现了疑似控制行为的流量通信，包序列为规律的发包、回包，且回包负载字节均大于发包负载字节。"+ desc);
            alarmReasonList.add(alarm_reason_heartbeat);
        }

        return alarmReasonList;
    }

    private static List<Map<String,String>> get_victim(Set<String> IP_list){
        List<Map<String,String>> result = new ArrayList<>();
        for(String ip:IP_list){
            Map<String,String> victim = new HashMap<>();
            victim.put("ip",ip);
            result.add(victim);
        }
        return result;
    }

    private static List<Map<String,String>> get_attacker(Set<String> IP_list){
        List<Map<String,String>> result = new ArrayList<>();
        for(String ip:IP_list){
            Map<String,String> attacker = new HashMap<>();
            attacker.put("ip",ip);
            result.add(attacker);
        }
        return result;
    }

    private static List<Map<String,Object>> get_targets(Row infoRow){
        String dIp = infoRow.getFieldAs(3);
        List<Map<String,Object>> targets = new ArrayList<>();
        Map<String,Object> target_ip = new HashMap<>();
        target_ip.put("name",dIp);
        target_ip.put("type","ip");
        List<String> labels_ip = new ArrayList<>();
        target_ip.put("labels",labels_ip);
        targets.add(target_ip);
        return targets;
    }

    private static List<Map<String,String>> get_family(){
        return new ArrayList<>();
    }

    private static String get_alarm_principle(Row infoRow){
        String RCPType = infoRow.getFieldAs(1);
        return String.format("基于远程控制协议的行为检测模型中发现 %s 的远程控制协议，并且在针对控制行为的检测中也发现了可疑的行为",RCPType);
    }

    private static String get_alarm_handle_method(){
        return "首先断开受感染设备的网络连接，防止恶意行为扩散；然后使用可靠的安全软件进行全面扫描和清除；及时更新操作系统和应用程序到最新版本，修补安全漏洞；加强账户密码，避免使用弱密码；备份重要数据，以防万一；最后，提高对可疑邮件和链接的警觉性，避免点击来源不明的链接或下载不明文件。";
    }

    private static List<Map<String,String>> get_attack_route(){
        return new ArrayList<>();
    }
}
