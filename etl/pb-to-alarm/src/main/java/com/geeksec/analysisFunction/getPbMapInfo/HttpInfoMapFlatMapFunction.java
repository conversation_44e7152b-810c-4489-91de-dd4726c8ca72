package com.geeksec.analysisFunction.getPbMapInfo;

import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import com.geeksec.common.LabelUtils.EsUtils;
import java.nio.charset.StandardCharsets;
import java.util.*;
import javax.xml.bind.DatatypeConverter;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class HttpInfoMapFlatMapFunction extends RichFlatMapFunction<Map<String,Object>, Map<String,Object>> {
    private final static Logger logger = LoggerFactory.getLogger(HttpInfoMapFlatMapFunction.class);
    private static transient GenericObjectPool<RestHighLevelClient> EsPool = null;
    public static List<String> client_filed = Arrays.asList("accept", "accept-charset", "accept-encoding",
            "accept-language", "cuthorization", "cache-control", "connection", "cookie", "content-length",
            "content-md5", "content-type", "date", "expect", "from", "host", "if-match", "if-modified-since",
            "if-none-match", "if-range", "if-unmodified-since", "max-forwards", "pragma", "proxy-authorization",
            "range", "referer", "te", "upgrade", "user-agent", "via", "warning","payload","x-requested-with",
            "qypid","conn","comm","osver","qdver","updver","module_id","x-online-host","bid","pid","pver",
            "origin","sec-websocket-key","icy-metadata","x-last-hr","x-http-attempts","x-retry-count",
            "x-goog-update-updater","x-goog-update-appid","x-goog-update-interactivity","x-old-uid",
            "x-last-http-status-code","proxy-connection","upgrade-insecure-requests","user-agent","dnt",
            "q-ua2","soapaction","authorization","purpose","charset","token","random","sign");
    public static List<String> server_filed = Arrays.asList("accept-ranges","age", "allow", "cache-control",
            "connection", "content-encoding", "content-language", "content-length", "content-location",
            "content-md5", "content-disposition", "content-range", "content-type", "date", "etag", "expires",
            "last-modified", "location", "p3p", "pragma", "proxy-authenticate", "refresh", "retry-after", "server",
            "set-cookie", "trailer", "transfer-encoding", "vary", "via", "warning", "www-authenticate","payload",
            "x-requested-with","user-agent","accept","range","host","accept-language","qypid","accept-encoding",
            "cookie","referer","version","conn","comm","osver","qdver","updver","module_id","x-online-host",
            "upgrade","if-range","bid","pid","pver","origin","sec-webSocket-key","icy-metadata","authorization",
            "x-http-attempts","x-last-hr","x-goog-update-updater","x-goog-update-interactivity","x-old-uid",
            "x-goog-update-appid","x-retry-count","x-last-http-status-code","proxy-connection","upgrade-insecure-requests",
            "if-none-match","user-agent","dnt","q-ua2","soapaction", "expect","from","purpose","charset","token","random","sign");

    public static List<String> Neo_user_agents = Arrays.asList(
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_3) AppleWebKit/600.6.3 (KHTML, like Gecko) Version/8.0.6 Safari/600.6.3",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_9_5) AppleWebKit/600.7.12 (KHTML, like Gecko) Version/7.1.7 Safari/537.85.16",
            "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 6.1; WOW64; rv:38.0) Gecko/20100101 Firefox/38.0",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.81 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_4) AppleWebKit/600.7.11 (KHTML, like Gecko) Version/8.0.7 Safari/600.7.11",
            "Mozilla/5.0 (X11; Linux x86_64; rv:38.0) Gecko/20100101 Firefox/38.0",
            "Mozilla/5.0 (Windows NT 6.1; rv:38.0) Gecko/20100101 Firefox/38.0",
            "Mozilla/5.0 (X11; Ubuntu; Linux i686; rv:38.0) Gecko/20100101 Firefox/38.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.7; rv:38.0) Gecko/20100101 Firefox/38.0"
            );

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // ES 初始化
        EsPool = EsUtils.initEsPool();
        logger.info("生成EsPool成功! {}", EsPool.getNumIdle(),EsPool.hashCode());
    }

    @Override
    public void flatMap(Map<String, Object> pbMap, Collector<Map<String, Object>> collector) throws Exception {
        String Act = (String) pbMap.get("Act");
        if (!Act.equals("")) {
            Map<String, Object> resultMap = new HashMap<>();
            Row web_login_info = getWeb_login_info(pbMap);
            Row Neoregeo_info = get_Neoregeo_info(pbMap);
            Row suo5_info = get_suo5_info(pbMap);
            Row BeHinder_info= get_BeHinder_info(pbMap);
            Row antSword_info = get_antSword_info(pbMap);
            resultMap.put("Web_Login_Info",web_login_info);
            if (Neoregeo_info!=null){
                resultMap.put("Neoregeo_info",Neoregeo_info);
            }
            if(suo5_info!=null){
                resultMap.put("suo5_info",suo5_info);
            }
            if(BeHinder_info!=null){
                resultMap.put("BeHinder_info",BeHinder_info);
            }
            if(antSword_info!=null){
                resultMap.put("antSword_info",antSword_info);
            }
            collector.collect(resultMap);
        }
    }

    private Row getWeb_login_info(Map<String, Object> pbMap){
        Row resultRow = new Row(7);
        Collection<String> SessionId_list = new HashSet<>();
        SessionId_list.add((String) pbMap.get("SessionId"));
        List<String> sipDipList = Arrays.asList(pbMap.get("sIp").toString(),pbMap.get("dIp").toString());
        String Act = (String) pbMap.get("Act");
        resultRow.setField(0,"Web_Login_Info");
        resultRow.setField(1,sipDipList);
        resultRow.setField(2,Act);
        resultRow.setField(3,pbMap.get("StartTime"));
        resultRow.setField(4,SessionId_list);
        resultRow.setField(5,pbMap.get("es_key"));
        resultRow.setField(6, new NeededInfo(pbMap));

        return resultRow;
    }

    //Neoregeo代理隧道，提取client请求头和server请求头数据，server的也存在随机的问题
    //请求头随机，检查不包含特殊字符，然后前几个字母可读。
    //accept_encoding:gzip, deflate。
    //user_agents的值位于列表之中。
    private Row get_Neoregeo_info(Map<String, Object> pbMap){
        Row resultRow = new Row(8);
        Map<String,String> Client_info = (Map<String, String>) pbMap.getOrDefault("Client",new HashMap<>());
        Map<String,String> Server_info = (Map<String, String>) pbMap.getOrDefault("Server",new HashMap<>());
        Map<String,String> abnormal_head = get_abnormal_head(Client_info,Server_info);
        String accept_encoding = Client_info.getOrDefault("accept_encoding","");
        String user_agent = Client_info.getOrDefault("user_agent","");
        if (abnormal_head.size()>0 && Neo_user_agents.contains(user_agent) && accept_encoding.equals("gzip, deflate")){
            resultRow.setField(0,"Neoregeo_info");
            resultRow.setField(1,pbMap.get("sIp").toString());
            resultRow.setField(2,pbMap.get("dIp").toString());
            resultRow.setField(3,pbMap.get("StartTime"));
            resultRow.setField(4,abnormal_head);
            resultRow.setField(5,pbMap.get("SessionId"));
            resultRow.setField(6,pbMap.get("es_key"));
            resultRow.setField(7, new NeededInfo(pbMap));
            return resultRow;
        }else {
            return null;
        }
    }

    // suo5代理隧道，提取client请求头和server响应头的特征字段
    // 2、请求包和响应包都包含Transfer-Encoding: chunked
    // 3、请求包和响应包都不包含：Content-Length
    // 4、返回包中包含：X-Accel-Buffering: no
    private Row get_suo5_info(Map<String, Object> pbMap){
        Row resultRow = new Row(8);
        Map<String,String> Client_info = (Map<String, String>) pbMap.getOrDefault("Client",new HashMap<>());
        Map<String,String> Server_info = (Map<String, String>) pbMap.getOrDefault("Server",new HashMap<>());
        boolean is_chunked = false;
        boolean is_Content_Length = false;
        boolean is_Buffering = false;
        String client_Transfer_Encoding = Client_info.getOrDefault("Transfer-Encoding","");
        String server_Transfer_Encoding = Server_info.getOrDefault("Transfer-Encoding","");
        String client_Content_Length = Client_info.getOrDefault("Content-Length","");
        String server_Content_Length = Server_info.getOrDefault("Content-Length","");
        String X_Accel_Buffering = Server_info.getOrDefault("X-Accel-Buffering","");
        if (client_Transfer_Encoding.equals("chunked") && server_Transfer_Encoding.equals("chunked")){
            is_chunked=true;
        }
        if (client_Content_Length.equals("") && server_Content_Length.equals("")){
            is_Content_Length=true;
        }
        if (X_Accel_Buffering.equals("no")){
            is_Buffering=true;
        }
        if (is_chunked && is_Content_Length && is_Buffering){
            resultRow.setField(0,"suo5_info");
            resultRow.setField(1,pbMap.get("sIp").toString());
            resultRow.setField(2,pbMap.get("dIp").toString());
            resultRow.setField(3,pbMap.get("StartTime"));
            resultRow.setField(4,true);
            resultRow.setField(5,pbMap.get("SessionId"));
            resultRow.setField(6,pbMap.get("es_key"));
            resultRow.setField(7, new NeededInfo(pbMap));
            return resultRow;
        }else {
            return null;
        }
    }


    // 冰蝎黑客工具，
    private Row get_BeHinder_info(Map<String, Object> pbMap) throws Exception {
        Row resultRow = new Row(8);
        Map<String,String> Client_info = (Map<String, String>) pbMap.getOrDefault("Client",new HashMap<>());
//        Map<String,String> Server_info = (Map<String, String>) pbMap.getOrDefault("Server",new HashMap<>());
        Boolean BeHinder3 = get_BeHinder3_info(Client_info);
        Boolean BeHinder4 = get_BeHinder4_info(Client_info);

        if (BeHinder3){
            resultRow.setField(0,"BeHinder_info");
            resultRow.setField(1,pbMap.get("sIp").toString());
            resultRow.setField(2,pbMap.get("dIp").toString());
            resultRow.setField(3,pbMap.get("StartTime"));
            resultRow.setField(4,"冰蝎3");
            resultRow.setField(5,pbMap.get("SessionId"));
            resultRow.setField(6,pbMap.get("es_key"));
            resultRow.setField(7, new NeededInfo(pbMap));
            return resultRow;
        }else if (BeHinder4){
            resultRow.setField(0,"BeHinder_info");
            resultRow.setField(1,pbMap.get("sIp").toString());
            resultRow.setField(2,pbMap.get("dIp").toString());
            resultRow.setField(3,pbMap.get("StartTime"));
            resultRow.setField(4,"冰蝎4");
            resultRow.setField(5,pbMap.get("SessionId"));
            resultRow.setField(6,pbMap.get("es_key"));
            resultRow.setField(7, new NeededInfo(pbMap));
            return resultRow;
        }else {
            return null;
        }
    }

    public static Map<String,String> get_abnormal_head(Map<String,String> Client_info,Map<String,String> Server_info){
        Map<String,String> abnormal_head = new HashMap<>();
        for (String key:Client_info.keySet()){
            if(!client_filed.contains(key.toLowerCase()) && !key.contains("-") && !key.contains("_")){
                abnormal_head.put("Client-"+key,Client_info.get(key));
            }
        }
        for (String key:Server_info.keySet()){
            if(!server_filed.contains(key.toLowerCase()) && !key.contains("-") && !key.contains("_")){
                abnormal_head.put("Server-"+key,Server_info.get(key));
            }
        }
        return abnormal_head;
    }

    // 冰蝎3：服务端IP+URL对应的客户端IP去重后数量<=2，负载返回内容差别;
    // (1)默认使用的是 aes128 的算法，会导致密文长度恒是 16 的整数倍;（强特征）
    // (2)固定请求头（弱特征）:
    // （Accept-Encoding: gzip, deflate, br）
    // （Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2）
    // （Cache-Control: no-cache）
    // （Pragma: no-cache）
    // (3)Referer与当前URL的host一致且没有被访问过(强特征)
    // (4) Content-Length计算公式： (Content-Length*0.75) mod 16 = [0,1,2]
    private static Boolean get_BeHinder3_info(Map<String,String> Client_info) throws Exception {
        boolean Accept_Encoding = false;
        boolean Accept_Language = false;
        boolean Cache_Control = false;
        boolean Pragma = false;
        boolean Referer = false;
        boolean URL_info = false;
        List<String> BeHinder3_Language = Arrays.asList("zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2","zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7");//冰蝎使用的语言规则的可能性
        String Accept_Encoding_info = Client_info.getOrDefault("Accept-Encoding","");
        String Accept_Language_info = Client_info.getOrDefault("Accept-Language","");
        String Cache_Control_info = Client_info.getOrDefault("Cache-Control","");
        Integer Content_Length_info = 0;
        try{
            Content_Length_info = Integer.parseInt(Client_info.getOrDefault("Content-Length","5"));
        }catch (Exception e){
            logger.info("Content_Length值有问题");
        }
        String Pragma_info = Client_info.getOrDefault("Pragma","");
        String Referer_info = Client_info.getOrDefault("Referer",Client_info.getOrDefault("referer",""));
        String Host_info = Client_info.getOrDefault("Host",Client_info.getOrDefault("host",""));
        if (Accept_Encoding_info.equals("gzip, deflate, br")){
            Accept_Encoding=true;
        }
        if (BeHinder3_Language.contains(Accept_Language_info)){
            Accept_Language=true;
        }
        if (Cache_Control_info.equals("no-cache")){
            Cache_Control=true;
        }
        if (Pragma_info.equals("no-cache")){
            Pragma=true;
        }
        if (Referer_info.contains(Host_info)){
            Referer=true;
            String URI = "/";
            try{
                URI = Referer_info.split(Host_info)[1];
            }catch (Exception e){
                logger.info("Referer_info不够长");
            }
            Integer count = Arrays.asList(URI.split("/+")).size();
            if (count==2){
                URL_info = true;
            }else {
                if (get_URI_info(URI)){
                    URL_info = true;
                }
            }
        }
        boolean Content_Length=get_CL_info(Content_Length_info,Referer_info);
        return Accept_Encoding && Accept_Language && Cache_Control && Pragma && Content_Length && Referer && URL_info;
    }

    // 冰蝎4：(1)默认使用的是 aes128 的算法，会导致密文长度恒是 16 的整数倍;（强特征）
    // (2)固定的请求头(弱特征)：
    // （Accept: application/json, text/javascript, */*; q=0.01）
    // （Accept-Language: zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7）
    // （Content-Length: 8244（较大））(暂时弃用)
    // （Connection: Keep-Alive）
    // (3)Referer与当前URL的host一致且没有被访问过(强特征)
    // (4) Content-Length计算公式： (Content-Length*0.75) mod 16 = [0,1,2]
    private static Boolean get_BeHinder4_info(Map<String,String> Client_info) throws Exception {
        boolean Accept = false;
        boolean Accept_Language = false;
//        boolean Connection = false;
        boolean Referer = false;
        boolean URL_info = false;
        List<String> BeHinder4_Language = Arrays.asList("zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7");//冰蝎使用的语言规则的可能性
        String Accept_info = Client_info.getOrDefault("Accept","");
        String Accept_Language_info = Client_info.getOrDefault("Accept-Language","");
//        String Connection_info = Client_info.getOrDefault("Connection","");
        Integer Content_Length_info = 0;
        try{
            Content_Length_info = Integer.parseInt(Client_info.getOrDefault("Content-Length","5"));
        }catch (Exception e){
            logger.info("Content_Length值有问题");
        }
        String Referer_info = Client_info.getOrDefault("Referer",Client_info.getOrDefault("referer",""));
        String Host_info = Client_info.getOrDefault("Host",Client_info.getOrDefault("host",""));
        if (Accept_info.equals("application/json, text/javascript, */*; q=0.01")){
            Accept=true;
        }
        if (BeHinder4_Language.contains(Accept_Language_info)){
            Accept_Language=true;
        }
//        if (Connection_info.equals("Keep-Alive")){
//            Connection=true;
//        }
        if (Referer_info.contains(Host_info)){
            Referer=true;
            String URI = "/";
            try{
                URI = Referer_info.split(Host_info)[1];
            }catch (Exception e){
                logger.info("Referer_info不够长");
            }
            Integer count = Arrays.asList(URI.split("/+")).size();
            if (count==2){
                URL_info = true;
            }else {
                if (get_URI_info(URI)){
                    URL_info = true;
                }
            }
        }
        boolean Content_Length = get_CL_info(Content_Length_info,Referer_info);

        return Accept && Accept_Language && Content_Length && Referer && URL_info;//&& Connection
    }

    private static Boolean get_CL_info(Integer CL,String Referer){
        if (Referer.equals("")){
            return false;
        }
        //判断是否是aspx，是的话直接mod16
        if (Referer.contains("aspx")){
            if (CL%16==0){
                return true;
            }else {
                return false;
            }
        }else {//判断是否是aspx，不是的话*0.75再mod16
            if (CL%4==0){
                int CL_tmp = (int) (CL*0.75);
                List<Integer> result = Arrays.asList(0,1,2);
                return result.contains(CL_tmp % 16);
            }else {
                return false;
            }
        }
    }
    private static Boolean get_URI_info(String URI) throws Exception {
        // 验证该URL是否被访问过
        RestHighLevelClient EsClient = null;
        try {
            EsClient = EsUtils.getClient(EsPool);
            //判断该URI是否在半小时内出现过
            return EsUtils.match_URL_Query(URI, EsClient);
        }catch (Exception e){
            logger.error("获取EsClient失败，error--->{}",e);
            return false;
        }finally {
            if(EsClient != null){
                EsUtils.returnClient(EsClient,EsPool);
            }
        }
    }


    //蚁剑黑客工具：
    //1.请求包里面xxxx={22个字符}%3D%3D 这22个字符是aesKey的base64
    //2.aesKey会在返回包中出现，出现位置：6-13位开始。
    //提取clientHello中的body中的第一个等号后面的22位，就是base64编码的密钥，提取出来之后，再去serverHello的body中判断是否存在，若存在就是蚁剑
    private Row get_antSword_info(Map<String, Object> pbMap){
        try{
            Row resultRow = new Row(8);
            Map<String,String> Client_info = (Map<String, String>) pbMap.getOrDefault("Client",new HashMap<>());
            Map<String,String> Server_info = (Map<String, String>) pbMap.getOrDefault("Server",new HashMap<>());
            String client_body = Client_info.getOrDefault("Payload","");
            String server_body = Server_info.getOrDefault("Payload","");
            server_body = new String(DatatypeConverter.parseHexBinary(server_body),StandardCharsets.UTF_8);//获取server的payload的utf-8解码
            client_body = new String(DatatypeConverter.parseHexBinary(client_body),StandardCharsets.UTF_8);//获取client的payload的utf-8解码
            if (client_body.contains("=") && client_body.contains("%3D%3D")){
                String key_base64 = client_body.split("=")[1].substring(0,22);
                String aes_key = new String(Base64.getDecoder().decode(key_base64));
                if (server_body.contains(aes_key)){
                    resultRow.setField(0,"antSword_info");
                    resultRow.setField(1,pbMap.get("sIp").toString());
                    resultRow.setField(2,pbMap.get("dIp").toString());
                    resultRow.setField(3,pbMap.get("StartTime"));
                    resultRow.setField(4,aes_key);
                    resultRow.setField(5,pbMap.get("SessionId"));
                    resultRow.setField(6,pbMap.get("es_key"));
                    resultRow.setField(7, new NeededInfo(pbMap));
                    return resultRow;
                }else {
                    return null;
                }
            }else {
                return null;
            }
        }catch (Exception e){
            return null;
        }

    }

}
