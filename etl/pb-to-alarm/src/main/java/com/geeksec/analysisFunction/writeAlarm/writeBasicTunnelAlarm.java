package com.geeksec.analysisFunction.writeAlarm;

import static com.geeksec.analysisFunction.getRowInfo.SslInfoRowFlatMap.Alarm_Info_Map;
import static com.geeksec.common.LabelUtils.AlarmUtils.get_attack_chain_list;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.connectRelatedInfo.ConnectBasicInfo;
import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import com.geeksec.analysisFunction.analysisEntity.tunnel.BasicTunnel;
import com.geeksec.common.LabelUtils.AlarmUtils;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

/**
 * <AUTHOR>
 * @Date 2024/8/1
 */

public class writeBasicTunnelAlarm {
    private static final Logger logger = LoggerFactory.getLogger(writeBasicTunnelAlarm.class);
    public static JSONObject getBasicTunnelAlarmJson(Row tunnelRow, Jedis jedis) throws NoSuchAlgorithmException {

        Row InfoRow = transToInfoRow(tunnelRow);

        String Alarm_type = InfoRow.getFieldAs(0);
        if (!Alarm_Info_Map.keySet().contains(Alarm_type)) {
            return null;
        }
        Map<String, Object> TunnelAlarmJson = AlarmUtils.get_known_alarm_info(InfoRow);
        TunnelAlarmJson.putAll(AlarmUtils.get_Default_alarm_map());
        List<Map<String,Object>> alarm_reason = get_alarm_reason(tunnelRow);//reason
        String dIp = InfoRow.getFieldAs(3);
        String sIp = InfoRow.getFieldAs(2);
        List<Map<String,String>> attack_family = get_family(InfoRow);//attack_family
        List<Map<String,Object>> targets = get_targets(InfoRow);//targets

        NeededInfo neededInfo = InfoRow.getFieldAs(8);
        TunnelAlarmJson.put("vPort",neededInfo.getDPort());
        TunnelAlarmJson.put("aPort",neededInfo.getSPort());
        TunnelAlarmJson.put("sPort",neededInfo.getSPort());
        TunnelAlarmJson.put("dPort",neededInfo.getDPort());
        TunnelAlarmJson.put("tranProto",neededInfo.getTranProto());
        TunnelAlarmJson.put("appProto",neededInfo.getAppProto());
        TunnelAlarmJson.put("httpDomain",neededInfo.getHttpDomain());
        TunnelAlarmJson.put("sniDomain",neededInfo.getSniDomain());
        TunnelAlarmJson.put("sIp",neededInfo.getSIp());
        TunnelAlarmJson.put("dIp",neededInfo.getDIp());

        Set<String> attacker_set = new HashSet<>();
        attacker_set.add(dIp);

        Set<String> victim_set = new HashSet<>();
        victim_set.add(sIp);

        //victim
        List<Map<String,String>> victim = get_victim(victim_set);
        if (!victim_set.contains(neededInfo.getSIp())){
            Map<String,String> victim_map = new HashMap<>();
            victim_map.put("ip",neededInfo.getSIp());
            victim.add(victim_map);
        }
        //attacker
        List<Map<String,String>> attacker = get_attacker(attacker_set);
        if (!attacker_set.contains(neededInfo.getDIp())){
            Map<String,String> victim_map = new HashMap<>();
            victim_map.put("ip",neededInfo.getDIp());
            attacker.add(victim_map);
        }
        List<String> alarm_related_label = new ArrayList<>(InfoRow.getFieldAs(5));
        List<Map<String,String>> attack_route = get_attack_route(InfoRow);
        Collection<String> alarm_session_list = Collections.singletonList(InfoRow.getFieldAs(6));
        List<String> alarm_attack_chain_list = get_attack_chain_list(victim,attacker,alarm_related_label, (String) TunnelAlarmJson.get("alarm_knowledge_id"));

        TunnelAlarmJson.put("alarm_reason",alarm_reason);
        TunnelAlarmJson.put("attack_family",attack_family);
        TunnelAlarmJson.put("targets",targets);
        String alarm_principle = get_alarm_principle(InfoRow);
        TunnelAlarmJson.put("alarm_principle",alarm_principle);
        String alarm_handle_method = get_alarm_handle_method(InfoRow);
        TunnelAlarmJson.put("alarm_handle_method",alarm_handle_method);
        TunnelAlarmJson.put("alarm_type","模型");
        TunnelAlarmJson.put("victim",victim);
        TunnelAlarmJson.put("attacker",attacker);
        TunnelAlarmJson.put("alarm_related_label",alarm_related_label);
        TunnelAlarmJson.put("attack_route",attack_route);
        TunnelAlarmJson.put("alarm_session_list",alarm_session_list.toArray());
        TunnelAlarmJson.put("attack_chain_list",alarm_attack_chain_list);
        String modelId = getModelId(InfoRow);
        TunnelAlarmJson.put("model_id",modelId);
        List<String> pcapFileList = AlarmUtils.getPcapFileList(alarm_session_list,jedis);
        TunnelAlarmJson.put("PcapFileList",pcapFileList);

        String esKey = InfoRow.getFieldAs(7);
        String taskId = esKey.split("_")[1];
        String batchId = esKey.split("_")[2];
        Map<String,Object> send_data = AlarmUtils.get_send_data(TunnelAlarmJson,taskId,batchId);
        JSONObject alarm_json = new JSONObject();
        alarm_json.putAll(send_data);
        return alarm_json;
    }

    private static String getModelId(Row infoRow) {
        String alarm_type = infoRow.getFieldAs(1);
        switch (alarm_type){
            case "TCP":
                return "99033";
            case "HTTP":
                return "99034";
            case "NTP":
                return "99029";
            case "SSL":
                return "99037";
            case "ICMP":
                return "99036";
            default:
//                logger.error("告警类型不属于隐蔽隧道行为");
                return "99029";
        }
    }

    private static Row transToInfoRow(Row tunnelRow){

        BasicTunnel basicTunnel = (BasicTunnel) tunnelRow.getField(2);

        ConnectBasicInfo connectBasicInfo = basicTunnel.getConnectBasicInfo();

        Integer labelCount = tunnelRow.getFieldAs(3);

        List<String> analysisLabelList = connectBasicInfo.getAnalysisLabelList();
        Row InfoRow =new Row(9);
        InfoRow.setField(0,"隐蔽隧道");
        InfoRow.setField(1,basicTunnel.getTunnelType());
        InfoRow.setField(2,connectBasicInfo.getSIp());// sIp
        InfoRow.setField(3,connectBasicInfo.getDIp());// dIp
        InfoRow.setField(4,labelCount);// 标签出现次数
        InfoRow.setField(5,analysisLabelList); // 关联新增标签
        InfoRow.setField(6,connectBasicInfo.getSessionId());
        InfoRow.setField(7,connectBasicInfo.getEsKey());
        InfoRow.setField(8,basicTunnel.getNeededInfo());

        return InfoRow;
    }

    private static List<Map<String,Object>> get_alarm_reason(Row tunnelRow){

        BasicTunnel basicTunnel = (BasicTunnel) tunnelRow.getField(2);
        ConnectBasicInfo connectBasicInfo = basicTunnel.getConnectBasicInfo();
        String onewayCommun = basicTunnel.getConnectBasicInfo().getLabels().contains("33032") ? "，会话类型为：单向会话。" : "，会话类型为：非单向会话。";
        List<Map<String,Object>> alarm_reason_list = new ArrayList<>();
        String tunnelType = basicTunnel.getTunnelType();

        // 心跳的检测结果
        if(connectBasicInfo.isHeartbeat()){
            Map<String,Object> alarm_reason_heartbeat = new HashMap<>();
            alarm_reason_heartbeat.put("key","对该会话的包序列中的特定长度的包的发送频率及数量进行检测");
            alarm_reason_heartbeat.put("actual_value","发现了疑似心跳行为的流量通信");
            alarm_reason_list.add(alarm_reason_heartbeat);
        }
        // 控制的检测结果
        if(connectBasicInfo.isControl()){
            Map<String,Object> alarm_reason_heartbeat = new HashMap<>();
            alarm_reason_heartbeat.put("key","对该会话的包序列中的包的发送方向及总包大小进行检测");
            alarm_reason_heartbeat.put("actual_value","发现了疑似控制行为的流量通信");
            alarm_reason_list.add(alarm_reason_heartbeat);
        }
        // 激活的检测结果
        if(connectBasicInfo.isActivation()){
            Map<String,Object> alarm_reason_heartbeat = new HashMap<>();
            alarm_reason_heartbeat.put("key","对该会话的包序列中负载的特定模式字段进行统计检测");
            alarm_reason_heartbeat.put("actual_value","发现了疑似激活行为的流量通信");
            alarm_reason_list.add(alarm_reason_heartbeat);
        }
        // 协议元数据的检测原理,隐蔽信道工具
        Map<String,Object> alarm_reason_protocol = new HashMap<>();
        alarm_reason_protocol.put("key",String.format("发现伪造%s协议流量特征",tunnelType));
        Map<String,Object> alarm_fake_protocol = new HashMap<>();
        alarm_fake_protocol.put("key",String.format("发现%s隐蔽信道异常流量行为",tunnelType));
        String alarm_type = tunnelRow.getFieldAs(1);
        Map<String,Object> alarmReasonTool = new HashMap<>();
        switch (alarm_type) {
            case "TCP":
                alarmReasonTool.put("key", "对该会话的ProID字段根据特征模式(TST|26.0|26.10)$匹配");
                alarmReasonTool.put("actual_value", "发现了Nps_Conf工具产生的流量通信"+onewayCommun);
                alarm_reason_list.add(alarmReasonTool);

                alarm_reason_protocol.put("actual_value","发现流量具有心跳行为与控制行为的特征");
                alarm_reason_list.add(alarm_reason_protocol);

                alarm_fake_protocol.put("actual_value","通过分析网络流量中的数据包大小和时间间隔，存在时间间隔一致且包大小不超过50的心跳包；" +
                        "通过分析包序列特征中存在固定收包回包且回包大小一直大于发包的控制行为。");
                alarm_reason_list.add(alarm_fake_protocol);
                break;
            case "HTTP":
                alarmReasonTool.put("key", "对该会话的ProID字段根据特征模式匹配");
                alarmReasonTool.put("actual_value", "发现了Neo-reGeorg工具产生的流量通信"+onewayCommun);
                alarm_reason_list.add(alarmReasonTool);

                alarm_reason_protocol.put("actual_value","发现流量具有请求头与响应头字段内容字符串熵值大于阈值4.9的特征。");
                alarm_reason_list.add(alarm_reason_protocol);

                alarm_fake_protocol.put("actual_value","通过分析网络流量中的数据包大小和时间间隔，存在时间间隔一致且包大小不超过50的心跳包；" +
                        "通过分析包序列特征中存在固定收包回包且回包大小一直大于发包的控制行为。");
                alarm_reason_list.add(alarm_fake_protocol);
                break;
            case "NTP":
                alarmReasonTool.put("key", "对该会话的ProID字段根据特征模式(TST|26.0|26.10)$匹配");
                alarmReasonTool.put("actual_value", "发现了NTP-Tunnel工具产生的流量通信"+onewayCommun);
                alarm_reason_list.add(alarmReasonTool);

                alarm_reason_protocol.put("actual_value","发现流量具有从客户端到 NTP 服务器的总延迟为0且时间同步的精度为0，发送者发送响应的时间为0，从服务端到 NTP 服务器的总延迟为0且时间同步的精度为0，" +
                        "发送者发送消息时的时间为0，时间校准的参考时钟ID为0.0.0.0的特征。");
                alarm_reason_list.add(alarm_reason_protocol);

                alarm_fake_protocol.put("actual_value","通过分析网络流量中的数据包大小和时间间隔，存在时间间隔一致且包大小不超过50的心跳包；" +
                        "通过分析包序列特征中存在固定收包回包且回包大小一直大于发包的控制行为。");
                alarm_reason_list.add(alarm_fake_protocol);
                break;
            case "SSL":
                alarmReasonTool.put("key", "对该会话的ProID字段根据特征模式^\\\\x17$匹配");
                alarmReasonTool.put("actual_value", "发现了FRP工具产生的流量通信"+onewayCommun);

                alarm_reason_protocol.put("actual_value","发现流量具有客户端指纹信息为特定内容1142710092471348808，服务端指纹信息为特定内容2873693454854970215的特征。");
                alarm_reason_list.add(alarm_reason_protocol);
                alarm_reason_list.add(alarmReasonTool);

                alarm_fake_protocol.put("actual_value","通过分析网络流量中的数据包大小和时间间隔，存在时间间隔一致且包大小不超过50的心跳包；" +
                        "通过分析包序列特征中存在固定收包回包且回包大小一直大于发包的控制行为。");
                alarm_reason_list.add(alarm_fake_protocol);
                break;
            case "ICMP":
                alarmReasonTool.put("key", "对该会话的流量模式进行检测");
                alarmReasonTool.put("actual_value", "发现了Go-ICMP工具产生的流量通信"+onewayCommun);

                alarm_reason_protocol.put("actual_value","发现流量中总包负载大于20，平均传输包负载大于10，且从包序列中发现客户端发往服务端的最大负载大于100。");
                alarm_reason_list.add(alarm_reason_protocol);
                alarm_reason_list.add(alarmReasonTool);

                alarm_fake_protocol.put("actual_value","通过分析网络流量中的数据包大小和时间间隔，存在时间间隔一致且包大小不超过50的心跳包；" +
                        "通过分析包序列特征中存在固定收包回包且回包大小一直大于发包的控制行为。");
                alarm_reason_list.add(alarm_fake_protocol);
                break;
            default:
//                logger.error("告警类型不属于隐蔽隧道行为");
                alarmReasonTool.put("key", "对该会话的特定模式字段进行统计检测");
                alarmReasonTool.put("actual_value", "发现了未知工具产生的流量通信"+onewayCommun);
                alarm_reason_list.add(alarmReasonTool);
                break;
        }
        return alarm_reason_list;
    }

    private static List<Map<String,String>> get_victim(Set<String> IP_list){
        List<Map<String,String>> result = new ArrayList<>();
        for(String ip:IP_list){
            Map<String,String> victim = new HashMap<>();
            victim.put("ip",ip);
            result.add(victim);
        }
        return result;
    }

    private static List<Map<String,String>> get_attacker(Set<String> IP_list){
        List<Map<String,String>> result = new ArrayList<>();
        for(String ip:IP_list){
            Map<String,String> attacker = new HashMap<>();
            attacker.put("ip",ip);
            result.add(attacker);
        }
        return result;
    }

    private static List<Map<String,Object>> get_targets(Row infoRow){
        String dIp = infoRow.getFieldAs(3);
        List<Map<String,Object>> targets = new ArrayList<>();
        Map<String,Object> target_ip = new HashMap<>();
        target_ip.put("name",dIp);
        target_ip.put("type","ip");
        List<String> labels_ip = new ArrayList<>();
        target_ip.put("labels",labels_ip);
        targets.add(target_ip);
        return targets;
    }

    private static List<Map<String,String>> get_family(Row InfoRow){
        List<Map<String,String>> families = new ArrayList<>();
        return families;
    }

    private static String get_alarm_principle(Row InfoRow){
        String alarm_type = InfoRow.getFieldAs(1);
        switch (alarm_type){
            case "TCP":
                return "TCP 隐蔽隧道检测模型发现 TCP 协议中违规字段，并且在针对心跳，控制，激活的行为检测中也发现了可疑的行为";
            case "HTTP":
                return "HTTP 隐蔽隧道检测模型发现 HTTP 协议中违规字段，并且在针对心跳，控制，激活的行为检测中也发现了可疑的行为";
            case "NTP":
                return "NTP 隐蔽隧道检测模型发现 NTP 协议中违规字段，并且在针对心跳，控制，激活的行为检测中也发现了可疑的行为";
            case "SSL":
                return "SSL 隐蔽隧道检测模型发现 SSL 协议中违规字段，并且在针对心跳，控制，激活的行为检测中也发现了可疑的行为";
            case "ICMP":
                return "ICMP 隐蔽隧道检测模型发现 ICMP 协议中违规字段，并且在针对心跳，控制，激活的行为检测中也发现了可疑的行为";
            default:
//                logger.error("告警类型不属于隐蔽隧道行为");
                return "客户端访问服务端存在隐蔽隧道行为";
        }
    }

    private static String get_alarm_handle_method(Row InfoRow){
        String alarm_type = InfoRow.getFieldAs(1);
        switch (alarm_type){
            case "TCP":
                return "一旦发现TCP隐蔽隧道，应立即阻断所有可疑的非标准端口连接，并审查现有的安全策略，确保所有入站和出站流量都符合组织的业务需求。同时，更新防火墙规则，仅允许已知的合法服务和应用程序端口的通信。";
            case "HTTP":
                return "对于HTTP隐蔽隧道，应该立即审查和限制所有Web服务的访问权限，确保只有授权的用户和系统可以访问。此外，可以设置访问控制列表（ACLs）来限制对敏感目录和文件的访问。";
            case "NTP":
                return "一旦发现NTP隧道，应立即检查NTP服务器的配置，确保服务仅对可信的客户端开放，并且关闭或限制不必要的NTP服务。同时，更新系统时间同步策略，确保使用安全的同步源。";
            case "SSL":
                return "对于SSL隐蔽隧道，首先应该审查和更新SSL/TLS证书，确保所有证书都是有效且未被滥用。其次，可以配置SSL/TLS检查点，要求所有通过SSL/TLS加密的通信都必须通过验证，以识别和阻止可疑的加密流量。";
            case "ICMP":
                return "对于ICMP隐蔽隧道，首先应该审查和更新SSL/TLS证书，确保所有证书都是有效且未被滥用。其次，可以配置SSL/TLS检查点，要求所有通过SSL/TLS加密的通信都必须通过验证，以识别和阻止可疑的加密流量。";
            default:
//                logger.error("告警类型不属于隐蔽隧道行为");
                return "过滤掉来自该客户端IP的访问";
        }
    }

    private static List<Map<String,String>> get_attack_route(Row InfoRow){
        List<Map<String,String>> attack_route = new ArrayList<>();
        return attack_route;
    }
}
