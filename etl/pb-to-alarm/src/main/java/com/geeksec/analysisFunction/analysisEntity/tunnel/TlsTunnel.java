package com.geeksec.analysisFunction.analysisEntity.tunnel;

import com.geeksec.SpecProtocolEnum;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/7/25
 */

@Data
public class TlsTunnel {
    /** 本次TLS协议中的 sFinger 位值 */
    private String sFinger;

    /** 本次TLS协议中的 dFinger 位值 */
    private String dFinger;

    public static final String ETL_TAG = SpecProtocolEnum.SPOOF_TLS.getCode();

    public static final String PROBE_TAG = SpecProtocolEnum.SPOOF_TLS_TUNN.getCode();

    private boolean isTlsTunnel = false;

    public static final String TUNNEL_SERVER_FINGER = "1142710092471348808";
    public static final String TUNNEL_CLIENT_FINGER = "2873693454854970215";

    public TlsTunnel(Map<String, Object> pbMap) {
        this.sFinger = (String) pbMap.getOrDefault("sSSLFinger","");
        this.dFinger = (String) pbMap.getOrDefault("dSSLFinger","");
    }

    public void handleTlsTunnel(){
        if(TUNNEL_SERVER_FINGER.equals(this.getDFinger())
                && TUNNEL_CLIENT_FINGER.equals(this.getSFinger())){
            this.isTlsTunnel = true;
        }
    }
}
