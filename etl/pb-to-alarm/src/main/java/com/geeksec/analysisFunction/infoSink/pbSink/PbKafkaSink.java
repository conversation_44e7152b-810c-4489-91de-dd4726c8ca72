package com.geeksec.analysisFunction.infoSink.pbSink;

import static com.geeksec.task.LabelKafka2ES.ALERT_LOG_PROPERTIES;
import static com.geeksec.task.LabelKafka2ES.PARALLELISM_4;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.flinkTool.serializer.AlertLogKeySerializationSchema;
import com.geeksec.flinkTool.serializer.AlertLogValueSerializationSchema;
import com.geeksec.proto.AlertLog;
import com.twitter.chill.protobuf.ProtobufSerializer;
import java.util.*;
import java.util.Properties;
import org.apache.commons.lang3.SerializationException;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.streaming.connectors.kafka.KafkaDeserializationSchema;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2024/11/25
 */

public class PbKafkaSink {

    private static final Logger logger = LoggerFactory.getLogger(PbKafkaSink.class);

    public static final String BROKER_LIST = ALERT_LOG_PROPERTIES.getOrDefault("host","kafka").toString()
            +":"+ALERT_LOG_PROPERTIES.getOrDefault("port",9094).toString();
    public static final String ALERT_TOPIC = ALERT_LOG_PROPERTIES.getOrDefault("topic","alert_log").toString();
    public static final String DEVICE_IP = ALERT_LOG_PROPERTIES.getOrDefault("device.ip","***********").toString();


    public static void pbAlarmKafkaSink(DataStream<JSONObject> alarmJsonStream) {
        Properties properties = new Properties();
        properties.put("bootstrap.servers", BROKER_LIST);
        // 优化参数
        properties.put("batch.size", 409600);
        properties.put("linger.ms", 300);
        properties.put("buffer.memory", 256 * 1024 * 1024);
        properties.put("max.request.size", 10 * 1024 * 1024);
        // properties.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, ByteArraySerializer.class.getName());
        logger.info("当前alert log 外发 kafka地址:{}", BROKER_LIST);

        // 根据告警信息提取 ALERT_LOG
        SingleOutputStreamOperator<AlertLog.ALERT_LOG> protoAlertInfoStream = alarmJsonStream
                .flatMap(new alertInfoMapFunction()).name("告警转化为 ALERT_LOG").setParallelism(1);

        // 告警数据写入到Kafka
        KafkaSink<AlertLog.ALERT_LOG> kafkaJsonSink = KafkaSink.<AlertLog.ALERT_LOG>builder()
                .setBootstrapServers(properties.getProperty("bootstrap.servers"))
                .setRecordSerializer(KafkaRecordSerializationSchema.<AlertLog.ALERT_LOG>builder()
                        .setTopic(ALERT_TOPIC)
                        .setKeySerializationSchema(new AlertLogKeySerializationSchema())
                        .setValueSerializationSchema(new AlertLogValueSerializationSchema())
                        .build()
                )
                .build();

        protoAlertInfoStream.sinkTo(kafkaJsonSink).name("Alert Log Sink").setParallelism(PARALLELISM_4);
    }

    public static void main(String[] args) throws Exception {
        // 创建执行环境
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        Properties properties = new Properties();
        properties.put("bootstrap.servers", BROKER_LIST);
        // 优化参数
        properties.put(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG, "60000");
        properties.put("batch.size", 409600);
        properties.put("linger.ms", 300);
        properties.put("buffer.memory", 256 * 1024 * 1024);
        properties.put("max.request.size", 10 * 1024 * 1024);
        properties.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, 5000);
        env.getConfig().registerTypeWithKryoSerializer(AlertLog.ALERT_LOG.class, ProtobufSerializer.class);

        // 创建 Kafka 消费者
        FlinkKafkaConsumer<AlertLog.ALERT_LOG> kafkaConsumer = new FlinkKafkaConsumer<>(
                ALERT_TOPIC, // Kafka主题名称
                new KafkaDeserializationSchema<AlertLog.ALERT_LOG>() {
                    @Override
                    public boolean isEndOfStream(AlertLog.ALERT_LOG alertLog) {
                        return false;
                    }

                    @Override
                    public AlertLog.ALERT_LOG deserialize(ConsumerRecord<byte[], byte[]> consumerRecord) throws Exception {
                        if (consumerRecord.value() == null){
                            logger.info("值为空");
                        }else {
                            try {
                                return AlertLog.ALERT_LOG.parseFrom(consumerRecord.value());
                            } catch (Exception e) {
                                throw new SerializationException("Error when serializing JSON byte[] " + e);
                            }
                        }
                        return null;
                    }

                    @Override
                    public TypeInformation<AlertLog.ALERT_LOG> getProducedType() {
                        return TypeInformation.of(new TypeHint<AlertLog.ALERT_LOG>(){});
                    }
                },properties);

        // 创建数据流
        env.addSource(kafkaConsumer)
                .map(protobufMessage -> {
                    // 处理反序列化后的Protobuf消息
                    return protobufMessage;
                })
                .print();

        // 执行 Flink 作业
        env.execute("Kafka Protobuf Consumer");
    }
}
