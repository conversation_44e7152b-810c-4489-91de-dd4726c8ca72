package com.geeksec.analysisFunction.writeAlarm;

import static com.geeksec.analysisFunction.getRowInfo.SslInfoRowFlatMap.Alarm_Info_Map;
import static com.geeksec.common.LabelUtils.AlarmUtils.get_attack_chain_list;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.neededInfo.NeededInfo;
import com.geeksec.common.LabelUtils.AlarmUtils;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

/**
 * <AUTHOR>
 * @Date 2023/4/26
 */

public class writeEncryptTunnelAttackAlarm {
    private static final Logger logger = LoggerFactory.getLogger(writeFingerDetectAlarm.class);

    public static JSONObject get_encryptTunnelAttackAlarm_Json(Row InfoRow, Jedis jedis) throws NoSuchAlgorithmException {
        String Alarm_type = InfoRow.getFieldAs(0);
        if (!Alarm_Info_Map.keySet().contains(Alarm_type)) {
            return null;
        }

        Map<String, Object> alarmJson = AlarmUtils.get_known_alarm_info(InfoRow);
        alarmJson.putAll(AlarmUtils.get_Default_alarm_map());
        List<Map<String,Object>> alarm_reason = get_alarm_reason(InfoRow);//reason
        List<Map<String,String>> attack_family = new ArrayList<>();//attack_family
        List<Map<String,Object>> targets = get_targets(InfoRow);//targets

        NeededInfo neededInfo = new NeededInfo();
        if ("awvs".equals(InfoRow.getFieldAs(1))){
            neededInfo = InfoRow.getFieldAs(8);
        }else {
            neededInfo = InfoRow.getFieldAs(7);
        }
        alarmJson.put("vPort",neededInfo.getDPort());
        alarmJson.put("aPort",neededInfo.getSPort());
        alarmJson.put("sPort",neededInfo.getSPort());
        alarmJson.put("dPort",neededInfo.getDPort());
        alarmJson.put("tranProto",neededInfo.getTranProto());
        alarmJson.put("appProto",neededInfo.getAppProto());
        alarmJson.put("httpDomain",neededInfo.getHttpDomain());
        alarmJson.put("sniDomain",neededInfo.getSniDomain());
        alarmJson.put("sIp",neededInfo.getSIp());
        alarmJson.put("dIp",neededInfo.getDIp());

        Set<String> dIpSet = new HashSet<>();
        String dIp = InfoRow.getFieldAs(3);
        dIpSet.add(dIp);

        Set<String> sIpSet = new HashSet<>();
        String sIp = InfoRow.getFieldAs(2);
        sIpSet.add(sIp);

        //victim
        List<Map<String,String>> victim = get_victim(dIpSet);
        if (!dIpSet.contains(neededInfo.getDIp())){
            Map<String,String> victim_map = new HashMap<>();
            victim_map.put("ip",neededInfo.getDIp());
            victim.add(victim_map);
        }
        //attacker
        List<Map<String,String>> attacker = get_attacker(sIpSet);
        if (!sIpSet.contains(neededInfo.getSIp())){
            Map<String,String> victim_map = new HashMap<>();
            victim_map.put("ip",neededInfo.getSIp());
            attacker.add(victim_map);
        }
        List<String> alarm_related_label = get_related_label(InfoRow);//related_label
        List<Map<String,Object>> attack_route = get_attack_route(InfoRow);
        List<String> alarm_session_list = get_alarm_session_list(InfoRow);
        List<String> alarm_attack_chain_list = get_attack_chain_list(victim,attacker,alarm_related_label, (String) alarmJson.get("alarm_knowledge_id"));

        alarmJson.put("alarm_reason",alarm_reason);
        alarmJson.put("attack_family",attack_family);
        alarmJson.put("targets",targets);
        String alarm_principle = get_alarm_principle(InfoRow);
        alarmJson.put("alarm_principle",alarm_principle);
        String alarm_handle_method = get_alarm_handle_method(InfoRow);
        alarmJson.put("alarm_handle_method",alarm_handle_method);
        alarmJson.put("alarm_type","模型");
        alarmJson.put("victim",victim);
        alarmJson.put("attacker",attacker);
        alarmJson.put("alarm_related_label",alarm_related_label);
        alarmJson.put("attack_route",attack_route);
        alarmJson.put("alarm_session_list",alarm_session_list);
        alarmJson.put("attack_chain_list",alarm_attack_chain_list);
        String modelId = getModelId(InfoRow);
        alarmJson.put("model_id",modelId);
        List<String> pcapFileList = AlarmUtils.getPcapFileList(alarm_session_list,jedis);
        alarmJson.put("PcapFileList",pcapFileList);

        String TunnelType = InfoRow.getFieldAs(1);
        String esKey = "0_0";
        if ("awvs".equals(TunnelType)) {
            esKey = InfoRow.getFieldAs(7);
        }else {
            esKey = InfoRow.getFieldAs(6);
        }
        String taskId = esKey.split("_")[1];
        String batchId = esKey.split("_")[2];
        Map<String,Object> send_data = AlarmUtils.get_send_data(alarmJson,taskId,batchId);
        JSONObject alarm_json = new JSONObject();
        alarm_json.putAll(send_data);
//        JSONObject alarm_data = new JSONObject();
//        List<Map<String,Object>> bulk_list = new ArrayList<>();
//        bulk_list.add(alarm_json);
//        alarm_data.put("type","ALARM_INSERT_ES");
//        alarm_data.put("Bulk",bulk_list);
        return alarm_json;
    }

    private static String getModelId(Row infoRow) {
        String type = infoRow.getFieldAs(1);
        switch (type){
            case "RDP爆破行为":
                return "99021";
            case "Oracle爆破行为":
                return "99022";
            case "MYSQL爆破行为":
                return "99023";
            case "SMB爆破行为":
                return "99024";
            case "xRay扫描行为":
                return "99025";
            case "AppScan工具":
            case "awvs":
            default:
                return "99016";
        }
    }

    private static List<Map<String,Object>> get_alarm_reason(Row InfoRow){
        List<Map<String,Object>> alarm_reason_list = new ArrayList<>();
        Map<String,Object> alarm_reason_fingerInfo = new HashMap<>();
        Map<String,Object> alarm_reason_fingerInfo2 = new HashMap<>();
        String TunnelType = InfoRow.getFieldAs(1);
        switch (TunnelType){
            case "awvs":
                alarm_reason_fingerInfo.put("key","使用了"+InfoRow.getField(1)+"加密通道攻击行为");
                alarm_reason_fingerInfo.put("actual_value","检测出该加密通道攻击行为特有指纹："+InfoRow.getField(4));//指纹
                alarm_reason_list.add(alarm_reason_fingerInfo);
                break;
            case "RDP爆破行为":
                Map<String,Integer> RDP_label_count = InfoRow.getFieldAs(4);
                Integer RDP_fail_count = RDP_label_count.get("27064");
                Integer RDP_return_count = RDP_label_count.get("27065");
                alarm_reason_fingerInfo.put("key","存在"+InfoRow.getField(1));
                alarm_reason_fingerInfo.put("actual_value",String.format("检测出在5s内，客户端登录RDP服务端失败次数：%d,登录返回次数：%d",RDP_fail_count,RDP_return_count));
                alarm_reason_list.add(alarm_reason_fingerInfo);
                break;
            case "Oracle爆破行为":
                Map<String,Integer> Oracle_label_count = InfoRow.getFieldAs(4);
                Integer Oracle_fail_count = Oracle_label_count.get("27068");
                Integer Oracle_request_count = Oracle_label_count.get("27069");
                alarm_reason_fingerInfo.put("key","存在"+InfoRow.getField(1));
                alarm_reason_fingerInfo.put("actual_value",String.format("检测出在5s内，客户端登录Oracle服务端请求次数：%d,登陆失败次数：%d",Oracle_request_count,Oracle_fail_count));
                alarm_reason_list.add(alarm_reason_fingerInfo);
                break;
            case "MYSQL爆破行为":
                Map<String,Integer> MYSQL_label_count = InfoRow.getFieldAs(4);
                Integer MYSQL_fail_count = MYSQL_label_count.get("27071");
                alarm_reason_fingerInfo.put("key","存在"+InfoRow.getField(1));
                alarm_reason_fingerInfo.put("actual_value",String.format("检测出在5s内，客户端登录MYSQL服务端失败次数：%d",MYSQL_fail_count));
                alarm_reason_list.add(alarm_reason_fingerInfo);
                break;
            case "SMB爆破行为":
                Map<String,Integer> SMB_label_count = InfoRow.getFieldAs(4);
                Integer SMB_fail_count = SMB_label_count.get("27066");
                Integer SMB_request_count = SMB_label_count.get("27067");
                alarm_reason_fingerInfo.put("key","存在"+InfoRow.getField(1));
                alarm_reason_fingerInfo.put("actual_value",String.format("检测出在5s内，客户端登录SMB服务端失败次数：%d,登录请求次数：%d",SMB_fail_count,SMB_request_count));
                alarm_reason_list.add(alarm_reason_fingerInfo);
                break;
            case "AppScan工具":
                Collection<String> fingerSet = InfoRow.getFieldAs(4);
                alarm_reason_fingerInfo.put("key","存在"+InfoRow.getField(1));
                alarm_reason_fingerInfo.put("actual_value","检测出在一分钟内，客户端使用了"+fingerSet.size()+"个指纹，使用的指纹如下："+fingerSet);
                alarm_reason_list.add(alarm_reason_fingerInfo);
                break;
            case "xRay扫描行为":
                String finger = InfoRow.getFieldAs(4);
                List<String> session_list = InfoRow.getFieldAs(5);
                alarm_reason_fingerInfo.put("key","存在"+InfoRow.getField(1));
                alarm_reason_fingerInfo.put("actual_value","检测出在一分钟内，客户端频繁进行了"+session_list.size()+"次ClientHello发送");
                alarm_reason_fingerInfo2.put("key","检测出xray使用的专有指纹:");
                alarm_reason_fingerInfo2.put("actual_value",finger);
                alarm_reason_list.add(alarm_reason_fingerInfo);
                alarm_reason_list.add(alarm_reason_fingerInfo2);
                break;
            default:
                logger.error("知识库中无当前加密通道攻击行为告警类型");
                alarm_reason_fingerInfo.put("key","使用了:");
                alarm_reason_fingerInfo.put("actual_value","加密通道攻击工具");
                alarm_reason_list.add(alarm_reason_fingerInfo);
                break;
        }
        return alarm_reason_list;
    }

    private static List<Map<String,String>> get_victim(Set<String> InfoRow){
        List<Map<String,String>> result = new ArrayList<>();
        for(String ip:InfoRow){
            Map<String,String> victim = new HashMap<>();
            victim.put("ip",ip);
            result.add(victim);
        }
        return result;
    }

    private static List<Map<String,String>> get_attacker(Set<String> InfoRow){
        List<Map<String,String>> result = new ArrayList<>();
        for(String ip:InfoRow){
            Map<String,String> victim = new HashMap<>();
            victim.put("ip",ip);
            result.add(victim);
        }
        return result;
    }

    private static List<Map<String,Object>> get_targets(Row InfoRow){
        List<Map<String,Object>> targets = new ArrayList<>();
        Map<String,Object> target_tmp = new HashMap<>();
        target_tmp.put("name",InfoRow.getField(2));
        target_tmp.put("type","ip");
        List<String> labels_ip = new ArrayList<>();
        target_tmp.put("labels",labels_ip);
        targets.add(target_tmp);
        return targets;
    }

    private static String get_alarm_principle(Row InfoRow){
        String TunnelType = InfoRow.getFieldAs(1);
        switch (TunnelType){
            case "awvs":
                return "客户端使用awvs加密通道攻击行为对服务器进行漏洞扫描，存在被利用漏洞攻击的风险";
            case "RDP爆破行为":
                return "客户端存在RDP爆破行为，短时间内出现大量登录爆破请求，RDP服务端有密钥泄露风险";
            case "Oracle爆破行为":
                return "客户端存在Oracle爆破行为，短时间内出现大量登录爆破请求，Oracle服务端有密钥泄露风险";
            case "MYSQL爆破行为":
                return "客户端存在MYSQL爆破行为，短时间内出现大量登录爆破请求，MYSQL服务端有密钥泄露风险";
            case "SMB爆破行为":
                return "客户端存在SMB爆破行为，短时间内出现大量登录爆破请求，SMB服务端有密钥泄露风险";
            case "AppScan工具":
                return "客户端使用AppScan工具，短时间内更换了大量的指纹信息，服务端存在被漏洞扫描的风险";
            case "xRay扫描行为":
                return "客户端使用xRay漏洞扫描工具，短时间内进行了大量的ClientHello数据包发送，进行了过多的SSL的会话服务端存在被漏洞扫描的风险";
            default:
                logger.error("知识库中无当前加密通道攻击行为告警类型");
                return "客户端访问服务端存在加密通道攻击行为";
        }
    }

    private static String get_alarm_handle_method(Row InfoRow){
        String TunnelType = InfoRow.getFieldAs(1);
        switch (TunnelType){
            case "awvs":
                return "检查服务器是否被攻击，排查是否存在awvs加密通道攻击工具的运行，依据指纹对相关客户端IP，端口过滤";
            case "RDP爆破行为":
                return "检查RDP服务端密钥是否泄露，根据告警信息对相关IP、端口进行过滤，限制登录访问请求次数";
            case "Oracle爆破行为":
                return "检查Oracle服务端密钥是否泄露，根据告警信息对相关IP、端口进行过滤，限制登录访问请求次数";
            case "MYSQL爆破行为":
                return "检查MYSQL服务端密钥是否泄露，根据告警信息对相关IP、端口进行过滤，限制登录访问请求次数";
            case "SMB爆破行为":
                return "检查SMB服务端密钥是否泄露，根据告警信息对相关IP、端口进行过滤，限制登录访问请求次数";
            case "AppScan工具":
            case "xRay扫描行为":
                return "检查服务端是否存在被利用漏洞进行攻击的情况，根据告警信息对相关IP、端口进行过滤，也可根据指纹情况进行过滤";
            default:
                logger.error("知识库中无当前加密通道攻击行为告警类型");
                return "根据告警信息对相关IP、端口进行过滤，限制登录访问请求次数";
        }
    }

    private static List<Map<String,Object>> get_attack_route(Row InfoRow){
        String TunnelType = InfoRow.getFieldAs(1);
        switch (TunnelType){
            case "RDP爆破行为":
            case "Oracle爆破行为":
            case "MYSQL爆破行为":
            case "SMB爆破行为":
                return new ArrayList<>();
            case "AppScan工具":
                List<Map<String,Object>> AppScan_attack_route = new ArrayList<>();
                Collection<String> finger_set = InfoRow.getFieldAs(4);
                for (String finger:finger_set){
                    Map<String,Object> attack_route1 = new HashMap<>();
                    List<String> labels = new ArrayList<>();
                    attack_route1.put("type","finger");
                    attack_route1.put("name",finger);
                    attack_route1.put("label",labels);
                    AppScan_attack_route.add(attack_route1);
                }
                return AppScan_attack_route;
            case "xRay扫描行为":
                List<Map<String,Object>> xRay_attack_route = new ArrayList<>();
                String fingerID = InfoRow.getFieldAs(4);
                Map<String,Object> attack_route1 = new HashMap<>();
                List<String> labels = new ArrayList<>();
                labels.add("14016");//x-ray是14016
                attack_route1.put("type","finger");
                attack_route1.put("name",fingerID);
                attack_route1.put("label",labels);
                xRay_attack_route.add(attack_route1);
                return xRay_attack_route;
            case "awvs":
                List<Map<String,Object>> awvs_attack_route = new ArrayList<>();
                String fingerID_awvs = InfoRow.getFieldAs(4);
                String finger_Label = InfoRow.getFieldAs(5);
                Map<String,Object> attack_route_awvs = new HashMap<>();
                List<String> labels_awvs = new ArrayList<>();
                labels_awvs.add(finger_Label);
                attack_route_awvs.put("type","finger");
                attack_route_awvs.put("name",fingerID_awvs);
                attack_route_awvs.put("label",labels_awvs);
                awvs_attack_route.add(attack_route_awvs);
                return awvs_attack_route;
            default:
                logger.error("知识库中无当前加密通道攻击行为告警类型");
                return new ArrayList<>();
        }

    }

    private static List<String> get_alarm_session_list(Row InfoRow){
        List<String> alarm_session_list = new ArrayList<>();
        alarm_session_list.add(InfoRow.getFieldAs(5));
        return alarm_session_list;
    }

    private static List<String> get_related_label(Row InfoRow){
        String TunnelType = InfoRow.getFieldAs(1);
        switch (TunnelType){
            case "RDP爆破行为":
            case "Oracle爆破行为":
            case "MYSQL爆破行为":
            case "SMB爆破行为":
                Map<String,Integer> label_count = InfoRow.getFieldAs(4);
                return new ArrayList<>(label_count.keySet());
            case "AppScan工具":
                return new ArrayList<>(Collections.singletonList("27062"));
            case "xRay扫描行为":
                return new ArrayList<>(Arrays.asList("27063","14016"));
            case "awvs":
                return new ArrayList<>(Arrays.asList("14015"));
            default:
                logger.error("知识库中无当前加密通道攻击行为告警类型");
                return new ArrayList<>();
        }
    }
}
