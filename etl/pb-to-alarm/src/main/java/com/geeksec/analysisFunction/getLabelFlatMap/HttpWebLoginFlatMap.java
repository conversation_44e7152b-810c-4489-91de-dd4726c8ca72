package com.geeksec.analysisFunction.getLabelFlatMap;

import com.geeksec.SpecProtocolEnum;
import java.util.*;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2022/12/6
 */

public class HttpWebLoginFlatMap extends RichFlatMapFunction<Row, Row> {
    private static final Logger logger = LoggerFactory.getLogger(HttpWebLoginFlatMap.class);
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void flatMap(Row httpWebLoginRow, Collector<Row> collector) throws Exception {
        int LoginCount = (int) httpWebLoginRow.getField(1);
        int PostCount = (int) httpWebLoginRow.getField(2);
        int GetCount = (int) httpWebLoginRow.getField(3);
        int Timediff = (int) httpWebLoginRow.getField(4);
        if (Timediff==0){
            Timediff = 1;
        }
        String SessionID = (String) httpWebLoginRow.getField(5);
        if (LoginCount/Timediff>=100 || PostCount/Timediff>=100 || GetCount/Timediff>=100){
            Row SessionLabelRow = new Row(4);
            SessionLabelRow.setField(0,"会话打标");
            SessionLabelRow.setField(1,SessionID);
            SessionLabelRow.setField(2, Collections.singleton(SpecProtocolEnum.WEB_LOG_BRUTE.getCode()));
            SessionLabelRow.setField(3,httpWebLoginRow.getFieldAs(7));
//                    AddTagToSession(SessionID,"21001");//web登录爆破
            collector.collect(SessionLabelRow);
            logger.info("Http_Web_Login ES插入{}",SessionID);

            Row alarm_row =new Row(8);
            alarm_row.setField(0,"扫描行为");
            alarm_row.setField(1,"web登录爆破");
            String sIp = httpWebLoginRow.getFieldAs(6);
            String dIp = httpWebLoginRow.getFieldAs(7);
            alarm_row.setField(2,sIp);
            alarm_row.setField(3,dIp);
            int max_count = Math.max(LoginCount/Timediff,Math.max(PostCount/Timediff,GetCount/Timediff));
            Map<String,Integer> max_count_map = new HashMap<>();
            max_count_map.put("Get或Post请求次数",max_count);
            alarm_row.setField(4,max_count_map);
            alarm_row.setField(5,SpecProtocolEnum.WEB_LOG_BRUTE.getCode());
            Collection<String> SessionID_list = new HashSet<>();
            SessionID_list.add(SessionID);
            alarm_row.setField(6,SessionID_list);
            alarm_row.setField(7,httpWebLoginRow.getFieldAs(9));
            logger.info("web登录爆破告警{}",sIp);
            collector.collect(alarm_row);
        }
    }
}
