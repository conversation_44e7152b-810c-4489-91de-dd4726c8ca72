package com.geeksec.analysisFunction.infoSink;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.common.LabelUtils.HttpUtils;
import com.geeksec.common.loader.PropertiesLoader;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2022/12/29
 */

public class AlarmSink extends RichSinkFunction<JSONObject> {
    private final static Logger logger = LoggerFactory.getLogger(AlarmSink.class);
    private static PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");
    public static final String WS_HOST = propertiesLoader.getProperty("ES.WS_HOST");

    private List<JSONObject> bulk_list = new ArrayList<>(20);
    private List<String> bulk_attack_chain_list = new ArrayList<>();
    private int alarm_time = 0;
    private transient ScheduledFuture<?> scheduledFuture;
    private transient ScheduledExecutorService executorService;
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        executorService = Executors.newSingleThreadScheduledExecutor();
        final long interval = 30_000L; // 10 秒钟
        scheduledFuture = executorService.scheduleAtFixedRate(
                new Runnable() {
                    @Override
                    public void run() {
                        if (bulk_list.size()!=0){
                            int time_delay = 10;
                            write_es(bulk_list,time_delay);
                            bulk_list.clear();
                            bulk_attack_chain_list.clear();
                        }
                    }
                },
                interval,
                interval,
                TimeUnit.MILLISECONDS);
    }

    @Override
    public void invoke(JSONObject Json_Info, RichSinkFunction.Context context) {
        Map<String, Object> alarm_Json = (Map<String, Object>) Json_Info.get("Alarm");
        List<String> attack_chain_list = (List<String>) alarm_Json.get("attack_chain_list");
        String attack_chain_list_SHA1 = String.join("-", attack_chain_list);
        if (bulk_attack_chain_list.contains(attack_chain_list_SHA1)){
            logger.info("告警在算子内重复，不予告警");
        }else {
            bulk_attack_chain_list.add(attack_chain_list_SHA1);
            bulk_list.add(Json_Info);
        }
        if (bulk_list.size()==1){
            alarm_time = (int) alarm_Json.get("time");

        }
        int now_time = (int) alarm_Json.get("time");
        int time_delay = now_time-alarm_time;
        //当缓存的数据超过20条或者时间延迟过去180s，就批量写入会话标签
        if(bulk_list.size()>15 || time_delay>=180){
            write_es(bulk_list,time_delay);
            bulk_list.clear();
            bulk_attack_chain_list.clear();
        }
    }

    public static void write_es(List<JSONObject> bulk_list,int time_delay){
        JSONObject send_json = new JSONObject();
        send_json.put("type","ALARM_INSERT_ES");
        send_json.put("Bulk", bulk_list);
        JSONObject responseData = HttpUtils.sendPost(WS_HOST,send_json);
        logger.info("写入ES返回结果{}",responseData);
//            HttpUtils.sendPost_only(WS_HOST,send_json);
        logger.info("批量写入ES——告警——，写入数量——{}——，间隔时间——{}s——",bulk_list.size(),time_delay);
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (scheduledFuture != null) {
            scheduledFuture.cancel(false);
        }
        if (executorService != null) {
            executorService.shutdown();
        }
    }
}
