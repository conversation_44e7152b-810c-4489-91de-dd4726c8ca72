package com.geeksec.analysisFunction.analysisEntity.connectRelatedInfo;

import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/8/2
 * @Description 较为简单的会话信息，主要包括五元组信息和探针打的标签，以及包序列及负载信息
 */
@Data
public class ConnectSimpleInfo {

    /**
     * 源IP
     */
    private String sIp;

    /**
     * 目的IP
     */
    private String dIp;

    /**
     * 源端口
     */
    private String sPort;

    /**
     * 目的端口
     */
    private String dPort;

    /**
     * 协议信息
     */
    private String appName;

    /**
     * 会话开始时间
     */
    private Long startTime;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 探针打的标签
     * */
    private List<Integer> labels;

    /**
     * 包序列信息
     */
    private List<PacketInfo> packetInfoList;

    /**
     * 客户端前4个包负载信息
     */
    private List<String> client4PayloadList;

    /**
     * 服务端前4个包负载信息
     */
    private List<String> server4PayloadList;


    public ConnectSimpleInfo(Map<String,Object> pbMap) {
        this.sIp = (String) pbMap.get("sIp");
        this.dIp = (String) pbMap.get("dIp");
        this.sPort = (String) pbMap.get("sPort");
        this.dPort = (String) pbMap.get("dPort");
        this.appName = (String) pbMap.get("AppProto");
        this.startTime = (Long) pbMap.get("StartTime");
        this.sessionId = (String) pbMap.get("SessionId");
        this.labels = (List<Integer>) pbMap.get("Labels");
        this.packetInfoList = (List<PacketInfo>) pbMap.get("pktInfo");
        this.client4PayloadList = (List<String>) pbMap.get("Server4PayloadList");
        this.server4PayloadList = (List<String>) pbMap.get("Client4PayloadList");
    }
}
