// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: IOA_ALERT_INFO.proto
package com.geeksec.proto.message;

public final class IoaAlertInfo {
  private IoaAlertInfo() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface IOA_ALERT_INFOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:IOA_ALERT_INFO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *	流ID	
     * </pre>
     *
     * <code>required uint64 ioa_stream_id = 1;</code>
     */
    boolean hasIoaStreamId();
    /**
     * <pre>
     *	流ID	
     * </pre>
     *
     * <code>required uint64 ioa_stream_id = 1;</code>
     */
    long getIoaStreamId();

    /**
     * <pre>
     *	任务号	
     * </pre>
     *
     * <code>optional uint64 ioa_task = 2;</code>
     */
    boolean hasIoaTask();
    /**
     * <pre>
     *	任务号	
     * </pre>
     *
     * <code>optional uint64 ioa_task = 2;</code>
     */
    long getIoaTask();

    /**
     * <pre>
     *	规则号	
     * </pre>
     *
     * <code>optional uint64 ioa_rule = 3;</code>
     */
    boolean hasIoaRule();
    /**
     * <pre>
     *	规则号	
     * </pre>
     *
     * <code>optional uint64 ioa_rule = 3;</code>
     */
    long getIoaRule();

    /**
     * <pre>
     *	告警名称	
     * </pre>
     *
     * <code>required string ioa_name = 4;</code>
     */
    boolean hasIoaName();
    /**
     * <pre>
     *	告警名称	
     * </pre>
     *
     * <code>required string ioa_name = 4;</code>
     */
    java.lang.String getIoaName();
    /**
     * <pre>
     *	告警名称	
     * </pre>
     *
     * <code>required string ioa_name = 4;</code>
     */
    com.google.protobuf.ByteString
        getIoaNameBytes();

    /**
     * <pre>
     *	规则内容	SDX规则语法描述
     * </pre>
     *
     * <code>required string ioa_value = 5;</code>
     */
    boolean hasIoaValue();
    /**
     * <pre>
     *	规则内容	SDX规则语法描述
     * </pre>
     *
     * <code>required string ioa_value = 5;</code>
     */
    java.lang.String getIoaValue();
    /**
     * <pre>
     *	规则内容	SDX规则语法描述
     * </pre>
     *
     * <code>required string ioa_value = 5;</code>
     */
    com.google.protobuf.ByteString
        getIoaValueBytes();

    /**
     * <pre>
     *	引用文档	参考文档
     * </pre>
     *
     * <code>required string ioa_refer = 6;</code>
     */
    boolean hasIoaRefer();
    /**
     * <pre>
     *	引用文档	参考文档
     * </pre>
     *
     * <code>required string ioa_refer = 6;</code>
     */
    java.lang.String getIoaRefer();
    /**
     * <pre>
     *	引用文档	参考文档
     * </pre>
     *
     * <code>required string ioa_refer = 6;</code>
     */
    com.google.protobuf.ByteString
        getIoaReferBytes();

    /**
     * <pre>
     *	规则版本	
     * </pre>
     *
     * <code>required string ioa_version = 7;</code>
     */
    boolean hasIoaVersion();
    /**
     * <pre>
     *	规则版本	
     * </pre>
     *
     * <code>required string ioa_version = 7;</code>
     */
    java.lang.String getIoaVersion();
    /**
     * <pre>
     *	规则版本	
     * </pre>
     *
     * <code>required string ioa_version = 7;</code>
     */
    com.google.protobuf.ByteString
        getIoaVersionBytes();

    /**
     * <pre>
     *	关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioa_vul = 8;</code>
     */
    boolean hasIoaVul();
    /**
     * <pre>
     *	关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioa_vul = 8;</code>
     */
    java.lang.String getIoaVul();
    /**
     * <pre>
     *	关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioa_vul = 8;</code>
     */
    com.google.protobuf.ByteString
        getIoaVulBytes();

    /**
     * <pre>
     *	攻击方向	cts/stc/to_client/from_server/from_client/to_server
     * </pre>
     *
     * <code>required string ioa_direction = 9;</code>
     */
    boolean hasIoaDirection();
    /**
     * <pre>
     *	攻击方向	cts/stc/to_client/from_server/from_client/to_server
     * </pre>
     *
     * <code>required string ioa_direction = 9;</code>
     */
    java.lang.String getIoaDirection();
    /**
     * <pre>
     *	攻击方向	cts/stc/to_client/from_server/from_client/to_server
     * </pre>
     *
     * <code>required string ioa_direction = 9;</code>
     */
    com.google.protobuf.ByteString
        getIoaDirectionBytes();

    /**
     * <pre>
     *	攻击结果	企图/成功/失败/失陷
     * </pre>
     *
     * <code>required string ioa_attack_result = 10;</code>
     */
    boolean hasIoaAttackResult();
    /**
     * <pre>
     *	攻击结果	企图/成功/失败/失陷
     * </pre>
     *
     * <code>required string ioa_attack_result = 10;</code>
     */
    java.lang.String getIoaAttackResult();
    /**
     * <pre>
     *	攻击结果	企图/成功/失败/失陷
     * </pre>
     *
     * <code>required string ioa_attack_result = 10;</code>
     */
    com.google.protobuf.ByteString
        getIoaAttackResultBytes();

    /**
     * <pre>
     *	代码语言	
     * </pre>
     *
     * <code>optional string ioa_code_language = 11;</code>
     */
    boolean hasIoaCodeLanguage();
    /**
     * <pre>
     *	代码语言	
     * </pre>
     *
     * <code>optional string ioa_code_language = 11;</code>
     */
    java.lang.String getIoaCodeLanguage();
    /**
     * <pre>
     *	代码语言	
     * </pre>
     *
     * <code>optional string ioa_code_language = 11;</code>
     */
    com.google.protobuf.ByteString
        getIoaCodeLanguageBytes();

    /**
     * <pre>
     *	影响平台	受影响APP/系统/应用
     * </pre>
     *
     * <code>optional string ioa_affected_product = 12;</code>
     */
    boolean hasIoaAffectedProduct();
    /**
     * <pre>
     *	影响平台	受影响APP/系统/应用
     * </pre>
     *
     * <code>optional string ioa_affected_product = 12;</code>
     */
    java.lang.String getIoaAffectedProduct();
    /**
     * <pre>
     *	影响平台	受影响APP/系统/应用
     * </pre>
     *
     * <code>optional string ioa_affected_product = 12;</code>
     */
    com.google.protobuf.ByteString
        getIoaAffectedProductBytes();

    /**
     * <pre>
     *	恶意代码家族	
     * </pre>
     *
     * <code>optional string ioa_malicious_family = 13;</code>
     */
    boolean hasIoaMaliciousFamily();
    /**
     * <pre>
     *	恶意代码家族	
     * </pre>
     *
     * <code>optional string ioa_malicious_family = 13;</code>
     */
    java.lang.String getIoaMaliciousFamily();
    /**
     * <pre>
     *	恶意代码家族	
     * </pre>
     *
     * <code>optional string ioa_malicious_family = 13;</code>
     */
    com.google.protobuf.ByteString
        getIoaMaliciousFamilyBytes();

    /**
     * <pre>
     *	APT组织名称	
     * </pre>
     *
     * <code>optional string ioa_apt_campaign = 14;</code>
     */
    boolean hasIoaAptCampaign();
    /**
     * <pre>
     *	APT组织名称	
     * </pre>
     *
     * <code>optional string ioa_apt_campaign = 14;</code>
     */
    java.lang.String getIoaAptCampaign();
    /**
     * <pre>
     *	APT组织名称	
     * </pre>
     *
     * <code>optional string ioa_apt_campaign = 14;</code>
     */
    com.google.protobuf.ByteString
        getIoaAptCampaignBytes();

    /**
     * <pre>
     *	威胁详情描述
     * </pre>
     *
     * <code>required string ioa_detail_info = 15;</code>
     */
    boolean hasIoaDetailInfo();
    /**
     * <pre>
     *	威胁详情描述
     * </pre>
     *
     * <code>required string ioa_detail_info = 15;</code>
     */
    java.lang.String getIoaDetailInfo();
    /**
     * <pre>
     *	威胁详情描述
     * </pre>
     *
     * <code>required string ioa_detail_info = 15;</code>
     */
    com.google.protobuf.ByteString
        getIoaDetailInfoBytes();
  }
  /**
   * <pre>
   *攻击利用告警信息
   * </pre>
   *
   * Protobuf type {@code IOA_ALERT_INFO}
   */
  public  static final class IOA_ALERT_INFO extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:IOA_ALERT_INFO)
      IOA_ALERT_INFOOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use IOA_ALERT_INFO.newBuilder() to construct.
    private IOA_ALERT_INFO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private IOA_ALERT_INFO() {
      ioaStreamId_ = 0L;
      ioaTask_ = 0L;
      ioaRule_ = 0L;
      ioaName_ = "";
      ioaValue_ = "";
      ioaRefer_ = "";
      ioaVersion_ = "";
      ioaVul_ = "";
      ioaDirection_ = "";
      ioaAttackResult_ = "";
      ioaCodeLanguage_ = "";
      ioaAffectedProduct_ = "";
      ioaMaliciousFamily_ = "";
      ioaAptCampaign_ = "";
      ioaDetailInfo_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private IOA_ALERT_INFO(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              ioaStreamId_ = input.readUInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              ioaTask_ = input.readUInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              ioaRule_ = input.readUInt64();
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              ioaName_ = bs;
              break;
            }
            case 42: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000010;
              ioaValue_ = bs;
              break;
            }
            case 50: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000020;
              ioaRefer_ = bs;
              break;
            }
            case 58: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000040;
              ioaVersion_ = bs;
              break;
            }
            case 66: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000080;
              ioaVul_ = bs;
              break;
            }
            case 74: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000100;
              ioaDirection_ = bs;
              break;
            }
            case 82: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000200;
              ioaAttackResult_ = bs;
              break;
            }
            case 90: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000400;
              ioaCodeLanguage_ = bs;
              break;
            }
            case 98: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000800;
              ioaAffectedProduct_ = bs;
              break;
            }
            case 106: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00001000;
              ioaMaliciousFamily_ = bs;
              break;
            }
            case 114: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00002000;
              ioaAptCampaign_ = bs;
              break;
            }
            case 122: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00004000;
              ioaDetailInfo_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return IoaAlertInfo.internal_static_IOA_ALERT_INFO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return IoaAlertInfo.internal_static_IOA_ALERT_INFO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              IoaAlertInfo.IOA_ALERT_INFO.class, IoaAlertInfo.IOA_ALERT_INFO.Builder.class);
    }

    private int bitField0_;
    public static final int IOA_STREAM_ID_FIELD_NUMBER = 1;
    private long ioaStreamId_;
    /**
     * <pre>
     *	流ID	
     * </pre>
     *
     * <code>required uint64 ioa_stream_id = 1;</code>
     */
    public boolean hasIoaStreamId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <pre>
     *	流ID	
     * </pre>
     *
     * <code>required uint64 ioa_stream_id = 1;</code>
     */
    public long getIoaStreamId() {
      return ioaStreamId_;
    }

    public static final int IOA_TASK_FIELD_NUMBER = 2;
    private long ioaTask_;
    /**
     * <pre>
     *	任务号	
     * </pre>
     *
     * <code>optional uint64 ioa_task = 2;</code>
     */
    public boolean hasIoaTask() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <pre>
     *	任务号	
     * </pre>
     *
     * <code>optional uint64 ioa_task = 2;</code>
     */
    public long getIoaTask() {
      return ioaTask_;
    }

    public static final int IOA_RULE_FIELD_NUMBER = 3;
    private long ioaRule_;
    /**
     * <pre>
     *	规则号	
     * </pre>
     *
     * <code>optional uint64 ioa_rule = 3;</code>
     */
    public boolean hasIoaRule() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <pre>
     *	规则号	
     * </pre>
     *
     * <code>optional uint64 ioa_rule = 3;</code>
     */
    public long getIoaRule() {
      return ioaRule_;
    }

    public static final int IOA_NAME_FIELD_NUMBER = 4;
    private volatile java.lang.Object ioaName_;
    /**
     * <pre>
     *	告警名称	
     * </pre>
     *
     * <code>required string ioa_name = 4;</code>
     */
    public boolean hasIoaName() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <pre>
     *	告警名称	
     * </pre>
     *
     * <code>required string ioa_name = 4;</code>
     */
    public java.lang.String getIoaName() {
      java.lang.Object ref = ioaName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	告警名称	
     * </pre>
     *
     * <code>required string ioa_name = 4;</code>
     */
    public com.google.protobuf.ByteString
        getIoaNameBytes() {
      java.lang.Object ref = ioaName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_VALUE_FIELD_NUMBER = 5;
    private volatile java.lang.Object ioaValue_;
    /**
     * <pre>
     *	规则内容	SDX规则语法描述
     * </pre>
     *
     * <code>required string ioa_value = 5;</code>
     */
    public boolean hasIoaValue() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <pre>
     *	规则内容	SDX规则语法描述
     * </pre>
     *
     * <code>required string ioa_value = 5;</code>
     */
    public java.lang.String getIoaValue() {
      java.lang.Object ref = ioaValue_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaValue_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	规则内容	SDX规则语法描述
     * </pre>
     *
     * <code>required string ioa_value = 5;</code>
     */
    public com.google.protobuf.ByteString
        getIoaValueBytes() {
      java.lang.Object ref = ioaValue_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaValue_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_REFER_FIELD_NUMBER = 6;
    private volatile java.lang.Object ioaRefer_;
    /**
     * <pre>
     *	引用文档	参考文档
     * </pre>
     *
     * <code>required string ioa_refer = 6;</code>
     */
    public boolean hasIoaRefer() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <pre>
     *	引用文档	参考文档
     * </pre>
     *
     * <code>required string ioa_refer = 6;</code>
     */
    public java.lang.String getIoaRefer() {
      java.lang.Object ref = ioaRefer_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaRefer_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	引用文档	参考文档
     * </pre>
     *
     * <code>required string ioa_refer = 6;</code>
     */
    public com.google.protobuf.ByteString
        getIoaReferBytes() {
      java.lang.Object ref = ioaRefer_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaRefer_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_VERSION_FIELD_NUMBER = 7;
    private volatile java.lang.Object ioaVersion_;
    /**
     * <pre>
     *	规则版本	
     * </pre>
     *
     * <code>required string ioa_version = 7;</code>
     */
    public boolean hasIoaVersion() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <pre>
     *	规则版本	
     * </pre>
     *
     * <code>required string ioa_version = 7;</code>
     */
    public java.lang.String getIoaVersion() {
      java.lang.Object ref = ioaVersion_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaVersion_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	规则版本	
     * </pre>
     *
     * <code>required string ioa_version = 7;</code>
     */
    public com.google.protobuf.ByteString
        getIoaVersionBytes() {
      java.lang.Object ref = ioaVersion_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_VUL_FIELD_NUMBER = 8;
    private volatile java.lang.Object ioaVul_;
    /**
     * <pre>
     *	关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioa_vul = 8;</code>
     */
    public boolean hasIoaVul() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <pre>
     *	关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioa_vul = 8;</code>
     */
    public java.lang.String getIoaVul() {
      java.lang.Object ref = ioaVul_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaVul_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioa_vul = 8;</code>
     */
    public com.google.protobuf.ByteString
        getIoaVulBytes() {
      java.lang.Object ref = ioaVul_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaVul_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_DIRECTION_FIELD_NUMBER = 9;
    private volatile java.lang.Object ioaDirection_;
    /**
     * <pre>
     *	攻击方向	cts/stc/to_client/from_server/from_client/to_server
     * </pre>
     *
     * <code>required string ioa_direction = 9;</code>
     */
    public boolean hasIoaDirection() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <pre>
     *	攻击方向	cts/stc/to_client/from_server/from_client/to_server
     * </pre>
     *
     * <code>required string ioa_direction = 9;</code>
     */
    public java.lang.String getIoaDirection() {
      java.lang.Object ref = ioaDirection_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaDirection_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	攻击方向	cts/stc/to_client/from_server/from_client/to_server
     * </pre>
     *
     * <code>required string ioa_direction = 9;</code>
     */
    public com.google.protobuf.ByteString
        getIoaDirectionBytes() {
      java.lang.Object ref = ioaDirection_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaDirection_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_ATTACK_RESULT_FIELD_NUMBER = 10;
    private volatile java.lang.Object ioaAttackResult_;
    /**
     * <pre>
     *	攻击结果	企图/成功/失败/失陷
     * </pre>
     *
     * <code>required string ioa_attack_result = 10;</code>
     */
    public boolean hasIoaAttackResult() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <pre>
     *	攻击结果	企图/成功/失败/失陷
     * </pre>
     *
     * <code>required string ioa_attack_result = 10;</code>
     */
    public java.lang.String getIoaAttackResult() {
      java.lang.Object ref = ioaAttackResult_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaAttackResult_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	攻击结果	企图/成功/失败/失陷
     * </pre>
     *
     * <code>required string ioa_attack_result = 10;</code>
     */
    public com.google.protobuf.ByteString
        getIoaAttackResultBytes() {
      java.lang.Object ref = ioaAttackResult_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaAttackResult_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_CODE_LANGUAGE_FIELD_NUMBER = 11;
    private volatile java.lang.Object ioaCodeLanguage_;
    /**
     * <pre>
     *	代码语言	
     * </pre>
     *
     * <code>optional string ioa_code_language = 11;</code>
     */
    public boolean hasIoaCodeLanguage() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <pre>
     *	代码语言	
     * </pre>
     *
     * <code>optional string ioa_code_language = 11;</code>
     */
    public java.lang.String getIoaCodeLanguage() {
      java.lang.Object ref = ioaCodeLanguage_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaCodeLanguage_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	代码语言	
     * </pre>
     *
     * <code>optional string ioa_code_language = 11;</code>
     */
    public com.google.protobuf.ByteString
        getIoaCodeLanguageBytes() {
      java.lang.Object ref = ioaCodeLanguage_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaCodeLanguage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_AFFECTED_PRODUCT_FIELD_NUMBER = 12;
    private volatile java.lang.Object ioaAffectedProduct_;
    /**
     * <pre>
     *	影响平台	受影响APP/系统/应用
     * </pre>
     *
     * <code>optional string ioa_affected_product = 12;</code>
     */
    public boolean hasIoaAffectedProduct() {
      return ((bitField0_ & 0x00000800) == 0x00000800);
    }
    /**
     * <pre>
     *	影响平台	受影响APP/系统/应用
     * </pre>
     *
     * <code>optional string ioa_affected_product = 12;</code>
     */
    public java.lang.String getIoaAffectedProduct() {
      java.lang.Object ref = ioaAffectedProduct_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaAffectedProduct_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	影响平台	受影响APP/系统/应用
     * </pre>
     *
     * <code>optional string ioa_affected_product = 12;</code>
     */
    public com.google.protobuf.ByteString
        getIoaAffectedProductBytes() {
      java.lang.Object ref = ioaAffectedProduct_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaAffectedProduct_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_MALICIOUS_FAMILY_FIELD_NUMBER = 13;
    private volatile java.lang.Object ioaMaliciousFamily_;
    /**
     * <pre>
     *	恶意代码家族	
     * </pre>
     *
     * <code>optional string ioa_malicious_family = 13;</code>
     */
    public boolean hasIoaMaliciousFamily() {
      return ((bitField0_ & 0x00001000) == 0x00001000);
    }
    /**
     * <pre>
     *	恶意代码家族	
     * </pre>
     *
     * <code>optional string ioa_malicious_family = 13;</code>
     */
    public java.lang.String getIoaMaliciousFamily() {
      java.lang.Object ref = ioaMaliciousFamily_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaMaliciousFamily_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	恶意代码家族	
     * </pre>
     *
     * <code>optional string ioa_malicious_family = 13;</code>
     */
    public com.google.protobuf.ByteString
        getIoaMaliciousFamilyBytes() {
      java.lang.Object ref = ioaMaliciousFamily_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaMaliciousFamily_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_APT_CAMPAIGN_FIELD_NUMBER = 14;
    private volatile java.lang.Object ioaAptCampaign_;
    /**
     * <pre>
     *	APT组织名称	
     * </pre>
     *
     * <code>optional string ioa_apt_campaign = 14;</code>
     */
    public boolean hasIoaAptCampaign() {
      return ((bitField0_ & 0x00002000) == 0x00002000);
    }
    /**
     * <pre>
     *	APT组织名称	
     * </pre>
     *
     * <code>optional string ioa_apt_campaign = 14;</code>
     */
    public java.lang.String getIoaAptCampaign() {
      java.lang.Object ref = ioaAptCampaign_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaAptCampaign_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	APT组织名称	
     * </pre>
     *
     * <code>optional string ioa_apt_campaign = 14;</code>
     */
    public com.google.protobuf.ByteString
        getIoaAptCampaignBytes() {
      java.lang.Object ref = ioaAptCampaign_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaAptCampaign_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_DETAIL_INFO_FIELD_NUMBER = 15;
    private volatile java.lang.Object ioaDetailInfo_;
    /**
     * <pre>
     *	威胁详情描述
     * </pre>
     *
     * <code>required string ioa_detail_info = 15;</code>
     */
    public boolean hasIoaDetailInfo() {
      return ((bitField0_ & 0x00004000) == 0x00004000);
    }
    /**
     * <pre>
     *	威胁详情描述
     * </pre>
     *
     * <code>required string ioa_detail_info = 15;</code>
     */
    public java.lang.String getIoaDetailInfo() {
      java.lang.Object ref = ioaDetailInfo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaDetailInfo_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	威胁详情描述
     * </pre>
     *
     * <code>required string ioa_detail_info = 15;</code>
     */
    public com.google.protobuf.ByteString
        getIoaDetailInfoBytes() {
      java.lang.Object ref = ioaDetailInfo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaDetailInfo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasIoaStreamId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIoaName()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIoaValue()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIoaRefer()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIoaVersion()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIoaDirection()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIoaAttackResult()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIoaDetailInfo()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeUInt64(1, ioaStreamId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeUInt64(2, ioaTask_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeUInt64(3, ioaRule_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, ioaName_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, ioaValue_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, ioaRefer_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 7, ioaVersion_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 8, ioaVul_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, ioaDirection_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 10, ioaAttackResult_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 11, ioaCodeLanguage_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 12, ioaAffectedProduct_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 13, ioaMaliciousFamily_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 14, ioaAptCampaign_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 15, ioaDetailInfo_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, ioaStreamId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, ioaTask_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(3, ioaRule_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, ioaName_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, ioaValue_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, ioaRefer_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, ioaVersion_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, ioaVul_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, ioaDirection_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, ioaAttackResult_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, ioaCodeLanguage_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, ioaAffectedProduct_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, ioaMaliciousFamily_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(14, ioaAptCampaign_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(15, ioaDetailInfo_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof IoaAlertInfo.IOA_ALERT_INFO)) {
        return super.equals(obj);
      }
      IoaAlertInfo.IOA_ALERT_INFO other = (IoaAlertInfo.IOA_ALERT_INFO) obj;

      boolean result = true;
      result = result && (hasIoaStreamId() == other.hasIoaStreamId());
      if (hasIoaStreamId()) {
        result = result && (getIoaStreamId()
            == other.getIoaStreamId());
      }
      result = result && (hasIoaTask() == other.hasIoaTask());
      if (hasIoaTask()) {
        result = result && (getIoaTask()
            == other.getIoaTask());
      }
      result = result && (hasIoaRule() == other.hasIoaRule());
      if (hasIoaRule()) {
        result = result && (getIoaRule()
            == other.getIoaRule());
      }
      result = result && (hasIoaName() == other.hasIoaName());
      if (hasIoaName()) {
        result = result && getIoaName()
            .equals(other.getIoaName());
      }
      result = result && (hasIoaValue() == other.hasIoaValue());
      if (hasIoaValue()) {
        result = result && getIoaValue()
            .equals(other.getIoaValue());
      }
      result = result && (hasIoaRefer() == other.hasIoaRefer());
      if (hasIoaRefer()) {
        result = result && getIoaRefer()
            .equals(other.getIoaRefer());
      }
      result = result && (hasIoaVersion() == other.hasIoaVersion());
      if (hasIoaVersion()) {
        result = result && getIoaVersion()
            .equals(other.getIoaVersion());
      }
      result = result && (hasIoaVul() == other.hasIoaVul());
      if (hasIoaVul()) {
        result = result && getIoaVul()
            .equals(other.getIoaVul());
      }
      result = result && (hasIoaDirection() == other.hasIoaDirection());
      if (hasIoaDirection()) {
        result = result && getIoaDirection()
            .equals(other.getIoaDirection());
      }
      result = result && (hasIoaAttackResult() == other.hasIoaAttackResult());
      if (hasIoaAttackResult()) {
        result = result && getIoaAttackResult()
            .equals(other.getIoaAttackResult());
      }
      result = result && (hasIoaCodeLanguage() == other.hasIoaCodeLanguage());
      if (hasIoaCodeLanguage()) {
        result = result && getIoaCodeLanguage()
            .equals(other.getIoaCodeLanguage());
      }
      result = result && (hasIoaAffectedProduct() == other.hasIoaAffectedProduct());
      if (hasIoaAffectedProduct()) {
        result = result && getIoaAffectedProduct()
            .equals(other.getIoaAffectedProduct());
      }
      result = result && (hasIoaMaliciousFamily() == other.hasIoaMaliciousFamily());
      if (hasIoaMaliciousFamily()) {
        result = result && getIoaMaliciousFamily()
            .equals(other.getIoaMaliciousFamily());
      }
      result = result && (hasIoaAptCampaign() == other.hasIoaAptCampaign());
      if (hasIoaAptCampaign()) {
        result = result && getIoaAptCampaign()
            .equals(other.getIoaAptCampaign());
      }
      result = result && (hasIoaDetailInfo() == other.hasIoaDetailInfo());
      if (hasIoaDetailInfo()) {
        result = result && getIoaDetailInfo()
            .equals(other.getIoaDetailInfo());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasIoaStreamId()) {
        hash = (37 * hash) + IOA_STREAM_ID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getIoaStreamId());
      }
      if (hasIoaTask()) {
        hash = (37 * hash) + IOA_TASK_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getIoaTask());
      }
      if (hasIoaRule()) {
        hash = (37 * hash) + IOA_RULE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getIoaRule());
      }
      if (hasIoaName()) {
        hash = (37 * hash) + IOA_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getIoaName().hashCode();
      }
      if (hasIoaValue()) {
        hash = (37 * hash) + IOA_VALUE_FIELD_NUMBER;
        hash = (53 * hash) + getIoaValue().hashCode();
      }
      if (hasIoaRefer()) {
        hash = (37 * hash) + IOA_REFER_FIELD_NUMBER;
        hash = (53 * hash) + getIoaRefer().hashCode();
      }
      if (hasIoaVersion()) {
        hash = (37 * hash) + IOA_VERSION_FIELD_NUMBER;
        hash = (53 * hash) + getIoaVersion().hashCode();
      }
      if (hasIoaVul()) {
        hash = (37 * hash) + IOA_VUL_FIELD_NUMBER;
        hash = (53 * hash) + getIoaVul().hashCode();
      }
      if (hasIoaDirection()) {
        hash = (37 * hash) + IOA_DIRECTION_FIELD_NUMBER;
        hash = (53 * hash) + getIoaDirection().hashCode();
      }
      if (hasIoaAttackResult()) {
        hash = (37 * hash) + IOA_ATTACK_RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getIoaAttackResult().hashCode();
      }
      if (hasIoaCodeLanguage()) {
        hash = (37 * hash) + IOA_CODE_LANGUAGE_FIELD_NUMBER;
        hash = (53 * hash) + getIoaCodeLanguage().hashCode();
      }
      if (hasIoaAffectedProduct()) {
        hash = (37 * hash) + IOA_AFFECTED_PRODUCT_FIELD_NUMBER;
        hash = (53 * hash) + getIoaAffectedProduct().hashCode();
      }
      if (hasIoaMaliciousFamily()) {
        hash = (37 * hash) + IOA_MALICIOUS_FAMILY_FIELD_NUMBER;
        hash = (53 * hash) + getIoaMaliciousFamily().hashCode();
      }
      if (hasIoaAptCampaign()) {
        hash = (37 * hash) + IOA_APT_CAMPAIGN_FIELD_NUMBER;
        hash = (53 * hash) + getIoaAptCampaign().hashCode();
      }
      if (hasIoaDetailInfo()) {
        hash = (37 * hash) + IOA_DETAIL_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getIoaDetailInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static IoaAlertInfo.IOA_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IoaAlertInfo.IOA_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IoaAlertInfo.IOA_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IoaAlertInfo.IOA_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IoaAlertInfo.IOA_ALERT_INFO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IoaAlertInfo.IOA_ALERT_INFO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IoaAlertInfo.IOA_ALERT_INFO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static IoaAlertInfo.IOA_ALERT_INFO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static IoaAlertInfo.IOA_ALERT_INFO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static IoaAlertInfo.IOA_ALERT_INFO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static IoaAlertInfo.IOA_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static IoaAlertInfo.IOA_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(IoaAlertInfo.IOA_ALERT_INFO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *攻击利用告警信息
     * </pre>
     *
     * Protobuf type {@code IOA_ALERT_INFO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:IOA_ALERT_INFO)
        IoaAlertInfo.IOA_ALERT_INFOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return IoaAlertInfo.internal_static_IOA_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return IoaAlertInfo.internal_static_IOA_ALERT_INFO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                IoaAlertInfo.IOA_ALERT_INFO.class, IoaAlertInfo.IOA_ALERT_INFO.Builder.class);
      }

      // Construct using IoaAlertInfo.IOA_ALERT_INFO.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        ioaStreamId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        ioaTask_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        ioaRule_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        ioaName_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        ioaValue_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        ioaRefer_ = "";
        bitField0_ = (bitField0_ & ~0x00000020);
        ioaVersion_ = "";
        bitField0_ = (bitField0_ & ~0x00000040);
        ioaVul_ = "";
        bitField0_ = (bitField0_ & ~0x00000080);
        ioaDirection_ = "";
        bitField0_ = (bitField0_ & ~0x00000100);
        ioaAttackResult_ = "";
        bitField0_ = (bitField0_ & ~0x00000200);
        ioaCodeLanguage_ = "";
        bitField0_ = (bitField0_ & ~0x00000400);
        ioaAffectedProduct_ = "";
        bitField0_ = (bitField0_ & ~0x00000800);
        ioaMaliciousFamily_ = "";
        bitField0_ = (bitField0_ & ~0x00001000);
        ioaAptCampaign_ = "";
        bitField0_ = (bitField0_ & ~0x00002000);
        ioaDetailInfo_ = "";
        bitField0_ = (bitField0_ & ~0x00004000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return IoaAlertInfo.internal_static_IOA_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      public IoaAlertInfo.IOA_ALERT_INFO getDefaultInstanceForType() {
        return IoaAlertInfo.IOA_ALERT_INFO.getDefaultInstance();
      }

      @java.lang.Override
      public IoaAlertInfo.IOA_ALERT_INFO build() {
        IoaAlertInfo.IOA_ALERT_INFO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public IoaAlertInfo.IOA_ALERT_INFO buildPartial() {
        IoaAlertInfo.IOA_ALERT_INFO result = new IoaAlertInfo.IOA_ALERT_INFO(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.ioaStreamId_ = ioaStreamId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.ioaTask_ = ioaTask_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.ioaRule_ = ioaRule_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.ioaName_ = ioaName_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.ioaValue_ = ioaValue_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.ioaRefer_ = ioaRefer_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.ioaVersion_ = ioaVersion_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.ioaVul_ = ioaVul_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.ioaDirection_ = ioaDirection_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000200;
        }
        result.ioaAttackResult_ = ioaAttackResult_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000400;
        }
        result.ioaCodeLanguage_ = ioaCodeLanguage_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000800;
        }
        result.ioaAffectedProduct_ = ioaAffectedProduct_;
        if (((from_bitField0_ & 0x00001000) == 0x00001000)) {
          to_bitField0_ |= 0x00001000;
        }
        result.ioaMaliciousFamily_ = ioaMaliciousFamily_;
        if (((from_bitField0_ & 0x00002000) == 0x00002000)) {
          to_bitField0_ |= 0x00002000;
        }
        result.ioaAptCampaign_ = ioaAptCampaign_;
        if (((from_bitField0_ & 0x00004000) == 0x00004000)) {
          to_bitField0_ |= 0x00004000;
        }
        result.ioaDetailInfo_ = ioaDetailInfo_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof IoaAlertInfo.IOA_ALERT_INFO) {
          return mergeFrom((IoaAlertInfo.IOA_ALERT_INFO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(IoaAlertInfo.IOA_ALERT_INFO other) {
        if (other == IoaAlertInfo.IOA_ALERT_INFO.getDefaultInstance()) return this;
        if (other.hasIoaStreamId()) {
          setIoaStreamId(other.getIoaStreamId());
        }
        if (other.hasIoaTask()) {
          setIoaTask(other.getIoaTask());
        }
        if (other.hasIoaRule()) {
          setIoaRule(other.getIoaRule());
        }
        if (other.hasIoaName()) {
          bitField0_ |= 0x00000008;
          ioaName_ = other.ioaName_;
          onChanged();
        }
        if (other.hasIoaValue()) {
          bitField0_ |= 0x00000010;
          ioaValue_ = other.ioaValue_;
          onChanged();
        }
        if (other.hasIoaRefer()) {
          bitField0_ |= 0x00000020;
          ioaRefer_ = other.ioaRefer_;
          onChanged();
        }
        if (other.hasIoaVersion()) {
          bitField0_ |= 0x00000040;
          ioaVersion_ = other.ioaVersion_;
          onChanged();
        }
        if (other.hasIoaVul()) {
          bitField0_ |= 0x00000080;
          ioaVul_ = other.ioaVul_;
          onChanged();
        }
        if (other.hasIoaDirection()) {
          bitField0_ |= 0x00000100;
          ioaDirection_ = other.ioaDirection_;
          onChanged();
        }
        if (other.hasIoaAttackResult()) {
          bitField0_ |= 0x00000200;
          ioaAttackResult_ = other.ioaAttackResult_;
          onChanged();
        }
        if (other.hasIoaCodeLanguage()) {
          bitField0_ |= 0x00000400;
          ioaCodeLanguage_ = other.ioaCodeLanguage_;
          onChanged();
        }
        if (other.hasIoaAffectedProduct()) {
          bitField0_ |= 0x00000800;
          ioaAffectedProduct_ = other.ioaAffectedProduct_;
          onChanged();
        }
        if (other.hasIoaMaliciousFamily()) {
          bitField0_ |= 0x00001000;
          ioaMaliciousFamily_ = other.ioaMaliciousFamily_;
          onChanged();
        }
        if (other.hasIoaAptCampaign()) {
          bitField0_ |= 0x00002000;
          ioaAptCampaign_ = other.ioaAptCampaign_;
          onChanged();
        }
        if (other.hasIoaDetailInfo()) {
          bitField0_ |= 0x00004000;
          ioaDetailInfo_ = other.ioaDetailInfo_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        if (!hasIoaStreamId()) {
          return false;
        }
        if (!hasIoaName()) {
          return false;
        }
        if (!hasIoaValue()) {
          return false;
        }
        if (!hasIoaRefer()) {
          return false;
        }
        if (!hasIoaVersion()) {
          return false;
        }
        if (!hasIoaDirection()) {
          return false;
        }
        if (!hasIoaAttackResult()) {
          return false;
        }
        if (!hasIoaDetailInfo()) {
          return false;
        }
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        IoaAlertInfo.IOA_ALERT_INFO parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (IoaAlertInfo.IOA_ALERT_INFO) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long ioaStreamId_ ;
      /**
       * <pre>
       *	流ID	
       * </pre>
       *
       * <code>required uint64 ioa_stream_id = 1;</code>
       */
      public boolean hasIoaStreamId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <pre>
       *	流ID	
       * </pre>
       *
       * <code>required uint64 ioa_stream_id = 1;</code>
       */
      public long getIoaStreamId() {
        return ioaStreamId_;
      }
      /**
       * <pre>
       *	流ID	
       * </pre>
       *
       * <code>required uint64 ioa_stream_id = 1;</code>
       */
      public Builder setIoaStreamId(long value) {
        bitField0_ |= 0x00000001;
        ioaStreamId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	流ID	
       * </pre>
       *
       * <code>required uint64 ioa_stream_id = 1;</code>
       */
      public Builder clearIoaStreamId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        ioaStreamId_ = 0L;
        onChanged();
        return this;
      }

      private long ioaTask_ ;
      /**
       * <pre>
       *	任务号	
       * </pre>
       *
       * <code>optional uint64 ioa_task = 2;</code>
       */
      public boolean hasIoaTask() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <pre>
       *	任务号	
       * </pre>
       *
       * <code>optional uint64 ioa_task = 2;</code>
       */
      public long getIoaTask() {
        return ioaTask_;
      }
      /**
       * <pre>
       *	任务号	
       * </pre>
       *
       * <code>optional uint64 ioa_task = 2;</code>
       */
      public Builder setIoaTask(long value) {
        bitField0_ |= 0x00000002;
        ioaTask_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	任务号	
       * </pre>
       *
       * <code>optional uint64 ioa_task = 2;</code>
       */
      public Builder clearIoaTask() {
        bitField0_ = (bitField0_ & ~0x00000002);
        ioaTask_ = 0L;
        onChanged();
        return this;
      }

      private long ioaRule_ ;
      /**
       * <pre>
       *	规则号	
       * </pre>
       *
       * <code>optional uint64 ioa_rule = 3;</code>
       */
      public boolean hasIoaRule() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <pre>
       *	规则号	
       * </pre>
       *
       * <code>optional uint64 ioa_rule = 3;</code>
       */
      public long getIoaRule() {
        return ioaRule_;
      }
      /**
       * <pre>
       *	规则号	
       * </pre>
       *
       * <code>optional uint64 ioa_rule = 3;</code>
       */
      public Builder setIoaRule(long value) {
        bitField0_ |= 0x00000004;
        ioaRule_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	规则号	
       * </pre>
       *
       * <code>optional uint64 ioa_rule = 3;</code>
       */
      public Builder clearIoaRule() {
        bitField0_ = (bitField0_ & ~0x00000004);
        ioaRule_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object ioaName_ = "";
      /**
       * <pre>
       *	告警名称	
       * </pre>
       *
       * <code>required string ioa_name = 4;</code>
       */
      public boolean hasIoaName() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <pre>
       *	告警名称	
       * </pre>
       *
       * <code>required string ioa_name = 4;</code>
       */
      public java.lang.String getIoaName() {
        java.lang.Object ref = ioaName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	告警名称	
       * </pre>
       *
       * <code>required string ioa_name = 4;</code>
       */
      public com.google.protobuf.ByteString
          getIoaNameBytes() {
        java.lang.Object ref = ioaName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	告警名称	
       * </pre>
       *
       * <code>required string ioa_name = 4;</code>
       */
      public Builder setIoaName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        ioaName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警名称	
       * </pre>
       *
       * <code>required string ioa_name = 4;</code>
       */
      public Builder clearIoaName() {
        bitField0_ = (bitField0_ & ~0x00000008);
        ioaName_ = getDefaultInstance().getIoaName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	告警名称	
       * </pre>
       *
       * <code>required string ioa_name = 4;</code>
       */
      public Builder setIoaNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        ioaName_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object ioaValue_ = "";
      /**
       * <pre>
       *	规则内容	SDX规则语法描述
       * </pre>
       *
       * <code>required string ioa_value = 5;</code>
       */
      public boolean hasIoaValue() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <pre>
       *	规则内容	SDX规则语法描述
       * </pre>
       *
       * <code>required string ioa_value = 5;</code>
       */
      public java.lang.String getIoaValue() {
        java.lang.Object ref = ioaValue_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaValue_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	规则内容	SDX规则语法描述
       * </pre>
       *
       * <code>required string ioa_value = 5;</code>
       */
      public com.google.protobuf.ByteString
          getIoaValueBytes() {
        java.lang.Object ref = ioaValue_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaValue_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	规则内容	SDX规则语法描述
       * </pre>
       *
       * <code>required string ioa_value = 5;</code>
       */
      public Builder setIoaValue(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        ioaValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	规则内容	SDX规则语法描述
       * </pre>
       *
       * <code>required string ioa_value = 5;</code>
       */
      public Builder clearIoaValue() {
        bitField0_ = (bitField0_ & ~0x00000010);
        ioaValue_ = getDefaultInstance().getIoaValue();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	规则内容	SDX规则语法描述
       * </pre>
       *
       * <code>required string ioa_value = 5;</code>
       */
      public Builder setIoaValueBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        ioaValue_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object ioaRefer_ = "";
      /**
       * <pre>
       *	引用文档	参考文档
       * </pre>
       *
       * <code>required string ioa_refer = 6;</code>
       */
      public boolean hasIoaRefer() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <pre>
       *	引用文档	参考文档
       * </pre>
       *
       * <code>required string ioa_refer = 6;</code>
       */
      public java.lang.String getIoaRefer() {
        java.lang.Object ref = ioaRefer_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaRefer_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	引用文档	参考文档
       * </pre>
       *
       * <code>required string ioa_refer = 6;</code>
       */
      public com.google.protobuf.ByteString
          getIoaReferBytes() {
        java.lang.Object ref = ioaRefer_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaRefer_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	引用文档	参考文档
       * </pre>
       *
       * <code>required string ioa_refer = 6;</code>
       */
      public Builder setIoaRefer(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        ioaRefer_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	引用文档	参考文档
       * </pre>
       *
       * <code>required string ioa_refer = 6;</code>
       */
      public Builder clearIoaRefer() {
        bitField0_ = (bitField0_ & ~0x00000020);
        ioaRefer_ = getDefaultInstance().getIoaRefer();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	引用文档	参考文档
       * </pre>
       *
       * <code>required string ioa_refer = 6;</code>
       */
      public Builder setIoaReferBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        ioaRefer_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object ioaVersion_ = "";
      /**
       * <pre>
       *	规则版本	
       * </pre>
       *
       * <code>required string ioa_version = 7;</code>
       */
      public boolean hasIoaVersion() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <pre>
       *	规则版本	
       * </pre>
       *
       * <code>required string ioa_version = 7;</code>
       */
      public java.lang.String getIoaVersion() {
        java.lang.Object ref = ioaVersion_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaVersion_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	规则版本	
       * </pre>
       *
       * <code>required string ioa_version = 7;</code>
       */
      public com.google.protobuf.ByteString
          getIoaVersionBytes() {
        java.lang.Object ref = ioaVersion_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaVersion_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	规则版本	
       * </pre>
       *
       * <code>required string ioa_version = 7;</code>
       */
      public Builder setIoaVersion(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        ioaVersion_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	规则版本	
       * </pre>
       *
       * <code>required string ioa_version = 7;</code>
       */
      public Builder clearIoaVersion() {
        bitField0_ = (bitField0_ & ~0x00000040);
        ioaVersion_ = getDefaultInstance().getIoaVersion();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	规则版本	
       * </pre>
       *
       * <code>required string ioa_version = 7;</code>
       */
      public Builder setIoaVersionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        ioaVersion_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object ioaVul_ = "";
      /**
       * <pre>
       *	关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioa_vul = 8;</code>
       */
      public boolean hasIoaVul() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <pre>
       *	关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioa_vul = 8;</code>
       */
      public java.lang.String getIoaVul() {
        java.lang.Object ref = ioaVul_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaVul_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioa_vul = 8;</code>
       */
      public com.google.protobuf.ByteString
          getIoaVulBytes() {
        java.lang.Object ref = ioaVul_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaVul_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioa_vul = 8;</code>
       */
      public Builder setIoaVul(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        ioaVul_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioa_vul = 8;</code>
       */
      public Builder clearIoaVul() {
        bitField0_ = (bitField0_ & ~0x00000080);
        ioaVul_ = getDefaultInstance().getIoaVul();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioa_vul = 8;</code>
       */
      public Builder setIoaVulBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        ioaVul_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object ioaDirection_ = "";
      /**
       * <pre>
       *	攻击方向	cts/stc/to_client/from_server/from_client/to_server
       * </pre>
       *
       * <code>required string ioa_direction = 9;</code>
       */
      public boolean hasIoaDirection() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <pre>
       *	攻击方向	cts/stc/to_client/from_server/from_client/to_server
       * </pre>
       *
       * <code>required string ioa_direction = 9;</code>
       */
      public java.lang.String getIoaDirection() {
        java.lang.Object ref = ioaDirection_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaDirection_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	攻击方向	cts/stc/to_client/from_server/from_client/to_server
       * </pre>
       *
       * <code>required string ioa_direction = 9;</code>
       */
      public com.google.protobuf.ByteString
          getIoaDirectionBytes() {
        java.lang.Object ref = ioaDirection_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaDirection_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	攻击方向	cts/stc/to_client/from_server/from_client/to_server
       * </pre>
       *
       * <code>required string ioa_direction = 9;</code>
       */
      public Builder setIoaDirection(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        ioaDirection_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	攻击方向	cts/stc/to_client/from_server/from_client/to_server
       * </pre>
       *
       * <code>required string ioa_direction = 9;</code>
       */
      public Builder clearIoaDirection() {
        bitField0_ = (bitField0_ & ~0x00000100);
        ioaDirection_ = getDefaultInstance().getIoaDirection();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	攻击方向	cts/stc/to_client/from_server/from_client/to_server
       * </pre>
       *
       * <code>required string ioa_direction = 9;</code>
       */
      public Builder setIoaDirectionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        ioaDirection_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object ioaAttackResult_ = "";
      /**
       * <pre>
       *	攻击结果	企图/成功/失败/失陷
       * </pre>
       *
       * <code>required string ioa_attack_result = 10;</code>
       */
      public boolean hasIoaAttackResult() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <pre>
       *	攻击结果	企图/成功/失败/失陷
       * </pre>
       *
       * <code>required string ioa_attack_result = 10;</code>
       */
      public java.lang.String getIoaAttackResult() {
        java.lang.Object ref = ioaAttackResult_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaAttackResult_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	攻击结果	企图/成功/失败/失陷
       * </pre>
       *
       * <code>required string ioa_attack_result = 10;</code>
       */
      public com.google.protobuf.ByteString
          getIoaAttackResultBytes() {
        java.lang.Object ref = ioaAttackResult_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaAttackResult_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	攻击结果	企图/成功/失败/失陷
       * </pre>
       *
       * <code>required string ioa_attack_result = 10;</code>
       */
      public Builder setIoaAttackResult(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
        ioaAttackResult_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	攻击结果	企图/成功/失败/失陷
       * </pre>
       *
       * <code>required string ioa_attack_result = 10;</code>
       */
      public Builder clearIoaAttackResult() {
        bitField0_ = (bitField0_ & ~0x00000200);
        ioaAttackResult_ = getDefaultInstance().getIoaAttackResult();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	攻击结果	企图/成功/失败/失陷
       * </pre>
       *
       * <code>required string ioa_attack_result = 10;</code>
       */
      public Builder setIoaAttackResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
        ioaAttackResult_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object ioaCodeLanguage_ = "";
      /**
       * <pre>
       *	代码语言	
       * </pre>
       *
       * <code>optional string ioa_code_language = 11;</code>
       */
      public boolean hasIoaCodeLanguage() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <pre>
       *	代码语言	
       * </pre>
       *
       * <code>optional string ioa_code_language = 11;</code>
       */
      public java.lang.String getIoaCodeLanguage() {
        java.lang.Object ref = ioaCodeLanguage_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaCodeLanguage_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	代码语言	
       * </pre>
       *
       * <code>optional string ioa_code_language = 11;</code>
       */
      public com.google.protobuf.ByteString
          getIoaCodeLanguageBytes() {
        java.lang.Object ref = ioaCodeLanguage_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaCodeLanguage_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	代码语言	
       * </pre>
       *
       * <code>optional string ioa_code_language = 11;</code>
       */
      public Builder setIoaCodeLanguage(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        ioaCodeLanguage_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	代码语言	
       * </pre>
       *
       * <code>optional string ioa_code_language = 11;</code>
       */
      public Builder clearIoaCodeLanguage() {
        bitField0_ = (bitField0_ & ~0x00000400);
        ioaCodeLanguage_ = getDefaultInstance().getIoaCodeLanguage();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	代码语言	
       * </pre>
       *
       * <code>optional string ioa_code_language = 11;</code>
       */
      public Builder setIoaCodeLanguageBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        ioaCodeLanguage_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object ioaAffectedProduct_ = "";
      /**
       * <pre>
       *	影响平台	受影响APP/系统/应用
       * </pre>
       *
       * <code>optional string ioa_affected_product = 12;</code>
       */
      public boolean hasIoaAffectedProduct() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <pre>
       *	影响平台	受影响APP/系统/应用
       * </pre>
       *
       * <code>optional string ioa_affected_product = 12;</code>
       */
      public java.lang.String getIoaAffectedProduct() {
        java.lang.Object ref = ioaAffectedProduct_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaAffectedProduct_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	影响平台	受影响APP/系统/应用
       * </pre>
       *
       * <code>optional string ioa_affected_product = 12;</code>
       */
      public com.google.protobuf.ByteString
          getIoaAffectedProductBytes() {
        java.lang.Object ref = ioaAffectedProduct_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaAffectedProduct_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	影响平台	受影响APP/系统/应用
       * </pre>
       *
       * <code>optional string ioa_affected_product = 12;</code>
       */
      public Builder setIoaAffectedProduct(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        ioaAffectedProduct_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	影响平台	受影响APP/系统/应用
       * </pre>
       *
       * <code>optional string ioa_affected_product = 12;</code>
       */
      public Builder clearIoaAffectedProduct() {
        bitField0_ = (bitField0_ & ~0x00000800);
        ioaAffectedProduct_ = getDefaultInstance().getIoaAffectedProduct();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	影响平台	受影响APP/系统/应用
       * </pre>
       *
       * <code>optional string ioa_affected_product = 12;</code>
       */
      public Builder setIoaAffectedProductBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        ioaAffectedProduct_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object ioaMaliciousFamily_ = "";
      /**
       * <pre>
       *	恶意代码家族	
       * </pre>
       *
       * <code>optional string ioa_malicious_family = 13;</code>
       */
      public boolean hasIoaMaliciousFamily() {
        return ((bitField0_ & 0x00001000) == 0x00001000);
      }
      /**
       * <pre>
       *	恶意代码家族	
       * </pre>
       *
       * <code>optional string ioa_malicious_family = 13;</code>
       */
      public java.lang.String getIoaMaliciousFamily() {
        java.lang.Object ref = ioaMaliciousFamily_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaMaliciousFamily_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	恶意代码家族	
       * </pre>
       *
       * <code>optional string ioa_malicious_family = 13;</code>
       */
      public com.google.protobuf.ByteString
          getIoaMaliciousFamilyBytes() {
        java.lang.Object ref = ioaMaliciousFamily_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaMaliciousFamily_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	恶意代码家族	
       * </pre>
       *
       * <code>optional string ioa_malicious_family = 13;</code>
       */
      public Builder setIoaMaliciousFamily(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00001000;
        ioaMaliciousFamily_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	恶意代码家族	
       * </pre>
       *
       * <code>optional string ioa_malicious_family = 13;</code>
       */
      public Builder clearIoaMaliciousFamily() {
        bitField0_ = (bitField0_ & ~0x00001000);
        ioaMaliciousFamily_ = getDefaultInstance().getIoaMaliciousFamily();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	恶意代码家族	
       * </pre>
       *
       * <code>optional string ioa_malicious_family = 13;</code>
       */
      public Builder setIoaMaliciousFamilyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00001000;
        ioaMaliciousFamily_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object ioaAptCampaign_ = "";
      /**
       * <pre>
       *	APT组织名称	
       * </pre>
       *
       * <code>optional string ioa_apt_campaign = 14;</code>
       */
      public boolean hasIoaAptCampaign() {
        return ((bitField0_ & 0x00002000) == 0x00002000);
      }
      /**
       * <pre>
       *	APT组织名称	
       * </pre>
       *
       * <code>optional string ioa_apt_campaign = 14;</code>
       */
      public java.lang.String getIoaAptCampaign() {
        java.lang.Object ref = ioaAptCampaign_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaAptCampaign_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	APT组织名称	
       * </pre>
       *
       * <code>optional string ioa_apt_campaign = 14;</code>
       */
      public com.google.protobuf.ByteString
          getIoaAptCampaignBytes() {
        java.lang.Object ref = ioaAptCampaign_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaAptCampaign_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	APT组织名称	
       * </pre>
       *
       * <code>optional string ioa_apt_campaign = 14;</code>
       */
      public Builder setIoaAptCampaign(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00002000;
        ioaAptCampaign_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	APT组织名称	
       * </pre>
       *
       * <code>optional string ioa_apt_campaign = 14;</code>
       */
      public Builder clearIoaAptCampaign() {
        bitField0_ = (bitField0_ & ~0x00002000);
        ioaAptCampaign_ = getDefaultInstance().getIoaAptCampaign();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	APT组织名称	
       * </pre>
       *
       * <code>optional string ioa_apt_campaign = 14;</code>
       */
      public Builder setIoaAptCampaignBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00002000;
        ioaAptCampaign_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object ioaDetailInfo_ = "";
      /**
       * <pre>
       *	威胁详情描述
       * </pre>
       *
       * <code>required string ioa_detail_info = 15;</code>
       */
      public boolean hasIoaDetailInfo() {
        return ((bitField0_ & 0x00004000) == 0x00004000);
      }
      /**
       * <pre>
       *	威胁详情描述
       * </pre>
       *
       * <code>required string ioa_detail_info = 15;</code>
       */
      public java.lang.String getIoaDetailInfo() {
        java.lang.Object ref = ioaDetailInfo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaDetailInfo_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	威胁详情描述
       * </pre>
       *
       * <code>required string ioa_detail_info = 15;</code>
       */
      public com.google.protobuf.ByteString
          getIoaDetailInfoBytes() {
        java.lang.Object ref = ioaDetailInfo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaDetailInfo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	威胁详情描述
       * </pre>
       *
       * <code>required string ioa_detail_info = 15;</code>
       */
      public Builder setIoaDetailInfo(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00004000;
        ioaDetailInfo_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁详情描述
       * </pre>
       *
       * <code>required string ioa_detail_info = 15;</code>
       */
      public Builder clearIoaDetailInfo() {
        bitField0_ = (bitField0_ & ~0x00004000);
        ioaDetailInfo_ = getDefaultInstance().getIoaDetailInfo();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁详情描述
       * </pre>
       *
       * <code>required string ioa_detail_info = 15;</code>
       */
      public Builder setIoaDetailInfoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00004000;
        ioaDetailInfo_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:IOA_ALERT_INFO)
    }

    // @@protoc_insertion_point(class_scope:IOA_ALERT_INFO)
    private static final IoaAlertInfo.IOA_ALERT_INFO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new IoaAlertInfo.IOA_ALERT_INFO();
    }

    public static IoaAlertInfo.IOA_ALERT_INFO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<IOA_ALERT_INFO>
        PARSER = new com.google.protobuf.AbstractParser<IOA_ALERT_INFO>() {
      @java.lang.Override
      public IOA_ALERT_INFO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new IOA_ALERT_INFO(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<IOA_ALERT_INFO> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IOA_ALERT_INFO> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public IoaAlertInfo.IOA_ALERT_INFO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_IOA_ALERT_INFO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_IOA_ALERT_INFO_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\024IOA_ALERT_INFO.proto\"\345\002\n\016IOA_ALERT_INF" +
      "O\022\025\n\rioa_stream_id\030\001 \002(\004\022\020\n\010ioa_task\030\002 \001" +
      "(\004\022\020\n\010ioa_rule\030\003 \001(\004\022\020\n\010ioa_name\030\004 \002(\t\022\021" +
      "\n\tioa_value\030\005 \002(\t\022\021\n\tioa_refer\030\006 \002(\t\022\023\n\013" +
      "ioa_version\030\007 \002(\t\022\017\n\007ioa_vul\030\010 \001(\t\022\025\n\rio" +
      "a_direction\030\t \002(\t\022\031\n\021ioa_attack_result\030\n" +
      " \002(\t\022\031\n\021ioa_code_language\030\013 \001(\t\022\034\n\024ioa_a" +
      "ffected_product\030\014 \001(\t\022\034\n\024ioa_malicious_f" +
      "amily\030\r \001(\t\022\030\n\020ioa_apt_campaign\030\016 \001(\t\022\027\n" +
      "\017ioa_detail_info\030\017 \002(\tB\016B\014IoaAlertInfo"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_IOA_ALERT_INFO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_IOA_ALERT_INFO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_IOA_ALERT_INFO_descriptor,
        new java.lang.String[] { "IoaStreamId", "IoaTask", "IoaRule", "IoaName", "IoaValue", "IoaRefer", "IoaVersion", "IoaVul", "IoaDirection", "IoaAttackResult", "IoaCodeLanguage", "IoaAffectedProduct", "IoaMaliciousFamily", "IoaAptCampaign", "IoaDetailInfo", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
