// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: IcmpInfo.proto
package com.geeksec.proto.output;

public final class IcmpInfoOuterClass {
  private IcmpInfoOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface IcmpInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:IcmpInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional uint32 msgType = 1;</code>
     */
    boolean hasMsgType();
    /**
     * <code>optional uint32 msgType = 1;</code>
     */
    int getMsgType();

    /**
     * <code>optional uint32 infoCode = 2;</code>
     */
    boolean hasInfoCode();
    /**
     * <code>optional uint32 infoCode = 2;</code>
     */
    int getInfoCode();

    /**
     * <code>optional uint32 echoSeqNum = 3;</code>
     */
    boolean hasEchoSeqNum();
    /**
     * <code>optional uint32 echoSeqNum = 3;</code>
     */
    int getEchoSeqNum();

    /**
     * <code>optional bytes dataCon = 4;</code>
     */
    boolean hasDataCon();
    /**
     * <code>optional bytes dataCon = 4;</code>
     */
    com.google.protobuf.ByteString getDataCon();

    /**
     * <code>optional bytes unrSrcAddr = 5;</code>
     */
    boolean hasUnrSrcAddr();
    /**
     * <code>optional bytes unrSrcAddr = 5;</code>
     */
    com.google.protobuf.ByteString getUnrSrcAddr();

    /**
     * <code>optional bytes unrDstAddr = 6;</code>
     */
    boolean hasUnrDstAddr();
    /**
     * <code>optional bytes unrDstAddr = 6;</code>
     */
    com.google.protobuf.ByteString getUnrDstAddr();

    /**
     * <code>optional uint32 unrProt = 7;</code>
     */
    boolean hasUnrProt();
    /**
     * <code>optional uint32 unrProt = 7;</code>
     */
    int getUnrProt();

    /**
     * <code>optional uint32 uncTTL = 8;</code>
     */
    boolean hasUncTTL();
    /**
     * <code>optional uint32 uncTTL = 8;</code>
     */
    int getUncTTL();

    /**
     * <code>optional uint32 ver = 9;</code>
     */
    boolean hasVer();
    /**
     * <code>optional uint32 ver = 9;</code>
     */
    int getVer();

    /**
     * <code>optional uint64 origTimeStamp = 10;</code>
     */
    boolean hasOrigTimeStamp();
    /**
     * <code>optional uint64 origTimeStamp = 10;</code>
     */
    long getOrigTimeStamp();

    /**
     * <code>optional uint64 recvTimeStamp = 11;</code>
     */
    boolean hasRecvTimeStamp();
    /**
     * <code>optional uint64 recvTimeStamp = 11;</code>
     */
    long getRecvTimeStamp();

    /**
     * <code>optional uint64 transTimeStamp = 12;</code>
     */
    boolean hasTransTimeStamp();
    /**
     * <code>optional uint64 transTimeStamp = 12;</code>
     */
    long getTransTimeStamp();

    /**
     * <code>optional uint32 mask = 13;</code>
     */
    boolean hasMask();
    /**
     * <code>optional uint32 mask = 13;</code>
     */
    int getMask();

    /**
     * <code>optional uint32 subNetId = 14;</code>
     */
    boolean hasSubNetId();
    /**
     * <code>optional uint32 subNetId = 14;</code>
     */
    int getSubNetId();

    /**
     * <code>optional uint32 rtrTimeOut = 15;</code>
     */
    boolean hasRtrTimeOut();
    /**
     * <code>optional uint32 rtrTimeOut = 15;</code>
     */
    int getRtrTimeOut();

    /**
     * <code>optional bytes excSrcAddr = 16;</code>
     */
    boolean hasExcSrcAddr();
    /**
     * <code>optional bytes excSrcAddr = 16;</code>
     */
    com.google.protobuf.ByteString getExcSrcAddr();

    /**
     * <code>optional bytes excDstAddr = 17;</code>
     */
    boolean hasExcDstAddr();
    /**
     * <code>optional bytes excDstAddr = 17;</code>
     */
    com.google.protobuf.ByteString getExcDstAddr();

    /**
     * <code>optional uint32 excProt = 18;</code>
     */
    boolean hasExcProt();
    /**
     * <code>optional uint32 excProt = 18;</code>
     */
    int getExcProt();

    /**
     * <code>optional uint32 excSrcPort = 19;</code>
     */
    boolean hasExcSrcPort();
    /**
     * <code>optional uint32 excSrcPort = 19;</code>
     */
    int getExcSrcPort();

    /**
     * <code>optional uint32 excDstPort = 20;</code>
     */
    boolean hasExcDstPort();
    /**
     * <code>optional uint32 excDstPort = 20;</code>
     */
    int getExcDstPort();

    /**
     * <code>optional bytes gwAddr = 21;</code>
     */
    boolean hasGwAddr();
    /**
     * <code>optional bytes gwAddr = 21;</code>
     */
    com.google.protobuf.ByteString getGwAddr();

    /**
     * <code>optional uint32 ttl = 22;</code>
     */
    boolean hasTtl();
    /**
     * <code>optional uint32 ttl = 22;</code>
     */
    int getTtl();

    /**
     * <code>optional uint32 repTtl = 23;</code>
     */
    boolean hasRepTtl();
    /**
     * <code>optional uint32 repTtl = 23;</code>
     */
    int getRepTtl();

    /**
     * <code>optional uint32 qurType = 24;</code>
     */
    boolean hasQurType();
    /**
     * <code>optional uint32 qurType = 24;</code>
     */
    int getQurType();

    /**
     * <code>optional bytes qurIpv6Addr = 25;</code>
     */
    boolean hasQurIpv6Addr();
    /**
     * <code>optional bytes qurIpv6Addr = 25;</code>
     */
    com.google.protobuf.ByteString getQurIpv6Addr();

    /**
     * <code>optional bytes qurIpv4Addr = 26;</code>
     */
    boolean hasQurIpv4Addr();
    /**
     * <code>optional bytes qurIpv4Addr = 26;</code>
     */
    com.google.protobuf.ByteString getQurIpv4Addr();

    /**
     * <code>optional bytes qurDNS = 27;</code>
     */
    boolean hasQurDNS();
    /**
     * <code>optional bytes qurDNS = 27;</code>
     */
    com.google.protobuf.ByteString getQurDNS();

    /**
     * <code>optional uint32 ndpLifeTime = 28;</code>
     */
    boolean hasNdpLifeTime();
    /**
     * <code>optional uint32 ndpLifeTime = 28;</code>
     */
    int getNdpLifeTime();

    /**
     * <code>optional bytes ndpLinkAddr = 29;</code>
     */
    boolean hasNdpLinkAddr();
    /**
     * <code>optional bytes ndpLinkAddr = 29;</code>
     */
    com.google.protobuf.ByteString getNdpLinkAddr();

    /**
     * <code>optional uint32 ndpPreLen = 30;</code>
     */
    boolean hasNdpPreLen();
    /**
     * <code>optional uint32 ndpPreLen = 30;</code>
     */
    int getNdpPreLen();

    /**
     * <code>optional bytes ndpPreFix = 31;</code>
     */
    boolean hasNdpPreFix();
    /**
     * <code>optional bytes ndpPreFix = 31;</code>
     */
    com.google.protobuf.ByteString getNdpPreFix();

    /**
     * <code>optional uint32 ndpValLifeTime = 32;</code>
     */
    boolean hasNdpValLifeTime();
    /**
     * <code>optional uint32 ndpValLifeTime = 32;</code>
     */
    int getNdpValLifeTime();

    /**
     * <code>optional uint32 ndpCurMtu = 33;</code>
     */
    boolean hasNdpCurMtu();
    /**
     * <code>optional uint32 ndpCurMtu = 33;</code>
     */
    int getNdpCurMtu();

    /**
     * <code>optional bytes ndpTarAddr = 34;</code>
     */
    boolean hasNdpTarAddr();
    /**
     * <code>optional bytes ndpTarAddr = 34;</code>
     */
    com.google.protobuf.ByteString getNdpTarAddr();

    /**
     * <code>optional uint32 nextHopMtu = 35;</code>
     */
    boolean hasNextHopMtu();
    /**
     * <code>optional uint32 nextHopMtu = 35;</code>
     */
    int getNextHopMtu();

    /**
     * <code>optional uint32 excPointer = 36;</code>
     */
    boolean hasExcPointer();
    /**
     * <code>optional uint32 excPointer = 36;</code>
     */
    int getExcPointer();

    /**
     * <code>optional bytes mulCastAddr = 37;</code>
     */
    boolean hasMulCastAddr();
    /**
     * <code>optional bytes mulCastAddr = 37;</code>
     */
    com.google.protobuf.ByteString getMulCastAddr();

    /**
     * <code>optional uint32 checkSum = 38;</code>
     */
    boolean hasCheckSum();
    /**
     * <code>optional uint32 checkSum = 38;</code>
     */
    int getCheckSum();

    /**
     * <code>optional uint32 checkSumReply = 39;</code>
     */
    boolean hasCheckSumReply();
    /**
     * <code>optional uint32 checkSumReply = 39;</code>
     */
    int getCheckSumReply();

    /**
     * <code>optional uint32 rtraddr = 40;</code>
     */
    boolean hasRtraddr();
    /**
     * <code>optional uint32 rtraddr = 40;</code>
     */
    int getRtraddr();

    /**
     * <code>optional uint64 resTime = 41;</code>
     */
    boolean hasResTime();
    /**
     * <code>optional uint64 resTime = 41;</code>
     */
    long getResTime();

    /**
     * <code>optional uint32 excTTL = 42;</code>
     */
    boolean hasExcTTL();
    /**
     * <code>optional uint32 excTTL = 42;</code>
     */
    int getExcTTL();

    /**
     * <code>optional uint64 ResponseTime = 43;</code>
     */
    boolean hasResponseTime();
    /**
     * <code>optional uint64 ResponseTime = 43;</code>
     */
    long getResponseTime();

    /**
     * <code>optional uint32 unreachableSourcePort = 44;</code>
     */
    boolean hasUnreachableSourcePort();
    /**
     * <code>optional uint32 unreachableSourcePort = 44;</code>
     */
    int getUnreachableSourcePort();

    /**
     * <code>optional uint32 unreachableDestinationPort = 45;</code>
     */
    boolean hasUnreachableDestinationPort();
    /**
     * <code>optional uint32 unreachableDestinationPort = 45;</code>
     */
    int getUnreachableDestinationPort();
  }
  /**
   * Protobuf type {@code IcmpInfo}
   */
  public  static final class IcmpInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:IcmpInfo)
      IcmpInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use IcmpInfo.newBuilder() to construct.
    private IcmpInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private IcmpInfo() {
      msgType_ = 0;
      infoCode_ = 0;
      echoSeqNum_ = 0;
      dataCon_ = com.google.protobuf.ByteString.EMPTY;
      unrSrcAddr_ = com.google.protobuf.ByteString.EMPTY;
      unrDstAddr_ = com.google.protobuf.ByteString.EMPTY;
      unrProt_ = 0;
      uncTTL_ = 0;
      ver_ = 0;
      origTimeStamp_ = 0L;
      recvTimeStamp_ = 0L;
      transTimeStamp_ = 0L;
      mask_ = 0;
      subNetId_ = 0;
      rtrTimeOut_ = 0;
      excSrcAddr_ = com.google.protobuf.ByteString.EMPTY;
      excDstAddr_ = com.google.protobuf.ByteString.EMPTY;
      excProt_ = 0;
      excSrcPort_ = 0;
      excDstPort_ = 0;
      gwAddr_ = com.google.protobuf.ByteString.EMPTY;
      ttl_ = 0;
      repTtl_ = 0;
      qurType_ = 0;
      qurIpv6Addr_ = com.google.protobuf.ByteString.EMPTY;
      qurIpv4Addr_ = com.google.protobuf.ByteString.EMPTY;
      qurDNS_ = com.google.protobuf.ByteString.EMPTY;
      ndpLifeTime_ = 0;
      ndpLinkAddr_ = com.google.protobuf.ByteString.EMPTY;
      ndpPreLen_ = 0;
      ndpPreFix_ = com.google.protobuf.ByteString.EMPTY;
      ndpValLifeTime_ = 0;
      ndpCurMtu_ = 0;
      ndpTarAddr_ = com.google.protobuf.ByteString.EMPTY;
      nextHopMtu_ = 0;
      excPointer_ = 0;
      mulCastAddr_ = com.google.protobuf.ByteString.EMPTY;
      checkSum_ = 0;
      checkSumReply_ = 0;
      rtraddr_ = 0;
      resTime_ = 0L;
      excTTL_ = 0;
      responseTime_ = 0L;
      unreachableSourcePort_ = 0;
      unreachableDestinationPort_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private IcmpInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      int mutable_bitField1_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              msgType_ = input.readUInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              infoCode_ = input.readUInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              echoSeqNum_ = input.readUInt32();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              dataCon_ = input.readBytes();
              break;
            }
            case 42: {
              bitField0_ |= 0x00000010;
              unrSrcAddr_ = input.readBytes();
              break;
            }
            case 50: {
              bitField0_ |= 0x00000020;
              unrDstAddr_ = input.readBytes();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              unrProt_ = input.readUInt32();
              break;
            }
            case 64: {
              bitField0_ |= 0x00000080;
              uncTTL_ = input.readUInt32();
              break;
            }
            case 72: {
              bitField0_ |= 0x00000100;
              ver_ = input.readUInt32();
              break;
            }
            case 80: {
              bitField0_ |= 0x00000200;
              origTimeStamp_ = input.readUInt64();
              break;
            }
            case 88: {
              bitField0_ |= 0x00000400;
              recvTimeStamp_ = input.readUInt64();
              break;
            }
            case 96: {
              bitField0_ |= 0x00000800;
              transTimeStamp_ = input.readUInt64();
              break;
            }
            case 104: {
              bitField0_ |= 0x00001000;
              mask_ = input.readUInt32();
              break;
            }
            case 112: {
              bitField0_ |= 0x00002000;
              subNetId_ = input.readUInt32();
              break;
            }
            case 120: {
              bitField0_ |= 0x00004000;
              rtrTimeOut_ = input.readUInt32();
              break;
            }
            case 130: {
              bitField0_ |= 0x00008000;
              excSrcAddr_ = input.readBytes();
              break;
            }
            case 138: {
              bitField0_ |= 0x00010000;
              excDstAddr_ = input.readBytes();
              break;
            }
            case 144: {
              bitField0_ |= 0x00020000;
              excProt_ = input.readUInt32();
              break;
            }
            case 152: {
              bitField0_ |= 0x00040000;
              excSrcPort_ = input.readUInt32();
              break;
            }
            case 160: {
              bitField0_ |= 0x00080000;
              excDstPort_ = input.readUInt32();
              break;
            }
            case 170: {
              bitField0_ |= 0x00100000;
              gwAddr_ = input.readBytes();
              break;
            }
            case 176: {
              bitField0_ |= 0x00200000;
              ttl_ = input.readUInt32();
              break;
            }
            case 184: {
              bitField0_ |= 0x00400000;
              repTtl_ = input.readUInt32();
              break;
            }
            case 192: {
              bitField0_ |= 0x00800000;
              qurType_ = input.readUInt32();
              break;
            }
            case 202: {
              bitField0_ |= 0x01000000;
              qurIpv6Addr_ = input.readBytes();
              break;
            }
            case 210: {
              bitField0_ |= 0x02000000;
              qurIpv4Addr_ = input.readBytes();
              break;
            }
            case 218: {
              bitField0_ |= 0x04000000;
              qurDNS_ = input.readBytes();
              break;
            }
            case 224: {
              bitField0_ |= 0x08000000;
              ndpLifeTime_ = input.readUInt32();
              break;
            }
            case 234: {
              bitField0_ |= 0x10000000;
              ndpLinkAddr_ = input.readBytes();
              break;
            }
            case 240: {
              bitField0_ |= 0x20000000;
              ndpPreLen_ = input.readUInt32();
              break;
            }
            case 250: {
              bitField0_ |= 0x40000000;
              ndpPreFix_ = input.readBytes();
              break;
            }
            case 256: {
              bitField0_ |= 0x80000000;
              ndpValLifeTime_ = input.readUInt32();
              break;
            }
            case 264: {
              bitField1_ |= 0x00000001;
              ndpCurMtu_ = input.readUInt32();
              break;
            }
            case 274: {
              bitField1_ |= 0x00000002;
              ndpTarAddr_ = input.readBytes();
              break;
            }
            case 280: {
              bitField1_ |= 0x00000004;
              nextHopMtu_ = input.readUInt32();
              break;
            }
            case 288: {
              bitField1_ |= 0x00000008;
              excPointer_ = input.readUInt32();
              break;
            }
            case 298: {
              bitField1_ |= 0x00000010;
              mulCastAddr_ = input.readBytes();
              break;
            }
            case 304: {
              bitField1_ |= 0x00000020;
              checkSum_ = input.readUInt32();
              break;
            }
            case 312: {
              bitField1_ |= 0x00000040;
              checkSumReply_ = input.readUInt32();
              break;
            }
            case 320: {
              bitField1_ |= 0x00000080;
              rtraddr_ = input.readUInt32();
              break;
            }
            case 328: {
              bitField1_ |= 0x00000100;
              resTime_ = input.readUInt64();
              break;
            }
            case 336: {
              bitField1_ |= 0x00000200;
              excTTL_ = input.readUInt32();
              break;
            }
            case 344: {
              bitField1_ |= 0x00000400;
              responseTime_ = input.readUInt64();
              break;
            }
            case 352: {
              bitField1_ |= 0x00000800;
              unreachableSourcePort_ = input.readUInt32();
              break;
            }
            case 360: {
              bitField1_ |= 0x00001000;
              unreachableDestinationPort_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return IcmpInfoOuterClass.internal_static_IcmpInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return IcmpInfoOuterClass.internal_static_IcmpInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              IcmpInfoOuterClass.IcmpInfo.class, IcmpInfoOuterClass.IcmpInfo.Builder.class);
    }

    private int bitField0_;
    private int bitField1_;
    public static final int MSGTYPE_FIELD_NUMBER = 1;
    private int msgType_;
    /**
     * <code>optional uint32 msgType = 1;</code>
     */
    public boolean hasMsgType() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional uint32 msgType = 1;</code>
     */
    public int getMsgType() {
      return msgType_;
    }

    public static final int INFOCODE_FIELD_NUMBER = 2;
    private int infoCode_;
    /**
     * <code>optional uint32 infoCode = 2;</code>
     */
    public boolean hasInfoCode() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional uint32 infoCode = 2;</code>
     */
    public int getInfoCode() {
      return infoCode_;
    }

    public static final int ECHOSEQNUM_FIELD_NUMBER = 3;
    private int echoSeqNum_;
    /**
     * <code>optional uint32 echoSeqNum = 3;</code>
     */
    public boolean hasEchoSeqNum() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional uint32 echoSeqNum = 3;</code>
     */
    public int getEchoSeqNum() {
      return echoSeqNum_;
    }

    public static final int DATACON_FIELD_NUMBER = 4;
    private com.google.protobuf.ByteString dataCon_;
    /**
     * <code>optional bytes dataCon = 4;</code>
     */
    public boolean hasDataCon() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional bytes dataCon = 4;</code>
     */
    public com.google.protobuf.ByteString getDataCon() {
      return dataCon_;
    }

    public static final int UNRSRCADDR_FIELD_NUMBER = 5;
    private com.google.protobuf.ByteString unrSrcAddr_;
    /**
     * <code>optional bytes unrSrcAddr = 5;</code>
     */
    public boolean hasUnrSrcAddr() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional bytes unrSrcAddr = 5;</code>
     */
    public com.google.protobuf.ByteString getUnrSrcAddr() {
      return unrSrcAddr_;
    }

    public static final int UNRDSTADDR_FIELD_NUMBER = 6;
    private com.google.protobuf.ByteString unrDstAddr_;
    /**
     * <code>optional bytes unrDstAddr = 6;</code>
     */
    public boolean hasUnrDstAddr() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional bytes unrDstAddr = 6;</code>
     */
    public com.google.protobuf.ByteString getUnrDstAddr() {
      return unrDstAddr_;
    }

    public static final int UNRPROT_FIELD_NUMBER = 7;
    private int unrProt_;
    /**
     * <code>optional uint32 unrProt = 7;</code>
     */
    public boolean hasUnrProt() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional uint32 unrProt = 7;</code>
     */
    public int getUnrProt() {
      return unrProt_;
    }

    public static final int UNCTTL_FIELD_NUMBER = 8;
    private int uncTTL_;
    /**
     * <code>optional uint32 uncTTL = 8;</code>
     */
    public boolean hasUncTTL() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional uint32 uncTTL = 8;</code>
     */
    public int getUncTTL() {
      return uncTTL_;
    }

    public static final int VER_FIELD_NUMBER = 9;
    private int ver_;
    /**
     * <code>optional uint32 ver = 9;</code>
     */
    public boolean hasVer() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional uint32 ver = 9;</code>
     */
    public int getVer() {
      return ver_;
    }

    public static final int ORIGTIMESTAMP_FIELD_NUMBER = 10;
    private long origTimeStamp_;
    /**
     * <code>optional uint64 origTimeStamp = 10;</code>
     */
    public boolean hasOrigTimeStamp() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>optional uint64 origTimeStamp = 10;</code>
     */
    public long getOrigTimeStamp() {
      return origTimeStamp_;
    }

    public static final int RECVTIMESTAMP_FIELD_NUMBER = 11;
    private long recvTimeStamp_;
    /**
     * <code>optional uint64 recvTimeStamp = 11;</code>
     */
    public boolean hasRecvTimeStamp() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <code>optional uint64 recvTimeStamp = 11;</code>
     */
    public long getRecvTimeStamp() {
      return recvTimeStamp_;
    }

    public static final int TRANSTIMESTAMP_FIELD_NUMBER = 12;
    private long transTimeStamp_;
    /**
     * <code>optional uint64 transTimeStamp = 12;</code>
     */
    public boolean hasTransTimeStamp() {
      return ((bitField0_ & 0x00000800) == 0x00000800);
    }
    /**
     * <code>optional uint64 transTimeStamp = 12;</code>
     */
    public long getTransTimeStamp() {
      return transTimeStamp_;
    }

    public static final int MASK_FIELD_NUMBER = 13;
    private int mask_;
    /**
     * <code>optional uint32 mask = 13;</code>
     */
    public boolean hasMask() {
      return ((bitField0_ & 0x00001000) == 0x00001000);
    }
    /**
     * <code>optional uint32 mask = 13;</code>
     */
    public int getMask() {
      return mask_;
    }

    public static final int SUBNETID_FIELD_NUMBER = 14;
    private int subNetId_;
    /**
     * <code>optional uint32 subNetId = 14;</code>
     */
    public boolean hasSubNetId() {
      return ((bitField0_ & 0x00002000) == 0x00002000);
    }
    /**
     * <code>optional uint32 subNetId = 14;</code>
     */
    public int getSubNetId() {
      return subNetId_;
    }

    public static final int RTRTIMEOUT_FIELD_NUMBER = 15;
    private int rtrTimeOut_;
    /**
     * <code>optional uint32 rtrTimeOut = 15;</code>
     */
    public boolean hasRtrTimeOut() {
      return ((bitField0_ & 0x00004000) == 0x00004000);
    }
    /**
     * <code>optional uint32 rtrTimeOut = 15;</code>
     */
    public int getRtrTimeOut() {
      return rtrTimeOut_;
    }

    public static final int EXCSRCADDR_FIELD_NUMBER = 16;
    private com.google.protobuf.ByteString excSrcAddr_;
    /**
     * <code>optional bytes excSrcAddr = 16;</code>
     */
    public boolean hasExcSrcAddr() {
      return ((bitField0_ & 0x00008000) == 0x00008000);
    }
    /**
     * <code>optional bytes excSrcAddr = 16;</code>
     */
    public com.google.protobuf.ByteString getExcSrcAddr() {
      return excSrcAddr_;
    }

    public static final int EXCDSTADDR_FIELD_NUMBER = 17;
    private com.google.protobuf.ByteString excDstAddr_;
    /**
     * <code>optional bytes excDstAddr = 17;</code>
     */
    public boolean hasExcDstAddr() {
      return ((bitField0_ & 0x00010000) == 0x00010000);
    }
    /**
     * <code>optional bytes excDstAddr = 17;</code>
     */
    public com.google.protobuf.ByteString getExcDstAddr() {
      return excDstAddr_;
    }

    public static final int EXCPROT_FIELD_NUMBER = 18;
    private int excProt_;
    /**
     * <code>optional uint32 excProt = 18;</code>
     */
    public boolean hasExcProt() {
      return ((bitField0_ & 0x00020000) == 0x00020000);
    }
    /**
     * <code>optional uint32 excProt = 18;</code>
     */
    public int getExcProt() {
      return excProt_;
    }

    public static final int EXCSRCPORT_FIELD_NUMBER = 19;
    private int excSrcPort_;
    /**
     * <code>optional uint32 excSrcPort = 19;</code>
     */
    public boolean hasExcSrcPort() {
      return ((bitField0_ & 0x00040000) == 0x00040000);
    }
    /**
     * <code>optional uint32 excSrcPort = 19;</code>
     */
    public int getExcSrcPort() {
      return excSrcPort_;
    }

    public static final int EXCDSTPORT_FIELD_NUMBER = 20;
    private int excDstPort_;
    /**
     * <code>optional uint32 excDstPort = 20;</code>
     */
    public boolean hasExcDstPort() {
      return ((bitField0_ & 0x00080000) == 0x00080000);
    }
    /**
     * <code>optional uint32 excDstPort = 20;</code>
     */
    public int getExcDstPort() {
      return excDstPort_;
    }

    public static final int GWADDR_FIELD_NUMBER = 21;
    private com.google.protobuf.ByteString gwAddr_;
    /**
     * <code>optional bytes gwAddr = 21;</code>
     */
    public boolean hasGwAddr() {
      return ((bitField0_ & 0x00100000) == 0x00100000);
    }
    /**
     * <code>optional bytes gwAddr = 21;</code>
     */
    public com.google.protobuf.ByteString getGwAddr() {
      return gwAddr_;
    }

    public static final int TTL_FIELD_NUMBER = 22;
    private int ttl_;
    /**
     * <code>optional uint32 ttl = 22;</code>
     */
    public boolean hasTtl() {
      return ((bitField0_ & 0x00200000) == 0x00200000);
    }
    /**
     * <code>optional uint32 ttl = 22;</code>
     */
    public int getTtl() {
      return ttl_;
    }

    public static final int REPTTL_FIELD_NUMBER = 23;
    private int repTtl_;
    /**
     * <code>optional uint32 repTtl = 23;</code>
     */
    public boolean hasRepTtl() {
      return ((bitField0_ & 0x00400000) == 0x00400000);
    }
    /**
     * <code>optional uint32 repTtl = 23;</code>
     */
    public int getRepTtl() {
      return repTtl_;
    }

    public static final int QURTYPE_FIELD_NUMBER = 24;
    private int qurType_;
    /**
     * <code>optional uint32 qurType = 24;</code>
     */
    public boolean hasQurType() {
      return ((bitField0_ & 0x00800000) == 0x00800000);
    }
    /**
     * <code>optional uint32 qurType = 24;</code>
     */
    public int getQurType() {
      return qurType_;
    }

    public static final int QURIPV6ADDR_FIELD_NUMBER = 25;
    private com.google.protobuf.ByteString qurIpv6Addr_;
    /**
     * <code>optional bytes qurIpv6Addr = 25;</code>
     */
    public boolean hasQurIpv6Addr() {
      return ((bitField0_ & 0x01000000) == 0x01000000);
    }
    /**
     * <code>optional bytes qurIpv6Addr = 25;</code>
     */
    public com.google.protobuf.ByteString getQurIpv6Addr() {
      return qurIpv6Addr_;
    }

    public static final int QURIPV4ADDR_FIELD_NUMBER = 26;
    private com.google.protobuf.ByteString qurIpv4Addr_;
    /**
     * <code>optional bytes qurIpv4Addr = 26;</code>
     */
    public boolean hasQurIpv4Addr() {
      return ((bitField0_ & 0x02000000) == 0x02000000);
    }
    /**
     * <code>optional bytes qurIpv4Addr = 26;</code>
     */
    public com.google.protobuf.ByteString getQurIpv4Addr() {
      return qurIpv4Addr_;
    }

    public static final int QURDNS_FIELD_NUMBER = 27;
    private com.google.protobuf.ByteString qurDNS_;
    /**
     * <code>optional bytes qurDNS = 27;</code>
     */
    public boolean hasQurDNS() {
      return ((bitField0_ & 0x04000000) == 0x04000000);
    }
    /**
     * <code>optional bytes qurDNS = 27;</code>
     */
    public com.google.protobuf.ByteString getQurDNS() {
      return qurDNS_;
    }

    public static final int NDPLIFETIME_FIELD_NUMBER = 28;
    private int ndpLifeTime_;
    /**
     * <code>optional uint32 ndpLifeTime = 28;</code>
     */
    public boolean hasNdpLifeTime() {
      return ((bitField0_ & 0x08000000) == 0x08000000);
    }
    /**
     * <code>optional uint32 ndpLifeTime = 28;</code>
     */
    public int getNdpLifeTime() {
      return ndpLifeTime_;
    }

    public static final int NDPLINKADDR_FIELD_NUMBER = 29;
    private com.google.protobuf.ByteString ndpLinkAddr_;
    /**
     * <code>optional bytes ndpLinkAddr = 29;</code>
     */
    public boolean hasNdpLinkAddr() {
      return ((bitField0_ & 0x10000000) == 0x10000000);
    }
    /**
     * <code>optional bytes ndpLinkAddr = 29;</code>
     */
    public com.google.protobuf.ByteString getNdpLinkAddr() {
      return ndpLinkAddr_;
    }

    public static final int NDPPRELEN_FIELD_NUMBER = 30;
    private int ndpPreLen_;
    /**
     * <code>optional uint32 ndpPreLen = 30;</code>
     */
    public boolean hasNdpPreLen() {
      return ((bitField0_ & 0x20000000) == 0x20000000);
    }
    /**
     * <code>optional uint32 ndpPreLen = 30;</code>
     */
    public int getNdpPreLen() {
      return ndpPreLen_;
    }

    public static final int NDPPREFIX_FIELD_NUMBER = 31;
    private com.google.protobuf.ByteString ndpPreFix_;
    /**
     * <code>optional bytes ndpPreFix = 31;</code>
     */
    public boolean hasNdpPreFix() {
      return ((bitField0_ & 0x40000000) == 0x40000000);
    }
    /**
     * <code>optional bytes ndpPreFix = 31;</code>
     */
    public com.google.protobuf.ByteString getNdpPreFix() {
      return ndpPreFix_;
    }

    public static final int NDPVALLIFETIME_FIELD_NUMBER = 32;
    private int ndpValLifeTime_;
    /**
     * <code>optional uint32 ndpValLifeTime = 32;</code>
     */
    public boolean hasNdpValLifeTime() {
      return ((bitField0_ & 0x80000000) == 0x80000000);
    }
    /**
     * <code>optional uint32 ndpValLifeTime = 32;</code>
     */
    public int getNdpValLifeTime() {
      return ndpValLifeTime_;
    }

    public static final int NDPCURMTU_FIELD_NUMBER = 33;
    private int ndpCurMtu_;
    /**
     * <code>optional uint32 ndpCurMtu = 33;</code>
     */
    public boolean hasNdpCurMtu() {
      return ((bitField1_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional uint32 ndpCurMtu = 33;</code>
     */
    public int getNdpCurMtu() {
      return ndpCurMtu_;
    }

    public static final int NDPTARADDR_FIELD_NUMBER = 34;
    private com.google.protobuf.ByteString ndpTarAddr_;
    /**
     * <code>optional bytes ndpTarAddr = 34;</code>
     */
    public boolean hasNdpTarAddr() {
      return ((bitField1_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional bytes ndpTarAddr = 34;</code>
     */
    public com.google.protobuf.ByteString getNdpTarAddr() {
      return ndpTarAddr_;
    }

    public static final int NEXTHOPMTU_FIELD_NUMBER = 35;
    private int nextHopMtu_;
    /**
     * <code>optional uint32 nextHopMtu = 35;</code>
     */
    public boolean hasNextHopMtu() {
      return ((bitField1_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional uint32 nextHopMtu = 35;</code>
     */
    public int getNextHopMtu() {
      return nextHopMtu_;
    }

    public static final int EXCPOINTER_FIELD_NUMBER = 36;
    private int excPointer_;
    /**
     * <code>optional uint32 excPointer = 36;</code>
     */
    public boolean hasExcPointer() {
      return ((bitField1_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional uint32 excPointer = 36;</code>
     */
    public int getExcPointer() {
      return excPointer_;
    }

    public static final int MULCASTADDR_FIELD_NUMBER = 37;
    private com.google.protobuf.ByteString mulCastAddr_;
    /**
     * <code>optional bytes mulCastAddr = 37;</code>
     */
    public boolean hasMulCastAddr() {
      return ((bitField1_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional bytes mulCastAddr = 37;</code>
     */
    public com.google.protobuf.ByteString getMulCastAddr() {
      return mulCastAddr_;
    }

    public static final int CHECKSUM_FIELD_NUMBER = 38;
    private int checkSum_;
    /**
     * <code>optional uint32 checkSum = 38;</code>
     */
    public boolean hasCheckSum() {
      return ((bitField1_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional uint32 checkSum = 38;</code>
     */
    public int getCheckSum() {
      return checkSum_;
    }

    public static final int CHECKSUMREPLY_FIELD_NUMBER = 39;
    private int checkSumReply_;
    /**
     * <code>optional uint32 checkSumReply = 39;</code>
     */
    public boolean hasCheckSumReply() {
      return ((bitField1_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional uint32 checkSumReply = 39;</code>
     */
    public int getCheckSumReply() {
      return checkSumReply_;
    }

    public static final int RTRADDR_FIELD_NUMBER = 40;
    private int rtraddr_;
    /**
     * <code>optional uint32 rtraddr = 40;</code>
     */
    public boolean hasRtraddr() {
      return ((bitField1_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional uint32 rtraddr = 40;</code>
     */
    public int getRtraddr() {
      return rtraddr_;
    }

    public static final int RESTIME_FIELD_NUMBER = 41;
    private long resTime_;
    /**
     * <code>optional uint64 resTime = 41;</code>
     */
    public boolean hasResTime() {
      return ((bitField1_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional uint64 resTime = 41;</code>
     */
    public long getResTime() {
      return resTime_;
    }

    public static final int EXCTTL_FIELD_NUMBER = 42;
    private int excTTL_;
    /**
     * <code>optional uint32 excTTL = 42;</code>
     */
    public boolean hasExcTTL() {
      return ((bitField1_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>optional uint32 excTTL = 42;</code>
     */
    public int getExcTTL() {
      return excTTL_;
    }

    public static final int RESPONSETIME_FIELD_NUMBER = 43;
    private long responseTime_;
    /**
     * <code>optional uint64 ResponseTime = 43;</code>
     */
    public boolean hasResponseTime() {
      return ((bitField1_ & 0x00000400) == 0x00000400);
    }
    /**
     * <code>optional uint64 ResponseTime = 43;</code>
     */
    public long getResponseTime() {
      return responseTime_;
    }

    public static final int UNREACHABLESOURCEPORT_FIELD_NUMBER = 44;
    private int unreachableSourcePort_;
    /**
     * <code>optional uint32 unreachableSourcePort = 44;</code>
     */
    public boolean hasUnreachableSourcePort() {
      return ((bitField1_ & 0x00000800) == 0x00000800);
    }
    /**
     * <code>optional uint32 unreachableSourcePort = 44;</code>
     */
    public int getUnreachableSourcePort() {
      return unreachableSourcePort_;
    }

    public static final int UNREACHABLEDESTINATIONPORT_FIELD_NUMBER = 45;
    private int unreachableDestinationPort_;
    /**
     * <code>optional uint32 unreachableDestinationPort = 45;</code>
     */
    public boolean hasUnreachableDestinationPort() {
      return ((bitField1_ & 0x00001000) == 0x00001000);
    }
    /**
     * <code>optional uint32 unreachableDestinationPort = 45;</code>
     */
    public int getUnreachableDestinationPort() {
      return unreachableDestinationPort_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeUInt32(1, msgType_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeUInt32(2, infoCode_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeUInt32(3, echoSeqNum_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, dataCon_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeBytes(5, unrSrcAddr_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeBytes(6, unrDstAddr_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeUInt32(7, unrProt_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeUInt32(8, uncTTL_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        output.writeUInt32(9, ver_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        output.writeUInt64(10, origTimeStamp_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        output.writeUInt64(11, recvTimeStamp_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        output.writeUInt64(12, transTimeStamp_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        output.writeUInt32(13, mask_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        output.writeUInt32(14, subNetId_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        output.writeUInt32(15, rtrTimeOut_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        output.writeBytes(16, excSrcAddr_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        output.writeBytes(17, excDstAddr_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        output.writeUInt32(18, excProt_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        output.writeUInt32(19, excSrcPort_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        output.writeUInt32(20, excDstPort_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        output.writeBytes(21, gwAddr_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        output.writeUInt32(22, ttl_);
      }
      if (((bitField0_ & 0x00400000) == 0x00400000)) {
        output.writeUInt32(23, repTtl_);
      }
      if (((bitField0_ & 0x00800000) == 0x00800000)) {
        output.writeUInt32(24, qurType_);
      }
      if (((bitField0_ & 0x01000000) == 0x01000000)) {
        output.writeBytes(25, qurIpv6Addr_);
      }
      if (((bitField0_ & 0x02000000) == 0x02000000)) {
        output.writeBytes(26, qurIpv4Addr_);
      }
      if (((bitField0_ & 0x04000000) == 0x04000000)) {
        output.writeBytes(27, qurDNS_);
      }
      if (((bitField0_ & 0x08000000) == 0x08000000)) {
        output.writeUInt32(28, ndpLifeTime_);
      }
      if (((bitField0_ & 0x10000000) == 0x10000000)) {
        output.writeBytes(29, ndpLinkAddr_);
      }
      if (((bitField0_ & 0x20000000) == 0x20000000)) {
        output.writeUInt32(30, ndpPreLen_);
      }
      if (((bitField0_ & 0x40000000) == 0x40000000)) {
        output.writeBytes(31, ndpPreFix_);
      }
      if (((bitField0_ & 0x80000000) == 0x80000000)) {
        output.writeUInt32(32, ndpValLifeTime_);
      }
      if (((bitField1_ & 0x00000001) == 0x00000001)) {
        output.writeUInt32(33, ndpCurMtu_);
      }
      if (((bitField1_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(34, ndpTarAddr_);
      }
      if (((bitField1_ & 0x00000004) == 0x00000004)) {
        output.writeUInt32(35, nextHopMtu_);
      }
      if (((bitField1_ & 0x00000008) == 0x00000008)) {
        output.writeUInt32(36, excPointer_);
      }
      if (((bitField1_ & 0x00000010) == 0x00000010)) {
        output.writeBytes(37, mulCastAddr_);
      }
      if (((bitField1_ & 0x00000020) == 0x00000020)) {
        output.writeUInt32(38, checkSum_);
      }
      if (((bitField1_ & 0x00000040) == 0x00000040)) {
        output.writeUInt32(39, checkSumReply_);
      }
      if (((bitField1_ & 0x00000080) == 0x00000080)) {
        output.writeUInt32(40, rtraddr_);
      }
      if (((bitField1_ & 0x00000100) == 0x00000100)) {
        output.writeUInt64(41, resTime_);
      }
      if (((bitField1_ & 0x00000200) == 0x00000200)) {
        output.writeUInt32(42, excTTL_);
      }
      if (((bitField1_ & 0x00000400) == 0x00000400)) {
        output.writeUInt64(43, responseTime_);
      }
      if (((bitField1_ & 0x00000800) == 0x00000800)) {
        output.writeUInt32(44, unreachableSourcePort_);
      }
      if (((bitField1_ & 0x00001000) == 0x00001000)) {
        output.writeUInt32(45, unreachableDestinationPort_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, msgType_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, infoCode_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, echoSeqNum_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, dataCon_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(5, unrSrcAddr_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, unrDstAddr_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(7, unrProt_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(8, uncTTL_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(9, ver_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(10, origTimeStamp_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(11, recvTimeStamp_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(12, transTimeStamp_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(13, mask_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(14, subNetId_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(15, rtrTimeOut_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(16, excSrcAddr_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(17, excDstAddr_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(18, excProt_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(19, excSrcPort_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(20, excDstPort_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(21, gwAddr_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(22, ttl_);
      }
      if (((bitField0_ & 0x00400000) == 0x00400000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(23, repTtl_);
      }
      if (((bitField0_ & 0x00800000) == 0x00800000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(24, qurType_);
      }
      if (((bitField0_ & 0x01000000) == 0x01000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(25, qurIpv6Addr_);
      }
      if (((bitField0_ & 0x02000000) == 0x02000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(26, qurIpv4Addr_);
      }
      if (((bitField0_ & 0x04000000) == 0x04000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(27, qurDNS_);
      }
      if (((bitField0_ & 0x08000000) == 0x08000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(28, ndpLifeTime_);
      }
      if (((bitField0_ & 0x10000000) == 0x10000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(29, ndpLinkAddr_);
      }
      if (((bitField0_ & 0x20000000) == 0x20000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(30, ndpPreLen_);
      }
      if (((bitField0_ & 0x40000000) == 0x40000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(31, ndpPreFix_);
      }
      if (((bitField0_ & 0x80000000) == 0x80000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(32, ndpValLifeTime_);
      }
      if (((bitField1_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(33, ndpCurMtu_);
      }
      if (((bitField1_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(34, ndpTarAddr_);
      }
      if (((bitField1_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(35, nextHopMtu_);
      }
      if (((bitField1_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(36, excPointer_);
      }
      if (((bitField1_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(37, mulCastAddr_);
      }
      if (((bitField1_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(38, checkSum_);
      }
      if (((bitField1_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(39, checkSumReply_);
      }
      if (((bitField1_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(40, rtraddr_);
      }
      if (((bitField1_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(41, resTime_);
      }
      if (((bitField1_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(42, excTTL_);
      }
      if (((bitField1_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(43, responseTime_);
      }
      if (((bitField1_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(44, unreachableSourcePort_);
      }
      if (((bitField1_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(45, unreachableDestinationPort_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof IcmpInfoOuterClass.IcmpInfo)) {
        return super.equals(obj);
      }
      IcmpInfoOuterClass.IcmpInfo other = (IcmpInfoOuterClass.IcmpInfo) obj;

      boolean result = true;
      result = result && (hasMsgType() == other.hasMsgType());
      if (hasMsgType()) {
        result = result && (getMsgType()
            == other.getMsgType());
      }
      result = result && (hasInfoCode() == other.hasInfoCode());
      if (hasInfoCode()) {
        result = result && (getInfoCode()
            == other.getInfoCode());
      }
      result = result && (hasEchoSeqNum() == other.hasEchoSeqNum());
      if (hasEchoSeqNum()) {
        result = result && (getEchoSeqNum()
            == other.getEchoSeqNum());
      }
      result = result && (hasDataCon() == other.hasDataCon());
      if (hasDataCon()) {
        result = result && getDataCon()
            .equals(other.getDataCon());
      }
      result = result && (hasUnrSrcAddr() == other.hasUnrSrcAddr());
      if (hasUnrSrcAddr()) {
        result = result && getUnrSrcAddr()
            .equals(other.getUnrSrcAddr());
      }
      result = result && (hasUnrDstAddr() == other.hasUnrDstAddr());
      if (hasUnrDstAddr()) {
        result = result && getUnrDstAddr()
            .equals(other.getUnrDstAddr());
      }
      result = result && (hasUnrProt() == other.hasUnrProt());
      if (hasUnrProt()) {
        result = result && (getUnrProt()
            == other.getUnrProt());
      }
      result = result && (hasUncTTL() == other.hasUncTTL());
      if (hasUncTTL()) {
        result = result && (getUncTTL()
            == other.getUncTTL());
      }
      result = result && (hasVer() == other.hasVer());
      if (hasVer()) {
        result = result && (getVer()
            == other.getVer());
      }
      result = result && (hasOrigTimeStamp() == other.hasOrigTimeStamp());
      if (hasOrigTimeStamp()) {
        result = result && (getOrigTimeStamp()
            == other.getOrigTimeStamp());
      }
      result = result && (hasRecvTimeStamp() == other.hasRecvTimeStamp());
      if (hasRecvTimeStamp()) {
        result = result && (getRecvTimeStamp()
            == other.getRecvTimeStamp());
      }
      result = result && (hasTransTimeStamp() == other.hasTransTimeStamp());
      if (hasTransTimeStamp()) {
        result = result && (getTransTimeStamp()
            == other.getTransTimeStamp());
      }
      result = result && (hasMask() == other.hasMask());
      if (hasMask()) {
        result = result && (getMask()
            == other.getMask());
      }
      result = result && (hasSubNetId() == other.hasSubNetId());
      if (hasSubNetId()) {
        result = result && (getSubNetId()
            == other.getSubNetId());
      }
      result = result && (hasRtrTimeOut() == other.hasRtrTimeOut());
      if (hasRtrTimeOut()) {
        result = result && (getRtrTimeOut()
            == other.getRtrTimeOut());
      }
      result = result && (hasExcSrcAddr() == other.hasExcSrcAddr());
      if (hasExcSrcAddr()) {
        result = result && getExcSrcAddr()
            .equals(other.getExcSrcAddr());
      }
      result = result && (hasExcDstAddr() == other.hasExcDstAddr());
      if (hasExcDstAddr()) {
        result = result && getExcDstAddr()
            .equals(other.getExcDstAddr());
      }
      result = result && (hasExcProt() == other.hasExcProt());
      if (hasExcProt()) {
        result = result && (getExcProt()
            == other.getExcProt());
      }
      result = result && (hasExcSrcPort() == other.hasExcSrcPort());
      if (hasExcSrcPort()) {
        result = result && (getExcSrcPort()
            == other.getExcSrcPort());
      }
      result = result && (hasExcDstPort() == other.hasExcDstPort());
      if (hasExcDstPort()) {
        result = result && (getExcDstPort()
            == other.getExcDstPort());
      }
      result = result && (hasGwAddr() == other.hasGwAddr());
      if (hasGwAddr()) {
        result = result && getGwAddr()
            .equals(other.getGwAddr());
      }
      result = result && (hasTtl() == other.hasTtl());
      if (hasTtl()) {
        result = result && (getTtl()
            == other.getTtl());
      }
      result = result && (hasRepTtl() == other.hasRepTtl());
      if (hasRepTtl()) {
        result = result && (getRepTtl()
            == other.getRepTtl());
      }
      result = result && (hasQurType() == other.hasQurType());
      if (hasQurType()) {
        result = result && (getQurType()
            == other.getQurType());
      }
      result = result && (hasQurIpv6Addr() == other.hasQurIpv6Addr());
      if (hasQurIpv6Addr()) {
        result = result && getQurIpv6Addr()
            .equals(other.getQurIpv6Addr());
      }
      result = result && (hasQurIpv4Addr() == other.hasQurIpv4Addr());
      if (hasQurIpv4Addr()) {
        result = result && getQurIpv4Addr()
            .equals(other.getQurIpv4Addr());
      }
      result = result && (hasQurDNS() == other.hasQurDNS());
      if (hasQurDNS()) {
        result = result && getQurDNS()
            .equals(other.getQurDNS());
      }
      result = result && (hasNdpLifeTime() == other.hasNdpLifeTime());
      if (hasNdpLifeTime()) {
        result = result && (getNdpLifeTime()
            == other.getNdpLifeTime());
      }
      result = result && (hasNdpLinkAddr() == other.hasNdpLinkAddr());
      if (hasNdpLinkAddr()) {
        result = result && getNdpLinkAddr()
            .equals(other.getNdpLinkAddr());
      }
      result = result && (hasNdpPreLen() == other.hasNdpPreLen());
      if (hasNdpPreLen()) {
        result = result && (getNdpPreLen()
            == other.getNdpPreLen());
      }
      result = result && (hasNdpPreFix() == other.hasNdpPreFix());
      if (hasNdpPreFix()) {
        result = result && getNdpPreFix()
            .equals(other.getNdpPreFix());
      }
      result = result && (hasNdpValLifeTime() == other.hasNdpValLifeTime());
      if (hasNdpValLifeTime()) {
        result = result && (getNdpValLifeTime()
            == other.getNdpValLifeTime());
      }
      result = result && (hasNdpCurMtu() == other.hasNdpCurMtu());
      if (hasNdpCurMtu()) {
        result = result && (getNdpCurMtu()
            == other.getNdpCurMtu());
      }
      result = result && (hasNdpTarAddr() == other.hasNdpTarAddr());
      if (hasNdpTarAddr()) {
        result = result && getNdpTarAddr()
            .equals(other.getNdpTarAddr());
      }
      result = result && (hasNextHopMtu() == other.hasNextHopMtu());
      if (hasNextHopMtu()) {
        result = result && (getNextHopMtu()
            == other.getNextHopMtu());
      }
      result = result && (hasExcPointer() == other.hasExcPointer());
      if (hasExcPointer()) {
        result = result && (getExcPointer()
            == other.getExcPointer());
      }
      result = result && (hasMulCastAddr() == other.hasMulCastAddr());
      if (hasMulCastAddr()) {
        result = result && getMulCastAddr()
            .equals(other.getMulCastAddr());
      }
      result = result && (hasCheckSum() == other.hasCheckSum());
      if (hasCheckSum()) {
        result = result && (getCheckSum()
            == other.getCheckSum());
      }
      result = result && (hasCheckSumReply() == other.hasCheckSumReply());
      if (hasCheckSumReply()) {
        result = result && (getCheckSumReply()
            == other.getCheckSumReply());
      }
      result = result && (hasRtraddr() == other.hasRtraddr());
      if (hasRtraddr()) {
        result = result && (getRtraddr()
            == other.getRtraddr());
      }
      result = result && (hasResTime() == other.hasResTime());
      if (hasResTime()) {
        result = result && (getResTime()
            == other.getResTime());
      }
      result = result && (hasExcTTL() == other.hasExcTTL());
      if (hasExcTTL()) {
        result = result && (getExcTTL()
            == other.getExcTTL());
      }
      result = result && (hasResponseTime() == other.hasResponseTime());
      if (hasResponseTime()) {
        result = result && (getResponseTime()
            == other.getResponseTime());
      }
      result = result && (hasUnreachableSourcePort() == other.hasUnreachableSourcePort());
      if (hasUnreachableSourcePort()) {
        result = result && (getUnreachableSourcePort()
            == other.getUnreachableSourcePort());
      }
      result = result && (hasUnreachableDestinationPort() == other.hasUnreachableDestinationPort());
      if (hasUnreachableDestinationPort()) {
        result = result && (getUnreachableDestinationPort()
            == other.getUnreachableDestinationPort());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMsgType()) {
        hash = (37 * hash) + MSGTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getMsgType();
      }
      if (hasInfoCode()) {
        hash = (37 * hash) + INFOCODE_FIELD_NUMBER;
        hash = (53 * hash) + getInfoCode();
      }
      if (hasEchoSeqNum()) {
        hash = (37 * hash) + ECHOSEQNUM_FIELD_NUMBER;
        hash = (53 * hash) + getEchoSeqNum();
      }
      if (hasDataCon()) {
        hash = (37 * hash) + DATACON_FIELD_NUMBER;
        hash = (53 * hash) + getDataCon().hashCode();
      }
      if (hasUnrSrcAddr()) {
        hash = (37 * hash) + UNRSRCADDR_FIELD_NUMBER;
        hash = (53 * hash) + getUnrSrcAddr().hashCode();
      }
      if (hasUnrDstAddr()) {
        hash = (37 * hash) + UNRDSTADDR_FIELD_NUMBER;
        hash = (53 * hash) + getUnrDstAddr().hashCode();
      }
      if (hasUnrProt()) {
        hash = (37 * hash) + UNRPROT_FIELD_NUMBER;
        hash = (53 * hash) + getUnrProt();
      }
      if (hasUncTTL()) {
        hash = (37 * hash) + UNCTTL_FIELD_NUMBER;
        hash = (53 * hash) + getUncTTL();
      }
      if (hasVer()) {
        hash = (37 * hash) + VER_FIELD_NUMBER;
        hash = (53 * hash) + getVer();
      }
      if (hasOrigTimeStamp()) {
        hash = (37 * hash) + ORIGTIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOrigTimeStamp());
      }
      if (hasRecvTimeStamp()) {
        hash = (37 * hash) + RECVTIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getRecvTimeStamp());
      }
      if (hasTransTimeStamp()) {
        hash = (37 * hash) + TRANSTIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTransTimeStamp());
      }
      if (hasMask()) {
        hash = (37 * hash) + MASK_FIELD_NUMBER;
        hash = (53 * hash) + getMask();
      }
      if (hasSubNetId()) {
        hash = (37 * hash) + SUBNETID_FIELD_NUMBER;
        hash = (53 * hash) + getSubNetId();
      }
      if (hasRtrTimeOut()) {
        hash = (37 * hash) + RTRTIMEOUT_FIELD_NUMBER;
        hash = (53 * hash) + getRtrTimeOut();
      }
      if (hasExcSrcAddr()) {
        hash = (37 * hash) + EXCSRCADDR_FIELD_NUMBER;
        hash = (53 * hash) + getExcSrcAddr().hashCode();
      }
      if (hasExcDstAddr()) {
        hash = (37 * hash) + EXCDSTADDR_FIELD_NUMBER;
        hash = (53 * hash) + getExcDstAddr().hashCode();
      }
      if (hasExcProt()) {
        hash = (37 * hash) + EXCPROT_FIELD_NUMBER;
        hash = (53 * hash) + getExcProt();
      }
      if (hasExcSrcPort()) {
        hash = (37 * hash) + EXCSRCPORT_FIELD_NUMBER;
        hash = (53 * hash) + getExcSrcPort();
      }
      if (hasExcDstPort()) {
        hash = (37 * hash) + EXCDSTPORT_FIELD_NUMBER;
        hash = (53 * hash) + getExcDstPort();
      }
      if (hasGwAddr()) {
        hash = (37 * hash) + GWADDR_FIELD_NUMBER;
        hash = (53 * hash) + getGwAddr().hashCode();
      }
      if (hasTtl()) {
        hash = (37 * hash) + TTL_FIELD_NUMBER;
        hash = (53 * hash) + getTtl();
      }
      if (hasRepTtl()) {
        hash = (37 * hash) + REPTTL_FIELD_NUMBER;
        hash = (53 * hash) + getRepTtl();
      }
      if (hasQurType()) {
        hash = (37 * hash) + QURTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getQurType();
      }
      if (hasQurIpv6Addr()) {
        hash = (37 * hash) + QURIPV6ADDR_FIELD_NUMBER;
        hash = (53 * hash) + getQurIpv6Addr().hashCode();
      }
      if (hasQurIpv4Addr()) {
        hash = (37 * hash) + QURIPV4ADDR_FIELD_NUMBER;
        hash = (53 * hash) + getQurIpv4Addr().hashCode();
      }
      if (hasQurDNS()) {
        hash = (37 * hash) + QURDNS_FIELD_NUMBER;
        hash = (53 * hash) + getQurDNS().hashCode();
      }
      if (hasNdpLifeTime()) {
        hash = (37 * hash) + NDPLIFETIME_FIELD_NUMBER;
        hash = (53 * hash) + getNdpLifeTime();
      }
      if (hasNdpLinkAddr()) {
        hash = (37 * hash) + NDPLINKADDR_FIELD_NUMBER;
        hash = (53 * hash) + getNdpLinkAddr().hashCode();
      }
      if (hasNdpPreLen()) {
        hash = (37 * hash) + NDPPRELEN_FIELD_NUMBER;
        hash = (53 * hash) + getNdpPreLen();
      }
      if (hasNdpPreFix()) {
        hash = (37 * hash) + NDPPREFIX_FIELD_NUMBER;
        hash = (53 * hash) + getNdpPreFix().hashCode();
      }
      if (hasNdpValLifeTime()) {
        hash = (37 * hash) + NDPVALLIFETIME_FIELD_NUMBER;
        hash = (53 * hash) + getNdpValLifeTime();
      }
      if (hasNdpCurMtu()) {
        hash = (37 * hash) + NDPCURMTU_FIELD_NUMBER;
        hash = (53 * hash) + getNdpCurMtu();
      }
      if (hasNdpTarAddr()) {
        hash = (37 * hash) + NDPTARADDR_FIELD_NUMBER;
        hash = (53 * hash) + getNdpTarAddr().hashCode();
      }
      if (hasNextHopMtu()) {
        hash = (37 * hash) + NEXTHOPMTU_FIELD_NUMBER;
        hash = (53 * hash) + getNextHopMtu();
      }
      if (hasExcPointer()) {
        hash = (37 * hash) + EXCPOINTER_FIELD_NUMBER;
        hash = (53 * hash) + getExcPointer();
      }
      if (hasMulCastAddr()) {
        hash = (37 * hash) + MULCASTADDR_FIELD_NUMBER;
        hash = (53 * hash) + getMulCastAddr().hashCode();
      }
      if (hasCheckSum()) {
        hash = (37 * hash) + CHECKSUM_FIELD_NUMBER;
        hash = (53 * hash) + getCheckSum();
      }
      if (hasCheckSumReply()) {
        hash = (37 * hash) + CHECKSUMREPLY_FIELD_NUMBER;
        hash = (53 * hash) + getCheckSumReply();
      }
      if (hasRtraddr()) {
        hash = (37 * hash) + RTRADDR_FIELD_NUMBER;
        hash = (53 * hash) + getRtraddr();
      }
      if (hasResTime()) {
        hash = (37 * hash) + RESTIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getResTime());
      }
      if (hasExcTTL()) {
        hash = (37 * hash) + EXCTTL_FIELD_NUMBER;
        hash = (53 * hash) + getExcTTL();
      }
      if (hasResponseTime()) {
        hash = (37 * hash) + RESPONSETIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getResponseTime());
      }
      if (hasUnreachableSourcePort()) {
        hash = (37 * hash) + UNREACHABLESOURCEPORT_FIELD_NUMBER;
        hash = (53 * hash) + getUnreachableSourcePort();
      }
      if (hasUnreachableDestinationPort()) {
        hash = (37 * hash) + UNREACHABLEDESTINATIONPORT_FIELD_NUMBER;
        hash = (53 * hash) + getUnreachableDestinationPort();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static IcmpInfoOuterClass.IcmpInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static IcmpInfoOuterClass.IcmpInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(IcmpInfoOuterClass.IcmpInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code IcmpInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:IcmpInfo)
        IcmpInfoOuterClass.IcmpInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return IcmpInfoOuterClass.internal_static_IcmpInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return IcmpInfoOuterClass.internal_static_IcmpInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                IcmpInfoOuterClass.IcmpInfo.class, IcmpInfoOuterClass.IcmpInfo.Builder.class);
      }

      // Construct using IcmpInfoOuterClass.IcmpInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        infoCode_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        echoSeqNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        dataCon_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000008);
        unrSrcAddr_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000010);
        unrDstAddr_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000020);
        unrProt_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        uncTTL_ = 0;
        bitField0_ = (bitField0_ & ~0x00000080);
        ver_ = 0;
        bitField0_ = (bitField0_ & ~0x00000100);
        origTimeStamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000200);
        recvTimeStamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000400);
        transTimeStamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000800);
        mask_ = 0;
        bitField0_ = (bitField0_ & ~0x00001000);
        subNetId_ = 0;
        bitField0_ = (bitField0_ & ~0x00002000);
        rtrTimeOut_ = 0;
        bitField0_ = (bitField0_ & ~0x00004000);
        excSrcAddr_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00008000);
        excDstAddr_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00010000);
        excProt_ = 0;
        bitField0_ = (bitField0_ & ~0x00020000);
        excSrcPort_ = 0;
        bitField0_ = (bitField0_ & ~0x00040000);
        excDstPort_ = 0;
        bitField0_ = (bitField0_ & ~0x00080000);
        gwAddr_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00100000);
        ttl_ = 0;
        bitField0_ = (bitField0_ & ~0x00200000);
        repTtl_ = 0;
        bitField0_ = (bitField0_ & ~0x00400000);
        qurType_ = 0;
        bitField0_ = (bitField0_ & ~0x00800000);
        qurIpv6Addr_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x01000000);
        qurIpv4Addr_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x02000000);
        qurDNS_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x04000000);
        ndpLifeTime_ = 0;
        bitField0_ = (bitField0_ & ~0x08000000);
        ndpLinkAddr_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x10000000);
        ndpPreLen_ = 0;
        bitField0_ = (bitField0_ & ~0x20000000);
        ndpPreFix_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x40000000);
        ndpValLifeTime_ = 0;
        bitField0_ = (bitField0_ & ~0x80000000);
        ndpCurMtu_ = 0;
        bitField1_ = (bitField1_ & ~0x00000001);
        ndpTarAddr_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000002);
        nextHopMtu_ = 0;
        bitField1_ = (bitField1_ & ~0x00000004);
        excPointer_ = 0;
        bitField1_ = (bitField1_ & ~0x00000008);
        mulCastAddr_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000010);
        checkSum_ = 0;
        bitField1_ = (bitField1_ & ~0x00000020);
        checkSumReply_ = 0;
        bitField1_ = (bitField1_ & ~0x00000040);
        rtraddr_ = 0;
        bitField1_ = (bitField1_ & ~0x00000080);
        resTime_ = 0L;
        bitField1_ = (bitField1_ & ~0x00000100);
        excTTL_ = 0;
        bitField1_ = (bitField1_ & ~0x00000200);
        responseTime_ = 0L;
        bitField1_ = (bitField1_ & ~0x00000400);
        unreachableSourcePort_ = 0;
        bitField1_ = (bitField1_ & ~0x00000800);
        unreachableDestinationPort_ = 0;
        bitField1_ = (bitField1_ & ~0x00001000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return IcmpInfoOuterClass.internal_static_IcmpInfo_descriptor;
      }

      @java.lang.Override
      public IcmpInfoOuterClass.IcmpInfo getDefaultInstanceForType() {
        return IcmpInfoOuterClass.IcmpInfo.getDefaultInstance();
      }

      @java.lang.Override
      public IcmpInfoOuterClass.IcmpInfo build() {
        IcmpInfoOuterClass.IcmpInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public IcmpInfoOuterClass.IcmpInfo buildPartial() {
        IcmpInfoOuterClass.IcmpInfo result = new IcmpInfoOuterClass.IcmpInfo(this);
        int from_bitField0_ = bitField0_;
        int from_bitField1_ = bitField1_;
        int to_bitField0_ = 0;
        int to_bitField1_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.msgType_ = msgType_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.infoCode_ = infoCode_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.echoSeqNum_ = echoSeqNum_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.dataCon_ = dataCon_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.unrSrcAddr_ = unrSrcAddr_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.unrDstAddr_ = unrDstAddr_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.unrProt_ = unrProt_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.uncTTL_ = uncTTL_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.ver_ = ver_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000200;
        }
        result.origTimeStamp_ = origTimeStamp_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000400;
        }
        result.recvTimeStamp_ = recvTimeStamp_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000800;
        }
        result.transTimeStamp_ = transTimeStamp_;
        if (((from_bitField0_ & 0x00001000) == 0x00001000)) {
          to_bitField0_ |= 0x00001000;
        }
        result.mask_ = mask_;
        if (((from_bitField0_ & 0x00002000) == 0x00002000)) {
          to_bitField0_ |= 0x00002000;
        }
        result.subNetId_ = subNetId_;
        if (((from_bitField0_ & 0x00004000) == 0x00004000)) {
          to_bitField0_ |= 0x00004000;
        }
        result.rtrTimeOut_ = rtrTimeOut_;
        if (((from_bitField0_ & 0x00008000) == 0x00008000)) {
          to_bitField0_ |= 0x00008000;
        }
        result.excSrcAddr_ = excSrcAddr_;
        if (((from_bitField0_ & 0x00010000) == 0x00010000)) {
          to_bitField0_ |= 0x00010000;
        }
        result.excDstAddr_ = excDstAddr_;
        if (((from_bitField0_ & 0x00020000) == 0x00020000)) {
          to_bitField0_ |= 0x00020000;
        }
        result.excProt_ = excProt_;
        if (((from_bitField0_ & 0x00040000) == 0x00040000)) {
          to_bitField0_ |= 0x00040000;
        }
        result.excSrcPort_ = excSrcPort_;
        if (((from_bitField0_ & 0x00080000) == 0x00080000)) {
          to_bitField0_ |= 0x00080000;
        }
        result.excDstPort_ = excDstPort_;
        if (((from_bitField0_ & 0x00100000) == 0x00100000)) {
          to_bitField0_ |= 0x00100000;
        }
        result.gwAddr_ = gwAddr_;
        if (((from_bitField0_ & 0x00200000) == 0x00200000)) {
          to_bitField0_ |= 0x00200000;
        }
        result.ttl_ = ttl_;
        if (((from_bitField0_ & 0x00400000) == 0x00400000)) {
          to_bitField0_ |= 0x00400000;
        }
        result.repTtl_ = repTtl_;
        if (((from_bitField0_ & 0x00800000) == 0x00800000)) {
          to_bitField0_ |= 0x00800000;
        }
        result.qurType_ = qurType_;
        if (((from_bitField0_ & 0x01000000) == 0x01000000)) {
          to_bitField0_ |= 0x01000000;
        }
        result.qurIpv6Addr_ = qurIpv6Addr_;
        if (((from_bitField0_ & 0x02000000) == 0x02000000)) {
          to_bitField0_ |= 0x02000000;
        }
        result.qurIpv4Addr_ = qurIpv4Addr_;
        if (((from_bitField0_ & 0x04000000) == 0x04000000)) {
          to_bitField0_ |= 0x04000000;
        }
        result.qurDNS_ = qurDNS_;
        if (((from_bitField0_ & 0x08000000) == 0x08000000)) {
          to_bitField0_ |= 0x08000000;
        }
        result.ndpLifeTime_ = ndpLifeTime_;
        if (((from_bitField0_ & 0x10000000) == 0x10000000)) {
          to_bitField0_ |= 0x10000000;
        }
        result.ndpLinkAddr_ = ndpLinkAddr_;
        if (((from_bitField0_ & 0x20000000) == 0x20000000)) {
          to_bitField0_ |= 0x20000000;
        }
        result.ndpPreLen_ = ndpPreLen_;
        if (((from_bitField0_ & 0x40000000) == 0x40000000)) {
          to_bitField0_ |= 0x40000000;
        }
        result.ndpPreFix_ = ndpPreFix_;
        if (((from_bitField0_ & 0x80000000) == 0x80000000)) {
          to_bitField0_ |= 0x80000000;
        }
        result.ndpValLifeTime_ = ndpValLifeTime_;
        if (((from_bitField1_ & 0x00000001) == 0x00000001)) {
          to_bitField1_ |= 0x00000001;
        }
        result.ndpCurMtu_ = ndpCurMtu_;
        if (((from_bitField1_ & 0x00000002) == 0x00000002)) {
          to_bitField1_ |= 0x00000002;
        }
        result.ndpTarAddr_ = ndpTarAddr_;
        if (((from_bitField1_ & 0x00000004) == 0x00000004)) {
          to_bitField1_ |= 0x00000004;
        }
        result.nextHopMtu_ = nextHopMtu_;
        if (((from_bitField1_ & 0x00000008) == 0x00000008)) {
          to_bitField1_ |= 0x00000008;
        }
        result.excPointer_ = excPointer_;
        if (((from_bitField1_ & 0x00000010) == 0x00000010)) {
          to_bitField1_ |= 0x00000010;
        }
        result.mulCastAddr_ = mulCastAddr_;
        if (((from_bitField1_ & 0x00000020) == 0x00000020)) {
          to_bitField1_ |= 0x00000020;
        }
        result.checkSum_ = checkSum_;
        if (((from_bitField1_ & 0x00000040) == 0x00000040)) {
          to_bitField1_ |= 0x00000040;
        }
        result.checkSumReply_ = checkSumReply_;
        if (((from_bitField1_ & 0x00000080) == 0x00000080)) {
          to_bitField1_ |= 0x00000080;
        }
        result.rtraddr_ = rtraddr_;
        if (((from_bitField1_ & 0x00000100) == 0x00000100)) {
          to_bitField1_ |= 0x00000100;
        }
        result.resTime_ = resTime_;
        if (((from_bitField1_ & 0x00000200) == 0x00000200)) {
          to_bitField1_ |= 0x00000200;
        }
        result.excTTL_ = excTTL_;
        if (((from_bitField1_ & 0x00000400) == 0x00000400)) {
          to_bitField1_ |= 0x00000400;
        }
        result.responseTime_ = responseTime_;
        if (((from_bitField1_ & 0x00000800) == 0x00000800)) {
          to_bitField1_ |= 0x00000800;
        }
        result.unreachableSourcePort_ = unreachableSourcePort_;
        if (((from_bitField1_ & 0x00001000) == 0x00001000)) {
          to_bitField1_ |= 0x00001000;
        }
        result.unreachableDestinationPort_ = unreachableDestinationPort_;
        result.bitField0_ = to_bitField0_;
        result.bitField1_ = to_bitField1_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof IcmpInfoOuterClass.IcmpInfo) {
          return mergeFrom((IcmpInfoOuterClass.IcmpInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(IcmpInfoOuterClass.IcmpInfo other) {
        if (other == IcmpInfoOuterClass.IcmpInfo.getDefaultInstance()) return this;
        if (other.hasMsgType()) {
          setMsgType(other.getMsgType());
        }
        if (other.hasInfoCode()) {
          setInfoCode(other.getInfoCode());
        }
        if (other.hasEchoSeqNum()) {
          setEchoSeqNum(other.getEchoSeqNum());
        }
        if (other.hasDataCon()) {
          setDataCon(other.getDataCon());
        }
        if (other.hasUnrSrcAddr()) {
          setUnrSrcAddr(other.getUnrSrcAddr());
        }
        if (other.hasUnrDstAddr()) {
          setUnrDstAddr(other.getUnrDstAddr());
        }
        if (other.hasUnrProt()) {
          setUnrProt(other.getUnrProt());
        }
        if (other.hasUncTTL()) {
          setUncTTL(other.getUncTTL());
        }
        if (other.hasVer()) {
          setVer(other.getVer());
        }
        if (other.hasOrigTimeStamp()) {
          setOrigTimeStamp(other.getOrigTimeStamp());
        }
        if (other.hasRecvTimeStamp()) {
          setRecvTimeStamp(other.getRecvTimeStamp());
        }
        if (other.hasTransTimeStamp()) {
          setTransTimeStamp(other.getTransTimeStamp());
        }
        if (other.hasMask()) {
          setMask(other.getMask());
        }
        if (other.hasSubNetId()) {
          setSubNetId(other.getSubNetId());
        }
        if (other.hasRtrTimeOut()) {
          setRtrTimeOut(other.getRtrTimeOut());
        }
        if (other.hasExcSrcAddr()) {
          setExcSrcAddr(other.getExcSrcAddr());
        }
        if (other.hasExcDstAddr()) {
          setExcDstAddr(other.getExcDstAddr());
        }
        if (other.hasExcProt()) {
          setExcProt(other.getExcProt());
        }
        if (other.hasExcSrcPort()) {
          setExcSrcPort(other.getExcSrcPort());
        }
        if (other.hasExcDstPort()) {
          setExcDstPort(other.getExcDstPort());
        }
        if (other.hasGwAddr()) {
          setGwAddr(other.getGwAddr());
        }
        if (other.hasTtl()) {
          setTtl(other.getTtl());
        }
        if (other.hasRepTtl()) {
          setRepTtl(other.getRepTtl());
        }
        if (other.hasQurType()) {
          setQurType(other.getQurType());
        }
        if (other.hasQurIpv6Addr()) {
          setQurIpv6Addr(other.getQurIpv6Addr());
        }
        if (other.hasQurIpv4Addr()) {
          setQurIpv4Addr(other.getQurIpv4Addr());
        }
        if (other.hasQurDNS()) {
          setQurDNS(other.getQurDNS());
        }
        if (other.hasNdpLifeTime()) {
          setNdpLifeTime(other.getNdpLifeTime());
        }
        if (other.hasNdpLinkAddr()) {
          setNdpLinkAddr(other.getNdpLinkAddr());
        }
        if (other.hasNdpPreLen()) {
          setNdpPreLen(other.getNdpPreLen());
        }
        if (other.hasNdpPreFix()) {
          setNdpPreFix(other.getNdpPreFix());
        }
        if (other.hasNdpValLifeTime()) {
          setNdpValLifeTime(other.getNdpValLifeTime());
        }
        if (other.hasNdpCurMtu()) {
          setNdpCurMtu(other.getNdpCurMtu());
        }
        if (other.hasNdpTarAddr()) {
          setNdpTarAddr(other.getNdpTarAddr());
        }
        if (other.hasNextHopMtu()) {
          setNextHopMtu(other.getNextHopMtu());
        }
        if (other.hasExcPointer()) {
          setExcPointer(other.getExcPointer());
        }
        if (other.hasMulCastAddr()) {
          setMulCastAddr(other.getMulCastAddr());
        }
        if (other.hasCheckSum()) {
          setCheckSum(other.getCheckSum());
        }
        if (other.hasCheckSumReply()) {
          setCheckSumReply(other.getCheckSumReply());
        }
        if (other.hasRtraddr()) {
          setRtraddr(other.getRtraddr());
        }
        if (other.hasResTime()) {
          setResTime(other.getResTime());
        }
        if (other.hasExcTTL()) {
          setExcTTL(other.getExcTTL());
        }
        if (other.hasResponseTime()) {
          setResponseTime(other.getResponseTime());
        }
        if (other.hasUnreachableSourcePort()) {
          setUnreachableSourcePort(other.getUnreachableSourcePort());
        }
        if (other.hasUnreachableDestinationPort()) {
          setUnreachableDestinationPort(other.getUnreachableDestinationPort());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        IcmpInfoOuterClass.IcmpInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (IcmpInfoOuterClass.IcmpInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;
      private int bitField1_;

      private int msgType_ ;
      /**
       * <code>optional uint32 msgType = 1;</code>
       */
      public boolean hasMsgType() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional uint32 msgType = 1;</code>
       */
      public int getMsgType() {
        return msgType_;
      }
      /**
       * <code>optional uint32 msgType = 1;</code>
       */
      public Builder setMsgType(int value) {
        bitField0_ |= 0x00000001;
        msgType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 msgType = 1;</code>
       */
      public Builder clearMsgType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        msgType_ = 0;
        onChanged();
        return this;
      }

      private int infoCode_ ;
      /**
       * <code>optional uint32 infoCode = 2;</code>
       */
      public boolean hasInfoCode() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional uint32 infoCode = 2;</code>
       */
      public int getInfoCode() {
        return infoCode_;
      }
      /**
       * <code>optional uint32 infoCode = 2;</code>
       */
      public Builder setInfoCode(int value) {
        bitField0_ |= 0x00000002;
        infoCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 infoCode = 2;</code>
       */
      public Builder clearInfoCode() {
        bitField0_ = (bitField0_ & ~0x00000002);
        infoCode_ = 0;
        onChanged();
        return this;
      }

      private int echoSeqNum_ ;
      /**
       * <code>optional uint32 echoSeqNum = 3;</code>
       */
      public boolean hasEchoSeqNum() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional uint32 echoSeqNum = 3;</code>
       */
      public int getEchoSeqNum() {
        return echoSeqNum_;
      }
      /**
       * <code>optional uint32 echoSeqNum = 3;</code>
       */
      public Builder setEchoSeqNum(int value) {
        bitField0_ |= 0x00000004;
        echoSeqNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 echoSeqNum = 3;</code>
       */
      public Builder clearEchoSeqNum() {
        bitField0_ = (bitField0_ & ~0x00000004);
        echoSeqNum_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dataCon_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes dataCon = 4;</code>
       */
      public boolean hasDataCon() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional bytes dataCon = 4;</code>
       */
      public com.google.protobuf.ByteString getDataCon() {
        return dataCon_;
      }
      /**
       * <code>optional bytes dataCon = 4;</code>
       */
      public Builder setDataCon(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        dataCon_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes dataCon = 4;</code>
       */
      public Builder clearDataCon() {
        bitField0_ = (bitField0_ & ~0x00000008);
        dataCon_ = getDefaultInstance().getDataCon();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString unrSrcAddr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes unrSrcAddr = 5;</code>
       */
      public boolean hasUnrSrcAddr() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional bytes unrSrcAddr = 5;</code>
       */
      public com.google.protobuf.ByteString getUnrSrcAddr() {
        return unrSrcAddr_;
      }
      /**
       * <code>optional bytes unrSrcAddr = 5;</code>
       */
      public Builder setUnrSrcAddr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        unrSrcAddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes unrSrcAddr = 5;</code>
       */
      public Builder clearUnrSrcAddr() {
        bitField0_ = (bitField0_ & ~0x00000010);
        unrSrcAddr_ = getDefaultInstance().getUnrSrcAddr();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString unrDstAddr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes unrDstAddr = 6;</code>
       */
      public boolean hasUnrDstAddr() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional bytes unrDstAddr = 6;</code>
       */
      public com.google.protobuf.ByteString getUnrDstAddr() {
        return unrDstAddr_;
      }
      /**
       * <code>optional bytes unrDstAddr = 6;</code>
       */
      public Builder setUnrDstAddr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        unrDstAddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes unrDstAddr = 6;</code>
       */
      public Builder clearUnrDstAddr() {
        bitField0_ = (bitField0_ & ~0x00000020);
        unrDstAddr_ = getDefaultInstance().getUnrDstAddr();
        onChanged();
        return this;
      }

      private int unrProt_ ;
      /**
       * <code>optional uint32 unrProt = 7;</code>
       */
      public boolean hasUnrProt() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional uint32 unrProt = 7;</code>
       */
      public int getUnrProt() {
        return unrProt_;
      }
      /**
       * <code>optional uint32 unrProt = 7;</code>
       */
      public Builder setUnrProt(int value) {
        bitField0_ |= 0x00000040;
        unrProt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 unrProt = 7;</code>
       */
      public Builder clearUnrProt() {
        bitField0_ = (bitField0_ & ~0x00000040);
        unrProt_ = 0;
        onChanged();
        return this;
      }

      private int uncTTL_ ;
      /**
       * <code>optional uint32 uncTTL = 8;</code>
       */
      public boolean hasUncTTL() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional uint32 uncTTL = 8;</code>
       */
      public int getUncTTL() {
        return uncTTL_;
      }
      /**
       * <code>optional uint32 uncTTL = 8;</code>
       */
      public Builder setUncTTL(int value) {
        bitField0_ |= 0x00000080;
        uncTTL_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 uncTTL = 8;</code>
       */
      public Builder clearUncTTL() {
        bitField0_ = (bitField0_ & ~0x00000080);
        uncTTL_ = 0;
        onChanged();
        return this;
      }

      private int ver_ ;
      /**
       * <code>optional uint32 ver = 9;</code>
       */
      public boolean hasVer() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional uint32 ver = 9;</code>
       */
      public int getVer() {
        return ver_;
      }
      /**
       * <code>optional uint32 ver = 9;</code>
       */
      public Builder setVer(int value) {
        bitField0_ |= 0x00000100;
        ver_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ver = 9;</code>
       */
      public Builder clearVer() {
        bitField0_ = (bitField0_ & ~0x00000100);
        ver_ = 0;
        onChanged();
        return this;
      }

      private long origTimeStamp_ ;
      /**
       * <code>optional uint64 origTimeStamp = 10;</code>
       */
      public boolean hasOrigTimeStamp() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>optional uint64 origTimeStamp = 10;</code>
       */
      public long getOrigTimeStamp() {
        return origTimeStamp_;
      }
      /**
       * <code>optional uint64 origTimeStamp = 10;</code>
       */
      public Builder setOrigTimeStamp(long value) {
        bitField0_ |= 0x00000200;
        origTimeStamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 origTimeStamp = 10;</code>
       */
      public Builder clearOrigTimeStamp() {
        bitField0_ = (bitField0_ & ~0x00000200);
        origTimeStamp_ = 0L;
        onChanged();
        return this;
      }

      private long recvTimeStamp_ ;
      /**
       * <code>optional uint64 recvTimeStamp = 11;</code>
       */
      public boolean hasRecvTimeStamp() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>optional uint64 recvTimeStamp = 11;</code>
       */
      public long getRecvTimeStamp() {
        return recvTimeStamp_;
      }
      /**
       * <code>optional uint64 recvTimeStamp = 11;</code>
       */
      public Builder setRecvTimeStamp(long value) {
        bitField0_ |= 0x00000400;
        recvTimeStamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 recvTimeStamp = 11;</code>
       */
      public Builder clearRecvTimeStamp() {
        bitField0_ = (bitField0_ & ~0x00000400);
        recvTimeStamp_ = 0L;
        onChanged();
        return this;
      }

      private long transTimeStamp_ ;
      /**
       * <code>optional uint64 transTimeStamp = 12;</code>
       */
      public boolean hasTransTimeStamp() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>optional uint64 transTimeStamp = 12;</code>
       */
      public long getTransTimeStamp() {
        return transTimeStamp_;
      }
      /**
       * <code>optional uint64 transTimeStamp = 12;</code>
       */
      public Builder setTransTimeStamp(long value) {
        bitField0_ |= 0x00000800;
        transTimeStamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 transTimeStamp = 12;</code>
       */
      public Builder clearTransTimeStamp() {
        bitField0_ = (bitField0_ & ~0x00000800);
        transTimeStamp_ = 0L;
        onChanged();
        return this;
      }

      private int mask_ ;
      /**
       * <code>optional uint32 mask = 13;</code>
       */
      public boolean hasMask() {
        return ((bitField0_ & 0x00001000) == 0x00001000);
      }
      /**
       * <code>optional uint32 mask = 13;</code>
       */
      public int getMask() {
        return mask_;
      }
      /**
       * <code>optional uint32 mask = 13;</code>
       */
      public Builder setMask(int value) {
        bitField0_ |= 0x00001000;
        mask_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 mask = 13;</code>
       */
      public Builder clearMask() {
        bitField0_ = (bitField0_ & ~0x00001000);
        mask_ = 0;
        onChanged();
        return this;
      }

      private int subNetId_ ;
      /**
       * <code>optional uint32 subNetId = 14;</code>
       */
      public boolean hasSubNetId() {
        return ((bitField0_ & 0x00002000) == 0x00002000);
      }
      /**
       * <code>optional uint32 subNetId = 14;</code>
       */
      public int getSubNetId() {
        return subNetId_;
      }
      /**
       * <code>optional uint32 subNetId = 14;</code>
       */
      public Builder setSubNetId(int value) {
        bitField0_ |= 0x00002000;
        subNetId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 subNetId = 14;</code>
       */
      public Builder clearSubNetId() {
        bitField0_ = (bitField0_ & ~0x00002000);
        subNetId_ = 0;
        onChanged();
        return this;
      }

      private int rtrTimeOut_ ;
      /**
       * <code>optional uint32 rtrTimeOut = 15;</code>
       */
      public boolean hasRtrTimeOut() {
        return ((bitField0_ & 0x00004000) == 0x00004000);
      }
      /**
       * <code>optional uint32 rtrTimeOut = 15;</code>
       */
      public int getRtrTimeOut() {
        return rtrTimeOut_;
      }
      /**
       * <code>optional uint32 rtrTimeOut = 15;</code>
       */
      public Builder setRtrTimeOut(int value) {
        bitField0_ |= 0x00004000;
        rtrTimeOut_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 rtrTimeOut = 15;</code>
       */
      public Builder clearRtrTimeOut() {
        bitField0_ = (bitField0_ & ~0x00004000);
        rtrTimeOut_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString excSrcAddr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes excSrcAddr = 16;</code>
       */
      public boolean hasExcSrcAddr() {
        return ((bitField0_ & 0x00008000) == 0x00008000);
      }
      /**
       * <code>optional bytes excSrcAddr = 16;</code>
       */
      public com.google.protobuf.ByteString getExcSrcAddr() {
        return excSrcAddr_;
      }
      /**
       * <code>optional bytes excSrcAddr = 16;</code>
       */
      public Builder setExcSrcAddr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00008000;
        excSrcAddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes excSrcAddr = 16;</code>
       */
      public Builder clearExcSrcAddr() {
        bitField0_ = (bitField0_ & ~0x00008000);
        excSrcAddr_ = getDefaultInstance().getExcSrcAddr();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString excDstAddr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes excDstAddr = 17;</code>
       */
      public boolean hasExcDstAddr() {
        return ((bitField0_ & 0x00010000) == 0x00010000);
      }
      /**
       * <code>optional bytes excDstAddr = 17;</code>
       */
      public com.google.protobuf.ByteString getExcDstAddr() {
        return excDstAddr_;
      }
      /**
       * <code>optional bytes excDstAddr = 17;</code>
       */
      public Builder setExcDstAddr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00010000;
        excDstAddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes excDstAddr = 17;</code>
       */
      public Builder clearExcDstAddr() {
        bitField0_ = (bitField0_ & ~0x00010000);
        excDstAddr_ = getDefaultInstance().getExcDstAddr();
        onChanged();
        return this;
      }

      private int excProt_ ;
      /**
       * <code>optional uint32 excProt = 18;</code>
       */
      public boolean hasExcProt() {
        return ((bitField0_ & 0x00020000) == 0x00020000);
      }
      /**
       * <code>optional uint32 excProt = 18;</code>
       */
      public int getExcProt() {
        return excProt_;
      }
      /**
       * <code>optional uint32 excProt = 18;</code>
       */
      public Builder setExcProt(int value) {
        bitField0_ |= 0x00020000;
        excProt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 excProt = 18;</code>
       */
      public Builder clearExcProt() {
        bitField0_ = (bitField0_ & ~0x00020000);
        excProt_ = 0;
        onChanged();
        return this;
      }

      private int excSrcPort_ ;
      /**
       * <code>optional uint32 excSrcPort = 19;</code>
       */
      public boolean hasExcSrcPort() {
        return ((bitField0_ & 0x00040000) == 0x00040000);
      }
      /**
       * <code>optional uint32 excSrcPort = 19;</code>
       */
      public int getExcSrcPort() {
        return excSrcPort_;
      }
      /**
       * <code>optional uint32 excSrcPort = 19;</code>
       */
      public Builder setExcSrcPort(int value) {
        bitField0_ |= 0x00040000;
        excSrcPort_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 excSrcPort = 19;</code>
       */
      public Builder clearExcSrcPort() {
        bitField0_ = (bitField0_ & ~0x00040000);
        excSrcPort_ = 0;
        onChanged();
        return this;
      }

      private int excDstPort_ ;
      /**
       * <code>optional uint32 excDstPort = 20;</code>
       */
      public boolean hasExcDstPort() {
        return ((bitField0_ & 0x00080000) == 0x00080000);
      }
      /**
       * <code>optional uint32 excDstPort = 20;</code>
       */
      public int getExcDstPort() {
        return excDstPort_;
      }
      /**
       * <code>optional uint32 excDstPort = 20;</code>
       */
      public Builder setExcDstPort(int value) {
        bitField0_ |= 0x00080000;
        excDstPort_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 excDstPort = 20;</code>
       */
      public Builder clearExcDstPort() {
        bitField0_ = (bitField0_ & ~0x00080000);
        excDstPort_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString gwAddr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes gwAddr = 21;</code>
       */
      public boolean hasGwAddr() {
        return ((bitField0_ & 0x00100000) == 0x00100000);
      }
      /**
       * <code>optional bytes gwAddr = 21;</code>
       */
      public com.google.protobuf.ByteString getGwAddr() {
        return gwAddr_;
      }
      /**
       * <code>optional bytes gwAddr = 21;</code>
       */
      public Builder setGwAddr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00100000;
        gwAddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes gwAddr = 21;</code>
       */
      public Builder clearGwAddr() {
        bitField0_ = (bitField0_ & ~0x00100000);
        gwAddr_ = getDefaultInstance().getGwAddr();
        onChanged();
        return this;
      }

      private int ttl_ ;
      /**
       * <code>optional uint32 ttl = 22;</code>
       */
      public boolean hasTtl() {
        return ((bitField0_ & 0x00200000) == 0x00200000);
      }
      /**
       * <code>optional uint32 ttl = 22;</code>
       */
      public int getTtl() {
        return ttl_;
      }
      /**
       * <code>optional uint32 ttl = 22;</code>
       */
      public Builder setTtl(int value) {
        bitField0_ |= 0x00200000;
        ttl_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ttl = 22;</code>
       */
      public Builder clearTtl() {
        bitField0_ = (bitField0_ & ~0x00200000);
        ttl_ = 0;
        onChanged();
        return this;
      }

      private int repTtl_ ;
      /**
       * <code>optional uint32 repTtl = 23;</code>
       */
      public boolean hasRepTtl() {
        return ((bitField0_ & 0x00400000) == 0x00400000);
      }
      /**
       * <code>optional uint32 repTtl = 23;</code>
       */
      public int getRepTtl() {
        return repTtl_;
      }
      /**
       * <code>optional uint32 repTtl = 23;</code>
       */
      public Builder setRepTtl(int value) {
        bitField0_ |= 0x00400000;
        repTtl_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 repTtl = 23;</code>
       */
      public Builder clearRepTtl() {
        bitField0_ = (bitField0_ & ~0x00400000);
        repTtl_ = 0;
        onChanged();
        return this;
      }

      private int qurType_ ;
      /**
       * <code>optional uint32 qurType = 24;</code>
       */
      public boolean hasQurType() {
        return ((bitField0_ & 0x00800000) == 0x00800000);
      }
      /**
       * <code>optional uint32 qurType = 24;</code>
       */
      public int getQurType() {
        return qurType_;
      }
      /**
       * <code>optional uint32 qurType = 24;</code>
       */
      public Builder setQurType(int value) {
        bitField0_ |= 0x00800000;
        qurType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 qurType = 24;</code>
       */
      public Builder clearQurType() {
        bitField0_ = (bitField0_ & ~0x00800000);
        qurType_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString qurIpv6Addr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes qurIpv6Addr = 25;</code>
       */
      public boolean hasQurIpv6Addr() {
        return ((bitField0_ & 0x01000000) == 0x01000000);
      }
      /**
       * <code>optional bytes qurIpv6Addr = 25;</code>
       */
      public com.google.protobuf.ByteString getQurIpv6Addr() {
        return qurIpv6Addr_;
      }
      /**
       * <code>optional bytes qurIpv6Addr = 25;</code>
       */
      public Builder setQurIpv6Addr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x01000000;
        qurIpv6Addr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes qurIpv6Addr = 25;</code>
       */
      public Builder clearQurIpv6Addr() {
        bitField0_ = (bitField0_ & ~0x01000000);
        qurIpv6Addr_ = getDefaultInstance().getQurIpv6Addr();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString qurIpv4Addr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes qurIpv4Addr = 26;</code>
       */
      public boolean hasQurIpv4Addr() {
        return ((bitField0_ & 0x02000000) == 0x02000000);
      }
      /**
       * <code>optional bytes qurIpv4Addr = 26;</code>
       */
      public com.google.protobuf.ByteString getQurIpv4Addr() {
        return qurIpv4Addr_;
      }
      /**
       * <code>optional bytes qurIpv4Addr = 26;</code>
       */
      public Builder setQurIpv4Addr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x02000000;
        qurIpv4Addr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes qurIpv4Addr = 26;</code>
       */
      public Builder clearQurIpv4Addr() {
        bitField0_ = (bitField0_ & ~0x02000000);
        qurIpv4Addr_ = getDefaultInstance().getQurIpv4Addr();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString qurDNS_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes qurDNS = 27;</code>
       */
      public boolean hasQurDNS() {
        return ((bitField0_ & 0x04000000) == 0x04000000);
      }
      /**
       * <code>optional bytes qurDNS = 27;</code>
       */
      public com.google.protobuf.ByteString getQurDNS() {
        return qurDNS_;
      }
      /**
       * <code>optional bytes qurDNS = 27;</code>
       */
      public Builder setQurDNS(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x04000000;
        qurDNS_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes qurDNS = 27;</code>
       */
      public Builder clearQurDNS() {
        bitField0_ = (bitField0_ & ~0x04000000);
        qurDNS_ = getDefaultInstance().getQurDNS();
        onChanged();
        return this;
      }

      private int ndpLifeTime_ ;
      /**
       * <code>optional uint32 ndpLifeTime = 28;</code>
       */
      public boolean hasNdpLifeTime() {
        return ((bitField0_ & 0x08000000) == 0x08000000);
      }
      /**
       * <code>optional uint32 ndpLifeTime = 28;</code>
       */
      public int getNdpLifeTime() {
        return ndpLifeTime_;
      }
      /**
       * <code>optional uint32 ndpLifeTime = 28;</code>
       */
      public Builder setNdpLifeTime(int value) {
        bitField0_ |= 0x08000000;
        ndpLifeTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ndpLifeTime = 28;</code>
       */
      public Builder clearNdpLifeTime() {
        bitField0_ = (bitField0_ & ~0x08000000);
        ndpLifeTime_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ndpLinkAddr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ndpLinkAddr = 29;</code>
       */
      public boolean hasNdpLinkAddr() {
        return ((bitField0_ & 0x10000000) == 0x10000000);
      }
      /**
       * <code>optional bytes ndpLinkAddr = 29;</code>
       */
      public com.google.protobuf.ByteString getNdpLinkAddr() {
        return ndpLinkAddr_;
      }
      /**
       * <code>optional bytes ndpLinkAddr = 29;</code>
       */
      public Builder setNdpLinkAddr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x10000000;
        ndpLinkAddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ndpLinkAddr = 29;</code>
       */
      public Builder clearNdpLinkAddr() {
        bitField0_ = (bitField0_ & ~0x10000000);
        ndpLinkAddr_ = getDefaultInstance().getNdpLinkAddr();
        onChanged();
        return this;
      }

      private int ndpPreLen_ ;
      /**
       * <code>optional uint32 ndpPreLen = 30;</code>
       */
      public boolean hasNdpPreLen() {
        return ((bitField0_ & 0x20000000) == 0x20000000);
      }
      /**
       * <code>optional uint32 ndpPreLen = 30;</code>
       */
      public int getNdpPreLen() {
        return ndpPreLen_;
      }
      /**
       * <code>optional uint32 ndpPreLen = 30;</code>
       */
      public Builder setNdpPreLen(int value) {
        bitField0_ |= 0x20000000;
        ndpPreLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ndpPreLen = 30;</code>
       */
      public Builder clearNdpPreLen() {
        bitField0_ = (bitField0_ & ~0x20000000);
        ndpPreLen_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ndpPreFix_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ndpPreFix = 31;</code>
       */
      public boolean hasNdpPreFix() {
        return ((bitField0_ & 0x40000000) == 0x40000000);
      }
      /**
       * <code>optional bytes ndpPreFix = 31;</code>
       */
      public com.google.protobuf.ByteString getNdpPreFix() {
        return ndpPreFix_;
      }
      /**
       * <code>optional bytes ndpPreFix = 31;</code>
       */
      public Builder setNdpPreFix(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x40000000;
        ndpPreFix_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ndpPreFix = 31;</code>
       */
      public Builder clearNdpPreFix() {
        bitField0_ = (bitField0_ & ~0x40000000);
        ndpPreFix_ = getDefaultInstance().getNdpPreFix();
        onChanged();
        return this;
      }

      private int ndpValLifeTime_ ;
      /**
       * <code>optional uint32 ndpValLifeTime = 32;</code>
       */
      public boolean hasNdpValLifeTime() {
        return ((bitField0_ & 0x80000000) == 0x80000000);
      }
      /**
       * <code>optional uint32 ndpValLifeTime = 32;</code>
       */
      public int getNdpValLifeTime() {
        return ndpValLifeTime_;
      }
      /**
       * <code>optional uint32 ndpValLifeTime = 32;</code>
       */
      public Builder setNdpValLifeTime(int value) {
        bitField0_ |= 0x80000000;
        ndpValLifeTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ndpValLifeTime = 32;</code>
       */
      public Builder clearNdpValLifeTime() {
        bitField0_ = (bitField0_ & ~0x80000000);
        ndpValLifeTime_ = 0;
        onChanged();
        return this;
      }

      private int ndpCurMtu_ ;
      /**
       * <code>optional uint32 ndpCurMtu = 33;</code>
       */
      public boolean hasNdpCurMtu() {
        return ((bitField1_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional uint32 ndpCurMtu = 33;</code>
       */
      public int getNdpCurMtu() {
        return ndpCurMtu_;
      }
      /**
       * <code>optional uint32 ndpCurMtu = 33;</code>
       */
      public Builder setNdpCurMtu(int value) {
        bitField1_ |= 0x00000001;
        ndpCurMtu_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ndpCurMtu = 33;</code>
       */
      public Builder clearNdpCurMtu() {
        bitField1_ = (bitField1_ & ~0x00000001);
        ndpCurMtu_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ndpTarAddr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ndpTarAddr = 34;</code>
       */
      public boolean hasNdpTarAddr() {
        return ((bitField1_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional bytes ndpTarAddr = 34;</code>
       */
      public com.google.protobuf.ByteString getNdpTarAddr() {
        return ndpTarAddr_;
      }
      /**
       * <code>optional bytes ndpTarAddr = 34;</code>
       */
      public Builder setNdpTarAddr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000002;
        ndpTarAddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ndpTarAddr = 34;</code>
       */
      public Builder clearNdpTarAddr() {
        bitField1_ = (bitField1_ & ~0x00000002);
        ndpTarAddr_ = getDefaultInstance().getNdpTarAddr();
        onChanged();
        return this;
      }

      private int nextHopMtu_ ;
      /**
       * <code>optional uint32 nextHopMtu = 35;</code>
       */
      public boolean hasNextHopMtu() {
        return ((bitField1_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional uint32 nextHopMtu = 35;</code>
       */
      public int getNextHopMtu() {
        return nextHopMtu_;
      }
      /**
       * <code>optional uint32 nextHopMtu = 35;</code>
       */
      public Builder setNextHopMtu(int value) {
        bitField1_ |= 0x00000004;
        nextHopMtu_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 nextHopMtu = 35;</code>
       */
      public Builder clearNextHopMtu() {
        bitField1_ = (bitField1_ & ~0x00000004);
        nextHopMtu_ = 0;
        onChanged();
        return this;
      }

      private int excPointer_ ;
      /**
       * <code>optional uint32 excPointer = 36;</code>
       */
      public boolean hasExcPointer() {
        return ((bitField1_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional uint32 excPointer = 36;</code>
       */
      public int getExcPointer() {
        return excPointer_;
      }
      /**
       * <code>optional uint32 excPointer = 36;</code>
       */
      public Builder setExcPointer(int value) {
        bitField1_ |= 0x00000008;
        excPointer_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 excPointer = 36;</code>
       */
      public Builder clearExcPointer() {
        bitField1_ = (bitField1_ & ~0x00000008);
        excPointer_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString mulCastAddr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes mulCastAddr = 37;</code>
       */
      public boolean hasMulCastAddr() {
        return ((bitField1_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional bytes mulCastAddr = 37;</code>
       */
      public com.google.protobuf.ByteString getMulCastAddr() {
        return mulCastAddr_;
      }
      /**
       * <code>optional bytes mulCastAddr = 37;</code>
       */
      public Builder setMulCastAddr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000010;
        mulCastAddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes mulCastAddr = 37;</code>
       */
      public Builder clearMulCastAddr() {
        bitField1_ = (bitField1_ & ~0x00000010);
        mulCastAddr_ = getDefaultInstance().getMulCastAddr();
        onChanged();
        return this;
      }

      private int checkSum_ ;
      /**
       * <code>optional uint32 checkSum = 38;</code>
       */
      public boolean hasCheckSum() {
        return ((bitField1_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional uint32 checkSum = 38;</code>
       */
      public int getCheckSum() {
        return checkSum_;
      }
      /**
       * <code>optional uint32 checkSum = 38;</code>
       */
      public Builder setCheckSum(int value) {
        bitField1_ |= 0x00000020;
        checkSum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 checkSum = 38;</code>
       */
      public Builder clearCheckSum() {
        bitField1_ = (bitField1_ & ~0x00000020);
        checkSum_ = 0;
        onChanged();
        return this;
      }

      private int checkSumReply_ ;
      /**
       * <code>optional uint32 checkSumReply = 39;</code>
       */
      public boolean hasCheckSumReply() {
        return ((bitField1_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional uint32 checkSumReply = 39;</code>
       */
      public int getCheckSumReply() {
        return checkSumReply_;
      }
      /**
       * <code>optional uint32 checkSumReply = 39;</code>
       */
      public Builder setCheckSumReply(int value) {
        bitField1_ |= 0x00000040;
        checkSumReply_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 checkSumReply = 39;</code>
       */
      public Builder clearCheckSumReply() {
        bitField1_ = (bitField1_ & ~0x00000040);
        checkSumReply_ = 0;
        onChanged();
        return this;
      }

      private int rtraddr_ ;
      /**
       * <code>optional uint32 rtraddr = 40;</code>
       */
      public boolean hasRtraddr() {
        return ((bitField1_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional uint32 rtraddr = 40;</code>
       */
      public int getRtraddr() {
        return rtraddr_;
      }
      /**
       * <code>optional uint32 rtraddr = 40;</code>
       */
      public Builder setRtraddr(int value) {
        bitField1_ |= 0x00000080;
        rtraddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 rtraddr = 40;</code>
       */
      public Builder clearRtraddr() {
        bitField1_ = (bitField1_ & ~0x00000080);
        rtraddr_ = 0;
        onChanged();
        return this;
      }

      private long resTime_ ;
      /**
       * <code>optional uint64 resTime = 41;</code>
       */
      public boolean hasResTime() {
        return ((bitField1_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional uint64 resTime = 41;</code>
       */
      public long getResTime() {
        return resTime_;
      }
      /**
       * <code>optional uint64 resTime = 41;</code>
       */
      public Builder setResTime(long value) {
        bitField1_ |= 0x00000100;
        resTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 resTime = 41;</code>
       */
      public Builder clearResTime() {
        bitField1_ = (bitField1_ & ~0x00000100);
        resTime_ = 0L;
        onChanged();
        return this;
      }

      private int excTTL_ ;
      /**
       * <code>optional uint32 excTTL = 42;</code>
       */
      public boolean hasExcTTL() {
        return ((bitField1_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>optional uint32 excTTL = 42;</code>
       */
      public int getExcTTL() {
        return excTTL_;
      }
      /**
       * <code>optional uint32 excTTL = 42;</code>
       */
      public Builder setExcTTL(int value) {
        bitField1_ |= 0x00000200;
        excTTL_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 excTTL = 42;</code>
       */
      public Builder clearExcTTL() {
        bitField1_ = (bitField1_ & ~0x00000200);
        excTTL_ = 0;
        onChanged();
        return this;
      }

      private long responseTime_ ;
      /**
       * <code>optional uint64 ResponseTime = 43;</code>
       */
      public boolean hasResponseTime() {
        return ((bitField1_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>optional uint64 ResponseTime = 43;</code>
       */
      public long getResponseTime() {
        return responseTime_;
      }
      /**
       * <code>optional uint64 ResponseTime = 43;</code>
       */
      public Builder setResponseTime(long value) {
        bitField1_ |= 0x00000400;
        responseTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 ResponseTime = 43;</code>
       */
      public Builder clearResponseTime() {
        bitField1_ = (bitField1_ & ~0x00000400);
        responseTime_ = 0L;
        onChanged();
        return this;
      }

      private int unreachableSourcePort_ ;
      /**
       * <code>optional uint32 unreachableSourcePort = 44;</code>
       */
      public boolean hasUnreachableSourcePort() {
        return ((bitField1_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>optional uint32 unreachableSourcePort = 44;</code>
       */
      public int getUnreachableSourcePort() {
        return unreachableSourcePort_;
      }
      /**
       * <code>optional uint32 unreachableSourcePort = 44;</code>
       */
      public Builder setUnreachableSourcePort(int value) {
        bitField1_ |= 0x00000800;
        unreachableSourcePort_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 unreachableSourcePort = 44;</code>
       */
      public Builder clearUnreachableSourcePort() {
        bitField1_ = (bitField1_ & ~0x00000800);
        unreachableSourcePort_ = 0;
        onChanged();
        return this;
      }

      private int unreachableDestinationPort_ ;
      /**
       * <code>optional uint32 unreachableDestinationPort = 45;</code>
       */
      public boolean hasUnreachableDestinationPort() {
        return ((bitField1_ & 0x00001000) == 0x00001000);
      }
      /**
       * <code>optional uint32 unreachableDestinationPort = 45;</code>
       */
      public int getUnreachableDestinationPort() {
        return unreachableDestinationPort_;
      }
      /**
       * <code>optional uint32 unreachableDestinationPort = 45;</code>
       */
      public Builder setUnreachableDestinationPort(int value) {
        bitField1_ |= 0x00001000;
        unreachableDestinationPort_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 unreachableDestinationPort = 45;</code>
       */
      public Builder clearUnreachableDestinationPort() {
        bitField1_ = (bitField1_ & ~0x00001000);
        unreachableDestinationPort_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:IcmpInfo)
    }

    // @@protoc_insertion_point(class_scope:IcmpInfo)
    private static final IcmpInfoOuterClass.IcmpInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new IcmpInfoOuterClass.IcmpInfo();
    }

    public static IcmpInfoOuterClass.IcmpInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<IcmpInfo>
        PARSER = new com.google.protobuf.AbstractParser<IcmpInfo>() {
      @java.lang.Override
      public IcmpInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new IcmpInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<IcmpInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IcmpInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public IcmpInfoOuterClass.IcmpInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_IcmpInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_IcmpInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016IcmpInfo.proto\"\373\006\n\010IcmpInfo\022\017\n\007msgType" +
      "\030\001 \001(\r\022\020\n\010infoCode\030\002 \001(\r\022\022\n\nechoSeqNum\030\003" +
      " \001(\r\022\017\n\007dataCon\030\004 \001(\014\022\022\n\nunrSrcAddr\030\005 \001(" +
      "\014\022\022\n\nunrDstAddr\030\006 \001(\014\022\017\n\007unrProt\030\007 \001(\r\022\016" +
      "\n\006uncTTL\030\010 \001(\r\022\013\n\003ver\030\t \001(\r\022\025\n\rorigTimeS" +
      "tamp\030\n \001(\004\022\025\n\rrecvTimeStamp\030\013 \001(\004\022\026\n\016tra" +
      "nsTimeStamp\030\014 \001(\004\022\014\n\004mask\030\r \001(\r\022\020\n\010subNe" +
      "tId\030\016 \001(\r\022\022\n\nrtrTimeOut\030\017 \001(\r\022\022\n\nexcSrcA" +
      "ddr\030\020 \001(\014\022\022\n\nexcDstAddr\030\021 \001(\014\022\017\n\007excProt" +
      "\030\022 \001(\r\022\022\n\nexcSrcPort\030\023 \001(\r\022\022\n\nexcDstPort" +
      "\030\024 \001(\r\022\016\n\006gwAddr\030\025 \001(\014\022\013\n\003ttl\030\026 \001(\r\022\016\n\006r" +
      "epTtl\030\027 \001(\r\022\017\n\007qurType\030\030 \001(\r\022\023\n\013qurIpv6A" +
      "ddr\030\031 \001(\014\022\023\n\013qurIpv4Addr\030\032 \001(\014\022\016\n\006qurDNS" +
      "\030\033 \001(\014\022\023\n\013ndpLifeTime\030\034 \001(\r\022\023\n\013ndpLinkAd" +
      "dr\030\035 \001(\014\022\021\n\tndpPreLen\030\036 \001(\r\022\021\n\tndpPreFix" +
      "\030\037 \001(\014\022\026\n\016ndpValLifeTime\030  \001(\r\022\021\n\tndpCur" +
      "Mtu\030! \001(\r\022\022\n\nndpTarAddr\030\" \001(\014\022\022\n\nnextHop" +
      "Mtu\030# \001(\r\022\022\n\nexcPointer\030$ \001(\r\022\023\n\013mulCast" +
      "Addr\030% \001(\014\022\020\n\010checkSum\030& \001(\r\022\025\n\rcheckSum" +
      "Reply\030\' \001(\r\022\017\n\007rtraddr\030( \001(\r\022\017\n\007resTime\030" +
      ") \001(\004\022\016\n\006excTTL\030* \001(\r\022\024\n\014ResponseTime\030+ " +
      "\001(\004\022\035\n\025unreachableSourcePort\030, \001(\r\022\"\n\032un" +
      "reachableDestinationPort\030- \001(\r"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_IcmpInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_IcmpInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_IcmpInfo_descriptor,
        new java.lang.String[] { "MsgType", "InfoCode", "EchoSeqNum", "DataCon", "UnrSrcAddr", "UnrDstAddr", "UnrProt", "UncTTL", "Ver", "OrigTimeStamp", "RecvTimeStamp", "TransTimeStamp", "Mask", "SubNetId", "RtrTimeOut", "ExcSrcAddr", "ExcDstAddr", "ExcProt", "ExcSrcPort", "ExcDstPort", "GwAddr", "Ttl", "RepTtl", "QurType", "QurIpv6Addr", "QurIpv4Addr", "QurDNS", "NdpLifeTime", "NdpLinkAddr", "NdpPreLen", "NdpPreFix", "NdpValLifeTime", "NdpCurMtu", "NdpTarAddr", "NextHopMtu", "ExcPointer", "MulCastAddr", "CheckSum", "CheckSumReply", "Rtraddr", "ResTime", "ExcTTL", "ResponseTime", "UnreachableSourcePort", "UnreachableDestinationPort", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
