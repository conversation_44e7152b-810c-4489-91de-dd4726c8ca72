// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Ssl_TlsInfo.proto
package com.geeksec.proto.output;

public final class SslTlsInfo {
  private SslTlsInfo() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Ssl_TlsInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Ssl_TlsInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional uint32 conType = 1;</code>
     */
    boolean hasConType();
    /**
     * <code>optional uint32 conType = 1;</code>
     */
    int getConType();

    /**
     * <code>optional uint32 aleLev = 2;</code>
     */
    boolean hasAleLev();
    /**
     * <code>optional uint32 aleLev = 2;</code>
     */
    int getAleLev();

    /**
     * <code>optional uint32 aleDes = 3;</code>
     */
    boolean hasAleDes();
    /**
     * <code>optional uint32 aleDes = 3;</code>
     */
    int getAleDes();

    /**
     * <code>optional uint32 handShaType = 4;</code>
     */
    boolean hasHandShaType();
    /**
     * <code>optional uint32 handShaType = 4;</code>
     */
    int getHandShaType();

    /**
     * <code>optional uint32 cliVer = 5;</code>
     */
    boolean hasCliVer();
    /**
     * <code>optional uint32 cliVer = 5;</code>
     */
    int getCliVer();

    /**
     * <code>optional uint64 cliGMTUniTime = 6;</code>
     */
    boolean hasCliGMTUniTime();
    /**
     * <code>optional uint64 cliGMTUniTime = 6;</code>
     */
    long getCliGMTUniTime();

    /**
     * <code>optional bytes cliRand = 7;</code>
     */
    boolean hasCliRand();
    /**
     * <code>optional bytes cliRand = 7;</code>
     */
    com.google.protobuf.ByteString getCliRand();

    /**
     * <code>optional bytes cliSesID = 8;</code>
     */
    boolean hasCliSesID();
    /**
     * <code>optional bytes cliSesID = 8;</code>
     */
    com.google.protobuf.ByteString getCliSesID();

    /**
     * <code>optional bytes cliCipSui = 9;</code>
     */
    boolean hasCliCipSui();
    /**
     * <code>optional bytes cliCipSui = 9;</code>
     */
    com.google.protobuf.ByteString getCliCipSui();

    /**
     * <code>optional bytes cliComMet = 10;</code>
     */
    boolean hasCliComMet();
    /**
     * <code>optional bytes cliComMet = 10;</code>
     */
    com.google.protobuf.ByteString getCliComMet();

    /**
     * <code>optional uint32 srvVer = 11;</code>
     */
    boolean hasSrvVer();
    /**
     * <code>optional uint32 srvVer = 11;</code>
     */
    int getSrvVer();

    /**
     * <code>optional bytes srvName = 12;</code>
     */
    boolean hasSrvName();
    /**
     * <code>optional bytes srvName = 12;</code>
     */
    com.google.protobuf.ByteString getSrvName();

    /**
     * <code>optional uint32 srvNameAttr = 13;</code>
     */
    boolean hasSrvNameAttr();
    /**
     * <code>optional uint32 srvNameAttr = 13;</code>
     */
    int getSrvNameAttr();

    /**
     * <code>optional uint64 srvGMTUniTime = 14;</code>
     */
    boolean hasSrvGMTUniTime14();
    /**
     * <code>optional uint64 srvGMTUniTime = 14;</code>
     */
    long getSrvGMTUniTime14();

    /**
     * <code>optional bytes srvRand = 15;</code>
     */
    boolean hasSrvRand();
    /**
     * <code>optional bytes srvRand = 15;</code>
     */
    com.google.protobuf.ByteString getSrvRand();

    /**
     * <code>optional bytes srvSesID = 16;</code>
     */
    boolean hasSrvSesID();
    /**
     * <code>optional bytes srvSesID = 16;</code>
     */
    com.google.protobuf.ByteString getSrvSesID();

    /**
     * <code>optional bytes srvComprMet = 17;</code>
     */
    boolean hasSrvComprMet();
    /**
     * <code>optional bytes srvComprMet = 17;</code>
     */
    com.google.protobuf.ByteString getSrvComprMet();

    /**
     * <code>optional uint32 srvCertLen = 18;</code>
     */
    boolean hasSrvCertLen();
    /**
     * <code>optional uint32 srvCertLen = 18;</code>
     */
    int getSrvCertLen();

    /**
     * <code>optional uint32 certResType = 19;</code>
     */
    boolean hasCertResType();
    /**
     * <code>optional uint32 certResType = 19;</code>
     */
    int getCertResType();

    /**
     * <code>optional uint32 cliCertLen = 20;</code>
     */
    boolean hasCliCertLen();
    /**
     * <code>optional uint32 cliCertLen = 20;</code>
     */
    int getCliCertLen();

    /**
     * <code>optional bytes RSAModOfSrvKeyExc = 21;</code>
     */
    boolean hasRSAModOfSrvKeyExc();
    /**
     * <code>optional bytes RSAModOfSrvKeyExc = 21;</code>
     */
    com.google.protobuf.ByteString getRSAModOfSrvKeyExc();

    /**
     * <code>optional uint64 RSAExpOfSrvKeyExc = 22;</code>
     */
    boolean hasRSAExpOfSrvKeyExc();
    /**
     * <code>optional uint64 RSAExpOfSrvKeyExc = 22;</code>
     */
    long getRSAExpOfSrvKeyExc();

    /**
     * <code>optional bytes DHModOfSrvKeyExc = 23;</code>
     */
    boolean hasDHModOfSrvKeyExc();
    /**
     * <code>optional bytes DHModOfSrvKeyExc = 23;</code>
     */
    com.google.protobuf.ByteString getDHModOfSrvKeyExc();

    /**
     * <code>optional bytes DHGenOfSrvKeyExc = 24;</code>
     */
    boolean hasDHGenOfSrvKeyExc();
    /**
     * <code>optional bytes DHGenOfSrvKeyExc = 24;</code>
     */
    com.google.protobuf.ByteString getDHGenOfSrvKeyExc();

    /**
     * <code>optional bytes srvDHPubKey = 25;</code>
     */
    boolean hasSrvDHPubKey();
    /**
     * <code>optional bytes srvDHPubKey = 25;</code>
     */
    com.google.protobuf.ByteString getSrvDHPubKey();

    /**
     * <code>optional bytes preMasKeyEncryByRSA = 26;</code>
     */
    boolean hasPreMasKeyEncryByRSA();
    /**
     * <code>optional bytes preMasKeyEncryByRSA = 26;</code>
     */
    com.google.protobuf.ByteString getPreMasKeyEncryByRSA();

    /**
     * <code>optional bytes cliDHPubKey = 27;</code>
     */
    boolean hasCliDHPubKey();
    /**
     * <code>optional bytes cliDHPubKey = 27;</code>
     */
    com.google.protobuf.ByteString getCliDHPubKey();

    /**
     * <code>optional uint32 extTypeInSSL = 28;</code>
     */
    boolean hasExtTypeInSSL();
    /**
     * <code>optional uint32 extTypeInSSL = 28;</code>
     */
    int getExtTypeInSSL();

    /**
     * <code>optional uint32 cliEllCurPoiFor = 29;</code>
     */
    boolean hasCliEllCurPoiFor();
    /**
     * <code>optional uint32 cliEllCurPoiFor = 29;</code>
     */
    int getCliEllCurPoiFor();

    /**
     * <code>optional uint32 cliEllCur = 30;</code>
     */
    boolean hasCliEllCur();
    /**
     * <code>optional uint32 cliEllCur = 30;</code>
     */
    int getCliEllCur();

    /**
     * <code>optional uint32 srvEllCurPoiFor = 31;</code>
     */
    boolean hasSrvEllCurPoiFor();
    /**
     * <code>optional uint32 srvEllCurPoiFor = 31;</code>
     */
    int getSrvEllCurPoiFor();

    /**
     * <code>optional uint32 srvEllCur = 32;</code>
     */
    boolean hasSrvEllCur();
    /**
     * <code>optional uint32 srvEllCur = 32;</code>
     */
    int getSrvEllCur();

    /**
     * <code>optional bytes srvEllCurDHPubKey = 33;</code>
     */
    boolean hasSrvEllCurDHPubKey();
    /**
     * <code>optional bytes srvEllCurDHPubKey = 33;</code>
     */
    com.google.protobuf.ByteString getSrvEllCurDHPubKey();

    /**
     * <code>optional bytes cliEllCurDHPubKey = 34;</code>
     */
    boolean hasCliEllCurDHPubKey();
    /**
     * <code>optional bytes cliEllCurDHPubKey = 34;</code>
     */
    com.google.protobuf.ByteString getCliEllCurDHPubKey();

    /**
     * <code>optional uint64 srvGMTUni_Time = 35;</code>
     */
    boolean hasSrvGMTUniTime35();
    /**
     * <code>optional uint64 srvGMTUni_Time = 35;</code>
     */
    long getSrvGMTUniTime35();

    /**
     * <code>optional uint32 cliExtCnt = 36;</code>
     */
    boolean hasCliExtCnt();
    /**
     * <code>optional uint32 cliExtCnt = 36;</code>
     */
    int getCliExtCnt();

    /**
     * <code>optional uint32 srvExtCnt = 37;</code>
     */
    boolean hasSrvExtCnt();
    /**
     * <code>optional uint32 srvExtCnt = 37;</code>
     */
    int getSrvExtCnt();

    /**
     * <code>optional uint32 cliHandSkLen = 38;</code>
     */
    boolean hasCliHandSkLen();
    /**
     * <code>optional uint32 cliHandSkLen = 38;</code>
     */
    int getCliHandSkLen();

    /**
     * <code>optional uint32 srvHandSkLen = 39;</code>
     */
    boolean hasSrvHandSkLen();
    /**
     * <code>optional uint32 srvHandSkLen = 39;</code>
     */
    int getSrvHandSkLen();

    /**
     * <code>optional bytes cliExt = 40;</code>
     */
    boolean hasCliExt();
    /**
     * <code>optional bytes cliExt = 40;</code>
     */
    com.google.protobuf.ByteString getCliExt();

    /**
     * <code>optional bytes srvExt = 41;</code>
     */
    boolean hasSrvExt();
    /**
     * <code>optional bytes srvExt = 41;</code>
     */
    com.google.protobuf.ByteString getSrvExt();

    /**
     * <code>optional uint32 cliExtGrease = 42;</code>
     */
    boolean hasCliExtGrease();
    /**
     * <code>optional uint32 cliExtGrease = 42;</code>
     */
    int getCliExtGrease();

    /**
     * <code>optional bytes cliJA3 = 43;</code>
     */
    boolean hasCliJA3();
    /**
     * <code>optional bytes cliJA3 = 43;</code>
     */
    com.google.protobuf.ByteString getCliJA3();

    /**
     * <code>optional bytes srvJA3 = 44;</code>
     */
    boolean hasSrvJA3();
    /**
     * <code>optional bytes srvJA3 = 44;</code>
     */
    com.google.protobuf.ByteString getSrvJA3();

    /**
     * <code>optional bytes cliSessTicket = 45;</code>
     */
    boolean hasCliSessTicket();
    /**
     * <code>optional bytes cliSessTicket = 45;</code>
     */
    com.google.protobuf.ByteString getCliSessTicket();

    /**
     * <code>optional bytes srvSessTicket = 46;</code>
     */
    boolean hasSrvSessTicket();
    /**
     * <code>optional bytes srvSessTicket = 46;</code>
     */
    com.google.protobuf.ByteString getSrvSessTicket();

    /**
     * <code>optional uint32 AuthTag = 47;</code>
     */
    boolean hasAuthTag();
    /**
     * <code>optional uint32 AuthTag = 47;</code>
     */
    int getAuthTag();

    /**
     * <code>optional uint32 cliCertCnt = 48;</code>
     */
    boolean hasCliCertCnt();
    /**
     * <code>optional uint32 cliCertCnt = 48;</code>
     */
    int getCliCertCnt();

    /**
     * <code>optional uint32 srvCertCnt = 49;</code>
     */
    boolean hasSrvCertCnt();
    /**
     * <code>optional uint32 srvCertCnt = 49;</code>
     */
    int getSrvCertCnt();

    /**
     * <code>optional bytes ecGroupsCli = 50;</code>
     */
    boolean hasEcGroupsCli();
    /**
     * <code>optional bytes ecGroupsCli = 50;</code>
     */
    com.google.protobuf.ByteString getEcGroupsCli();

    /**
     * <code>optional bytes ecPoiForByServ = 51;</code>
     */
    boolean hasEcPoiForByServ();
    /**
     * <code>optional bytes ecPoiForByServ = 51;</code>
     */
    com.google.protobuf.ByteString getEcPoiForByServ();

    /**
     * <code>optional bytes etags = 52;</code>
     */
    boolean hasEtags();
    /**
     * <code>optional bytes etags = 52;</code>
     */
    com.google.protobuf.ByteString getEtags();

    /**
     * <code>optional bytes ttags = 53;</code>
     */
    boolean hasTtags();
    /**
     * <code>optional bytes ttags = 53;</code>
     */
    com.google.protobuf.ByteString getTtags();

    /**
     * <code>optional bytes cliSesIDLen = 54;</code>
     */
    boolean hasCliSesIDLen();
    /**
     * <code>optional bytes cliSesIDLen = 54;</code>
     */
    com.google.protobuf.ByteString getCliSesIDLen();

    /**
     * <code>optional bytes srvSesIDLen = 55;</code>
     */
    boolean hasSrvSesIDLen();
    /**
     * <code>optional bytes srvSesIDLen = 55;</code>
     */
    com.google.protobuf.ByteString getSrvSesIDLen();

    /**
     * <code>optional bytes srvKeyExcLen = 56;</code>
     */
    boolean hasSrvKeyExcLen();
    /**
     * <code>optional bytes srvKeyExcLen = 56;</code>
     */
    com.google.protobuf.ByteString getSrvKeyExcLen();

    /**
     * <code>optional bytes ECDHCurType = 57;</code>
     */
    boolean hasECDHCurType();
    /**
     * <code>optional bytes ECDHCurType = 57;</code>
     */
    com.google.protobuf.ByteString getECDHCurType();

    /**
     * <code>optional bytes ECDHSig = 58;</code>
     */
    boolean hasECDHSig();
    /**
     * <code>optional bytes ECDHSig = 58;</code>
     */
    com.google.protobuf.ByteString getECDHSig();

    /**
     * <code>optional bytes DHEPLen = 59;</code>
     */
    boolean hasDHEPLen();
    /**
     * <code>optional bytes DHEPLen = 59;</code>
     */
    com.google.protobuf.ByteString getDHEPLen();

    /**
     * <code>optional bytes DHEGLen = 60;</code>
     */
    boolean hasDHEGLen();
    /**
     * <code>optional bytes DHEGLen = 60;</code>
     */
    com.google.protobuf.ByteString getDHEGLen();

    /**
     * <code>optional bytes cliKeyExcLen = 61;</code>
     */
    boolean hasCliKeyExcLen();
    /**
     * <code>optional bytes cliKeyExcLen = 61;</code>
     */
    com.google.protobuf.ByteString getCliKeyExcLen();

    /**
     * <code>optional bytes encPubKey = 62;</code>
     */
    boolean hasEncPubKey();
    /**
     * <code>optional bytes encPubKey = 62;</code>
     */
    com.google.protobuf.ByteString getEncPubKey();

    /**
     * <code>optional bytes encPubKeyLen = 63;</code>
     */
    boolean hasEncPubKeyLen();
    /**
     * <code>optional bytes encPubKeyLen = 63;</code>
     */
    com.google.protobuf.ByteString getEncPubKeyLen();

    /**
     * <code>optional bytes cliExtLen = 64;</code>
     */
    boolean hasCliExtLen();
    /**
     * <code>optional bytes cliExtLen = 64;</code>
     */
    com.google.protobuf.ByteString getCliExtLen();

    /**
     * <code>optional bytes srvExtLen = 65;</code>
     */
    boolean hasSrvExtLen();
    /**
     * <code>optional bytes srvExtLen = 65;</code>
     */
    com.google.protobuf.ByteString getSrvExtLen();

    /**
     * <code>optional bytes ECDHPubKeyLen = 66;</code>
     */
    boolean hasECDHPubKeyLen();
    /**
     * <code>optional bytes ECDHPubKeyLen = 66;</code>
     */
    com.google.protobuf.ByteString getECDHPubKeyLen();

    /**
     * <code>optional bytes namType = 67;</code>
     */
    boolean hasNamType();
    /**
     * <code>optional bytes namType = 67;</code>
     */
    com.google.protobuf.ByteString getNamType();

    /**
     * <code>optional bytes namLen = 68;</code>
     */
    boolean hasNamLen();
    /**
     * <code>optional bytes namLen = 68;</code>
     */
    com.google.protobuf.ByteString getNamLen();

    /**
     * <code>optional bytes ticDat = 69;</code>
     */
    boolean hasTicDat();
    /**
     * <code>optional bytes ticDat = 69;</code>
     */
    com.google.protobuf.ByteString getTicDat();

    /**
     * <code>optional bytes srvCipSui = 70;</code>
     */
    boolean hasSrvCipSui();
    /**
     * <code>optional bytes srvCipSui = 70;</code>
     */
    com.google.protobuf.ByteString getSrvCipSui();

    /**
     * <code>optional uint32 cipSuiNum = 71;</code>
     */
    boolean hasCipSuiNum();
    /**
     * <code>optional uint32 cipSuiNum = 71;</code>
     */
    int getCipSuiNum();

    /**
     * <code>optional bytes ECDHSigHash = 72;</code>
     */
    boolean hasECDHSigHash();
    /**
     * <code>optional bytes ECDHSigHash = 72;</code>
     */
    com.google.protobuf.ByteString getECDHSigHash();

    /**
     * <code>optional bytes DHESigHash = 73;</code>
     */
    boolean hasDHESigHash();
    /**
     * <code>optional bytes DHESigHash = 73;</code>
     */
    com.google.protobuf.ByteString getDHESigHash();

    /**
     * <code>optional bytes RSASigHash = 74;</code>
     */
    boolean hasRSASigHash();
    /**
     * <code>optional bytes RSASigHash = 74;</code>
     */
    com.google.protobuf.ByteString getRSASigHash();

    /**
     * <code>optional bytes greaseFlag = 75;</code>
     */
    boolean hasGreaseFlag();
    /**
     * <code>optional bytes greaseFlag = 75;</code>
     */
    com.google.protobuf.ByteString getGreaseFlag();

    /**
     * <code>optional bytes RSAModLen = 76;</code>
     */
    boolean hasRSAModLen();
    /**
     * <code>optional bytes RSAModLen = 76;</code>
     */
    com.google.protobuf.ByteString getRSAModLen();

    /**
     * <code>optional bytes RSAExpLen = 77;</code>
     */
    boolean hasRSAExpLen();
    /**
     * <code>optional bytes RSAExpLen = 77;</code>
     */
    com.google.protobuf.ByteString getRSAExpLen();

    /**
     * <code>optional bytes RSASig = 78;</code>
     */
    boolean hasRSASig();
    /**
     * <code>optional bytes RSASig = 78;</code>
     */
    com.google.protobuf.ByteString getRSASig();

    /**
     * <code>optional bytes DHESig = 79;</code>
     */
    boolean hasDHESig();
    /**
     * <code>optional bytes DHESig = 79;</code>
     */
    com.google.protobuf.ByteString getDHESig();

    /**
     * <code>optional bytes DHEPubKeyLen = 80;</code>
     */
    boolean hasDHEPubKeyLen();
    /**
     * <code>optional bytes DHEPubKeyLen = 80;</code>
     */
    com.google.protobuf.ByteString getDHEPubKeyLen();

    /**
     * <code>optional bytes DHEPubKey = 81;</code>
     */
    boolean hasDHEPubKey();
    /**
     * <code>optional bytes DHEPubKey = 81;</code>
     */
    com.google.protobuf.ByteString getDHEPubKey();

    /**
     * <code>optional bytes SigAlgType = 82;</code>
     */
    boolean hasSigAlgType();
    /**
     * <code>optional bytes SigAlgType = 82;</code>
     */
    com.google.protobuf.ByteString getSigAlgType();

    /**
     * <code>optional bytes sigAlg = 83;</code>
     */
    boolean hasSigAlg();
    /**
     * <code>optional bytes sigAlg = 83;</code>
     */
    com.google.protobuf.ByteString getSigAlg();

    /**
     * <code>optional bytes SigHashAlg = 84;</code>
     */
    boolean hasSigHashAlg();
    /**
     * <code>optional bytes SigHashAlg = 84;</code>
     */
    com.google.protobuf.ByteString getSigHashAlg();

    /**
     * <code>optional bytes JOY = 85;</code>
     */
    boolean hasJOY();
    /**
     * <code>optional bytes JOY = 85;</code>
     */
    com.google.protobuf.ByteString getJOY();

    /**
     * <code>optional bytes JOYS = 86;</code>
     */
    boolean hasJOYS();
    /**
     * <code>optional bytes JOYS = 86;</code>
     */
    com.google.protobuf.ByteString getJOYS();

    /**
     * <code>optional bytes STARTTLS = 87;</code>
     */
    boolean hasSTARTTLS();
    /**
     * <code>optional bytes STARTTLS = 87;</code>
     */
    com.google.protobuf.ByteString getSTARTTLS();

    /**
     * <code>optional bytes certNonFlag = 88;</code>
     */
    boolean hasCertNonFlag();
    /**
     * <code>optional bytes certNonFlag = 88;</code>
     */
    com.google.protobuf.ByteString getCertNonFlag();

    /**
     * <code>optional bytes JoyFp = 89;</code>
     */
    boolean hasJoyFp();
    /**
     * <code>optional bytes JoyFp = 89;</code>
     */
    com.google.protobuf.ByteString getJoyFp();

    /**
     * <code>optional bytes certIntactFlag = 90;</code>
     */
    boolean hasCertIntactFlag();
    /**
     * <code>optional bytes certIntactFlag = 90;</code>
     */
    com.google.protobuf.ByteString getCertIntactFlag();

    /**
     * <code>optional bytes certPath = 91;</code>
     */
    boolean hasCertPath();
    /**
     * <code>optional bytes certPath = 91;</code>
     */
    com.google.protobuf.ByteString getCertPath();

    /**
     * <code>optional bytes sessSecFlag = 92;</code>
     */
    boolean hasSessSecFlag();
    /**
     * <code>optional bytes sessSecFlag = 92;</code>
     */
    com.google.protobuf.ByteString getSessSecFlag();

    /**
     * <code>optional bytes fullText = 93;</code>
     */
    boolean hasFullText();
    /**
     * <code>optional bytes fullText = 93;</code>
     */
    com.google.protobuf.ByteString getFullText();

    /**
     * <code>optional bytes cliCertHashes = 94;</code>
     */
    boolean hasCliCertHashes();
    /**
     * <code>optional bytes cliCertHashes = 94;</code>
     */
    com.google.protobuf.ByteString getCliCertHashes();

    /**
     * <code>optional bytes srvCertHashes = 95;</code>
     */
    boolean hasSrvCertHashes();
    /**
     * <code>optional bytes srvCertHashes = 95;</code>
     */
    com.google.protobuf.ByteString getSrvCertHashes();

    /**
     * <code>optional uint32 cliCertNum = 96;</code>
     */
    boolean hasCliCertNum();
    /**
     * <code>optional uint32 cliCertNum = 96;</code>
     */
    int getCliCertNum();

    /**
     * <code>optional uint32 srvCertNum = 97;</code>
     */
    boolean hasSrvCertNum();
    /**
     * <code>optional uint32 srvCertNum = 97;</code>
     */
    int getSrvCertNum();

    /**
     * <code>optional uint32 certExist = 98;</code>
     */
    boolean hasCertExist();
    /**
     * <code>optional uint32 certExist = 98;</code>
     */
    int getCertExist();

    /**
     * <code>optional bytes extendEcGroupsClient = 99;</code>
     */
    boolean hasExtendEcGroupsClient();
    /**
     * <code>optional bytes extendEcGroupsClient = 99;</code>
     */
    com.google.protobuf.ByteString getExtendEcGroupsClient();

    /**
     * <code>optional uint32 leafCertDaysRemaining = 100;</code>
     */
    boolean hasLeafCertDaysRemaining();
    /**
     * <code>optional uint32 leafCertDaysRemaining = 100;</code>
     */
    int getLeafCertDaysRemaining();
  }
  /**
   * Protobuf type {@code Ssl_TlsInfo}
   */
  public  static final class Ssl_TlsInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Ssl_TlsInfo)
      Ssl_TlsInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Ssl_TlsInfo.newBuilder() to construct.
    private Ssl_TlsInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Ssl_TlsInfo() {
      conType_ = 0;
      aleLev_ = 0;
      aleDes_ = 0;
      handShaType_ = 0;
      cliVer_ = 0;
      cliGMTUniTime_ = 0L;
      cliRand_ = com.google.protobuf.ByteString.EMPTY;
      cliSesID_ = com.google.protobuf.ByteString.EMPTY;
      cliCipSui_ = com.google.protobuf.ByteString.EMPTY;
      cliComMet_ = com.google.protobuf.ByteString.EMPTY;
      srvVer_ = 0;
      srvName_ = com.google.protobuf.ByteString.EMPTY;
      srvNameAttr_ = 0;
      srvGMTUniTime14_ = 0L;
      srvRand_ = com.google.protobuf.ByteString.EMPTY;
      srvSesID_ = com.google.protobuf.ByteString.EMPTY;
      srvComprMet_ = com.google.protobuf.ByteString.EMPTY;
      srvCertLen_ = 0;
      certResType_ = 0;
      cliCertLen_ = 0;
      rSAModOfSrvKeyExc_ = com.google.protobuf.ByteString.EMPTY;
      rSAExpOfSrvKeyExc_ = 0L;
      dHModOfSrvKeyExc_ = com.google.protobuf.ByteString.EMPTY;
      dHGenOfSrvKeyExc_ = com.google.protobuf.ByteString.EMPTY;
      srvDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      preMasKeyEncryByRSA_ = com.google.protobuf.ByteString.EMPTY;
      cliDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      extTypeInSSL_ = 0;
      cliEllCurPoiFor_ = 0;
      cliEllCur_ = 0;
      srvEllCurPoiFor_ = 0;
      srvEllCur_ = 0;
      srvEllCurDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      cliEllCurDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      srvGMTUniTime35_ = 0L;
      cliExtCnt_ = 0;
      srvExtCnt_ = 0;
      cliHandSkLen_ = 0;
      srvHandSkLen_ = 0;
      cliExt_ = com.google.protobuf.ByteString.EMPTY;
      srvExt_ = com.google.protobuf.ByteString.EMPTY;
      cliExtGrease_ = 0;
      cliJA3_ = com.google.protobuf.ByteString.EMPTY;
      srvJA3_ = com.google.protobuf.ByteString.EMPTY;
      cliSessTicket_ = com.google.protobuf.ByteString.EMPTY;
      srvSessTicket_ = com.google.protobuf.ByteString.EMPTY;
      authTag_ = 0;
      cliCertCnt_ = 0;
      srvCertCnt_ = 0;
      ecGroupsCli_ = com.google.protobuf.ByteString.EMPTY;
      ecPoiForByServ_ = com.google.protobuf.ByteString.EMPTY;
      etags_ = com.google.protobuf.ByteString.EMPTY;
      ttags_ = com.google.protobuf.ByteString.EMPTY;
      cliSesIDLen_ = com.google.protobuf.ByteString.EMPTY;
      srvSesIDLen_ = com.google.protobuf.ByteString.EMPTY;
      srvKeyExcLen_ = com.google.protobuf.ByteString.EMPTY;
      eCDHCurType_ = com.google.protobuf.ByteString.EMPTY;
      eCDHSig_ = com.google.protobuf.ByteString.EMPTY;
      dHEPLen_ = com.google.protobuf.ByteString.EMPTY;
      dHEGLen_ = com.google.protobuf.ByteString.EMPTY;
      cliKeyExcLen_ = com.google.protobuf.ByteString.EMPTY;
      encPubKey_ = com.google.protobuf.ByteString.EMPTY;
      encPubKeyLen_ = com.google.protobuf.ByteString.EMPTY;
      cliExtLen_ = com.google.protobuf.ByteString.EMPTY;
      srvExtLen_ = com.google.protobuf.ByteString.EMPTY;
      eCDHPubKeyLen_ = com.google.protobuf.ByteString.EMPTY;
      namType_ = com.google.protobuf.ByteString.EMPTY;
      namLen_ = com.google.protobuf.ByteString.EMPTY;
      ticDat_ = com.google.protobuf.ByteString.EMPTY;
      srvCipSui_ = com.google.protobuf.ByteString.EMPTY;
      cipSuiNum_ = 0;
      eCDHSigHash_ = com.google.protobuf.ByteString.EMPTY;
      dHESigHash_ = com.google.protobuf.ByteString.EMPTY;
      rSASigHash_ = com.google.protobuf.ByteString.EMPTY;
      greaseFlag_ = com.google.protobuf.ByteString.EMPTY;
      rSAModLen_ = com.google.protobuf.ByteString.EMPTY;
      rSAExpLen_ = com.google.protobuf.ByteString.EMPTY;
      rSASig_ = com.google.protobuf.ByteString.EMPTY;
      dHESig_ = com.google.protobuf.ByteString.EMPTY;
      dHEPubKeyLen_ = com.google.protobuf.ByteString.EMPTY;
      dHEPubKey_ = com.google.protobuf.ByteString.EMPTY;
      sigAlgType_ = com.google.protobuf.ByteString.EMPTY;
      sigAlg_ = com.google.protobuf.ByteString.EMPTY;
      sigHashAlg_ = com.google.protobuf.ByteString.EMPTY;
      jOY_ = com.google.protobuf.ByteString.EMPTY;
      jOYS_ = com.google.protobuf.ByteString.EMPTY;
      sTARTTLS_ = com.google.protobuf.ByteString.EMPTY;
      certNonFlag_ = com.google.protobuf.ByteString.EMPTY;
      joyFp_ = com.google.protobuf.ByteString.EMPTY;
      certIntactFlag_ = com.google.protobuf.ByteString.EMPTY;
      certPath_ = com.google.protobuf.ByteString.EMPTY;
      sessSecFlag_ = com.google.protobuf.ByteString.EMPTY;
      fullText_ = com.google.protobuf.ByteString.EMPTY;
      cliCertHashes_ = com.google.protobuf.ByteString.EMPTY;
      srvCertHashes_ = com.google.protobuf.ByteString.EMPTY;
      cliCertNum_ = 0;
      srvCertNum_ = 0;
      certExist_ = 0;
      extendEcGroupsClient_ = com.google.protobuf.ByteString.EMPTY;
      leafCertDaysRemaining_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Ssl_TlsInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      int mutable_bitField1_ = 0;
      int mutable_bitField2_ = 0;
      int mutable_bitField3_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              conType_ = input.readUInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              aleLev_ = input.readUInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              aleDes_ = input.readUInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              handShaType_ = input.readUInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              cliVer_ = input.readUInt32();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              cliGMTUniTime_ = input.readUInt64();
              break;
            }
            case 58: {
              bitField0_ |= 0x00000040;
              cliRand_ = input.readBytes();
              break;
            }
            case 66: {
              bitField0_ |= 0x00000080;
              cliSesID_ = input.readBytes();
              break;
            }
            case 74: {
              bitField0_ |= 0x00000100;
              cliCipSui_ = input.readBytes();
              break;
            }
            case 82: {
              bitField0_ |= 0x00000200;
              cliComMet_ = input.readBytes();
              break;
            }
            case 88: {
              bitField0_ |= 0x00000400;
              srvVer_ = input.readUInt32();
              break;
            }
            case 98: {
              bitField0_ |= 0x00000800;
              srvName_ = input.readBytes();
              break;
            }
            case 104: {
              bitField0_ |= 0x00001000;
              srvNameAttr_ = input.readUInt32();
              break;
            }
            case 112: {
              bitField0_ |= 0x00002000;
              srvGMTUniTime14_ = input.readUInt64();
              break;
            }
            case 122: {
              bitField0_ |= 0x00004000;
              srvRand_ = input.readBytes();
              break;
            }
            case 130: {
              bitField0_ |= 0x00008000;
              srvSesID_ = input.readBytes();
              break;
            }
            case 138: {
              bitField0_ |= 0x00010000;
              srvComprMet_ = input.readBytes();
              break;
            }
            case 144: {
              bitField0_ |= 0x00020000;
              srvCertLen_ = input.readUInt32();
              break;
            }
            case 152: {
              bitField0_ |= 0x00040000;
              certResType_ = input.readUInt32();
              break;
            }
            case 160: {
              bitField0_ |= 0x00080000;
              cliCertLen_ = input.readUInt32();
              break;
            }
            case 170: {
              bitField0_ |= 0x00100000;
              rSAModOfSrvKeyExc_ = input.readBytes();
              break;
            }
            case 176: {
              bitField0_ |= 0x00200000;
              rSAExpOfSrvKeyExc_ = input.readUInt64();
              break;
            }
            case 186: {
              bitField0_ |= 0x00400000;
              dHModOfSrvKeyExc_ = input.readBytes();
              break;
            }
            case 194: {
              bitField0_ |= 0x00800000;
              dHGenOfSrvKeyExc_ = input.readBytes();
              break;
            }
            case 202: {
              bitField0_ |= 0x01000000;
              srvDHPubKey_ = input.readBytes();
              break;
            }
            case 210: {
              bitField0_ |= 0x02000000;
              preMasKeyEncryByRSA_ = input.readBytes();
              break;
            }
            case 218: {
              bitField0_ |= 0x04000000;
              cliDHPubKey_ = input.readBytes();
              break;
            }
            case 224: {
              bitField0_ |= 0x08000000;
              extTypeInSSL_ = input.readUInt32();
              break;
            }
            case 232: {
              bitField0_ |= 0x10000000;
              cliEllCurPoiFor_ = input.readUInt32();
              break;
            }
            case 240: {
              bitField0_ |= 0x20000000;
              cliEllCur_ = input.readUInt32();
              break;
            }
            case 248: {
              bitField0_ |= 0x40000000;
              srvEllCurPoiFor_ = input.readUInt32();
              break;
            }
            case 256: {
              bitField0_ |= 0x80000000;
              srvEllCur_ = input.readUInt32();
              break;
            }
            case 266: {
              bitField1_ |= 0x00000001;
              srvEllCurDHPubKey_ = input.readBytes();
              break;
            }
            case 274: {
              bitField1_ |= 0x00000002;
              cliEllCurDHPubKey_ = input.readBytes();
              break;
            }
            case 280: {
              bitField1_ |= 0x00000004;
              srvGMTUniTime35_ = input.readUInt64();
              break;
            }
            case 288: {
              bitField1_ |= 0x00000008;
              cliExtCnt_ = input.readUInt32();
              break;
            }
            case 296: {
              bitField1_ |= 0x00000010;
              srvExtCnt_ = input.readUInt32();
              break;
            }
            case 304: {
              bitField1_ |= 0x00000020;
              cliHandSkLen_ = input.readUInt32();
              break;
            }
            case 312: {
              bitField1_ |= 0x00000040;
              srvHandSkLen_ = input.readUInt32();
              break;
            }
            case 322: {
              bitField1_ |= 0x00000080;
              cliExt_ = input.readBytes();
              break;
            }
            case 330: {
              bitField1_ |= 0x00000100;
              srvExt_ = input.readBytes();
              break;
            }
            case 336: {
              bitField1_ |= 0x00000200;
              cliExtGrease_ = input.readUInt32();
              break;
            }
            case 346: {
              bitField1_ |= 0x00000400;
              cliJA3_ = input.readBytes();
              break;
            }
            case 354: {
              bitField1_ |= 0x00000800;
              srvJA3_ = input.readBytes();
              break;
            }
            case 362: {
              bitField1_ |= 0x00001000;
              cliSessTicket_ = input.readBytes();
              break;
            }
            case 370: {
              bitField1_ |= 0x00002000;
              srvSessTicket_ = input.readBytes();
              break;
            }
            case 376: {
              bitField1_ |= 0x00004000;
              authTag_ = input.readUInt32();
              break;
            }
            case 384: {
              bitField1_ |= 0x00008000;
              cliCertCnt_ = input.readUInt32();
              break;
            }
            case 392: {
              bitField1_ |= 0x00010000;
              srvCertCnt_ = input.readUInt32();
              break;
            }
            case 402: {
              bitField1_ |= 0x00020000;
              ecGroupsCli_ = input.readBytes();
              break;
            }
            case 410: {
              bitField1_ |= 0x00040000;
              ecPoiForByServ_ = input.readBytes();
              break;
            }
            case 418: {
              bitField1_ |= 0x00080000;
              etags_ = input.readBytes();
              break;
            }
            case 426: {
              bitField1_ |= 0x00100000;
              ttags_ = input.readBytes();
              break;
            }
            case 434: {
              bitField1_ |= 0x00200000;
              cliSesIDLen_ = input.readBytes();
              break;
            }
            case 442: {
              bitField1_ |= 0x00400000;
              srvSesIDLen_ = input.readBytes();
              break;
            }
            case 450: {
              bitField1_ |= 0x00800000;
              srvKeyExcLen_ = input.readBytes();
              break;
            }
            case 458: {
              bitField1_ |= 0x01000000;
              eCDHCurType_ = input.readBytes();
              break;
            }
            case 466: {
              bitField1_ |= 0x02000000;
              eCDHSig_ = input.readBytes();
              break;
            }
            case 474: {
              bitField1_ |= 0x04000000;
              dHEPLen_ = input.readBytes();
              break;
            }
            case 482: {
              bitField1_ |= 0x08000000;
              dHEGLen_ = input.readBytes();
              break;
            }
            case 490: {
              bitField1_ |= 0x10000000;
              cliKeyExcLen_ = input.readBytes();
              break;
            }
            case 498: {
              bitField1_ |= 0x20000000;
              encPubKey_ = input.readBytes();
              break;
            }
            case 506: {
              bitField1_ |= 0x40000000;
              encPubKeyLen_ = input.readBytes();
              break;
            }
            case 514: {
              bitField1_ |= 0x80000000;
              cliExtLen_ = input.readBytes();
              break;
            }
            case 522: {
              bitField2_ |= 0x00000001;
              srvExtLen_ = input.readBytes();
              break;
            }
            case 530: {
              bitField2_ |= 0x00000002;
              eCDHPubKeyLen_ = input.readBytes();
              break;
            }
            case 538: {
              bitField2_ |= 0x00000004;
              namType_ = input.readBytes();
              break;
            }
            case 546: {
              bitField2_ |= 0x00000008;
              namLen_ = input.readBytes();
              break;
            }
            case 554: {
              bitField2_ |= 0x00000010;
              ticDat_ = input.readBytes();
              break;
            }
            case 562: {
              bitField2_ |= 0x00000020;
              srvCipSui_ = input.readBytes();
              break;
            }
            case 568: {
              bitField2_ |= 0x00000040;
              cipSuiNum_ = input.readUInt32();
              break;
            }
            case 578: {
              bitField2_ |= 0x00000080;
              eCDHSigHash_ = input.readBytes();
              break;
            }
            case 586: {
              bitField2_ |= 0x00000100;
              dHESigHash_ = input.readBytes();
              break;
            }
            case 594: {
              bitField2_ |= 0x00000200;
              rSASigHash_ = input.readBytes();
              break;
            }
            case 602: {
              bitField2_ |= 0x00000400;
              greaseFlag_ = input.readBytes();
              break;
            }
            case 610: {
              bitField2_ |= 0x00000800;
              rSAModLen_ = input.readBytes();
              break;
            }
            case 618: {
              bitField2_ |= 0x00001000;
              rSAExpLen_ = input.readBytes();
              break;
            }
            case 626: {
              bitField2_ |= 0x00002000;
              rSASig_ = input.readBytes();
              break;
            }
            case 634: {
              bitField2_ |= 0x00004000;
              dHESig_ = input.readBytes();
              break;
            }
            case 642: {
              bitField2_ |= 0x00008000;
              dHEPubKeyLen_ = input.readBytes();
              break;
            }
            case 650: {
              bitField2_ |= 0x00010000;
              dHEPubKey_ = input.readBytes();
              break;
            }
            case 658: {
              bitField2_ |= 0x00020000;
              sigAlgType_ = input.readBytes();
              break;
            }
            case 666: {
              bitField2_ |= 0x00040000;
              sigAlg_ = input.readBytes();
              break;
            }
            case 674: {
              bitField2_ |= 0x00080000;
              sigHashAlg_ = input.readBytes();
              break;
            }
            case 682: {
              bitField2_ |= 0x00100000;
              jOY_ = input.readBytes();
              break;
            }
            case 690: {
              bitField2_ |= 0x00200000;
              jOYS_ = input.readBytes();
              break;
            }
            case 698: {
              bitField2_ |= 0x00400000;
              sTARTTLS_ = input.readBytes();
              break;
            }
            case 706: {
              bitField2_ |= 0x00800000;
              certNonFlag_ = input.readBytes();
              break;
            }
            case 714: {
              bitField2_ |= 0x01000000;
              joyFp_ = input.readBytes();
              break;
            }
            case 722: {
              bitField2_ |= 0x02000000;
              certIntactFlag_ = input.readBytes();
              break;
            }
            case 730: {
              bitField2_ |= 0x04000000;
              certPath_ = input.readBytes();
              break;
            }
            case 738: {
              bitField2_ |= 0x08000000;
              sessSecFlag_ = input.readBytes();
              break;
            }
            case 746: {
              bitField2_ |= 0x10000000;
              fullText_ = input.readBytes();
              break;
            }
            case 754: {
              bitField2_ |= 0x20000000;
              cliCertHashes_ = input.readBytes();
              break;
            }
            case 762: {
              bitField2_ |= 0x40000000;
              srvCertHashes_ = input.readBytes();
              break;
            }
            case 768: {
              bitField2_ |= 0x80000000;
              cliCertNum_ = input.readUInt32();
              break;
            }
            case 776: {
              bitField3_ |= 0x00000001;
              srvCertNum_ = input.readUInt32();
              break;
            }
            case 784: {
              bitField3_ |= 0x00000002;
              certExist_ = input.readUInt32();
              break;
            }
            case 794: {
              bitField3_ |= 0x00000004;
              extendEcGroupsClient_ = input.readBytes();
              break;
            }
            case 800: {
              bitField3_ |= 0x00000008;
              leafCertDaysRemaining_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return SslTlsInfo.internal_static_Ssl_TlsInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return SslTlsInfo.internal_static_Ssl_TlsInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              SslTlsInfo.Ssl_TlsInfo.class, SslTlsInfo.Ssl_TlsInfo.Builder.class);
    }

    private int bitField0_;
    private int bitField1_;
    private int bitField2_;
    private int bitField3_;
    public static final int CONTYPE_FIELD_NUMBER = 1;
    private int conType_;
    /**
     * <code>optional uint32 conType = 1;</code>
     */
    public boolean hasConType() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional uint32 conType = 1;</code>
     */
    public int getConType() {
      return conType_;
    }

    public static final int ALELEV_FIELD_NUMBER = 2;
    private int aleLev_;
    /**
     * <code>optional uint32 aleLev = 2;</code>
     */
    public boolean hasAleLev() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional uint32 aleLev = 2;</code>
     */
    public int getAleLev() {
      return aleLev_;
    }

    public static final int ALEDES_FIELD_NUMBER = 3;
    private int aleDes_;
    /**
     * <code>optional uint32 aleDes = 3;</code>
     */
    public boolean hasAleDes() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional uint32 aleDes = 3;</code>
     */
    public int getAleDes() {
      return aleDes_;
    }

    public static final int HANDSHATYPE_FIELD_NUMBER = 4;
    private int handShaType_;
    /**
     * <code>optional uint32 handShaType = 4;</code>
     */
    public boolean hasHandShaType() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional uint32 handShaType = 4;</code>
     */
    public int getHandShaType() {
      return handShaType_;
    }

    public static final int CLIVER_FIELD_NUMBER = 5;
    private int cliVer_;
    /**
     * <code>optional uint32 cliVer = 5;</code>
     */
    public boolean hasCliVer() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional uint32 cliVer = 5;</code>
     */
    public int getCliVer() {
      return cliVer_;
    }

    public static final int CLIGMTUNITIME_FIELD_NUMBER = 6;
    private long cliGMTUniTime_;
    /**
     * <code>optional uint64 cliGMTUniTime = 6;</code>
     */
    public boolean hasCliGMTUniTime() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional uint64 cliGMTUniTime = 6;</code>
     */
    public long getCliGMTUniTime() {
      return cliGMTUniTime_;
    }

    public static final int CLIRAND_FIELD_NUMBER = 7;
    private com.google.protobuf.ByteString cliRand_;
    /**
     * <code>optional bytes cliRand = 7;</code>
     */
    public boolean hasCliRand() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional bytes cliRand = 7;</code>
     */
    public com.google.protobuf.ByteString getCliRand() {
      return cliRand_;
    }

    public static final int CLISESID_FIELD_NUMBER = 8;
    private com.google.protobuf.ByteString cliSesID_;
    /**
     * <code>optional bytes cliSesID = 8;</code>
     */
    public boolean hasCliSesID() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional bytes cliSesID = 8;</code>
     */
    public com.google.protobuf.ByteString getCliSesID() {
      return cliSesID_;
    }

    public static final int CLICIPSUI_FIELD_NUMBER = 9;
    private com.google.protobuf.ByteString cliCipSui_;
    /**
     * <code>optional bytes cliCipSui = 9;</code>
     */
    public boolean hasCliCipSui() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional bytes cliCipSui = 9;</code>
     */
    public com.google.protobuf.ByteString getCliCipSui() {
      return cliCipSui_;
    }

    public static final int CLICOMMET_FIELD_NUMBER = 10;
    private com.google.protobuf.ByteString cliComMet_;
    /**
     * <code>optional bytes cliComMet = 10;</code>
     */
    public boolean hasCliComMet() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>optional bytes cliComMet = 10;</code>
     */
    public com.google.protobuf.ByteString getCliComMet() {
      return cliComMet_;
    }

    public static final int SRVVER_FIELD_NUMBER = 11;
    private int srvVer_;
    /**
     * <code>optional uint32 srvVer = 11;</code>
     */
    public boolean hasSrvVer() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <code>optional uint32 srvVer = 11;</code>
     */
    public int getSrvVer() {
      return srvVer_;
    }

    public static final int SRVNAME_FIELD_NUMBER = 12;
    private com.google.protobuf.ByteString srvName_;
    /**
     * <code>optional bytes srvName = 12;</code>
     */
    public boolean hasSrvName() {
      return ((bitField0_ & 0x00000800) == 0x00000800);
    }
    /**
     * <code>optional bytes srvName = 12;</code>
     */
    public com.google.protobuf.ByteString getSrvName() {
      return srvName_;
    }

    public static final int SRVNAMEATTR_FIELD_NUMBER = 13;
    private int srvNameAttr_;
    /**
     * <code>optional uint32 srvNameAttr = 13;</code>
     */
    public boolean hasSrvNameAttr() {
      return ((bitField0_ & 0x00001000) == 0x00001000);
    }
    /**
     * <code>optional uint32 srvNameAttr = 13;</code>
     */
    public int getSrvNameAttr() {
      return srvNameAttr_;
    }

    public static final int SRVGMTUNITIME_FIELD_NUMBER = 14;
    private long srvGMTUniTime14_;
    // An alternative name is used for field "srvGMTUniTime" because:
    //     capitalized name of field "srvGMTUniTime" conflicts with field "srvGMTUni_Time"
    /**
     * <code>optional uint64 srvGMTUniTime = 14;</code>
     */
    public boolean hasSrvGMTUniTime14() {
      return ((bitField0_ & 0x00002000) == 0x00002000);
    }
    /**
     * <code>optional uint64 srvGMTUniTime = 14;</code>
     */
    public long getSrvGMTUniTime14() {
      return srvGMTUniTime14_;
    }

    public static final int SRVRAND_FIELD_NUMBER = 15;
    private com.google.protobuf.ByteString srvRand_;
    /**
     * <code>optional bytes srvRand = 15;</code>
     */
    public boolean hasSrvRand() {
      return ((bitField0_ & 0x00004000) == 0x00004000);
    }
    /**
     * <code>optional bytes srvRand = 15;</code>
     */
    public com.google.protobuf.ByteString getSrvRand() {
      return srvRand_;
    }

    public static final int SRVSESID_FIELD_NUMBER = 16;
    private com.google.protobuf.ByteString srvSesID_;
    /**
     * <code>optional bytes srvSesID = 16;</code>
     */
    public boolean hasSrvSesID() {
      return ((bitField0_ & 0x00008000) == 0x00008000);
    }
    /**
     * <code>optional bytes srvSesID = 16;</code>
     */
    public com.google.protobuf.ByteString getSrvSesID() {
      return srvSesID_;
    }

    public static final int SRVCOMPRMET_FIELD_NUMBER = 17;
    private com.google.protobuf.ByteString srvComprMet_;
    /**
     * <code>optional bytes srvComprMet = 17;</code>
     */
    public boolean hasSrvComprMet() {
      return ((bitField0_ & 0x00010000) == 0x00010000);
    }
    /**
     * <code>optional bytes srvComprMet = 17;</code>
     */
    public com.google.protobuf.ByteString getSrvComprMet() {
      return srvComprMet_;
    }

    public static final int SRVCERTLEN_FIELD_NUMBER = 18;
    private int srvCertLen_;
    /**
     * <code>optional uint32 srvCertLen = 18;</code>
     */
    public boolean hasSrvCertLen() {
      return ((bitField0_ & 0x00020000) == 0x00020000);
    }
    /**
     * <code>optional uint32 srvCertLen = 18;</code>
     */
    public int getSrvCertLen() {
      return srvCertLen_;
    }

    public static final int CERTRESTYPE_FIELD_NUMBER = 19;
    private int certResType_;
    /**
     * <code>optional uint32 certResType = 19;</code>
     */
    public boolean hasCertResType() {
      return ((bitField0_ & 0x00040000) == 0x00040000);
    }
    /**
     * <code>optional uint32 certResType = 19;</code>
     */
    public int getCertResType() {
      return certResType_;
    }

    public static final int CLICERTLEN_FIELD_NUMBER = 20;
    private int cliCertLen_;
    /**
     * <code>optional uint32 cliCertLen = 20;</code>
     */
    public boolean hasCliCertLen() {
      return ((bitField0_ & 0x00080000) == 0x00080000);
    }
    /**
     * <code>optional uint32 cliCertLen = 20;</code>
     */
    public int getCliCertLen() {
      return cliCertLen_;
    }

    public static final int RSAMODOFSRVKEYEXC_FIELD_NUMBER = 21;
    private com.google.protobuf.ByteString rSAModOfSrvKeyExc_;
    /**
     * <code>optional bytes RSAModOfSrvKeyExc = 21;</code>
     */
    public boolean hasRSAModOfSrvKeyExc() {
      return ((bitField0_ & 0x00100000) == 0x00100000);
    }
    /**
     * <code>optional bytes RSAModOfSrvKeyExc = 21;</code>
     */
    public com.google.protobuf.ByteString getRSAModOfSrvKeyExc() {
      return rSAModOfSrvKeyExc_;
    }

    public static final int RSAEXPOFSRVKEYEXC_FIELD_NUMBER = 22;
    private long rSAExpOfSrvKeyExc_;
    /**
     * <code>optional uint64 RSAExpOfSrvKeyExc = 22;</code>
     */
    public boolean hasRSAExpOfSrvKeyExc() {
      return ((bitField0_ & 0x00200000) == 0x00200000);
    }
    /**
     * <code>optional uint64 RSAExpOfSrvKeyExc = 22;</code>
     */
    public long getRSAExpOfSrvKeyExc() {
      return rSAExpOfSrvKeyExc_;
    }

    public static final int DHMODOFSRVKEYEXC_FIELD_NUMBER = 23;
    private com.google.protobuf.ByteString dHModOfSrvKeyExc_;
    /**
     * <code>optional bytes DHModOfSrvKeyExc = 23;</code>
     */
    public boolean hasDHModOfSrvKeyExc() {
      return ((bitField0_ & 0x00400000) == 0x00400000);
    }
    /**
     * <code>optional bytes DHModOfSrvKeyExc = 23;</code>
     */
    public com.google.protobuf.ByteString getDHModOfSrvKeyExc() {
      return dHModOfSrvKeyExc_;
    }

    public static final int DHGENOFSRVKEYEXC_FIELD_NUMBER = 24;
    private com.google.protobuf.ByteString dHGenOfSrvKeyExc_;
    /**
     * <code>optional bytes DHGenOfSrvKeyExc = 24;</code>
     */
    public boolean hasDHGenOfSrvKeyExc() {
      return ((bitField0_ & 0x00800000) == 0x00800000);
    }
    /**
     * <code>optional bytes DHGenOfSrvKeyExc = 24;</code>
     */
    public com.google.protobuf.ByteString getDHGenOfSrvKeyExc() {
      return dHGenOfSrvKeyExc_;
    }

    public static final int SRVDHPUBKEY_FIELD_NUMBER = 25;
    private com.google.protobuf.ByteString srvDHPubKey_;
    /**
     * <code>optional bytes srvDHPubKey = 25;</code>
     */
    public boolean hasSrvDHPubKey() {
      return ((bitField0_ & 0x01000000) == 0x01000000);
    }
    /**
     * <code>optional bytes srvDHPubKey = 25;</code>
     */
    public com.google.protobuf.ByteString getSrvDHPubKey() {
      return srvDHPubKey_;
    }

    public static final int PREMASKEYENCRYBYRSA_FIELD_NUMBER = 26;
    private com.google.protobuf.ByteString preMasKeyEncryByRSA_;
    /**
     * <code>optional bytes preMasKeyEncryByRSA = 26;</code>
     */
    public boolean hasPreMasKeyEncryByRSA() {
      return ((bitField0_ & 0x02000000) == 0x02000000);
    }
    /**
     * <code>optional bytes preMasKeyEncryByRSA = 26;</code>
     */
    public com.google.protobuf.ByteString getPreMasKeyEncryByRSA() {
      return preMasKeyEncryByRSA_;
    }

    public static final int CLIDHPUBKEY_FIELD_NUMBER = 27;
    private com.google.protobuf.ByteString cliDHPubKey_;
    /**
     * <code>optional bytes cliDHPubKey = 27;</code>
     */
    public boolean hasCliDHPubKey() {
      return ((bitField0_ & 0x04000000) == 0x04000000);
    }
    /**
     * <code>optional bytes cliDHPubKey = 27;</code>
     */
    public com.google.protobuf.ByteString getCliDHPubKey() {
      return cliDHPubKey_;
    }

    public static final int EXTTYPEINSSL_FIELD_NUMBER = 28;
    private int extTypeInSSL_;
    /**
     * <code>optional uint32 extTypeInSSL = 28;</code>
     */
    public boolean hasExtTypeInSSL() {
      return ((bitField0_ & 0x08000000) == 0x08000000);
    }
    /**
     * <code>optional uint32 extTypeInSSL = 28;</code>
     */
    public int getExtTypeInSSL() {
      return extTypeInSSL_;
    }

    public static final int CLIELLCURPOIFOR_FIELD_NUMBER = 29;
    private int cliEllCurPoiFor_;
    /**
     * <code>optional uint32 cliEllCurPoiFor = 29;</code>
     */
    public boolean hasCliEllCurPoiFor() {
      return ((bitField0_ & 0x10000000) == 0x10000000);
    }
    /**
     * <code>optional uint32 cliEllCurPoiFor = 29;</code>
     */
    public int getCliEllCurPoiFor() {
      return cliEllCurPoiFor_;
    }

    public static final int CLIELLCUR_FIELD_NUMBER = 30;
    private int cliEllCur_;
    /**
     * <code>optional uint32 cliEllCur = 30;</code>
     */
    public boolean hasCliEllCur() {
      return ((bitField0_ & 0x20000000) == 0x20000000);
    }
    /**
     * <code>optional uint32 cliEllCur = 30;</code>
     */
    public int getCliEllCur() {
      return cliEllCur_;
    }

    public static final int SRVELLCURPOIFOR_FIELD_NUMBER = 31;
    private int srvEllCurPoiFor_;
    /**
     * <code>optional uint32 srvEllCurPoiFor = 31;</code>
     */
    public boolean hasSrvEllCurPoiFor() {
      return ((bitField0_ & 0x40000000) == 0x40000000);
    }
    /**
     * <code>optional uint32 srvEllCurPoiFor = 31;</code>
     */
    public int getSrvEllCurPoiFor() {
      return srvEllCurPoiFor_;
    }

    public static final int SRVELLCUR_FIELD_NUMBER = 32;
    private int srvEllCur_;
    /**
     * <code>optional uint32 srvEllCur = 32;</code>
     */
    public boolean hasSrvEllCur() {
      return ((bitField0_ & 0x80000000) == 0x80000000);
    }
    /**
     * <code>optional uint32 srvEllCur = 32;</code>
     */
    public int getSrvEllCur() {
      return srvEllCur_;
    }

    public static final int SRVELLCURDHPUBKEY_FIELD_NUMBER = 33;
    private com.google.protobuf.ByteString srvEllCurDHPubKey_;
    /**
     * <code>optional bytes srvEllCurDHPubKey = 33;</code>
     */
    public boolean hasSrvEllCurDHPubKey() {
      return ((bitField1_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional bytes srvEllCurDHPubKey = 33;</code>
     */
    public com.google.protobuf.ByteString getSrvEllCurDHPubKey() {
      return srvEllCurDHPubKey_;
    }

    public static final int CLIELLCURDHPUBKEY_FIELD_NUMBER = 34;
    private com.google.protobuf.ByteString cliEllCurDHPubKey_;
    /**
     * <code>optional bytes cliEllCurDHPubKey = 34;</code>
     */
    public boolean hasCliEllCurDHPubKey() {
      return ((bitField1_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional bytes cliEllCurDHPubKey = 34;</code>
     */
    public com.google.protobuf.ByteString getCliEllCurDHPubKey() {
      return cliEllCurDHPubKey_;
    }

    public static final int SRVGMTUNI_TIME_FIELD_NUMBER = 35;
    private long srvGMTUniTime35_;
    // An alternative name is used for field "srvGMTUni_Time" because:
    //     capitalized name of field "srvGMTUniTime" conflicts with field "srvGMTUni_Time"
    /**
     * <code>optional uint64 srvGMTUni_Time = 35;</code>
     */
    public boolean hasSrvGMTUniTime35() {
      return ((bitField1_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional uint64 srvGMTUni_Time = 35;</code>
     */
    public long getSrvGMTUniTime35() {
      return srvGMTUniTime35_;
    }

    public static final int CLIEXTCNT_FIELD_NUMBER = 36;
    private int cliExtCnt_;
    /**
     * <code>optional uint32 cliExtCnt = 36;</code>
     */
    public boolean hasCliExtCnt() {
      return ((bitField1_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional uint32 cliExtCnt = 36;</code>
     */
    public int getCliExtCnt() {
      return cliExtCnt_;
    }

    public static final int SRVEXTCNT_FIELD_NUMBER = 37;
    private int srvExtCnt_;
    /**
     * <code>optional uint32 srvExtCnt = 37;</code>
     */
    public boolean hasSrvExtCnt() {
      return ((bitField1_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional uint32 srvExtCnt = 37;</code>
     */
    public int getSrvExtCnt() {
      return srvExtCnt_;
    }

    public static final int CLIHANDSKLEN_FIELD_NUMBER = 38;
    private int cliHandSkLen_;
    /**
     * <code>optional uint32 cliHandSkLen = 38;</code>
     */
    public boolean hasCliHandSkLen() {
      return ((bitField1_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional uint32 cliHandSkLen = 38;</code>
     */
    public int getCliHandSkLen() {
      return cliHandSkLen_;
    }

    public static final int SRVHANDSKLEN_FIELD_NUMBER = 39;
    private int srvHandSkLen_;
    /**
     * <code>optional uint32 srvHandSkLen = 39;</code>
     */
    public boolean hasSrvHandSkLen() {
      return ((bitField1_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional uint32 srvHandSkLen = 39;</code>
     */
    public int getSrvHandSkLen() {
      return srvHandSkLen_;
    }

    public static final int CLIEXT_FIELD_NUMBER = 40;
    private com.google.protobuf.ByteString cliExt_;
    /**
     * <code>optional bytes cliExt = 40;</code>
     */
    public boolean hasCliExt() {
      return ((bitField1_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional bytes cliExt = 40;</code>
     */
    public com.google.protobuf.ByteString getCliExt() {
      return cliExt_;
    }

    public static final int SRVEXT_FIELD_NUMBER = 41;
    private com.google.protobuf.ByteString srvExt_;
    /**
     * <code>optional bytes srvExt = 41;</code>
     */
    public boolean hasSrvExt() {
      return ((bitField1_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional bytes srvExt = 41;</code>
     */
    public com.google.protobuf.ByteString getSrvExt() {
      return srvExt_;
    }

    public static final int CLIEXTGREASE_FIELD_NUMBER = 42;
    private int cliExtGrease_;
    /**
     * <code>optional uint32 cliExtGrease = 42;</code>
     */
    public boolean hasCliExtGrease() {
      return ((bitField1_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>optional uint32 cliExtGrease = 42;</code>
     */
    public int getCliExtGrease() {
      return cliExtGrease_;
    }

    public static final int CLIJA3_FIELD_NUMBER = 43;
    private com.google.protobuf.ByteString cliJA3_;
    /**
     * <code>optional bytes cliJA3 = 43;</code>
     */
    public boolean hasCliJA3() {
      return ((bitField1_ & 0x00000400) == 0x00000400);
    }
    /**
     * <code>optional bytes cliJA3 = 43;</code>
     */
    public com.google.protobuf.ByteString getCliJA3() {
      return cliJA3_;
    }

    public static final int SRVJA3_FIELD_NUMBER = 44;
    private com.google.protobuf.ByteString srvJA3_;
    /**
     * <code>optional bytes srvJA3 = 44;</code>
     */
    public boolean hasSrvJA3() {
      return ((bitField1_ & 0x00000800) == 0x00000800);
    }
    /**
     * <code>optional bytes srvJA3 = 44;</code>
     */
    public com.google.protobuf.ByteString getSrvJA3() {
      return srvJA3_;
    }

    public static final int CLISESSTICKET_FIELD_NUMBER = 45;
    private com.google.protobuf.ByteString cliSessTicket_;
    /**
     * <code>optional bytes cliSessTicket = 45;</code>
     */
    public boolean hasCliSessTicket() {
      return ((bitField1_ & 0x00001000) == 0x00001000);
    }
    /**
     * <code>optional bytes cliSessTicket = 45;</code>
     */
    public com.google.protobuf.ByteString getCliSessTicket() {
      return cliSessTicket_;
    }

    public static final int SRVSESSTICKET_FIELD_NUMBER = 46;
    private com.google.protobuf.ByteString srvSessTicket_;
    /**
     * <code>optional bytes srvSessTicket = 46;</code>
     */
    public boolean hasSrvSessTicket() {
      return ((bitField1_ & 0x00002000) == 0x00002000);
    }
    /**
     * <code>optional bytes srvSessTicket = 46;</code>
     */
    public com.google.protobuf.ByteString getSrvSessTicket() {
      return srvSessTicket_;
    }

    public static final int AUTHTAG_FIELD_NUMBER = 47;
    private int authTag_;
    /**
     * <code>optional uint32 AuthTag = 47;</code>
     */
    public boolean hasAuthTag() {
      return ((bitField1_ & 0x00004000) == 0x00004000);
    }
    /**
     * <code>optional uint32 AuthTag = 47;</code>
     */
    public int getAuthTag() {
      return authTag_;
    }

    public static final int CLICERTCNT_FIELD_NUMBER = 48;
    private int cliCertCnt_;
    /**
     * <code>optional uint32 cliCertCnt = 48;</code>
     */
    public boolean hasCliCertCnt() {
      return ((bitField1_ & 0x00008000) == 0x00008000);
    }
    /**
     * <code>optional uint32 cliCertCnt = 48;</code>
     */
    public int getCliCertCnt() {
      return cliCertCnt_;
    }

    public static final int SRVCERTCNT_FIELD_NUMBER = 49;
    private int srvCertCnt_;
    /**
     * <code>optional uint32 srvCertCnt = 49;</code>
     */
    public boolean hasSrvCertCnt() {
      return ((bitField1_ & 0x00010000) == 0x00010000);
    }
    /**
     * <code>optional uint32 srvCertCnt = 49;</code>
     */
    public int getSrvCertCnt() {
      return srvCertCnt_;
    }

    public static final int ECGROUPSCLI_FIELD_NUMBER = 50;
    private com.google.protobuf.ByteString ecGroupsCli_;
    /**
     * <code>optional bytes ecGroupsCli = 50;</code>
     */
    public boolean hasEcGroupsCli() {
      return ((bitField1_ & 0x00020000) == 0x00020000);
    }
    /**
     * <code>optional bytes ecGroupsCli = 50;</code>
     */
    public com.google.protobuf.ByteString getEcGroupsCli() {
      return ecGroupsCli_;
    }

    public static final int ECPOIFORBYSERV_FIELD_NUMBER = 51;
    private com.google.protobuf.ByteString ecPoiForByServ_;
    /**
     * <code>optional bytes ecPoiForByServ = 51;</code>
     */
    public boolean hasEcPoiForByServ() {
      return ((bitField1_ & 0x00040000) == 0x00040000);
    }
    /**
     * <code>optional bytes ecPoiForByServ = 51;</code>
     */
    public com.google.protobuf.ByteString getEcPoiForByServ() {
      return ecPoiForByServ_;
    }

    public static final int ETAGS_FIELD_NUMBER = 52;
    private com.google.protobuf.ByteString etags_;
    /**
     * <code>optional bytes etags = 52;</code>
     */
    public boolean hasEtags() {
      return ((bitField1_ & 0x00080000) == 0x00080000);
    }
    /**
     * <code>optional bytes etags = 52;</code>
     */
    public com.google.protobuf.ByteString getEtags() {
      return etags_;
    }

    public static final int TTAGS_FIELD_NUMBER = 53;
    private com.google.protobuf.ByteString ttags_;
    /**
     * <code>optional bytes ttags = 53;</code>
     */
    public boolean hasTtags() {
      return ((bitField1_ & 0x00100000) == 0x00100000);
    }
    /**
     * <code>optional bytes ttags = 53;</code>
     */
    public com.google.protobuf.ByteString getTtags() {
      return ttags_;
    }

    public static final int CLISESIDLEN_FIELD_NUMBER = 54;
    private com.google.protobuf.ByteString cliSesIDLen_;
    /**
     * <code>optional bytes cliSesIDLen = 54;</code>
     */
    public boolean hasCliSesIDLen() {
      return ((bitField1_ & 0x00200000) == 0x00200000);
    }
    /**
     * <code>optional bytes cliSesIDLen = 54;</code>
     */
    public com.google.protobuf.ByteString getCliSesIDLen() {
      return cliSesIDLen_;
    }

    public static final int SRVSESIDLEN_FIELD_NUMBER = 55;
    private com.google.protobuf.ByteString srvSesIDLen_;
    /**
     * <code>optional bytes srvSesIDLen = 55;</code>
     */
    public boolean hasSrvSesIDLen() {
      return ((bitField1_ & 0x00400000) == 0x00400000);
    }
    /**
     * <code>optional bytes srvSesIDLen = 55;</code>
     */
    public com.google.protobuf.ByteString getSrvSesIDLen() {
      return srvSesIDLen_;
    }

    public static final int SRVKEYEXCLEN_FIELD_NUMBER = 56;
    private com.google.protobuf.ByteString srvKeyExcLen_;
    /**
     * <code>optional bytes srvKeyExcLen = 56;</code>
     */
    public boolean hasSrvKeyExcLen() {
      return ((bitField1_ & 0x00800000) == 0x00800000);
    }
    /**
     * <code>optional bytes srvKeyExcLen = 56;</code>
     */
    public com.google.protobuf.ByteString getSrvKeyExcLen() {
      return srvKeyExcLen_;
    }

    public static final int ECDHCURTYPE_FIELD_NUMBER = 57;
    private com.google.protobuf.ByteString eCDHCurType_;
    /**
     * <code>optional bytes ECDHCurType = 57;</code>
     */
    public boolean hasECDHCurType() {
      return ((bitField1_ & 0x01000000) == 0x01000000);
    }
    /**
     * <code>optional bytes ECDHCurType = 57;</code>
     */
    public com.google.protobuf.ByteString getECDHCurType() {
      return eCDHCurType_;
    }

    public static final int ECDHSIG_FIELD_NUMBER = 58;
    private com.google.protobuf.ByteString eCDHSig_;
    /**
     * <code>optional bytes ECDHSig = 58;</code>
     */
    public boolean hasECDHSig() {
      return ((bitField1_ & 0x02000000) == 0x02000000);
    }
    /**
     * <code>optional bytes ECDHSig = 58;</code>
     */
    public com.google.protobuf.ByteString getECDHSig() {
      return eCDHSig_;
    }

    public static final int DHEPLEN_FIELD_NUMBER = 59;
    private com.google.protobuf.ByteString dHEPLen_;
    /**
     * <code>optional bytes DHEPLen = 59;</code>
     */
    public boolean hasDHEPLen() {
      return ((bitField1_ & 0x04000000) == 0x04000000);
    }
    /**
     * <code>optional bytes DHEPLen = 59;</code>
     */
    public com.google.protobuf.ByteString getDHEPLen() {
      return dHEPLen_;
    }

    public static final int DHEGLEN_FIELD_NUMBER = 60;
    private com.google.protobuf.ByteString dHEGLen_;
    /**
     * <code>optional bytes DHEGLen = 60;</code>
     */
    public boolean hasDHEGLen() {
      return ((bitField1_ & 0x08000000) == 0x08000000);
    }
    /**
     * <code>optional bytes DHEGLen = 60;</code>
     */
    public com.google.protobuf.ByteString getDHEGLen() {
      return dHEGLen_;
    }

    public static final int CLIKEYEXCLEN_FIELD_NUMBER = 61;
    private com.google.protobuf.ByteString cliKeyExcLen_;
    /**
     * <code>optional bytes cliKeyExcLen = 61;</code>
     */
    public boolean hasCliKeyExcLen() {
      return ((bitField1_ & 0x10000000) == 0x10000000);
    }
    /**
     * <code>optional bytes cliKeyExcLen = 61;</code>
     */
    public com.google.protobuf.ByteString getCliKeyExcLen() {
      return cliKeyExcLen_;
    }

    public static final int ENCPUBKEY_FIELD_NUMBER = 62;
    private com.google.protobuf.ByteString encPubKey_;
    /**
     * <code>optional bytes encPubKey = 62;</code>
     */
    public boolean hasEncPubKey() {
      return ((bitField1_ & 0x20000000) == 0x20000000);
    }
    /**
     * <code>optional bytes encPubKey = 62;</code>
     */
    public com.google.protobuf.ByteString getEncPubKey() {
      return encPubKey_;
    }

    public static final int ENCPUBKEYLEN_FIELD_NUMBER = 63;
    private com.google.protobuf.ByteString encPubKeyLen_;
    /**
     * <code>optional bytes encPubKeyLen = 63;</code>
     */
    public boolean hasEncPubKeyLen() {
      return ((bitField1_ & 0x40000000) == 0x40000000);
    }
    /**
     * <code>optional bytes encPubKeyLen = 63;</code>
     */
    public com.google.protobuf.ByteString getEncPubKeyLen() {
      return encPubKeyLen_;
    }

    public static final int CLIEXTLEN_FIELD_NUMBER = 64;
    private com.google.protobuf.ByteString cliExtLen_;
    /**
     * <code>optional bytes cliExtLen = 64;</code>
     */
    public boolean hasCliExtLen() {
      return ((bitField1_ & 0x80000000) == 0x80000000);
    }
    /**
     * <code>optional bytes cliExtLen = 64;</code>
     */
    public com.google.protobuf.ByteString getCliExtLen() {
      return cliExtLen_;
    }

    public static final int SRVEXTLEN_FIELD_NUMBER = 65;
    private com.google.protobuf.ByteString srvExtLen_;
    /**
     * <code>optional bytes srvExtLen = 65;</code>
     */
    public boolean hasSrvExtLen() {
      return ((bitField2_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional bytes srvExtLen = 65;</code>
     */
    public com.google.protobuf.ByteString getSrvExtLen() {
      return srvExtLen_;
    }

    public static final int ECDHPUBKEYLEN_FIELD_NUMBER = 66;
    private com.google.protobuf.ByteString eCDHPubKeyLen_;
    /**
     * <code>optional bytes ECDHPubKeyLen = 66;</code>
     */
    public boolean hasECDHPubKeyLen() {
      return ((bitField2_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional bytes ECDHPubKeyLen = 66;</code>
     */
    public com.google.protobuf.ByteString getECDHPubKeyLen() {
      return eCDHPubKeyLen_;
    }

    public static final int NAMTYPE_FIELD_NUMBER = 67;
    private com.google.protobuf.ByteString namType_;
    /**
     * <code>optional bytes namType = 67;</code>
     */
    public boolean hasNamType() {
      return ((bitField2_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional bytes namType = 67;</code>
     */
    public com.google.protobuf.ByteString getNamType() {
      return namType_;
    }

    public static final int NAMLEN_FIELD_NUMBER = 68;
    private com.google.protobuf.ByteString namLen_;
    /**
     * <code>optional bytes namLen = 68;</code>
     */
    public boolean hasNamLen() {
      return ((bitField2_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional bytes namLen = 68;</code>
     */
    public com.google.protobuf.ByteString getNamLen() {
      return namLen_;
    }

    public static final int TICDAT_FIELD_NUMBER = 69;
    private com.google.protobuf.ByteString ticDat_;
    /**
     * <code>optional bytes ticDat = 69;</code>
     */
    public boolean hasTicDat() {
      return ((bitField2_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional bytes ticDat = 69;</code>
     */
    public com.google.protobuf.ByteString getTicDat() {
      return ticDat_;
    }

    public static final int SRVCIPSUI_FIELD_NUMBER = 70;
    private com.google.protobuf.ByteString srvCipSui_;
    /**
     * <code>optional bytes srvCipSui = 70;</code>
     */
    public boolean hasSrvCipSui() {
      return ((bitField2_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional bytes srvCipSui = 70;</code>
     */
    public com.google.protobuf.ByteString getSrvCipSui() {
      return srvCipSui_;
    }

    public static final int CIPSUINUM_FIELD_NUMBER = 71;
    private int cipSuiNum_;
    /**
     * <code>optional uint32 cipSuiNum = 71;</code>
     */
    public boolean hasCipSuiNum() {
      return ((bitField2_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional uint32 cipSuiNum = 71;</code>
     */
    public int getCipSuiNum() {
      return cipSuiNum_;
    }

    public static final int ECDHSIGHASH_FIELD_NUMBER = 72;
    private com.google.protobuf.ByteString eCDHSigHash_;
    /**
     * <code>optional bytes ECDHSigHash = 72;</code>
     */
    public boolean hasECDHSigHash() {
      return ((bitField2_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional bytes ECDHSigHash = 72;</code>
     */
    public com.google.protobuf.ByteString getECDHSigHash() {
      return eCDHSigHash_;
    }

    public static final int DHESIGHASH_FIELD_NUMBER = 73;
    private com.google.protobuf.ByteString dHESigHash_;
    /**
     * <code>optional bytes DHESigHash = 73;</code>
     */
    public boolean hasDHESigHash() {
      return ((bitField2_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional bytes DHESigHash = 73;</code>
     */
    public com.google.protobuf.ByteString getDHESigHash() {
      return dHESigHash_;
    }

    public static final int RSASIGHASH_FIELD_NUMBER = 74;
    private com.google.protobuf.ByteString rSASigHash_;
    /**
     * <code>optional bytes RSASigHash = 74;</code>
     */
    public boolean hasRSASigHash() {
      return ((bitField2_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>optional bytes RSASigHash = 74;</code>
     */
    public com.google.protobuf.ByteString getRSASigHash() {
      return rSASigHash_;
    }

    public static final int GREASEFLAG_FIELD_NUMBER = 75;
    private com.google.protobuf.ByteString greaseFlag_;
    /**
     * <code>optional bytes greaseFlag = 75;</code>
     */
    public boolean hasGreaseFlag() {
      return ((bitField2_ & 0x00000400) == 0x00000400);
    }
    /**
     * <code>optional bytes greaseFlag = 75;</code>
     */
    public com.google.protobuf.ByteString getGreaseFlag() {
      return greaseFlag_;
    }

    public static final int RSAMODLEN_FIELD_NUMBER = 76;
    private com.google.protobuf.ByteString rSAModLen_;
    /**
     * <code>optional bytes RSAModLen = 76;</code>
     */
    public boolean hasRSAModLen() {
      return ((bitField2_ & 0x00000800) == 0x00000800);
    }
    /**
     * <code>optional bytes RSAModLen = 76;</code>
     */
    public com.google.protobuf.ByteString getRSAModLen() {
      return rSAModLen_;
    }

    public static final int RSAEXPLEN_FIELD_NUMBER = 77;
    private com.google.protobuf.ByteString rSAExpLen_;
    /**
     * <code>optional bytes RSAExpLen = 77;</code>
     */
    public boolean hasRSAExpLen() {
      return ((bitField2_ & 0x00001000) == 0x00001000);
    }
    /**
     * <code>optional bytes RSAExpLen = 77;</code>
     */
    public com.google.protobuf.ByteString getRSAExpLen() {
      return rSAExpLen_;
    }

    public static final int RSASIG_FIELD_NUMBER = 78;
    private com.google.protobuf.ByteString rSASig_;
    /**
     * <code>optional bytes RSASig = 78;</code>
     */
    public boolean hasRSASig() {
      return ((bitField2_ & 0x00002000) == 0x00002000);
    }
    /**
     * <code>optional bytes RSASig = 78;</code>
     */
    public com.google.protobuf.ByteString getRSASig() {
      return rSASig_;
    }

    public static final int DHESIG_FIELD_NUMBER = 79;
    private com.google.protobuf.ByteString dHESig_;
    /**
     * <code>optional bytes DHESig = 79;</code>
     */
    public boolean hasDHESig() {
      return ((bitField2_ & 0x00004000) == 0x00004000);
    }
    /**
     * <code>optional bytes DHESig = 79;</code>
     */
    public com.google.protobuf.ByteString getDHESig() {
      return dHESig_;
    }

    public static final int DHEPUBKEYLEN_FIELD_NUMBER = 80;
    private com.google.protobuf.ByteString dHEPubKeyLen_;
    /**
     * <code>optional bytes DHEPubKeyLen = 80;</code>
     */
    public boolean hasDHEPubKeyLen() {
      return ((bitField2_ & 0x00008000) == 0x00008000);
    }
    /**
     * <code>optional bytes DHEPubKeyLen = 80;</code>
     */
    public com.google.protobuf.ByteString getDHEPubKeyLen() {
      return dHEPubKeyLen_;
    }

    public static final int DHEPUBKEY_FIELD_NUMBER = 81;
    private com.google.protobuf.ByteString dHEPubKey_;
    /**
     * <code>optional bytes DHEPubKey = 81;</code>
     */
    public boolean hasDHEPubKey() {
      return ((bitField2_ & 0x00010000) == 0x00010000);
    }
    /**
     * <code>optional bytes DHEPubKey = 81;</code>
     */
    public com.google.protobuf.ByteString getDHEPubKey() {
      return dHEPubKey_;
    }

    public static final int SIGALGTYPE_FIELD_NUMBER = 82;
    private com.google.protobuf.ByteString sigAlgType_;
    /**
     * <code>optional bytes SigAlgType = 82;</code>
     */
    public boolean hasSigAlgType() {
      return ((bitField2_ & 0x00020000) == 0x00020000);
    }
    /**
     * <code>optional bytes SigAlgType = 82;</code>
     */
    public com.google.protobuf.ByteString getSigAlgType() {
      return sigAlgType_;
    }

    public static final int SIGALG_FIELD_NUMBER = 83;
    private com.google.protobuf.ByteString sigAlg_;
    /**
     * <code>optional bytes sigAlg = 83;</code>
     */
    public boolean hasSigAlg() {
      return ((bitField2_ & 0x00040000) == 0x00040000);
    }
    /**
     * <code>optional bytes sigAlg = 83;</code>
     */
    public com.google.protobuf.ByteString getSigAlg() {
      return sigAlg_;
    }

    public static final int SIGHASHALG_FIELD_NUMBER = 84;
    private com.google.protobuf.ByteString sigHashAlg_;
    /**
     * <code>optional bytes SigHashAlg = 84;</code>
     */
    public boolean hasSigHashAlg() {
      return ((bitField2_ & 0x00080000) == 0x00080000);
    }
    /**
     * <code>optional bytes SigHashAlg = 84;</code>
     */
    public com.google.protobuf.ByteString getSigHashAlg() {
      return sigHashAlg_;
    }

    public static final int JOY_FIELD_NUMBER = 85;
    private com.google.protobuf.ByteString jOY_;
    /**
     * <code>optional bytes JOY = 85;</code>
     */
    public boolean hasJOY() {
      return ((bitField2_ & 0x00100000) == 0x00100000);
    }
    /**
     * <code>optional bytes JOY = 85;</code>
     */
    public com.google.protobuf.ByteString getJOY() {
      return jOY_;
    }

    public static final int JOYS_FIELD_NUMBER = 86;
    private com.google.protobuf.ByteString jOYS_;
    /**
     * <code>optional bytes JOYS = 86;</code>
     */
    public boolean hasJOYS() {
      return ((bitField2_ & 0x00200000) == 0x00200000);
    }
    /**
     * <code>optional bytes JOYS = 86;</code>
     */
    public com.google.protobuf.ByteString getJOYS() {
      return jOYS_;
    }

    public static final int STARTTLS_FIELD_NUMBER = 87;
    private com.google.protobuf.ByteString sTARTTLS_;
    /**
     * <code>optional bytes STARTTLS = 87;</code>
     */
    public boolean hasSTARTTLS() {
      return ((bitField2_ & 0x00400000) == 0x00400000);
    }
    /**
     * <code>optional bytes STARTTLS = 87;</code>
     */
    public com.google.protobuf.ByteString getSTARTTLS() {
      return sTARTTLS_;
    }

    public static final int CERTNONFLAG_FIELD_NUMBER = 88;
    private com.google.protobuf.ByteString certNonFlag_;
    /**
     * <code>optional bytes certNonFlag = 88;</code>
     */
    public boolean hasCertNonFlag() {
      return ((bitField2_ & 0x00800000) == 0x00800000);
    }
    /**
     * <code>optional bytes certNonFlag = 88;</code>
     */
    public com.google.protobuf.ByteString getCertNonFlag() {
      return certNonFlag_;
    }

    public static final int JOYFP_FIELD_NUMBER = 89;
    private com.google.protobuf.ByteString joyFp_;
    /**
     * <code>optional bytes JoyFp = 89;</code>
     */
    public boolean hasJoyFp() {
      return ((bitField2_ & 0x01000000) == 0x01000000);
    }
    /**
     * <code>optional bytes JoyFp = 89;</code>
     */
    public com.google.protobuf.ByteString getJoyFp() {
      return joyFp_;
    }

    public static final int CERTINTACTFLAG_FIELD_NUMBER = 90;
    private com.google.protobuf.ByteString certIntactFlag_;
    /**
     * <code>optional bytes certIntactFlag = 90;</code>
     */
    public boolean hasCertIntactFlag() {
      return ((bitField2_ & 0x02000000) == 0x02000000);
    }
    /**
     * <code>optional bytes certIntactFlag = 90;</code>
     */
    public com.google.protobuf.ByteString getCertIntactFlag() {
      return certIntactFlag_;
    }

    public static final int CERTPATH_FIELD_NUMBER = 91;
    private com.google.protobuf.ByteString certPath_;
    /**
     * <code>optional bytes certPath = 91;</code>
     */
    public boolean hasCertPath() {
      return ((bitField2_ & 0x04000000) == 0x04000000);
    }
    /**
     * <code>optional bytes certPath = 91;</code>
     */
    public com.google.protobuf.ByteString getCertPath() {
      return certPath_;
    }

    public static final int SESSSECFLAG_FIELD_NUMBER = 92;
    private com.google.protobuf.ByteString sessSecFlag_;
    /**
     * <code>optional bytes sessSecFlag = 92;</code>
     */
    public boolean hasSessSecFlag() {
      return ((bitField2_ & 0x08000000) == 0x08000000);
    }
    /**
     * <code>optional bytes sessSecFlag = 92;</code>
     */
    public com.google.protobuf.ByteString getSessSecFlag() {
      return sessSecFlag_;
    }

    public static final int FULLTEXT_FIELD_NUMBER = 93;
    private com.google.protobuf.ByteString fullText_;
    /**
     * <code>optional bytes fullText = 93;</code>
     */
    public boolean hasFullText() {
      return ((bitField2_ & 0x10000000) == 0x10000000);
    }
    /**
     * <code>optional bytes fullText = 93;</code>
     */
    public com.google.protobuf.ByteString getFullText() {
      return fullText_;
    }

    public static final int CLICERTHASHES_FIELD_NUMBER = 94;
    private com.google.protobuf.ByteString cliCertHashes_;
    /**
     * <code>optional bytes cliCertHashes = 94;</code>
     */
    public boolean hasCliCertHashes() {
      return ((bitField2_ & 0x20000000) == 0x20000000);
    }
    /**
     * <code>optional bytes cliCertHashes = 94;</code>
     */
    public com.google.protobuf.ByteString getCliCertHashes() {
      return cliCertHashes_;
    }

    public static final int SRVCERTHASHES_FIELD_NUMBER = 95;
    private com.google.protobuf.ByteString srvCertHashes_;
    /**
     * <code>optional bytes srvCertHashes = 95;</code>
     */
    public boolean hasSrvCertHashes() {
      return ((bitField2_ & 0x40000000) == 0x40000000);
    }
    /**
     * <code>optional bytes srvCertHashes = 95;</code>
     */
    public com.google.protobuf.ByteString getSrvCertHashes() {
      return srvCertHashes_;
    }

    public static final int CLICERTNUM_FIELD_NUMBER = 96;
    private int cliCertNum_;
    /**
     * <code>optional uint32 cliCertNum = 96;</code>
     */
    public boolean hasCliCertNum() {
      return ((bitField2_ & 0x80000000) == 0x80000000);
    }
    /**
     * <code>optional uint32 cliCertNum = 96;</code>
     */
    public int getCliCertNum() {
      return cliCertNum_;
    }

    public static final int SRVCERTNUM_FIELD_NUMBER = 97;
    private int srvCertNum_;
    /**
     * <code>optional uint32 srvCertNum = 97;</code>
     */
    public boolean hasSrvCertNum() {
      return ((bitField3_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional uint32 srvCertNum = 97;</code>
     */
    public int getSrvCertNum() {
      return srvCertNum_;
    }

    public static final int CERTEXIST_FIELD_NUMBER = 98;
    private int certExist_;
    /**
     * <code>optional uint32 certExist = 98;</code>
     */
    public boolean hasCertExist() {
      return ((bitField3_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional uint32 certExist = 98;</code>
     */
    public int getCertExist() {
      return certExist_;
    }

    public static final int EXTENDECGROUPSCLIENT_FIELD_NUMBER = 99;
    private com.google.protobuf.ByteString extendEcGroupsClient_;
    /**
     * <code>optional bytes extendEcGroupsClient = 99;</code>
     */
    public boolean hasExtendEcGroupsClient() {
      return ((bitField3_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional bytes extendEcGroupsClient = 99;</code>
     */
    public com.google.protobuf.ByteString getExtendEcGroupsClient() {
      return extendEcGroupsClient_;
    }

    public static final int LEAFCERTDAYSREMAINING_FIELD_NUMBER = 100;
    private int leafCertDaysRemaining_;
    /**
     * <code>optional uint32 leafCertDaysRemaining = 100;</code>
     */
    public boolean hasLeafCertDaysRemaining() {
      return ((bitField3_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional uint32 leafCertDaysRemaining = 100;</code>
     */
    public int getLeafCertDaysRemaining() {
      return leafCertDaysRemaining_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeUInt32(1, conType_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeUInt32(2, aleLev_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeUInt32(3, aleDes_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeUInt32(4, handShaType_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeUInt32(5, cliVer_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeUInt64(6, cliGMTUniTime_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeBytes(7, cliRand_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeBytes(8, cliSesID_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        output.writeBytes(9, cliCipSui_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        output.writeBytes(10, cliComMet_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        output.writeUInt32(11, srvVer_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        output.writeBytes(12, srvName_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        output.writeUInt32(13, srvNameAttr_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        output.writeUInt64(14, srvGMTUniTime14_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        output.writeBytes(15, srvRand_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        output.writeBytes(16, srvSesID_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        output.writeBytes(17, srvComprMet_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        output.writeUInt32(18, srvCertLen_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        output.writeUInt32(19, certResType_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        output.writeUInt32(20, cliCertLen_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        output.writeBytes(21, rSAModOfSrvKeyExc_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        output.writeUInt64(22, rSAExpOfSrvKeyExc_);
      }
      if (((bitField0_ & 0x00400000) == 0x00400000)) {
        output.writeBytes(23, dHModOfSrvKeyExc_);
      }
      if (((bitField0_ & 0x00800000) == 0x00800000)) {
        output.writeBytes(24, dHGenOfSrvKeyExc_);
      }
      if (((bitField0_ & 0x01000000) == 0x01000000)) {
        output.writeBytes(25, srvDHPubKey_);
      }
      if (((bitField0_ & 0x02000000) == 0x02000000)) {
        output.writeBytes(26, preMasKeyEncryByRSA_);
      }
      if (((bitField0_ & 0x04000000) == 0x04000000)) {
        output.writeBytes(27, cliDHPubKey_);
      }
      if (((bitField0_ & 0x08000000) == 0x08000000)) {
        output.writeUInt32(28, extTypeInSSL_);
      }
      if (((bitField0_ & 0x10000000) == 0x10000000)) {
        output.writeUInt32(29, cliEllCurPoiFor_);
      }
      if (((bitField0_ & 0x20000000) == 0x20000000)) {
        output.writeUInt32(30, cliEllCur_);
      }
      if (((bitField0_ & 0x40000000) == 0x40000000)) {
        output.writeUInt32(31, srvEllCurPoiFor_);
      }
      if (((bitField0_ & 0x80000000) == 0x80000000)) {
        output.writeUInt32(32, srvEllCur_);
      }
      if (((bitField1_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(33, srvEllCurDHPubKey_);
      }
      if (((bitField1_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(34, cliEllCurDHPubKey_);
      }
      if (((bitField1_ & 0x00000004) == 0x00000004)) {
        output.writeUInt64(35, srvGMTUniTime35_);
      }
      if (((bitField1_ & 0x00000008) == 0x00000008)) {
        output.writeUInt32(36, cliExtCnt_);
      }
      if (((bitField1_ & 0x00000010) == 0x00000010)) {
        output.writeUInt32(37, srvExtCnt_);
      }
      if (((bitField1_ & 0x00000020) == 0x00000020)) {
        output.writeUInt32(38, cliHandSkLen_);
      }
      if (((bitField1_ & 0x00000040) == 0x00000040)) {
        output.writeUInt32(39, srvHandSkLen_);
      }
      if (((bitField1_ & 0x00000080) == 0x00000080)) {
        output.writeBytes(40, cliExt_);
      }
      if (((bitField1_ & 0x00000100) == 0x00000100)) {
        output.writeBytes(41, srvExt_);
      }
      if (((bitField1_ & 0x00000200) == 0x00000200)) {
        output.writeUInt32(42, cliExtGrease_);
      }
      if (((bitField1_ & 0x00000400) == 0x00000400)) {
        output.writeBytes(43, cliJA3_);
      }
      if (((bitField1_ & 0x00000800) == 0x00000800)) {
        output.writeBytes(44, srvJA3_);
      }
      if (((bitField1_ & 0x00001000) == 0x00001000)) {
        output.writeBytes(45, cliSessTicket_);
      }
      if (((bitField1_ & 0x00002000) == 0x00002000)) {
        output.writeBytes(46, srvSessTicket_);
      }
      if (((bitField1_ & 0x00004000) == 0x00004000)) {
        output.writeUInt32(47, authTag_);
      }
      if (((bitField1_ & 0x00008000) == 0x00008000)) {
        output.writeUInt32(48, cliCertCnt_);
      }
      if (((bitField1_ & 0x00010000) == 0x00010000)) {
        output.writeUInt32(49, srvCertCnt_);
      }
      if (((bitField1_ & 0x00020000) == 0x00020000)) {
        output.writeBytes(50, ecGroupsCli_);
      }
      if (((bitField1_ & 0x00040000) == 0x00040000)) {
        output.writeBytes(51, ecPoiForByServ_);
      }
      if (((bitField1_ & 0x00080000) == 0x00080000)) {
        output.writeBytes(52, etags_);
      }
      if (((bitField1_ & 0x00100000) == 0x00100000)) {
        output.writeBytes(53, ttags_);
      }
      if (((bitField1_ & 0x00200000) == 0x00200000)) {
        output.writeBytes(54, cliSesIDLen_);
      }
      if (((bitField1_ & 0x00400000) == 0x00400000)) {
        output.writeBytes(55, srvSesIDLen_);
      }
      if (((bitField1_ & 0x00800000) == 0x00800000)) {
        output.writeBytes(56, srvKeyExcLen_);
      }
      if (((bitField1_ & 0x01000000) == 0x01000000)) {
        output.writeBytes(57, eCDHCurType_);
      }
      if (((bitField1_ & 0x02000000) == 0x02000000)) {
        output.writeBytes(58, eCDHSig_);
      }
      if (((bitField1_ & 0x04000000) == 0x04000000)) {
        output.writeBytes(59, dHEPLen_);
      }
      if (((bitField1_ & 0x08000000) == 0x08000000)) {
        output.writeBytes(60, dHEGLen_);
      }
      if (((bitField1_ & 0x10000000) == 0x10000000)) {
        output.writeBytes(61, cliKeyExcLen_);
      }
      if (((bitField1_ & 0x20000000) == 0x20000000)) {
        output.writeBytes(62, encPubKey_);
      }
      if (((bitField1_ & 0x40000000) == 0x40000000)) {
        output.writeBytes(63, encPubKeyLen_);
      }
      if (((bitField1_ & 0x80000000) == 0x80000000)) {
        output.writeBytes(64, cliExtLen_);
      }
      if (((bitField2_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(65, srvExtLen_);
      }
      if (((bitField2_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(66, eCDHPubKeyLen_);
      }
      if (((bitField2_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(67, namType_);
      }
      if (((bitField2_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(68, namLen_);
      }
      if (((bitField2_ & 0x00000010) == 0x00000010)) {
        output.writeBytes(69, ticDat_);
      }
      if (((bitField2_ & 0x00000020) == 0x00000020)) {
        output.writeBytes(70, srvCipSui_);
      }
      if (((bitField2_ & 0x00000040) == 0x00000040)) {
        output.writeUInt32(71, cipSuiNum_);
      }
      if (((bitField2_ & 0x00000080) == 0x00000080)) {
        output.writeBytes(72, eCDHSigHash_);
      }
      if (((bitField2_ & 0x00000100) == 0x00000100)) {
        output.writeBytes(73, dHESigHash_);
      }
      if (((bitField2_ & 0x00000200) == 0x00000200)) {
        output.writeBytes(74, rSASigHash_);
      }
      if (((bitField2_ & 0x00000400) == 0x00000400)) {
        output.writeBytes(75, greaseFlag_);
      }
      if (((bitField2_ & 0x00000800) == 0x00000800)) {
        output.writeBytes(76, rSAModLen_);
      }
      if (((bitField2_ & 0x00001000) == 0x00001000)) {
        output.writeBytes(77, rSAExpLen_);
      }
      if (((bitField2_ & 0x00002000) == 0x00002000)) {
        output.writeBytes(78, rSASig_);
      }
      if (((bitField2_ & 0x00004000) == 0x00004000)) {
        output.writeBytes(79, dHESig_);
      }
      if (((bitField2_ & 0x00008000) == 0x00008000)) {
        output.writeBytes(80, dHEPubKeyLen_);
      }
      if (((bitField2_ & 0x00010000) == 0x00010000)) {
        output.writeBytes(81, dHEPubKey_);
      }
      if (((bitField2_ & 0x00020000) == 0x00020000)) {
        output.writeBytes(82, sigAlgType_);
      }
      if (((bitField2_ & 0x00040000) == 0x00040000)) {
        output.writeBytes(83, sigAlg_);
      }
      if (((bitField2_ & 0x00080000) == 0x00080000)) {
        output.writeBytes(84, sigHashAlg_);
      }
      if (((bitField2_ & 0x00100000) == 0x00100000)) {
        output.writeBytes(85, jOY_);
      }
      if (((bitField2_ & 0x00200000) == 0x00200000)) {
        output.writeBytes(86, jOYS_);
      }
      if (((bitField2_ & 0x00400000) == 0x00400000)) {
        output.writeBytes(87, sTARTTLS_);
      }
      if (((bitField2_ & 0x00800000) == 0x00800000)) {
        output.writeBytes(88, certNonFlag_);
      }
      if (((bitField2_ & 0x01000000) == 0x01000000)) {
        output.writeBytes(89, joyFp_);
      }
      if (((bitField2_ & 0x02000000) == 0x02000000)) {
        output.writeBytes(90, certIntactFlag_);
      }
      if (((bitField2_ & 0x04000000) == 0x04000000)) {
        output.writeBytes(91, certPath_);
      }
      if (((bitField2_ & 0x08000000) == 0x08000000)) {
        output.writeBytes(92, sessSecFlag_);
      }
      if (((bitField2_ & 0x10000000) == 0x10000000)) {
        output.writeBytes(93, fullText_);
      }
      if (((bitField2_ & 0x20000000) == 0x20000000)) {
        output.writeBytes(94, cliCertHashes_);
      }
      if (((bitField2_ & 0x40000000) == 0x40000000)) {
        output.writeBytes(95, srvCertHashes_);
      }
      if (((bitField2_ & 0x80000000) == 0x80000000)) {
        output.writeUInt32(96, cliCertNum_);
      }
      if (((bitField3_ & 0x00000001) == 0x00000001)) {
        output.writeUInt32(97, srvCertNum_);
      }
      if (((bitField3_ & 0x00000002) == 0x00000002)) {
        output.writeUInt32(98, certExist_);
      }
      if (((bitField3_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(99, extendEcGroupsClient_);
      }
      if (((bitField3_ & 0x00000008) == 0x00000008)) {
        output.writeUInt32(100, leafCertDaysRemaining_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, conType_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, aleLev_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, aleDes_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, handShaType_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, cliVer_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(6, cliGMTUniTime_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(7, cliRand_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(8, cliSesID_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(9, cliCipSui_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(10, cliComMet_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(11, srvVer_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(12, srvName_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(13, srvNameAttr_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(14, srvGMTUniTime14_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(15, srvRand_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(16, srvSesID_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(17, srvComprMet_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(18, srvCertLen_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(19, certResType_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(20, cliCertLen_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(21, rSAModOfSrvKeyExc_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(22, rSAExpOfSrvKeyExc_);
      }
      if (((bitField0_ & 0x00400000) == 0x00400000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(23, dHModOfSrvKeyExc_);
      }
      if (((bitField0_ & 0x00800000) == 0x00800000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(24, dHGenOfSrvKeyExc_);
      }
      if (((bitField0_ & 0x01000000) == 0x01000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(25, srvDHPubKey_);
      }
      if (((bitField0_ & 0x02000000) == 0x02000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(26, preMasKeyEncryByRSA_);
      }
      if (((bitField0_ & 0x04000000) == 0x04000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(27, cliDHPubKey_);
      }
      if (((bitField0_ & 0x08000000) == 0x08000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(28, extTypeInSSL_);
      }
      if (((bitField0_ & 0x10000000) == 0x10000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(29, cliEllCurPoiFor_);
      }
      if (((bitField0_ & 0x20000000) == 0x20000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(30, cliEllCur_);
      }
      if (((bitField0_ & 0x40000000) == 0x40000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(31, srvEllCurPoiFor_);
      }
      if (((bitField0_ & 0x80000000) == 0x80000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(32, srvEllCur_);
      }
      if (((bitField1_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(33, srvEllCurDHPubKey_);
      }
      if (((bitField1_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(34, cliEllCurDHPubKey_);
      }
      if (((bitField1_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(35, srvGMTUniTime35_);
      }
      if (((bitField1_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(36, cliExtCnt_);
      }
      if (((bitField1_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(37, srvExtCnt_);
      }
      if (((bitField1_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(38, cliHandSkLen_);
      }
      if (((bitField1_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(39, srvHandSkLen_);
      }
      if (((bitField1_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(40, cliExt_);
      }
      if (((bitField1_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(41, srvExt_);
      }
      if (((bitField1_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(42, cliExtGrease_);
      }
      if (((bitField1_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(43, cliJA3_);
      }
      if (((bitField1_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(44, srvJA3_);
      }
      if (((bitField1_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(45, cliSessTicket_);
      }
      if (((bitField1_ & 0x00002000) == 0x00002000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(46, srvSessTicket_);
      }
      if (((bitField1_ & 0x00004000) == 0x00004000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(47, authTag_);
      }
      if (((bitField1_ & 0x00008000) == 0x00008000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(48, cliCertCnt_);
      }
      if (((bitField1_ & 0x00010000) == 0x00010000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(49, srvCertCnt_);
      }
      if (((bitField1_ & 0x00020000) == 0x00020000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(50, ecGroupsCli_);
      }
      if (((bitField1_ & 0x00040000) == 0x00040000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(51, ecPoiForByServ_);
      }
      if (((bitField1_ & 0x00080000) == 0x00080000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(52, etags_);
      }
      if (((bitField1_ & 0x00100000) == 0x00100000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(53, ttags_);
      }
      if (((bitField1_ & 0x00200000) == 0x00200000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(54, cliSesIDLen_);
      }
      if (((bitField1_ & 0x00400000) == 0x00400000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(55, srvSesIDLen_);
      }
      if (((bitField1_ & 0x00800000) == 0x00800000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(56, srvKeyExcLen_);
      }
      if (((bitField1_ & 0x01000000) == 0x01000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(57, eCDHCurType_);
      }
      if (((bitField1_ & 0x02000000) == 0x02000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(58, eCDHSig_);
      }
      if (((bitField1_ & 0x04000000) == 0x04000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(59, dHEPLen_);
      }
      if (((bitField1_ & 0x08000000) == 0x08000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(60, dHEGLen_);
      }
      if (((bitField1_ & 0x10000000) == 0x10000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(61, cliKeyExcLen_);
      }
      if (((bitField1_ & 0x20000000) == 0x20000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(62, encPubKey_);
      }
      if (((bitField1_ & 0x40000000) == 0x40000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(63, encPubKeyLen_);
      }
      if (((bitField1_ & 0x80000000) == 0x80000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(64, cliExtLen_);
      }
      if (((bitField2_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(65, srvExtLen_);
      }
      if (((bitField2_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(66, eCDHPubKeyLen_);
      }
      if (((bitField2_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(67, namType_);
      }
      if (((bitField2_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(68, namLen_);
      }
      if (((bitField2_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(69, ticDat_);
      }
      if (((bitField2_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(70, srvCipSui_);
      }
      if (((bitField2_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(71, cipSuiNum_);
      }
      if (((bitField2_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(72, eCDHSigHash_);
      }
      if (((bitField2_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(73, dHESigHash_);
      }
      if (((bitField2_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(74, rSASigHash_);
      }
      if (((bitField2_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(75, greaseFlag_);
      }
      if (((bitField2_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(76, rSAModLen_);
      }
      if (((bitField2_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(77, rSAExpLen_);
      }
      if (((bitField2_ & 0x00002000) == 0x00002000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(78, rSASig_);
      }
      if (((bitField2_ & 0x00004000) == 0x00004000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(79, dHESig_);
      }
      if (((bitField2_ & 0x00008000) == 0x00008000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(80, dHEPubKeyLen_);
      }
      if (((bitField2_ & 0x00010000) == 0x00010000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(81, dHEPubKey_);
      }
      if (((bitField2_ & 0x00020000) == 0x00020000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(82, sigAlgType_);
      }
      if (((bitField2_ & 0x00040000) == 0x00040000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(83, sigAlg_);
      }
      if (((bitField2_ & 0x00080000) == 0x00080000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(84, sigHashAlg_);
      }
      if (((bitField2_ & 0x00100000) == 0x00100000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(85, jOY_);
      }
      if (((bitField2_ & 0x00200000) == 0x00200000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(86, jOYS_);
      }
      if (((bitField2_ & 0x00400000) == 0x00400000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(87, sTARTTLS_);
      }
      if (((bitField2_ & 0x00800000) == 0x00800000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(88, certNonFlag_);
      }
      if (((bitField2_ & 0x01000000) == 0x01000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(89, joyFp_);
      }
      if (((bitField2_ & 0x02000000) == 0x02000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(90, certIntactFlag_);
      }
      if (((bitField2_ & 0x04000000) == 0x04000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(91, certPath_);
      }
      if (((bitField2_ & 0x08000000) == 0x08000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(92, sessSecFlag_);
      }
      if (((bitField2_ & 0x10000000) == 0x10000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(93, fullText_);
      }
      if (((bitField2_ & 0x20000000) == 0x20000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(94, cliCertHashes_);
      }
      if (((bitField2_ & 0x40000000) == 0x40000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(95, srvCertHashes_);
      }
      if (((bitField2_ & 0x80000000) == 0x80000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(96, cliCertNum_);
      }
      if (((bitField3_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(97, srvCertNum_);
      }
      if (((bitField3_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(98, certExist_);
      }
      if (((bitField3_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(99, extendEcGroupsClient_);
      }
      if (((bitField3_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(100, leafCertDaysRemaining_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof SslTlsInfo.Ssl_TlsInfo)) {
        return super.equals(obj);
      }
      SslTlsInfo.Ssl_TlsInfo other = (SslTlsInfo.Ssl_TlsInfo) obj;

      boolean result = true;
      result = result && (hasConType() == other.hasConType());
      if (hasConType()) {
        result = result && (getConType()
            == other.getConType());
      }
      result = result && (hasAleLev() == other.hasAleLev());
      if (hasAleLev()) {
        result = result && (getAleLev()
            == other.getAleLev());
      }
      result = result && (hasAleDes() == other.hasAleDes());
      if (hasAleDes()) {
        result = result && (getAleDes()
            == other.getAleDes());
      }
      result = result && (hasHandShaType() == other.hasHandShaType());
      if (hasHandShaType()) {
        result = result && (getHandShaType()
            == other.getHandShaType());
      }
      result = result && (hasCliVer() == other.hasCliVer());
      if (hasCliVer()) {
        result = result && (getCliVer()
            == other.getCliVer());
      }
      result = result && (hasCliGMTUniTime() == other.hasCliGMTUniTime());
      if (hasCliGMTUniTime()) {
        result = result && (getCliGMTUniTime()
            == other.getCliGMTUniTime());
      }
      result = result && (hasCliRand() == other.hasCliRand());
      if (hasCliRand()) {
        result = result && getCliRand()
            .equals(other.getCliRand());
      }
      result = result && (hasCliSesID() == other.hasCliSesID());
      if (hasCliSesID()) {
        result = result && getCliSesID()
            .equals(other.getCliSesID());
      }
      result = result && (hasCliCipSui() == other.hasCliCipSui());
      if (hasCliCipSui()) {
        result = result && getCliCipSui()
            .equals(other.getCliCipSui());
      }
      result = result && (hasCliComMet() == other.hasCliComMet());
      if (hasCliComMet()) {
        result = result && getCliComMet()
            .equals(other.getCliComMet());
      }
      result = result && (hasSrvVer() == other.hasSrvVer());
      if (hasSrvVer()) {
        result = result && (getSrvVer()
            == other.getSrvVer());
      }
      result = result && (hasSrvName() == other.hasSrvName());
      if (hasSrvName()) {
        result = result && getSrvName()
            .equals(other.getSrvName());
      }
      result = result && (hasSrvNameAttr() == other.hasSrvNameAttr());
      if (hasSrvNameAttr()) {
        result = result && (getSrvNameAttr()
            == other.getSrvNameAttr());
      }
      result = result && (hasSrvGMTUniTime14() == other.hasSrvGMTUniTime14());
      if (hasSrvGMTUniTime14()) {
        result = result && (getSrvGMTUniTime14()
            == other.getSrvGMTUniTime14());
      }
      result = result && (hasSrvRand() == other.hasSrvRand());
      if (hasSrvRand()) {
        result = result && getSrvRand()
            .equals(other.getSrvRand());
      }
      result = result && (hasSrvSesID() == other.hasSrvSesID());
      if (hasSrvSesID()) {
        result = result && getSrvSesID()
            .equals(other.getSrvSesID());
      }
      result = result && (hasSrvComprMet() == other.hasSrvComprMet());
      if (hasSrvComprMet()) {
        result = result && getSrvComprMet()
            .equals(other.getSrvComprMet());
      }
      result = result && (hasSrvCertLen() == other.hasSrvCertLen());
      if (hasSrvCertLen()) {
        result = result && (getSrvCertLen()
            == other.getSrvCertLen());
      }
      result = result && (hasCertResType() == other.hasCertResType());
      if (hasCertResType()) {
        result = result && (getCertResType()
            == other.getCertResType());
      }
      result = result && (hasCliCertLen() == other.hasCliCertLen());
      if (hasCliCertLen()) {
        result = result && (getCliCertLen()
            == other.getCliCertLen());
      }
      result = result && (hasRSAModOfSrvKeyExc() == other.hasRSAModOfSrvKeyExc());
      if (hasRSAModOfSrvKeyExc()) {
        result = result && getRSAModOfSrvKeyExc()
            .equals(other.getRSAModOfSrvKeyExc());
      }
      result = result && (hasRSAExpOfSrvKeyExc() == other.hasRSAExpOfSrvKeyExc());
      if (hasRSAExpOfSrvKeyExc()) {
        result = result && (getRSAExpOfSrvKeyExc()
            == other.getRSAExpOfSrvKeyExc());
      }
      result = result && (hasDHModOfSrvKeyExc() == other.hasDHModOfSrvKeyExc());
      if (hasDHModOfSrvKeyExc()) {
        result = result && getDHModOfSrvKeyExc()
            .equals(other.getDHModOfSrvKeyExc());
      }
      result = result && (hasDHGenOfSrvKeyExc() == other.hasDHGenOfSrvKeyExc());
      if (hasDHGenOfSrvKeyExc()) {
        result = result && getDHGenOfSrvKeyExc()
            .equals(other.getDHGenOfSrvKeyExc());
      }
      result = result && (hasSrvDHPubKey() == other.hasSrvDHPubKey());
      if (hasSrvDHPubKey()) {
        result = result && getSrvDHPubKey()
            .equals(other.getSrvDHPubKey());
      }
      result = result && (hasPreMasKeyEncryByRSA() == other.hasPreMasKeyEncryByRSA());
      if (hasPreMasKeyEncryByRSA()) {
        result = result && getPreMasKeyEncryByRSA()
            .equals(other.getPreMasKeyEncryByRSA());
      }
      result = result && (hasCliDHPubKey() == other.hasCliDHPubKey());
      if (hasCliDHPubKey()) {
        result = result && getCliDHPubKey()
            .equals(other.getCliDHPubKey());
      }
      result = result && (hasExtTypeInSSL() == other.hasExtTypeInSSL());
      if (hasExtTypeInSSL()) {
        result = result && (getExtTypeInSSL()
            == other.getExtTypeInSSL());
      }
      result = result && (hasCliEllCurPoiFor() == other.hasCliEllCurPoiFor());
      if (hasCliEllCurPoiFor()) {
        result = result && (getCliEllCurPoiFor()
            == other.getCliEllCurPoiFor());
      }
      result = result && (hasCliEllCur() == other.hasCliEllCur());
      if (hasCliEllCur()) {
        result = result && (getCliEllCur()
            == other.getCliEllCur());
      }
      result = result && (hasSrvEllCurPoiFor() == other.hasSrvEllCurPoiFor());
      if (hasSrvEllCurPoiFor()) {
        result = result && (getSrvEllCurPoiFor()
            == other.getSrvEllCurPoiFor());
      }
      result = result && (hasSrvEllCur() == other.hasSrvEllCur());
      if (hasSrvEllCur()) {
        result = result && (getSrvEllCur()
            == other.getSrvEllCur());
      }
      result = result && (hasSrvEllCurDHPubKey() == other.hasSrvEllCurDHPubKey());
      if (hasSrvEllCurDHPubKey()) {
        result = result && getSrvEllCurDHPubKey()
            .equals(other.getSrvEllCurDHPubKey());
      }
      result = result && (hasCliEllCurDHPubKey() == other.hasCliEllCurDHPubKey());
      if (hasCliEllCurDHPubKey()) {
        result = result && getCliEllCurDHPubKey()
            .equals(other.getCliEllCurDHPubKey());
      }
      result = result && (hasSrvGMTUniTime35() == other.hasSrvGMTUniTime35());
      if (hasSrvGMTUniTime35()) {
        result = result && (getSrvGMTUniTime35()
            == other.getSrvGMTUniTime35());
      }
      result = result && (hasCliExtCnt() == other.hasCliExtCnt());
      if (hasCliExtCnt()) {
        result = result && (getCliExtCnt()
            == other.getCliExtCnt());
      }
      result = result && (hasSrvExtCnt() == other.hasSrvExtCnt());
      if (hasSrvExtCnt()) {
        result = result && (getSrvExtCnt()
            == other.getSrvExtCnt());
      }
      result = result && (hasCliHandSkLen() == other.hasCliHandSkLen());
      if (hasCliHandSkLen()) {
        result = result && (getCliHandSkLen()
            == other.getCliHandSkLen());
      }
      result = result && (hasSrvHandSkLen() == other.hasSrvHandSkLen());
      if (hasSrvHandSkLen()) {
        result = result && (getSrvHandSkLen()
            == other.getSrvHandSkLen());
      }
      result = result && (hasCliExt() == other.hasCliExt());
      if (hasCliExt()) {
        result = result && getCliExt()
            .equals(other.getCliExt());
      }
      result = result && (hasSrvExt() == other.hasSrvExt());
      if (hasSrvExt()) {
        result = result && getSrvExt()
            .equals(other.getSrvExt());
      }
      result = result && (hasCliExtGrease() == other.hasCliExtGrease());
      if (hasCliExtGrease()) {
        result = result && (getCliExtGrease()
            == other.getCliExtGrease());
      }
      result = result && (hasCliJA3() == other.hasCliJA3());
      if (hasCliJA3()) {
        result = result && getCliJA3()
            .equals(other.getCliJA3());
      }
      result = result && (hasSrvJA3() == other.hasSrvJA3());
      if (hasSrvJA3()) {
        result = result && getSrvJA3()
            .equals(other.getSrvJA3());
      }
      result = result && (hasCliSessTicket() == other.hasCliSessTicket());
      if (hasCliSessTicket()) {
        result = result && getCliSessTicket()
            .equals(other.getCliSessTicket());
      }
      result = result && (hasSrvSessTicket() == other.hasSrvSessTicket());
      if (hasSrvSessTicket()) {
        result = result && getSrvSessTicket()
            .equals(other.getSrvSessTicket());
      }
      result = result && (hasAuthTag() == other.hasAuthTag());
      if (hasAuthTag()) {
        result = result && (getAuthTag()
            == other.getAuthTag());
      }
      result = result && (hasCliCertCnt() == other.hasCliCertCnt());
      if (hasCliCertCnt()) {
        result = result && (getCliCertCnt()
            == other.getCliCertCnt());
      }
      result = result && (hasSrvCertCnt() == other.hasSrvCertCnt());
      if (hasSrvCertCnt()) {
        result = result && (getSrvCertCnt()
            == other.getSrvCertCnt());
      }
      result = result && (hasEcGroupsCli() == other.hasEcGroupsCli());
      if (hasEcGroupsCli()) {
        result = result && getEcGroupsCli()
            .equals(other.getEcGroupsCli());
      }
      result = result && (hasEcPoiForByServ() == other.hasEcPoiForByServ());
      if (hasEcPoiForByServ()) {
        result = result && getEcPoiForByServ()
            .equals(other.getEcPoiForByServ());
      }
      result = result && (hasEtags() == other.hasEtags());
      if (hasEtags()) {
        result = result && getEtags()
            .equals(other.getEtags());
      }
      result = result && (hasTtags() == other.hasTtags());
      if (hasTtags()) {
        result = result && getTtags()
            .equals(other.getTtags());
      }
      result = result && (hasCliSesIDLen() == other.hasCliSesIDLen());
      if (hasCliSesIDLen()) {
        result = result && getCliSesIDLen()
            .equals(other.getCliSesIDLen());
      }
      result = result && (hasSrvSesIDLen() == other.hasSrvSesIDLen());
      if (hasSrvSesIDLen()) {
        result = result && getSrvSesIDLen()
            .equals(other.getSrvSesIDLen());
      }
      result = result && (hasSrvKeyExcLen() == other.hasSrvKeyExcLen());
      if (hasSrvKeyExcLen()) {
        result = result && getSrvKeyExcLen()
            .equals(other.getSrvKeyExcLen());
      }
      result = result && (hasECDHCurType() == other.hasECDHCurType());
      if (hasECDHCurType()) {
        result = result && getECDHCurType()
            .equals(other.getECDHCurType());
      }
      result = result && (hasECDHSig() == other.hasECDHSig());
      if (hasECDHSig()) {
        result = result && getECDHSig()
            .equals(other.getECDHSig());
      }
      result = result && (hasDHEPLen() == other.hasDHEPLen());
      if (hasDHEPLen()) {
        result = result && getDHEPLen()
            .equals(other.getDHEPLen());
      }
      result = result && (hasDHEGLen() == other.hasDHEGLen());
      if (hasDHEGLen()) {
        result = result && getDHEGLen()
            .equals(other.getDHEGLen());
      }
      result = result && (hasCliKeyExcLen() == other.hasCliKeyExcLen());
      if (hasCliKeyExcLen()) {
        result = result && getCliKeyExcLen()
            .equals(other.getCliKeyExcLen());
      }
      result = result && (hasEncPubKey() == other.hasEncPubKey());
      if (hasEncPubKey()) {
        result = result && getEncPubKey()
            .equals(other.getEncPubKey());
      }
      result = result && (hasEncPubKeyLen() == other.hasEncPubKeyLen());
      if (hasEncPubKeyLen()) {
        result = result && getEncPubKeyLen()
            .equals(other.getEncPubKeyLen());
      }
      result = result && (hasCliExtLen() == other.hasCliExtLen());
      if (hasCliExtLen()) {
        result = result && getCliExtLen()
            .equals(other.getCliExtLen());
      }
      result = result && (hasSrvExtLen() == other.hasSrvExtLen());
      if (hasSrvExtLen()) {
        result = result && getSrvExtLen()
            .equals(other.getSrvExtLen());
      }
      result = result && (hasECDHPubKeyLen() == other.hasECDHPubKeyLen());
      if (hasECDHPubKeyLen()) {
        result = result && getECDHPubKeyLen()
            .equals(other.getECDHPubKeyLen());
      }
      result = result && (hasNamType() == other.hasNamType());
      if (hasNamType()) {
        result = result && getNamType()
            .equals(other.getNamType());
      }
      result = result && (hasNamLen() == other.hasNamLen());
      if (hasNamLen()) {
        result = result && getNamLen()
            .equals(other.getNamLen());
      }
      result = result && (hasTicDat() == other.hasTicDat());
      if (hasTicDat()) {
        result = result && getTicDat()
            .equals(other.getTicDat());
      }
      result = result && (hasSrvCipSui() == other.hasSrvCipSui());
      if (hasSrvCipSui()) {
        result = result && getSrvCipSui()
            .equals(other.getSrvCipSui());
      }
      result = result && (hasCipSuiNum() == other.hasCipSuiNum());
      if (hasCipSuiNum()) {
        result = result && (getCipSuiNum()
            == other.getCipSuiNum());
      }
      result = result && (hasECDHSigHash() == other.hasECDHSigHash());
      if (hasECDHSigHash()) {
        result = result && getECDHSigHash()
            .equals(other.getECDHSigHash());
      }
      result = result && (hasDHESigHash() == other.hasDHESigHash());
      if (hasDHESigHash()) {
        result = result && getDHESigHash()
            .equals(other.getDHESigHash());
      }
      result = result && (hasRSASigHash() == other.hasRSASigHash());
      if (hasRSASigHash()) {
        result = result && getRSASigHash()
            .equals(other.getRSASigHash());
      }
      result = result && (hasGreaseFlag() == other.hasGreaseFlag());
      if (hasGreaseFlag()) {
        result = result && getGreaseFlag()
            .equals(other.getGreaseFlag());
      }
      result = result && (hasRSAModLen() == other.hasRSAModLen());
      if (hasRSAModLen()) {
        result = result && getRSAModLen()
            .equals(other.getRSAModLen());
      }
      result = result && (hasRSAExpLen() == other.hasRSAExpLen());
      if (hasRSAExpLen()) {
        result = result && getRSAExpLen()
            .equals(other.getRSAExpLen());
      }
      result = result && (hasRSASig() == other.hasRSASig());
      if (hasRSASig()) {
        result = result && getRSASig()
            .equals(other.getRSASig());
      }
      result = result && (hasDHESig() == other.hasDHESig());
      if (hasDHESig()) {
        result = result && getDHESig()
            .equals(other.getDHESig());
      }
      result = result && (hasDHEPubKeyLen() == other.hasDHEPubKeyLen());
      if (hasDHEPubKeyLen()) {
        result = result && getDHEPubKeyLen()
            .equals(other.getDHEPubKeyLen());
      }
      result = result && (hasDHEPubKey() == other.hasDHEPubKey());
      if (hasDHEPubKey()) {
        result = result && getDHEPubKey()
            .equals(other.getDHEPubKey());
      }
      result = result && (hasSigAlgType() == other.hasSigAlgType());
      if (hasSigAlgType()) {
        result = result && getSigAlgType()
            .equals(other.getSigAlgType());
      }
      result = result && (hasSigAlg() == other.hasSigAlg());
      if (hasSigAlg()) {
        result = result && getSigAlg()
            .equals(other.getSigAlg());
      }
      result = result && (hasSigHashAlg() == other.hasSigHashAlg());
      if (hasSigHashAlg()) {
        result = result && getSigHashAlg()
            .equals(other.getSigHashAlg());
      }
      result = result && (hasJOY() == other.hasJOY());
      if (hasJOY()) {
        result = result && getJOY()
            .equals(other.getJOY());
      }
      result = result && (hasJOYS() == other.hasJOYS());
      if (hasJOYS()) {
        result = result && getJOYS()
            .equals(other.getJOYS());
      }
      result = result && (hasSTARTTLS() == other.hasSTARTTLS());
      if (hasSTARTTLS()) {
        result = result && getSTARTTLS()
            .equals(other.getSTARTTLS());
      }
      result = result && (hasCertNonFlag() == other.hasCertNonFlag());
      if (hasCertNonFlag()) {
        result = result && getCertNonFlag()
            .equals(other.getCertNonFlag());
      }
      result = result && (hasJoyFp() == other.hasJoyFp());
      if (hasJoyFp()) {
        result = result && getJoyFp()
            .equals(other.getJoyFp());
      }
      result = result && (hasCertIntactFlag() == other.hasCertIntactFlag());
      if (hasCertIntactFlag()) {
        result = result && getCertIntactFlag()
            .equals(other.getCertIntactFlag());
      }
      result = result && (hasCertPath() == other.hasCertPath());
      if (hasCertPath()) {
        result = result && getCertPath()
            .equals(other.getCertPath());
      }
      result = result && (hasSessSecFlag() == other.hasSessSecFlag());
      if (hasSessSecFlag()) {
        result = result && getSessSecFlag()
            .equals(other.getSessSecFlag());
      }
      result = result && (hasFullText() == other.hasFullText());
      if (hasFullText()) {
        result = result && getFullText()
            .equals(other.getFullText());
      }
      result = result && (hasCliCertHashes() == other.hasCliCertHashes());
      if (hasCliCertHashes()) {
        result = result && getCliCertHashes()
            .equals(other.getCliCertHashes());
      }
      result = result && (hasSrvCertHashes() == other.hasSrvCertHashes());
      if (hasSrvCertHashes()) {
        result = result && getSrvCertHashes()
            .equals(other.getSrvCertHashes());
      }
      result = result && (hasCliCertNum() == other.hasCliCertNum());
      if (hasCliCertNum()) {
        result = result && (getCliCertNum()
            == other.getCliCertNum());
      }
      result = result && (hasSrvCertNum() == other.hasSrvCertNum());
      if (hasSrvCertNum()) {
        result = result && (getSrvCertNum()
            == other.getSrvCertNum());
      }
      result = result && (hasCertExist() == other.hasCertExist());
      if (hasCertExist()) {
        result = result && (getCertExist()
            == other.getCertExist());
      }
      result = result && (hasExtendEcGroupsClient() == other.hasExtendEcGroupsClient());
      if (hasExtendEcGroupsClient()) {
        result = result && getExtendEcGroupsClient()
            .equals(other.getExtendEcGroupsClient());
      }
      result = result && (hasLeafCertDaysRemaining() == other.hasLeafCertDaysRemaining());
      if (hasLeafCertDaysRemaining()) {
        result = result && (getLeafCertDaysRemaining()
            == other.getLeafCertDaysRemaining());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasConType()) {
        hash = (37 * hash) + CONTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getConType();
      }
      if (hasAleLev()) {
        hash = (37 * hash) + ALELEV_FIELD_NUMBER;
        hash = (53 * hash) + getAleLev();
      }
      if (hasAleDes()) {
        hash = (37 * hash) + ALEDES_FIELD_NUMBER;
        hash = (53 * hash) + getAleDes();
      }
      if (hasHandShaType()) {
        hash = (37 * hash) + HANDSHATYPE_FIELD_NUMBER;
        hash = (53 * hash) + getHandShaType();
      }
      if (hasCliVer()) {
        hash = (37 * hash) + CLIVER_FIELD_NUMBER;
        hash = (53 * hash) + getCliVer();
      }
      if (hasCliGMTUniTime()) {
        hash = (37 * hash) + CLIGMTUNITIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCliGMTUniTime());
      }
      if (hasCliRand()) {
        hash = (37 * hash) + CLIRAND_FIELD_NUMBER;
        hash = (53 * hash) + getCliRand().hashCode();
      }
      if (hasCliSesID()) {
        hash = (37 * hash) + CLISESID_FIELD_NUMBER;
        hash = (53 * hash) + getCliSesID().hashCode();
      }
      if (hasCliCipSui()) {
        hash = (37 * hash) + CLICIPSUI_FIELD_NUMBER;
        hash = (53 * hash) + getCliCipSui().hashCode();
      }
      if (hasCliComMet()) {
        hash = (37 * hash) + CLICOMMET_FIELD_NUMBER;
        hash = (53 * hash) + getCliComMet().hashCode();
      }
      if (hasSrvVer()) {
        hash = (37 * hash) + SRVVER_FIELD_NUMBER;
        hash = (53 * hash) + getSrvVer();
      }
      if (hasSrvName()) {
        hash = (37 * hash) + SRVNAME_FIELD_NUMBER;
        hash = (53 * hash) + getSrvName().hashCode();
      }
      if (hasSrvNameAttr()) {
        hash = (37 * hash) + SRVNAMEATTR_FIELD_NUMBER;
        hash = (53 * hash) + getSrvNameAttr();
      }
      if (hasSrvGMTUniTime14()) {
        hash = (37 * hash) + SRVGMTUNITIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getSrvGMTUniTime14());
      }
      if (hasSrvRand()) {
        hash = (37 * hash) + SRVRAND_FIELD_NUMBER;
        hash = (53 * hash) + getSrvRand().hashCode();
      }
      if (hasSrvSesID()) {
        hash = (37 * hash) + SRVSESID_FIELD_NUMBER;
        hash = (53 * hash) + getSrvSesID().hashCode();
      }
      if (hasSrvComprMet()) {
        hash = (37 * hash) + SRVCOMPRMET_FIELD_NUMBER;
        hash = (53 * hash) + getSrvComprMet().hashCode();
      }
      if (hasSrvCertLen()) {
        hash = (37 * hash) + SRVCERTLEN_FIELD_NUMBER;
        hash = (53 * hash) + getSrvCertLen();
      }
      if (hasCertResType()) {
        hash = (37 * hash) + CERTRESTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getCertResType();
      }
      if (hasCliCertLen()) {
        hash = (37 * hash) + CLICERTLEN_FIELD_NUMBER;
        hash = (53 * hash) + getCliCertLen();
      }
      if (hasRSAModOfSrvKeyExc()) {
        hash = (37 * hash) + RSAMODOFSRVKEYEXC_FIELD_NUMBER;
        hash = (53 * hash) + getRSAModOfSrvKeyExc().hashCode();
      }
      if (hasRSAExpOfSrvKeyExc()) {
        hash = (37 * hash) + RSAEXPOFSRVKEYEXC_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getRSAExpOfSrvKeyExc());
      }
      if (hasDHModOfSrvKeyExc()) {
        hash = (37 * hash) + DHMODOFSRVKEYEXC_FIELD_NUMBER;
        hash = (53 * hash) + getDHModOfSrvKeyExc().hashCode();
      }
      if (hasDHGenOfSrvKeyExc()) {
        hash = (37 * hash) + DHGENOFSRVKEYEXC_FIELD_NUMBER;
        hash = (53 * hash) + getDHGenOfSrvKeyExc().hashCode();
      }
      if (hasSrvDHPubKey()) {
        hash = (37 * hash) + SRVDHPUBKEY_FIELD_NUMBER;
        hash = (53 * hash) + getSrvDHPubKey().hashCode();
      }
      if (hasPreMasKeyEncryByRSA()) {
        hash = (37 * hash) + PREMASKEYENCRYBYRSA_FIELD_NUMBER;
        hash = (53 * hash) + getPreMasKeyEncryByRSA().hashCode();
      }
      if (hasCliDHPubKey()) {
        hash = (37 * hash) + CLIDHPUBKEY_FIELD_NUMBER;
        hash = (53 * hash) + getCliDHPubKey().hashCode();
      }
      if (hasExtTypeInSSL()) {
        hash = (37 * hash) + EXTTYPEINSSL_FIELD_NUMBER;
        hash = (53 * hash) + getExtTypeInSSL();
      }
      if (hasCliEllCurPoiFor()) {
        hash = (37 * hash) + CLIELLCURPOIFOR_FIELD_NUMBER;
        hash = (53 * hash) + getCliEllCurPoiFor();
      }
      if (hasCliEllCur()) {
        hash = (37 * hash) + CLIELLCUR_FIELD_NUMBER;
        hash = (53 * hash) + getCliEllCur();
      }
      if (hasSrvEllCurPoiFor()) {
        hash = (37 * hash) + SRVELLCURPOIFOR_FIELD_NUMBER;
        hash = (53 * hash) + getSrvEllCurPoiFor();
      }
      if (hasSrvEllCur()) {
        hash = (37 * hash) + SRVELLCUR_FIELD_NUMBER;
        hash = (53 * hash) + getSrvEllCur();
      }
      if (hasSrvEllCurDHPubKey()) {
        hash = (37 * hash) + SRVELLCURDHPUBKEY_FIELD_NUMBER;
        hash = (53 * hash) + getSrvEllCurDHPubKey().hashCode();
      }
      if (hasCliEllCurDHPubKey()) {
        hash = (37 * hash) + CLIELLCURDHPUBKEY_FIELD_NUMBER;
        hash = (53 * hash) + getCliEllCurDHPubKey().hashCode();
      }
      if (hasSrvGMTUniTime35()) {
        hash = (37 * hash) + SRVGMTUNI_TIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getSrvGMTUniTime35());
      }
      if (hasCliExtCnt()) {
        hash = (37 * hash) + CLIEXTCNT_FIELD_NUMBER;
        hash = (53 * hash) + getCliExtCnt();
      }
      if (hasSrvExtCnt()) {
        hash = (37 * hash) + SRVEXTCNT_FIELD_NUMBER;
        hash = (53 * hash) + getSrvExtCnt();
      }
      if (hasCliHandSkLen()) {
        hash = (37 * hash) + CLIHANDSKLEN_FIELD_NUMBER;
        hash = (53 * hash) + getCliHandSkLen();
      }
      if (hasSrvHandSkLen()) {
        hash = (37 * hash) + SRVHANDSKLEN_FIELD_NUMBER;
        hash = (53 * hash) + getSrvHandSkLen();
      }
      if (hasCliExt()) {
        hash = (37 * hash) + CLIEXT_FIELD_NUMBER;
        hash = (53 * hash) + getCliExt().hashCode();
      }
      if (hasSrvExt()) {
        hash = (37 * hash) + SRVEXT_FIELD_NUMBER;
        hash = (53 * hash) + getSrvExt().hashCode();
      }
      if (hasCliExtGrease()) {
        hash = (37 * hash) + CLIEXTGREASE_FIELD_NUMBER;
        hash = (53 * hash) + getCliExtGrease();
      }
      if (hasCliJA3()) {
        hash = (37 * hash) + CLIJA3_FIELD_NUMBER;
        hash = (53 * hash) + getCliJA3().hashCode();
      }
      if (hasSrvJA3()) {
        hash = (37 * hash) + SRVJA3_FIELD_NUMBER;
        hash = (53 * hash) + getSrvJA3().hashCode();
      }
      if (hasCliSessTicket()) {
        hash = (37 * hash) + CLISESSTICKET_FIELD_NUMBER;
        hash = (53 * hash) + getCliSessTicket().hashCode();
      }
      if (hasSrvSessTicket()) {
        hash = (37 * hash) + SRVSESSTICKET_FIELD_NUMBER;
        hash = (53 * hash) + getSrvSessTicket().hashCode();
      }
      if (hasAuthTag()) {
        hash = (37 * hash) + AUTHTAG_FIELD_NUMBER;
        hash = (53 * hash) + getAuthTag();
      }
      if (hasCliCertCnt()) {
        hash = (37 * hash) + CLICERTCNT_FIELD_NUMBER;
        hash = (53 * hash) + getCliCertCnt();
      }
      if (hasSrvCertCnt()) {
        hash = (37 * hash) + SRVCERTCNT_FIELD_NUMBER;
        hash = (53 * hash) + getSrvCertCnt();
      }
      if (hasEcGroupsCli()) {
        hash = (37 * hash) + ECGROUPSCLI_FIELD_NUMBER;
        hash = (53 * hash) + getEcGroupsCli().hashCode();
      }
      if (hasEcPoiForByServ()) {
        hash = (37 * hash) + ECPOIFORBYSERV_FIELD_NUMBER;
        hash = (53 * hash) + getEcPoiForByServ().hashCode();
      }
      if (hasEtags()) {
        hash = (37 * hash) + ETAGS_FIELD_NUMBER;
        hash = (53 * hash) + getEtags().hashCode();
      }
      if (hasTtags()) {
        hash = (37 * hash) + TTAGS_FIELD_NUMBER;
        hash = (53 * hash) + getTtags().hashCode();
      }
      if (hasCliSesIDLen()) {
        hash = (37 * hash) + CLISESIDLEN_FIELD_NUMBER;
        hash = (53 * hash) + getCliSesIDLen().hashCode();
      }
      if (hasSrvSesIDLen()) {
        hash = (37 * hash) + SRVSESIDLEN_FIELD_NUMBER;
        hash = (53 * hash) + getSrvSesIDLen().hashCode();
      }
      if (hasSrvKeyExcLen()) {
        hash = (37 * hash) + SRVKEYEXCLEN_FIELD_NUMBER;
        hash = (53 * hash) + getSrvKeyExcLen().hashCode();
      }
      if (hasECDHCurType()) {
        hash = (37 * hash) + ECDHCURTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getECDHCurType().hashCode();
      }
      if (hasECDHSig()) {
        hash = (37 * hash) + ECDHSIG_FIELD_NUMBER;
        hash = (53 * hash) + getECDHSig().hashCode();
      }
      if (hasDHEPLen()) {
        hash = (37 * hash) + DHEPLEN_FIELD_NUMBER;
        hash = (53 * hash) + getDHEPLen().hashCode();
      }
      if (hasDHEGLen()) {
        hash = (37 * hash) + DHEGLEN_FIELD_NUMBER;
        hash = (53 * hash) + getDHEGLen().hashCode();
      }
      if (hasCliKeyExcLen()) {
        hash = (37 * hash) + CLIKEYEXCLEN_FIELD_NUMBER;
        hash = (53 * hash) + getCliKeyExcLen().hashCode();
      }
      if (hasEncPubKey()) {
        hash = (37 * hash) + ENCPUBKEY_FIELD_NUMBER;
        hash = (53 * hash) + getEncPubKey().hashCode();
      }
      if (hasEncPubKeyLen()) {
        hash = (37 * hash) + ENCPUBKEYLEN_FIELD_NUMBER;
        hash = (53 * hash) + getEncPubKeyLen().hashCode();
      }
      if (hasCliExtLen()) {
        hash = (37 * hash) + CLIEXTLEN_FIELD_NUMBER;
        hash = (53 * hash) + getCliExtLen().hashCode();
      }
      if (hasSrvExtLen()) {
        hash = (37 * hash) + SRVEXTLEN_FIELD_NUMBER;
        hash = (53 * hash) + getSrvExtLen().hashCode();
      }
      if (hasECDHPubKeyLen()) {
        hash = (37 * hash) + ECDHPUBKEYLEN_FIELD_NUMBER;
        hash = (53 * hash) + getECDHPubKeyLen().hashCode();
      }
      if (hasNamType()) {
        hash = (37 * hash) + NAMTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getNamType().hashCode();
      }
      if (hasNamLen()) {
        hash = (37 * hash) + NAMLEN_FIELD_NUMBER;
        hash = (53 * hash) + getNamLen().hashCode();
      }
      if (hasTicDat()) {
        hash = (37 * hash) + TICDAT_FIELD_NUMBER;
        hash = (53 * hash) + getTicDat().hashCode();
      }
      if (hasSrvCipSui()) {
        hash = (37 * hash) + SRVCIPSUI_FIELD_NUMBER;
        hash = (53 * hash) + getSrvCipSui().hashCode();
      }
      if (hasCipSuiNum()) {
        hash = (37 * hash) + CIPSUINUM_FIELD_NUMBER;
        hash = (53 * hash) + getCipSuiNum();
      }
      if (hasECDHSigHash()) {
        hash = (37 * hash) + ECDHSIGHASH_FIELD_NUMBER;
        hash = (53 * hash) + getECDHSigHash().hashCode();
      }
      if (hasDHESigHash()) {
        hash = (37 * hash) + DHESIGHASH_FIELD_NUMBER;
        hash = (53 * hash) + getDHESigHash().hashCode();
      }
      if (hasRSASigHash()) {
        hash = (37 * hash) + RSASIGHASH_FIELD_NUMBER;
        hash = (53 * hash) + getRSASigHash().hashCode();
      }
      if (hasGreaseFlag()) {
        hash = (37 * hash) + GREASEFLAG_FIELD_NUMBER;
        hash = (53 * hash) + getGreaseFlag().hashCode();
      }
      if (hasRSAModLen()) {
        hash = (37 * hash) + RSAMODLEN_FIELD_NUMBER;
        hash = (53 * hash) + getRSAModLen().hashCode();
      }
      if (hasRSAExpLen()) {
        hash = (37 * hash) + RSAEXPLEN_FIELD_NUMBER;
        hash = (53 * hash) + getRSAExpLen().hashCode();
      }
      if (hasRSASig()) {
        hash = (37 * hash) + RSASIG_FIELD_NUMBER;
        hash = (53 * hash) + getRSASig().hashCode();
      }
      if (hasDHESig()) {
        hash = (37 * hash) + DHESIG_FIELD_NUMBER;
        hash = (53 * hash) + getDHESig().hashCode();
      }
      if (hasDHEPubKeyLen()) {
        hash = (37 * hash) + DHEPUBKEYLEN_FIELD_NUMBER;
        hash = (53 * hash) + getDHEPubKeyLen().hashCode();
      }
      if (hasDHEPubKey()) {
        hash = (37 * hash) + DHEPUBKEY_FIELD_NUMBER;
        hash = (53 * hash) + getDHEPubKey().hashCode();
      }
      if (hasSigAlgType()) {
        hash = (37 * hash) + SIGALGTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getSigAlgType().hashCode();
      }
      if (hasSigAlg()) {
        hash = (37 * hash) + SIGALG_FIELD_NUMBER;
        hash = (53 * hash) + getSigAlg().hashCode();
      }
      if (hasSigHashAlg()) {
        hash = (37 * hash) + SIGHASHALG_FIELD_NUMBER;
        hash = (53 * hash) + getSigHashAlg().hashCode();
      }
      if (hasJOY()) {
        hash = (37 * hash) + JOY_FIELD_NUMBER;
        hash = (53 * hash) + getJOY().hashCode();
      }
      if (hasJOYS()) {
        hash = (37 * hash) + JOYS_FIELD_NUMBER;
        hash = (53 * hash) + getJOYS().hashCode();
      }
      if (hasSTARTTLS()) {
        hash = (37 * hash) + STARTTLS_FIELD_NUMBER;
        hash = (53 * hash) + getSTARTTLS().hashCode();
      }
      if (hasCertNonFlag()) {
        hash = (37 * hash) + CERTNONFLAG_FIELD_NUMBER;
        hash = (53 * hash) + getCertNonFlag().hashCode();
      }
      if (hasJoyFp()) {
        hash = (37 * hash) + JOYFP_FIELD_NUMBER;
        hash = (53 * hash) + getJoyFp().hashCode();
      }
      if (hasCertIntactFlag()) {
        hash = (37 * hash) + CERTINTACTFLAG_FIELD_NUMBER;
        hash = (53 * hash) + getCertIntactFlag().hashCode();
      }
      if (hasCertPath()) {
        hash = (37 * hash) + CERTPATH_FIELD_NUMBER;
        hash = (53 * hash) + getCertPath().hashCode();
      }
      if (hasSessSecFlag()) {
        hash = (37 * hash) + SESSSECFLAG_FIELD_NUMBER;
        hash = (53 * hash) + getSessSecFlag().hashCode();
      }
      if (hasFullText()) {
        hash = (37 * hash) + FULLTEXT_FIELD_NUMBER;
        hash = (53 * hash) + getFullText().hashCode();
      }
      if (hasCliCertHashes()) {
        hash = (37 * hash) + CLICERTHASHES_FIELD_NUMBER;
        hash = (53 * hash) + getCliCertHashes().hashCode();
      }
      if (hasSrvCertHashes()) {
        hash = (37 * hash) + SRVCERTHASHES_FIELD_NUMBER;
        hash = (53 * hash) + getSrvCertHashes().hashCode();
      }
      if (hasCliCertNum()) {
        hash = (37 * hash) + CLICERTNUM_FIELD_NUMBER;
        hash = (53 * hash) + getCliCertNum();
      }
      if (hasSrvCertNum()) {
        hash = (37 * hash) + SRVCERTNUM_FIELD_NUMBER;
        hash = (53 * hash) + getSrvCertNum();
      }
      if (hasCertExist()) {
        hash = (37 * hash) + CERTEXIST_FIELD_NUMBER;
        hash = (53 * hash) + getCertExist();
      }
      if (hasExtendEcGroupsClient()) {
        hash = (37 * hash) + EXTENDECGROUPSCLIENT_FIELD_NUMBER;
        hash = (53 * hash) + getExtendEcGroupsClient().hashCode();
      }
      if (hasLeafCertDaysRemaining()) {
        hash = (37 * hash) + LEAFCERTDAYSREMAINING_FIELD_NUMBER;
        hash = (53 * hash) + getLeafCertDaysRemaining();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static SslTlsInfo.Ssl_TlsInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static SslTlsInfo.Ssl_TlsInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static SslTlsInfo.Ssl_TlsInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static SslTlsInfo.Ssl_TlsInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static SslTlsInfo.Ssl_TlsInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static SslTlsInfo.Ssl_TlsInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static SslTlsInfo.Ssl_TlsInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static SslTlsInfo.Ssl_TlsInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static SslTlsInfo.Ssl_TlsInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static SslTlsInfo.Ssl_TlsInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static SslTlsInfo.Ssl_TlsInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static SslTlsInfo.Ssl_TlsInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(SslTlsInfo.Ssl_TlsInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Ssl_TlsInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Ssl_TlsInfo)
        SslTlsInfo.Ssl_TlsInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return SslTlsInfo.internal_static_Ssl_TlsInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return SslTlsInfo.internal_static_Ssl_TlsInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                SslTlsInfo.Ssl_TlsInfo.class, SslTlsInfo.Ssl_TlsInfo.Builder.class);
      }

      // Construct using SslTlsInfo.Ssl_TlsInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        conType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        aleLev_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        aleDes_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        handShaType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        cliVer_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        cliGMTUniTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000020);
        cliRand_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000040);
        cliSesID_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000080);
        cliCipSui_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000100);
        cliComMet_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000200);
        srvVer_ = 0;
        bitField0_ = (bitField0_ & ~0x00000400);
        srvName_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000800);
        srvNameAttr_ = 0;
        bitField0_ = (bitField0_ & ~0x00001000);
        srvGMTUniTime14_ = 0L;
        bitField0_ = (bitField0_ & ~0x00002000);
        srvRand_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00004000);
        srvSesID_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00008000);
        srvComprMet_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00010000);
        srvCertLen_ = 0;
        bitField0_ = (bitField0_ & ~0x00020000);
        certResType_ = 0;
        bitField0_ = (bitField0_ & ~0x00040000);
        cliCertLen_ = 0;
        bitField0_ = (bitField0_ & ~0x00080000);
        rSAModOfSrvKeyExc_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00100000);
        rSAExpOfSrvKeyExc_ = 0L;
        bitField0_ = (bitField0_ & ~0x00200000);
        dHModOfSrvKeyExc_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00400000);
        dHGenOfSrvKeyExc_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00800000);
        srvDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x01000000);
        preMasKeyEncryByRSA_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x02000000);
        cliDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x04000000);
        extTypeInSSL_ = 0;
        bitField0_ = (bitField0_ & ~0x08000000);
        cliEllCurPoiFor_ = 0;
        bitField0_ = (bitField0_ & ~0x10000000);
        cliEllCur_ = 0;
        bitField0_ = (bitField0_ & ~0x20000000);
        srvEllCurPoiFor_ = 0;
        bitField0_ = (bitField0_ & ~0x40000000);
        srvEllCur_ = 0;
        bitField0_ = (bitField0_ & ~0x80000000);
        srvEllCurDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000001);
        cliEllCurDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000002);
        srvGMTUniTime35_ = 0L;
        bitField1_ = (bitField1_ & ~0x00000004);
        cliExtCnt_ = 0;
        bitField1_ = (bitField1_ & ~0x00000008);
        srvExtCnt_ = 0;
        bitField1_ = (bitField1_ & ~0x00000010);
        cliHandSkLen_ = 0;
        bitField1_ = (bitField1_ & ~0x00000020);
        srvHandSkLen_ = 0;
        bitField1_ = (bitField1_ & ~0x00000040);
        cliExt_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000080);
        srvExt_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000100);
        cliExtGrease_ = 0;
        bitField1_ = (bitField1_ & ~0x00000200);
        cliJA3_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000400);
        srvJA3_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000800);
        cliSessTicket_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00001000);
        srvSessTicket_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00002000);
        authTag_ = 0;
        bitField1_ = (bitField1_ & ~0x00004000);
        cliCertCnt_ = 0;
        bitField1_ = (bitField1_ & ~0x00008000);
        srvCertCnt_ = 0;
        bitField1_ = (bitField1_ & ~0x00010000);
        ecGroupsCli_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00020000);
        ecPoiForByServ_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00040000);
        etags_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00080000);
        ttags_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00100000);
        cliSesIDLen_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00200000);
        srvSesIDLen_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00400000);
        srvKeyExcLen_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00800000);
        eCDHCurType_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x01000000);
        eCDHSig_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x02000000);
        dHEPLen_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x04000000);
        dHEGLen_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x08000000);
        cliKeyExcLen_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x10000000);
        encPubKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x20000000);
        encPubKeyLen_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x40000000);
        cliExtLen_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x80000000);
        srvExtLen_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00000001);
        eCDHPubKeyLen_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00000002);
        namType_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00000004);
        namLen_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00000008);
        ticDat_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00000010);
        srvCipSui_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00000020);
        cipSuiNum_ = 0;
        bitField2_ = (bitField2_ & ~0x00000040);
        eCDHSigHash_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00000080);
        dHESigHash_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00000100);
        rSASigHash_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00000200);
        greaseFlag_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00000400);
        rSAModLen_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00000800);
        rSAExpLen_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00001000);
        rSASig_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00002000);
        dHESig_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00004000);
        dHEPubKeyLen_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00008000);
        dHEPubKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00010000);
        sigAlgType_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00020000);
        sigAlg_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00040000);
        sigHashAlg_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00080000);
        jOY_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00100000);
        jOYS_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00200000);
        sTARTTLS_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00400000);
        certNonFlag_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00800000);
        joyFp_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x01000000);
        certIntactFlag_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x02000000);
        certPath_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x04000000);
        sessSecFlag_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x08000000);
        fullText_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x10000000);
        cliCertHashes_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x20000000);
        srvCertHashes_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x40000000);
        cliCertNum_ = 0;
        bitField2_ = (bitField2_ & ~0x80000000);
        srvCertNum_ = 0;
        bitField3_ = (bitField3_ & ~0x00000001);
        certExist_ = 0;
        bitField3_ = (bitField3_ & ~0x00000002);
        extendEcGroupsClient_ = com.google.protobuf.ByteString.EMPTY;
        bitField3_ = (bitField3_ & ~0x00000004);
        leafCertDaysRemaining_ = 0;
        bitField3_ = (bitField3_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return SslTlsInfo.internal_static_Ssl_TlsInfo_descriptor;
      }

      @java.lang.Override
      public SslTlsInfo.Ssl_TlsInfo getDefaultInstanceForType() {
        return SslTlsInfo.Ssl_TlsInfo.getDefaultInstance();
      }

      @java.lang.Override
      public SslTlsInfo.Ssl_TlsInfo build() {
        SslTlsInfo.Ssl_TlsInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public SslTlsInfo.Ssl_TlsInfo buildPartial() {
        SslTlsInfo.Ssl_TlsInfo result = new SslTlsInfo.Ssl_TlsInfo(this);
        int from_bitField0_ = bitField0_;
        int from_bitField1_ = bitField1_;
        int from_bitField2_ = bitField2_;
        int from_bitField3_ = bitField3_;
        int to_bitField0_ = 0;
        int to_bitField1_ = 0;
        int to_bitField2_ = 0;
        int to_bitField3_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.conType_ = conType_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.aleLev_ = aleLev_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.aleDes_ = aleDes_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.handShaType_ = handShaType_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.cliVer_ = cliVer_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.cliGMTUniTime_ = cliGMTUniTime_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.cliRand_ = cliRand_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.cliSesID_ = cliSesID_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.cliCipSui_ = cliCipSui_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000200;
        }
        result.cliComMet_ = cliComMet_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000400;
        }
        result.srvVer_ = srvVer_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000800;
        }
        result.srvName_ = srvName_;
        if (((from_bitField0_ & 0x00001000) == 0x00001000)) {
          to_bitField0_ |= 0x00001000;
        }
        result.srvNameAttr_ = srvNameAttr_;
        if (((from_bitField0_ & 0x00002000) == 0x00002000)) {
          to_bitField0_ |= 0x00002000;
        }
        result.srvGMTUniTime14_ = srvGMTUniTime14_;
        if (((from_bitField0_ & 0x00004000) == 0x00004000)) {
          to_bitField0_ |= 0x00004000;
        }
        result.srvRand_ = srvRand_;
        if (((from_bitField0_ & 0x00008000) == 0x00008000)) {
          to_bitField0_ |= 0x00008000;
        }
        result.srvSesID_ = srvSesID_;
        if (((from_bitField0_ & 0x00010000) == 0x00010000)) {
          to_bitField0_ |= 0x00010000;
        }
        result.srvComprMet_ = srvComprMet_;
        if (((from_bitField0_ & 0x00020000) == 0x00020000)) {
          to_bitField0_ |= 0x00020000;
        }
        result.srvCertLen_ = srvCertLen_;
        if (((from_bitField0_ & 0x00040000) == 0x00040000)) {
          to_bitField0_ |= 0x00040000;
        }
        result.certResType_ = certResType_;
        if (((from_bitField0_ & 0x00080000) == 0x00080000)) {
          to_bitField0_ |= 0x00080000;
        }
        result.cliCertLen_ = cliCertLen_;
        if (((from_bitField0_ & 0x00100000) == 0x00100000)) {
          to_bitField0_ |= 0x00100000;
        }
        result.rSAModOfSrvKeyExc_ = rSAModOfSrvKeyExc_;
        if (((from_bitField0_ & 0x00200000) == 0x00200000)) {
          to_bitField0_ |= 0x00200000;
        }
        result.rSAExpOfSrvKeyExc_ = rSAExpOfSrvKeyExc_;
        if (((from_bitField0_ & 0x00400000) == 0x00400000)) {
          to_bitField0_ |= 0x00400000;
        }
        result.dHModOfSrvKeyExc_ = dHModOfSrvKeyExc_;
        if (((from_bitField0_ & 0x00800000) == 0x00800000)) {
          to_bitField0_ |= 0x00800000;
        }
        result.dHGenOfSrvKeyExc_ = dHGenOfSrvKeyExc_;
        if (((from_bitField0_ & 0x01000000) == 0x01000000)) {
          to_bitField0_ |= 0x01000000;
        }
        result.srvDHPubKey_ = srvDHPubKey_;
        if (((from_bitField0_ & 0x02000000) == 0x02000000)) {
          to_bitField0_ |= 0x02000000;
        }
        result.preMasKeyEncryByRSA_ = preMasKeyEncryByRSA_;
        if (((from_bitField0_ & 0x04000000) == 0x04000000)) {
          to_bitField0_ |= 0x04000000;
        }
        result.cliDHPubKey_ = cliDHPubKey_;
        if (((from_bitField0_ & 0x08000000) == 0x08000000)) {
          to_bitField0_ |= 0x08000000;
        }
        result.extTypeInSSL_ = extTypeInSSL_;
        if (((from_bitField0_ & 0x10000000) == 0x10000000)) {
          to_bitField0_ |= 0x10000000;
        }
        result.cliEllCurPoiFor_ = cliEllCurPoiFor_;
        if (((from_bitField0_ & 0x20000000) == 0x20000000)) {
          to_bitField0_ |= 0x20000000;
        }
        result.cliEllCur_ = cliEllCur_;
        if (((from_bitField0_ & 0x40000000) == 0x40000000)) {
          to_bitField0_ |= 0x40000000;
        }
        result.srvEllCurPoiFor_ = srvEllCurPoiFor_;
        if (((from_bitField0_ & 0x80000000) == 0x80000000)) {
          to_bitField0_ |= 0x80000000;
        }
        result.srvEllCur_ = srvEllCur_;
        if (((from_bitField1_ & 0x00000001) == 0x00000001)) {
          to_bitField1_ |= 0x00000001;
        }
        result.srvEllCurDHPubKey_ = srvEllCurDHPubKey_;
        if (((from_bitField1_ & 0x00000002) == 0x00000002)) {
          to_bitField1_ |= 0x00000002;
        }
        result.cliEllCurDHPubKey_ = cliEllCurDHPubKey_;
        if (((from_bitField1_ & 0x00000004) == 0x00000004)) {
          to_bitField1_ |= 0x00000004;
        }
        result.srvGMTUniTime35_ = srvGMTUniTime35_;
        if (((from_bitField1_ & 0x00000008) == 0x00000008)) {
          to_bitField1_ |= 0x00000008;
        }
        result.cliExtCnt_ = cliExtCnt_;
        if (((from_bitField1_ & 0x00000010) == 0x00000010)) {
          to_bitField1_ |= 0x00000010;
        }
        result.srvExtCnt_ = srvExtCnt_;
        if (((from_bitField1_ & 0x00000020) == 0x00000020)) {
          to_bitField1_ |= 0x00000020;
        }
        result.cliHandSkLen_ = cliHandSkLen_;
        if (((from_bitField1_ & 0x00000040) == 0x00000040)) {
          to_bitField1_ |= 0x00000040;
        }
        result.srvHandSkLen_ = srvHandSkLen_;
        if (((from_bitField1_ & 0x00000080) == 0x00000080)) {
          to_bitField1_ |= 0x00000080;
        }
        result.cliExt_ = cliExt_;
        if (((from_bitField1_ & 0x00000100) == 0x00000100)) {
          to_bitField1_ |= 0x00000100;
        }
        result.srvExt_ = srvExt_;
        if (((from_bitField1_ & 0x00000200) == 0x00000200)) {
          to_bitField1_ |= 0x00000200;
        }
        result.cliExtGrease_ = cliExtGrease_;
        if (((from_bitField1_ & 0x00000400) == 0x00000400)) {
          to_bitField1_ |= 0x00000400;
        }
        result.cliJA3_ = cliJA3_;
        if (((from_bitField1_ & 0x00000800) == 0x00000800)) {
          to_bitField1_ |= 0x00000800;
        }
        result.srvJA3_ = srvJA3_;
        if (((from_bitField1_ & 0x00001000) == 0x00001000)) {
          to_bitField1_ |= 0x00001000;
        }
        result.cliSessTicket_ = cliSessTicket_;
        if (((from_bitField1_ & 0x00002000) == 0x00002000)) {
          to_bitField1_ |= 0x00002000;
        }
        result.srvSessTicket_ = srvSessTicket_;
        if (((from_bitField1_ & 0x00004000) == 0x00004000)) {
          to_bitField1_ |= 0x00004000;
        }
        result.authTag_ = authTag_;
        if (((from_bitField1_ & 0x00008000) == 0x00008000)) {
          to_bitField1_ |= 0x00008000;
        }
        result.cliCertCnt_ = cliCertCnt_;
        if (((from_bitField1_ & 0x00010000) == 0x00010000)) {
          to_bitField1_ |= 0x00010000;
        }
        result.srvCertCnt_ = srvCertCnt_;
        if (((from_bitField1_ & 0x00020000) == 0x00020000)) {
          to_bitField1_ |= 0x00020000;
        }
        result.ecGroupsCli_ = ecGroupsCli_;
        if (((from_bitField1_ & 0x00040000) == 0x00040000)) {
          to_bitField1_ |= 0x00040000;
        }
        result.ecPoiForByServ_ = ecPoiForByServ_;
        if (((from_bitField1_ & 0x00080000) == 0x00080000)) {
          to_bitField1_ |= 0x00080000;
        }
        result.etags_ = etags_;
        if (((from_bitField1_ & 0x00100000) == 0x00100000)) {
          to_bitField1_ |= 0x00100000;
        }
        result.ttags_ = ttags_;
        if (((from_bitField1_ & 0x00200000) == 0x00200000)) {
          to_bitField1_ |= 0x00200000;
        }
        result.cliSesIDLen_ = cliSesIDLen_;
        if (((from_bitField1_ & 0x00400000) == 0x00400000)) {
          to_bitField1_ |= 0x00400000;
        }
        result.srvSesIDLen_ = srvSesIDLen_;
        if (((from_bitField1_ & 0x00800000) == 0x00800000)) {
          to_bitField1_ |= 0x00800000;
        }
        result.srvKeyExcLen_ = srvKeyExcLen_;
        if (((from_bitField1_ & 0x01000000) == 0x01000000)) {
          to_bitField1_ |= 0x01000000;
        }
        result.eCDHCurType_ = eCDHCurType_;
        if (((from_bitField1_ & 0x02000000) == 0x02000000)) {
          to_bitField1_ |= 0x02000000;
        }
        result.eCDHSig_ = eCDHSig_;
        if (((from_bitField1_ & 0x04000000) == 0x04000000)) {
          to_bitField1_ |= 0x04000000;
        }
        result.dHEPLen_ = dHEPLen_;
        if (((from_bitField1_ & 0x08000000) == 0x08000000)) {
          to_bitField1_ |= 0x08000000;
        }
        result.dHEGLen_ = dHEGLen_;
        if (((from_bitField1_ & 0x10000000) == 0x10000000)) {
          to_bitField1_ |= 0x10000000;
        }
        result.cliKeyExcLen_ = cliKeyExcLen_;
        if (((from_bitField1_ & 0x20000000) == 0x20000000)) {
          to_bitField1_ |= 0x20000000;
        }
        result.encPubKey_ = encPubKey_;
        if (((from_bitField1_ & 0x40000000) == 0x40000000)) {
          to_bitField1_ |= 0x40000000;
        }
        result.encPubKeyLen_ = encPubKeyLen_;
        if (((from_bitField1_ & 0x80000000) == 0x80000000)) {
          to_bitField1_ |= 0x80000000;
        }
        result.cliExtLen_ = cliExtLen_;
        if (((from_bitField2_ & 0x00000001) == 0x00000001)) {
          to_bitField2_ |= 0x00000001;
        }
        result.srvExtLen_ = srvExtLen_;
        if (((from_bitField2_ & 0x00000002) == 0x00000002)) {
          to_bitField2_ |= 0x00000002;
        }
        result.eCDHPubKeyLen_ = eCDHPubKeyLen_;
        if (((from_bitField2_ & 0x00000004) == 0x00000004)) {
          to_bitField2_ |= 0x00000004;
        }
        result.namType_ = namType_;
        if (((from_bitField2_ & 0x00000008) == 0x00000008)) {
          to_bitField2_ |= 0x00000008;
        }
        result.namLen_ = namLen_;
        if (((from_bitField2_ & 0x00000010) == 0x00000010)) {
          to_bitField2_ |= 0x00000010;
        }
        result.ticDat_ = ticDat_;
        if (((from_bitField2_ & 0x00000020) == 0x00000020)) {
          to_bitField2_ |= 0x00000020;
        }
        result.srvCipSui_ = srvCipSui_;
        if (((from_bitField2_ & 0x00000040) == 0x00000040)) {
          to_bitField2_ |= 0x00000040;
        }
        result.cipSuiNum_ = cipSuiNum_;
        if (((from_bitField2_ & 0x00000080) == 0x00000080)) {
          to_bitField2_ |= 0x00000080;
        }
        result.eCDHSigHash_ = eCDHSigHash_;
        if (((from_bitField2_ & 0x00000100) == 0x00000100)) {
          to_bitField2_ |= 0x00000100;
        }
        result.dHESigHash_ = dHESigHash_;
        if (((from_bitField2_ & 0x00000200) == 0x00000200)) {
          to_bitField2_ |= 0x00000200;
        }
        result.rSASigHash_ = rSASigHash_;
        if (((from_bitField2_ & 0x00000400) == 0x00000400)) {
          to_bitField2_ |= 0x00000400;
        }
        result.greaseFlag_ = greaseFlag_;
        if (((from_bitField2_ & 0x00000800) == 0x00000800)) {
          to_bitField2_ |= 0x00000800;
        }
        result.rSAModLen_ = rSAModLen_;
        if (((from_bitField2_ & 0x00001000) == 0x00001000)) {
          to_bitField2_ |= 0x00001000;
        }
        result.rSAExpLen_ = rSAExpLen_;
        if (((from_bitField2_ & 0x00002000) == 0x00002000)) {
          to_bitField2_ |= 0x00002000;
        }
        result.rSASig_ = rSASig_;
        if (((from_bitField2_ & 0x00004000) == 0x00004000)) {
          to_bitField2_ |= 0x00004000;
        }
        result.dHESig_ = dHESig_;
        if (((from_bitField2_ & 0x00008000) == 0x00008000)) {
          to_bitField2_ |= 0x00008000;
        }
        result.dHEPubKeyLen_ = dHEPubKeyLen_;
        if (((from_bitField2_ & 0x00010000) == 0x00010000)) {
          to_bitField2_ |= 0x00010000;
        }
        result.dHEPubKey_ = dHEPubKey_;
        if (((from_bitField2_ & 0x00020000) == 0x00020000)) {
          to_bitField2_ |= 0x00020000;
        }
        result.sigAlgType_ = sigAlgType_;
        if (((from_bitField2_ & 0x00040000) == 0x00040000)) {
          to_bitField2_ |= 0x00040000;
        }
        result.sigAlg_ = sigAlg_;
        if (((from_bitField2_ & 0x00080000) == 0x00080000)) {
          to_bitField2_ |= 0x00080000;
        }
        result.sigHashAlg_ = sigHashAlg_;
        if (((from_bitField2_ & 0x00100000) == 0x00100000)) {
          to_bitField2_ |= 0x00100000;
        }
        result.jOY_ = jOY_;
        if (((from_bitField2_ & 0x00200000) == 0x00200000)) {
          to_bitField2_ |= 0x00200000;
        }
        result.jOYS_ = jOYS_;
        if (((from_bitField2_ & 0x00400000) == 0x00400000)) {
          to_bitField2_ |= 0x00400000;
        }
        result.sTARTTLS_ = sTARTTLS_;
        if (((from_bitField2_ & 0x00800000) == 0x00800000)) {
          to_bitField2_ |= 0x00800000;
        }
        result.certNonFlag_ = certNonFlag_;
        if (((from_bitField2_ & 0x01000000) == 0x01000000)) {
          to_bitField2_ |= 0x01000000;
        }
        result.joyFp_ = joyFp_;
        if (((from_bitField2_ & 0x02000000) == 0x02000000)) {
          to_bitField2_ |= 0x02000000;
        }
        result.certIntactFlag_ = certIntactFlag_;
        if (((from_bitField2_ & 0x04000000) == 0x04000000)) {
          to_bitField2_ |= 0x04000000;
        }
        result.certPath_ = certPath_;
        if (((from_bitField2_ & 0x08000000) == 0x08000000)) {
          to_bitField2_ |= 0x08000000;
        }
        result.sessSecFlag_ = sessSecFlag_;
        if (((from_bitField2_ & 0x10000000) == 0x10000000)) {
          to_bitField2_ |= 0x10000000;
        }
        result.fullText_ = fullText_;
        if (((from_bitField2_ & 0x20000000) == 0x20000000)) {
          to_bitField2_ |= 0x20000000;
        }
        result.cliCertHashes_ = cliCertHashes_;
        if (((from_bitField2_ & 0x40000000) == 0x40000000)) {
          to_bitField2_ |= 0x40000000;
        }
        result.srvCertHashes_ = srvCertHashes_;
        if (((from_bitField2_ & 0x80000000) == 0x80000000)) {
          to_bitField2_ |= 0x80000000;
        }
        result.cliCertNum_ = cliCertNum_;
        if (((from_bitField3_ & 0x00000001) == 0x00000001)) {
          to_bitField3_ |= 0x00000001;
        }
        result.srvCertNum_ = srvCertNum_;
        if (((from_bitField3_ & 0x00000002) == 0x00000002)) {
          to_bitField3_ |= 0x00000002;
        }
        result.certExist_ = certExist_;
        if (((from_bitField3_ & 0x00000004) == 0x00000004)) {
          to_bitField3_ |= 0x00000004;
        }
        result.extendEcGroupsClient_ = extendEcGroupsClient_;
        if (((from_bitField3_ & 0x00000008) == 0x00000008)) {
          to_bitField3_ |= 0x00000008;
        }
        result.leafCertDaysRemaining_ = leafCertDaysRemaining_;
        result.bitField0_ = to_bitField0_;
        result.bitField1_ = to_bitField1_;
        result.bitField2_ = to_bitField2_;
        result.bitField3_ = to_bitField3_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof SslTlsInfo.Ssl_TlsInfo) {
          return mergeFrom((SslTlsInfo.Ssl_TlsInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(SslTlsInfo.Ssl_TlsInfo other) {
        if (other == SslTlsInfo.Ssl_TlsInfo.getDefaultInstance()) return this;
        if (other.hasConType()) {
          setConType(other.getConType());
        }
        if (other.hasAleLev()) {
          setAleLev(other.getAleLev());
        }
        if (other.hasAleDes()) {
          setAleDes(other.getAleDes());
        }
        if (other.hasHandShaType()) {
          setHandShaType(other.getHandShaType());
        }
        if (other.hasCliVer()) {
          setCliVer(other.getCliVer());
        }
        if (other.hasCliGMTUniTime()) {
          setCliGMTUniTime(other.getCliGMTUniTime());
        }
        if (other.hasCliRand()) {
          setCliRand(other.getCliRand());
        }
        if (other.hasCliSesID()) {
          setCliSesID(other.getCliSesID());
        }
        if (other.hasCliCipSui()) {
          setCliCipSui(other.getCliCipSui());
        }
        if (other.hasCliComMet()) {
          setCliComMet(other.getCliComMet());
        }
        if (other.hasSrvVer()) {
          setSrvVer(other.getSrvVer());
        }
        if (other.hasSrvName()) {
          setSrvName(other.getSrvName());
        }
        if (other.hasSrvNameAttr()) {
          setSrvNameAttr(other.getSrvNameAttr());
        }
        if (other.hasSrvGMTUniTime14()) {
          setSrvGMTUniTime14(other.getSrvGMTUniTime14());
        }
        if (other.hasSrvRand()) {
          setSrvRand(other.getSrvRand());
        }
        if (other.hasSrvSesID()) {
          setSrvSesID(other.getSrvSesID());
        }
        if (other.hasSrvComprMet()) {
          setSrvComprMet(other.getSrvComprMet());
        }
        if (other.hasSrvCertLen()) {
          setSrvCertLen(other.getSrvCertLen());
        }
        if (other.hasCertResType()) {
          setCertResType(other.getCertResType());
        }
        if (other.hasCliCertLen()) {
          setCliCertLen(other.getCliCertLen());
        }
        if (other.hasRSAModOfSrvKeyExc()) {
          setRSAModOfSrvKeyExc(other.getRSAModOfSrvKeyExc());
        }
        if (other.hasRSAExpOfSrvKeyExc()) {
          setRSAExpOfSrvKeyExc(other.getRSAExpOfSrvKeyExc());
        }
        if (other.hasDHModOfSrvKeyExc()) {
          setDHModOfSrvKeyExc(other.getDHModOfSrvKeyExc());
        }
        if (other.hasDHGenOfSrvKeyExc()) {
          setDHGenOfSrvKeyExc(other.getDHGenOfSrvKeyExc());
        }
        if (other.hasSrvDHPubKey()) {
          setSrvDHPubKey(other.getSrvDHPubKey());
        }
        if (other.hasPreMasKeyEncryByRSA()) {
          setPreMasKeyEncryByRSA(other.getPreMasKeyEncryByRSA());
        }
        if (other.hasCliDHPubKey()) {
          setCliDHPubKey(other.getCliDHPubKey());
        }
        if (other.hasExtTypeInSSL()) {
          setExtTypeInSSL(other.getExtTypeInSSL());
        }
        if (other.hasCliEllCurPoiFor()) {
          setCliEllCurPoiFor(other.getCliEllCurPoiFor());
        }
        if (other.hasCliEllCur()) {
          setCliEllCur(other.getCliEllCur());
        }
        if (other.hasSrvEllCurPoiFor()) {
          setSrvEllCurPoiFor(other.getSrvEllCurPoiFor());
        }
        if (other.hasSrvEllCur()) {
          setSrvEllCur(other.getSrvEllCur());
        }
        if (other.hasSrvEllCurDHPubKey()) {
          setSrvEllCurDHPubKey(other.getSrvEllCurDHPubKey());
        }
        if (other.hasCliEllCurDHPubKey()) {
          setCliEllCurDHPubKey(other.getCliEllCurDHPubKey());
        }
        if (other.hasSrvGMTUniTime35()) {
          setSrvGMTUniTime35(other.getSrvGMTUniTime35());
        }
        if (other.hasCliExtCnt()) {
          setCliExtCnt(other.getCliExtCnt());
        }
        if (other.hasSrvExtCnt()) {
          setSrvExtCnt(other.getSrvExtCnt());
        }
        if (other.hasCliHandSkLen()) {
          setCliHandSkLen(other.getCliHandSkLen());
        }
        if (other.hasSrvHandSkLen()) {
          setSrvHandSkLen(other.getSrvHandSkLen());
        }
        if (other.hasCliExt()) {
          setCliExt(other.getCliExt());
        }
        if (other.hasSrvExt()) {
          setSrvExt(other.getSrvExt());
        }
        if (other.hasCliExtGrease()) {
          setCliExtGrease(other.getCliExtGrease());
        }
        if (other.hasCliJA3()) {
          setCliJA3(other.getCliJA3());
        }
        if (other.hasSrvJA3()) {
          setSrvJA3(other.getSrvJA3());
        }
        if (other.hasCliSessTicket()) {
          setCliSessTicket(other.getCliSessTicket());
        }
        if (other.hasSrvSessTicket()) {
          setSrvSessTicket(other.getSrvSessTicket());
        }
        if (other.hasAuthTag()) {
          setAuthTag(other.getAuthTag());
        }
        if (other.hasCliCertCnt()) {
          setCliCertCnt(other.getCliCertCnt());
        }
        if (other.hasSrvCertCnt()) {
          setSrvCertCnt(other.getSrvCertCnt());
        }
        if (other.hasEcGroupsCli()) {
          setEcGroupsCli(other.getEcGroupsCli());
        }
        if (other.hasEcPoiForByServ()) {
          setEcPoiForByServ(other.getEcPoiForByServ());
        }
        if (other.hasEtags()) {
          setEtags(other.getEtags());
        }
        if (other.hasTtags()) {
          setTtags(other.getTtags());
        }
        if (other.hasCliSesIDLen()) {
          setCliSesIDLen(other.getCliSesIDLen());
        }
        if (other.hasSrvSesIDLen()) {
          setSrvSesIDLen(other.getSrvSesIDLen());
        }
        if (other.hasSrvKeyExcLen()) {
          setSrvKeyExcLen(other.getSrvKeyExcLen());
        }
        if (other.hasECDHCurType()) {
          setECDHCurType(other.getECDHCurType());
        }
        if (other.hasECDHSig()) {
          setECDHSig(other.getECDHSig());
        }
        if (other.hasDHEPLen()) {
          setDHEPLen(other.getDHEPLen());
        }
        if (other.hasDHEGLen()) {
          setDHEGLen(other.getDHEGLen());
        }
        if (other.hasCliKeyExcLen()) {
          setCliKeyExcLen(other.getCliKeyExcLen());
        }
        if (other.hasEncPubKey()) {
          setEncPubKey(other.getEncPubKey());
        }
        if (other.hasEncPubKeyLen()) {
          setEncPubKeyLen(other.getEncPubKeyLen());
        }
        if (other.hasCliExtLen()) {
          setCliExtLen(other.getCliExtLen());
        }
        if (other.hasSrvExtLen()) {
          setSrvExtLen(other.getSrvExtLen());
        }
        if (other.hasECDHPubKeyLen()) {
          setECDHPubKeyLen(other.getECDHPubKeyLen());
        }
        if (other.hasNamType()) {
          setNamType(other.getNamType());
        }
        if (other.hasNamLen()) {
          setNamLen(other.getNamLen());
        }
        if (other.hasTicDat()) {
          setTicDat(other.getTicDat());
        }
        if (other.hasSrvCipSui()) {
          setSrvCipSui(other.getSrvCipSui());
        }
        if (other.hasCipSuiNum()) {
          setCipSuiNum(other.getCipSuiNum());
        }
        if (other.hasECDHSigHash()) {
          setECDHSigHash(other.getECDHSigHash());
        }
        if (other.hasDHESigHash()) {
          setDHESigHash(other.getDHESigHash());
        }
        if (other.hasRSASigHash()) {
          setRSASigHash(other.getRSASigHash());
        }
        if (other.hasGreaseFlag()) {
          setGreaseFlag(other.getGreaseFlag());
        }
        if (other.hasRSAModLen()) {
          setRSAModLen(other.getRSAModLen());
        }
        if (other.hasRSAExpLen()) {
          setRSAExpLen(other.getRSAExpLen());
        }
        if (other.hasRSASig()) {
          setRSASig(other.getRSASig());
        }
        if (other.hasDHESig()) {
          setDHESig(other.getDHESig());
        }
        if (other.hasDHEPubKeyLen()) {
          setDHEPubKeyLen(other.getDHEPubKeyLen());
        }
        if (other.hasDHEPubKey()) {
          setDHEPubKey(other.getDHEPubKey());
        }
        if (other.hasSigAlgType()) {
          setSigAlgType(other.getSigAlgType());
        }
        if (other.hasSigAlg()) {
          setSigAlg(other.getSigAlg());
        }
        if (other.hasSigHashAlg()) {
          setSigHashAlg(other.getSigHashAlg());
        }
        if (other.hasJOY()) {
          setJOY(other.getJOY());
        }
        if (other.hasJOYS()) {
          setJOYS(other.getJOYS());
        }
        if (other.hasSTARTTLS()) {
          setSTARTTLS(other.getSTARTTLS());
        }
        if (other.hasCertNonFlag()) {
          setCertNonFlag(other.getCertNonFlag());
        }
        if (other.hasJoyFp()) {
          setJoyFp(other.getJoyFp());
        }
        if (other.hasCertIntactFlag()) {
          setCertIntactFlag(other.getCertIntactFlag());
        }
        if (other.hasCertPath()) {
          setCertPath(other.getCertPath());
        }
        if (other.hasSessSecFlag()) {
          setSessSecFlag(other.getSessSecFlag());
        }
        if (other.hasFullText()) {
          setFullText(other.getFullText());
        }
        if (other.hasCliCertHashes()) {
          setCliCertHashes(other.getCliCertHashes());
        }
        if (other.hasSrvCertHashes()) {
          setSrvCertHashes(other.getSrvCertHashes());
        }
        if (other.hasCliCertNum()) {
          setCliCertNum(other.getCliCertNum());
        }
        if (other.hasSrvCertNum()) {
          setSrvCertNum(other.getSrvCertNum());
        }
        if (other.hasCertExist()) {
          setCertExist(other.getCertExist());
        }
        if (other.hasExtendEcGroupsClient()) {
          setExtendEcGroupsClient(other.getExtendEcGroupsClient());
        }
        if (other.hasLeafCertDaysRemaining()) {
          setLeafCertDaysRemaining(other.getLeafCertDaysRemaining());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        SslTlsInfo.Ssl_TlsInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (SslTlsInfo.Ssl_TlsInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;
      private int bitField1_;
      private int bitField2_;
      private int bitField3_;

      private int conType_ ;
      /**
       * <code>optional uint32 conType = 1;</code>
       */
      public boolean hasConType() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional uint32 conType = 1;</code>
       */
      public int getConType() {
        return conType_;
      }
      /**
       * <code>optional uint32 conType = 1;</code>
       */
      public Builder setConType(int value) {
        bitField0_ |= 0x00000001;
        conType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 conType = 1;</code>
       */
      public Builder clearConType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        conType_ = 0;
        onChanged();
        return this;
      }

      private int aleLev_ ;
      /**
       * <code>optional uint32 aleLev = 2;</code>
       */
      public boolean hasAleLev() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional uint32 aleLev = 2;</code>
       */
      public int getAleLev() {
        return aleLev_;
      }
      /**
       * <code>optional uint32 aleLev = 2;</code>
       */
      public Builder setAleLev(int value) {
        bitField0_ |= 0x00000002;
        aleLev_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 aleLev = 2;</code>
       */
      public Builder clearAleLev() {
        bitField0_ = (bitField0_ & ~0x00000002);
        aleLev_ = 0;
        onChanged();
        return this;
      }

      private int aleDes_ ;
      /**
       * <code>optional uint32 aleDes = 3;</code>
       */
      public boolean hasAleDes() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional uint32 aleDes = 3;</code>
       */
      public int getAleDes() {
        return aleDes_;
      }
      /**
       * <code>optional uint32 aleDes = 3;</code>
       */
      public Builder setAleDes(int value) {
        bitField0_ |= 0x00000004;
        aleDes_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 aleDes = 3;</code>
       */
      public Builder clearAleDes() {
        bitField0_ = (bitField0_ & ~0x00000004);
        aleDes_ = 0;
        onChanged();
        return this;
      }

      private int handShaType_ ;
      /**
       * <code>optional uint32 handShaType = 4;</code>
       */
      public boolean hasHandShaType() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional uint32 handShaType = 4;</code>
       */
      public int getHandShaType() {
        return handShaType_;
      }
      /**
       * <code>optional uint32 handShaType = 4;</code>
       */
      public Builder setHandShaType(int value) {
        bitField0_ |= 0x00000008;
        handShaType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 handShaType = 4;</code>
       */
      public Builder clearHandShaType() {
        bitField0_ = (bitField0_ & ~0x00000008);
        handShaType_ = 0;
        onChanged();
        return this;
      }

      private int cliVer_ ;
      /**
       * <code>optional uint32 cliVer = 5;</code>
       */
      public boolean hasCliVer() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional uint32 cliVer = 5;</code>
       */
      public int getCliVer() {
        return cliVer_;
      }
      /**
       * <code>optional uint32 cliVer = 5;</code>
       */
      public Builder setCliVer(int value) {
        bitField0_ |= 0x00000010;
        cliVer_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cliVer = 5;</code>
       */
      public Builder clearCliVer() {
        bitField0_ = (bitField0_ & ~0x00000010);
        cliVer_ = 0;
        onChanged();
        return this;
      }

      private long cliGMTUniTime_ ;
      /**
       * <code>optional uint64 cliGMTUniTime = 6;</code>
       */
      public boolean hasCliGMTUniTime() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional uint64 cliGMTUniTime = 6;</code>
       */
      public long getCliGMTUniTime() {
        return cliGMTUniTime_;
      }
      /**
       * <code>optional uint64 cliGMTUniTime = 6;</code>
       */
      public Builder setCliGMTUniTime(long value) {
        bitField0_ |= 0x00000020;
        cliGMTUniTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 cliGMTUniTime = 6;</code>
       */
      public Builder clearCliGMTUniTime() {
        bitField0_ = (bitField0_ & ~0x00000020);
        cliGMTUniTime_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliRand_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliRand = 7;</code>
       */
      public boolean hasCliRand() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional bytes cliRand = 7;</code>
       */
      public com.google.protobuf.ByteString getCliRand() {
        return cliRand_;
      }
      /**
       * <code>optional bytes cliRand = 7;</code>
       */
      public Builder setCliRand(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        cliRand_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliRand = 7;</code>
       */
      public Builder clearCliRand() {
        bitField0_ = (bitField0_ & ~0x00000040);
        cliRand_ = getDefaultInstance().getCliRand();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliSesID_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliSesID = 8;</code>
       */
      public boolean hasCliSesID() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional bytes cliSesID = 8;</code>
       */
      public com.google.protobuf.ByteString getCliSesID() {
        return cliSesID_;
      }
      /**
       * <code>optional bytes cliSesID = 8;</code>
       */
      public Builder setCliSesID(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        cliSesID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliSesID = 8;</code>
       */
      public Builder clearCliSesID() {
        bitField0_ = (bitField0_ & ~0x00000080);
        cliSesID_ = getDefaultInstance().getCliSesID();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliCipSui_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliCipSui = 9;</code>
       */
      public boolean hasCliCipSui() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional bytes cliCipSui = 9;</code>
       */
      public com.google.protobuf.ByteString getCliCipSui() {
        return cliCipSui_;
      }
      /**
       * <code>optional bytes cliCipSui = 9;</code>
       */
      public Builder setCliCipSui(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        cliCipSui_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliCipSui = 9;</code>
       */
      public Builder clearCliCipSui() {
        bitField0_ = (bitField0_ & ~0x00000100);
        cliCipSui_ = getDefaultInstance().getCliCipSui();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliComMet_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliComMet = 10;</code>
       */
      public boolean hasCliComMet() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>optional bytes cliComMet = 10;</code>
       */
      public com.google.protobuf.ByteString getCliComMet() {
        return cliComMet_;
      }
      /**
       * <code>optional bytes cliComMet = 10;</code>
       */
      public Builder setCliComMet(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
        cliComMet_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliComMet = 10;</code>
       */
      public Builder clearCliComMet() {
        bitField0_ = (bitField0_ & ~0x00000200);
        cliComMet_ = getDefaultInstance().getCliComMet();
        onChanged();
        return this;
      }

      private int srvVer_ ;
      /**
       * <code>optional uint32 srvVer = 11;</code>
       */
      public boolean hasSrvVer() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>optional uint32 srvVer = 11;</code>
       */
      public int getSrvVer() {
        return srvVer_;
      }
      /**
       * <code>optional uint32 srvVer = 11;</code>
       */
      public Builder setSrvVer(int value) {
        bitField0_ |= 0x00000400;
        srvVer_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvVer = 11;</code>
       */
      public Builder clearSrvVer() {
        bitField0_ = (bitField0_ & ~0x00000400);
        srvVer_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvName = 12;</code>
       */
      public boolean hasSrvName() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>optional bytes srvName = 12;</code>
       */
      public com.google.protobuf.ByteString getSrvName() {
        return srvName_;
      }
      /**
       * <code>optional bytes srvName = 12;</code>
       */
      public Builder setSrvName(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        srvName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvName = 12;</code>
       */
      public Builder clearSrvName() {
        bitField0_ = (bitField0_ & ~0x00000800);
        srvName_ = getDefaultInstance().getSrvName();
        onChanged();
        return this;
      }

      private int srvNameAttr_ ;
      /**
       * <code>optional uint32 srvNameAttr = 13;</code>
       */
      public boolean hasSrvNameAttr() {
        return ((bitField0_ & 0x00001000) == 0x00001000);
      }
      /**
       * <code>optional uint32 srvNameAttr = 13;</code>
       */
      public int getSrvNameAttr() {
        return srvNameAttr_;
      }
      /**
       * <code>optional uint32 srvNameAttr = 13;</code>
       */
      public Builder setSrvNameAttr(int value) {
        bitField0_ |= 0x00001000;
        srvNameAttr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvNameAttr = 13;</code>
       */
      public Builder clearSrvNameAttr() {
        bitField0_ = (bitField0_ & ~0x00001000);
        srvNameAttr_ = 0;
        onChanged();
        return this;
      }

      private long srvGMTUniTime14_ ;
      /**
       * <code>optional uint64 srvGMTUniTime = 14;</code>
       */
      public boolean hasSrvGMTUniTime14() {
        return ((bitField0_ & 0x00002000) == 0x00002000);
      }
      /**
       * <code>optional uint64 srvGMTUniTime = 14;</code>
       */
      public long getSrvGMTUniTime14() {
        return srvGMTUniTime14_;
      }
      /**
       * <code>optional uint64 srvGMTUniTime = 14;</code>
       */
      public Builder setSrvGMTUniTime14(long value) {
        bitField0_ |= 0x00002000;
        srvGMTUniTime14_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 srvGMTUniTime = 14;</code>
       */
      public Builder clearSrvGMTUniTime14() {
        bitField0_ = (bitField0_ & ~0x00002000);
        srvGMTUniTime14_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvRand_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvRand = 15;</code>
       */
      public boolean hasSrvRand() {
        return ((bitField0_ & 0x00004000) == 0x00004000);
      }
      /**
       * <code>optional bytes srvRand = 15;</code>
       */
      public com.google.protobuf.ByteString getSrvRand() {
        return srvRand_;
      }
      /**
       * <code>optional bytes srvRand = 15;</code>
       */
      public Builder setSrvRand(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00004000;
        srvRand_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvRand = 15;</code>
       */
      public Builder clearSrvRand() {
        bitField0_ = (bitField0_ & ~0x00004000);
        srvRand_ = getDefaultInstance().getSrvRand();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvSesID_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvSesID = 16;</code>
       */
      public boolean hasSrvSesID() {
        return ((bitField0_ & 0x00008000) == 0x00008000);
      }
      /**
       * <code>optional bytes srvSesID = 16;</code>
       */
      public com.google.protobuf.ByteString getSrvSesID() {
        return srvSesID_;
      }
      /**
       * <code>optional bytes srvSesID = 16;</code>
       */
      public Builder setSrvSesID(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00008000;
        srvSesID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvSesID = 16;</code>
       */
      public Builder clearSrvSesID() {
        bitField0_ = (bitField0_ & ~0x00008000);
        srvSesID_ = getDefaultInstance().getSrvSesID();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvComprMet_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvComprMet = 17;</code>
       */
      public boolean hasSrvComprMet() {
        return ((bitField0_ & 0x00010000) == 0x00010000);
      }
      /**
       * <code>optional bytes srvComprMet = 17;</code>
       */
      public com.google.protobuf.ByteString getSrvComprMet() {
        return srvComprMet_;
      }
      /**
       * <code>optional bytes srvComprMet = 17;</code>
       */
      public Builder setSrvComprMet(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00010000;
        srvComprMet_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvComprMet = 17;</code>
       */
      public Builder clearSrvComprMet() {
        bitField0_ = (bitField0_ & ~0x00010000);
        srvComprMet_ = getDefaultInstance().getSrvComprMet();
        onChanged();
        return this;
      }

      private int srvCertLen_ ;
      /**
       * <code>optional uint32 srvCertLen = 18;</code>
       */
      public boolean hasSrvCertLen() {
        return ((bitField0_ & 0x00020000) == 0x00020000);
      }
      /**
       * <code>optional uint32 srvCertLen = 18;</code>
       */
      public int getSrvCertLen() {
        return srvCertLen_;
      }
      /**
       * <code>optional uint32 srvCertLen = 18;</code>
       */
      public Builder setSrvCertLen(int value) {
        bitField0_ |= 0x00020000;
        srvCertLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvCertLen = 18;</code>
       */
      public Builder clearSrvCertLen() {
        bitField0_ = (bitField0_ & ~0x00020000);
        srvCertLen_ = 0;
        onChanged();
        return this;
      }

      private int certResType_ ;
      /**
       * <code>optional uint32 certResType = 19;</code>
       */
      public boolean hasCertResType() {
        return ((bitField0_ & 0x00040000) == 0x00040000);
      }
      /**
       * <code>optional uint32 certResType = 19;</code>
       */
      public int getCertResType() {
        return certResType_;
      }
      /**
       * <code>optional uint32 certResType = 19;</code>
       */
      public Builder setCertResType(int value) {
        bitField0_ |= 0x00040000;
        certResType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 certResType = 19;</code>
       */
      public Builder clearCertResType() {
        bitField0_ = (bitField0_ & ~0x00040000);
        certResType_ = 0;
        onChanged();
        return this;
      }

      private int cliCertLen_ ;
      /**
       * <code>optional uint32 cliCertLen = 20;</code>
       */
      public boolean hasCliCertLen() {
        return ((bitField0_ & 0x00080000) == 0x00080000);
      }
      /**
       * <code>optional uint32 cliCertLen = 20;</code>
       */
      public int getCliCertLen() {
        return cliCertLen_;
      }
      /**
       * <code>optional uint32 cliCertLen = 20;</code>
       */
      public Builder setCliCertLen(int value) {
        bitField0_ |= 0x00080000;
        cliCertLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cliCertLen = 20;</code>
       */
      public Builder clearCliCertLen() {
        bitField0_ = (bitField0_ & ~0x00080000);
        cliCertLen_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString rSAModOfSrvKeyExc_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes RSAModOfSrvKeyExc = 21;</code>
       */
      public boolean hasRSAModOfSrvKeyExc() {
        return ((bitField0_ & 0x00100000) == 0x00100000);
      }
      /**
       * <code>optional bytes RSAModOfSrvKeyExc = 21;</code>
       */
      public com.google.protobuf.ByteString getRSAModOfSrvKeyExc() {
        return rSAModOfSrvKeyExc_;
      }
      /**
       * <code>optional bytes RSAModOfSrvKeyExc = 21;</code>
       */
      public Builder setRSAModOfSrvKeyExc(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00100000;
        rSAModOfSrvKeyExc_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes RSAModOfSrvKeyExc = 21;</code>
       */
      public Builder clearRSAModOfSrvKeyExc() {
        bitField0_ = (bitField0_ & ~0x00100000);
        rSAModOfSrvKeyExc_ = getDefaultInstance().getRSAModOfSrvKeyExc();
        onChanged();
        return this;
      }

      private long rSAExpOfSrvKeyExc_ ;
      /**
       * <code>optional uint64 RSAExpOfSrvKeyExc = 22;</code>
       */
      public boolean hasRSAExpOfSrvKeyExc() {
        return ((bitField0_ & 0x00200000) == 0x00200000);
      }
      /**
       * <code>optional uint64 RSAExpOfSrvKeyExc = 22;</code>
       */
      public long getRSAExpOfSrvKeyExc() {
        return rSAExpOfSrvKeyExc_;
      }
      /**
       * <code>optional uint64 RSAExpOfSrvKeyExc = 22;</code>
       */
      public Builder setRSAExpOfSrvKeyExc(long value) {
        bitField0_ |= 0x00200000;
        rSAExpOfSrvKeyExc_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 RSAExpOfSrvKeyExc = 22;</code>
       */
      public Builder clearRSAExpOfSrvKeyExc() {
        bitField0_ = (bitField0_ & ~0x00200000);
        rSAExpOfSrvKeyExc_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHModOfSrvKeyExc_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHModOfSrvKeyExc = 23;</code>
       */
      public boolean hasDHModOfSrvKeyExc() {
        return ((bitField0_ & 0x00400000) == 0x00400000);
      }
      /**
       * <code>optional bytes DHModOfSrvKeyExc = 23;</code>
       */
      public com.google.protobuf.ByteString getDHModOfSrvKeyExc() {
        return dHModOfSrvKeyExc_;
      }
      /**
       * <code>optional bytes DHModOfSrvKeyExc = 23;</code>
       */
      public Builder setDHModOfSrvKeyExc(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00400000;
        dHModOfSrvKeyExc_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHModOfSrvKeyExc = 23;</code>
       */
      public Builder clearDHModOfSrvKeyExc() {
        bitField0_ = (bitField0_ & ~0x00400000);
        dHModOfSrvKeyExc_ = getDefaultInstance().getDHModOfSrvKeyExc();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHGenOfSrvKeyExc_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHGenOfSrvKeyExc = 24;</code>
       */
      public boolean hasDHGenOfSrvKeyExc() {
        return ((bitField0_ & 0x00800000) == 0x00800000);
      }
      /**
       * <code>optional bytes DHGenOfSrvKeyExc = 24;</code>
       */
      public com.google.protobuf.ByteString getDHGenOfSrvKeyExc() {
        return dHGenOfSrvKeyExc_;
      }
      /**
       * <code>optional bytes DHGenOfSrvKeyExc = 24;</code>
       */
      public Builder setDHGenOfSrvKeyExc(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00800000;
        dHGenOfSrvKeyExc_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHGenOfSrvKeyExc = 24;</code>
       */
      public Builder clearDHGenOfSrvKeyExc() {
        bitField0_ = (bitField0_ & ~0x00800000);
        dHGenOfSrvKeyExc_ = getDefaultInstance().getDHGenOfSrvKeyExc();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvDHPubKey = 25;</code>
       */
      public boolean hasSrvDHPubKey() {
        return ((bitField0_ & 0x01000000) == 0x01000000);
      }
      /**
       * <code>optional bytes srvDHPubKey = 25;</code>
       */
      public com.google.protobuf.ByteString getSrvDHPubKey() {
        return srvDHPubKey_;
      }
      /**
       * <code>optional bytes srvDHPubKey = 25;</code>
       */
      public Builder setSrvDHPubKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x01000000;
        srvDHPubKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvDHPubKey = 25;</code>
       */
      public Builder clearSrvDHPubKey() {
        bitField0_ = (bitField0_ & ~0x01000000);
        srvDHPubKey_ = getDefaultInstance().getSrvDHPubKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString preMasKeyEncryByRSA_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes preMasKeyEncryByRSA = 26;</code>
       */
      public boolean hasPreMasKeyEncryByRSA() {
        return ((bitField0_ & 0x02000000) == 0x02000000);
      }
      /**
       * <code>optional bytes preMasKeyEncryByRSA = 26;</code>
       */
      public com.google.protobuf.ByteString getPreMasKeyEncryByRSA() {
        return preMasKeyEncryByRSA_;
      }
      /**
       * <code>optional bytes preMasKeyEncryByRSA = 26;</code>
       */
      public Builder setPreMasKeyEncryByRSA(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x02000000;
        preMasKeyEncryByRSA_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes preMasKeyEncryByRSA = 26;</code>
       */
      public Builder clearPreMasKeyEncryByRSA() {
        bitField0_ = (bitField0_ & ~0x02000000);
        preMasKeyEncryByRSA_ = getDefaultInstance().getPreMasKeyEncryByRSA();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliDHPubKey = 27;</code>
       */
      public boolean hasCliDHPubKey() {
        return ((bitField0_ & 0x04000000) == 0x04000000);
      }
      /**
       * <code>optional bytes cliDHPubKey = 27;</code>
       */
      public com.google.protobuf.ByteString getCliDHPubKey() {
        return cliDHPubKey_;
      }
      /**
       * <code>optional bytes cliDHPubKey = 27;</code>
       */
      public Builder setCliDHPubKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x04000000;
        cliDHPubKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliDHPubKey = 27;</code>
       */
      public Builder clearCliDHPubKey() {
        bitField0_ = (bitField0_ & ~0x04000000);
        cliDHPubKey_ = getDefaultInstance().getCliDHPubKey();
        onChanged();
        return this;
      }

      private int extTypeInSSL_ ;
      /**
       * <code>optional uint32 extTypeInSSL = 28;</code>
       */
      public boolean hasExtTypeInSSL() {
        return ((bitField0_ & 0x08000000) == 0x08000000);
      }
      /**
       * <code>optional uint32 extTypeInSSL = 28;</code>
       */
      public int getExtTypeInSSL() {
        return extTypeInSSL_;
      }
      /**
       * <code>optional uint32 extTypeInSSL = 28;</code>
       */
      public Builder setExtTypeInSSL(int value) {
        bitField0_ |= 0x08000000;
        extTypeInSSL_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 extTypeInSSL = 28;</code>
       */
      public Builder clearExtTypeInSSL() {
        bitField0_ = (bitField0_ & ~0x08000000);
        extTypeInSSL_ = 0;
        onChanged();
        return this;
      }

      private int cliEllCurPoiFor_ ;
      /**
       * <code>optional uint32 cliEllCurPoiFor = 29;</code>
       */
      public boolean hasCliEllCurPoiFor() {
        return ((bitField0_ & 0x10000000) == 0x10000000);
      }
      /**
       * <code>optional uint32 cliEllCurPoiFor = 29;</code>
       */
      public int getCliEllCurPoiFor() {
        return cliEllCurPoiFor_;
      }
      /**
       * <code>optional uint32 cliEllCurPoiFor = 29;</code>
       */
      public Builder setCliEllCurPoiFor(int value) {
        bitField0_ |= 0x10000000;
        cliEllCurPoiFor_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cliEllCurPoiFor = 29;</code>
       */
      public Builder clearCliEllCurPoiFor() {
        bitField0_ = (bitField0_ & ~0x10000000);
        cliEllCurPoiFor_ = 0;
        onChanged();
        return this;
      }

      private int cliEllCur_ ;
      /**
       * <code>optional uint32 cliEllCur = 30;</code>
       */
      public boolean hasCliEllCur() {
        return ((bitField0_ & 0x20000000) == 0x20000000);
      }
      /**
       * <code>optional uint32 cliEllCur = 30;</code>
       */
      public int getCliEllCur() {
        return cliEllCur_;
      }
      /**
       * <code>optional uint32 cliEllCur = 30;</code>
       */
      public Builder setCliEllCur(int value) {
        bitField0_ |= 0x20000000;
        cliEllCur_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cliEllCur = 30;</code>
       */
      public Builder clearCliEllCur() {
        bitField0_ = (bitField0_ & ~0x20000000);
        cliEllCur_ = 0;
        onChanged();
        return this;
      }

      private int srvEllCurPoiFor_ ;
      /**
       * <code>optional uint32 srvEllCurPoiFor = 31;</code>
       */
      public boolean hasSrvEllCurPoiFor() {
        return ((bitField0_ & 0x40000000) == 0x40000000);
      }
      /**
       * <code>optional uint32 srvEllCurPoiFor = 31;</code>
       */
      public int getSrvEllCurPoiFor() {
        return srvEllCurPoiFor_;
      }
      /**
       * <code>optional uint32 srvEllCurPoiFor = 31;</code>
       */
      public Builder setSrvEllCurPoiFor(int value) {
        bitField0_ |= 0x40000000;
        srvEllCurPoiFor_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvEllCurPoiFor = 31;</code>
       */
      public Builder clearSrvEllCurPoiFor() {
        bitField0_ = (bitField0_ & ~0x40000000);
        srvEllCurPoiFor_ = 0;
        onChanged();
        return this;
      }

      private int srvEllCur_ ;
      /**
       * <code>optional uint32 srvEllCur = 32;</code>
       */
      public boolean hasSrvEllCur() {
        return ((bitField0_ & 0x80000000) == 0x80000000);
      }
      /**
       * <code>optional uint32 srvEllCur = 32;</code>
       */
      public int getSrvEllCur() {
        return srvEllCur_;
      }
      /**
       * <code>optional uint32 srvEllCur = 32;</code>
       */
      public Builder setSrvEllCur(int value) {
        bitField0_ |= 0x80000000;
        srvEllCur_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvEllCur = 32;</code>
       */
      public Builder clearSrvEllCur() {
        bitField0_ = (bitField0_ & ~0x80000000);
        srvEllCur_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvEllCurDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvEllCurDHPubKey = 33;</code>
       */
      public boolean hasSrvEllCurDHPubKey() {
        return ((bitField1_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional bytes srvEllCurDHPubKey = 33;</code>
       */
      public com.google.protobuf.ByteString getSrvEllCurDHPubKey() {
        return srvEllCurDHPubKey_;
      }
      /**
       * <code>optional bytes srvEllCurDHPubKey = 33;</code>
       */
      public Builder setSrvEllCurDHPubKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000001;
        srvEllCurDHPubKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvEllCurDHPubKey = 33;</code>
       */
      public Builder clearSrvEllCurDHPubKey() {
        bitField1_ = (bitField1_ & ~0x00000001);
        srvEllCurDHPubKey_ = getDefaultInstance().getSrvEllCurDHPubKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliEllCurDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliEllCurDHPubKey = 34;</code>
       */
      public boolean hasCliEllCurDHPubKey() {
        return ((bitField1_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional bytes cliEllCurDHPubKey = 34;</code>
       */
      public com.google.protobuf.ByteString getCliEllCurDHPubKey() {
        return cliEllCurDHPubKey_;
      }
      /**
       * <code>optional bytes cliEllCurDHPubKey = 34;</code>
       */
      public Builder setCliEllCurDHPubKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000002;
        cliEllCurDHPubKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliEllCurDHPubKey = 34;</code>
       */
      public Builder clearCliEllCurDHPubKey() {
        bitField1_ = (bitField1_ & ~0x00000002);
        cliEllCurDHPubKey_ = getDefaultInstance().getCliEllCurDHPubKey();
        onChanged();
        return this;
      }

      private long srvGMTUniTime35_ ;
      /**
       * <code>optional uint64 srvGMTUni_Time = 35;</code>
       */
      public boolean hasSrvGMTUniTime35() {
        return ((bitField1_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional uint64 srvGMTUni_Time = 35;</code>
       */
      public long getSrvGMTUniTime35() {
        return srvGMTUniTime35_;
      }
      /**
       * <code>optional uint64 srvGMTUni_Time = 35;</code>
       */
      public Builder setSrvGMTUniTime35(long value) {
        bitField1_ |= 0x00000004;
        srvGMTUniTime35_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 srvGMTUni_Time = 35;</code>
       */
      public Builder clearSrvGMTUniTime35() {
        bitField1_ = (bitField1_ & ~0x00000004);
        srvGMTUniTime35_ = 0L;
        onChanged();
        return this;
      }

      private int cliExtCnt_ ;
      /**
       * <code>optional uint32 cliExtCnt = 36;</code>
       */
      public boolean hasCliExtCnt() {
        return ((bitField1_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional uint32 cliExtCnt = 36;</code>
       */
      public int getCliExtCnt() {
        return cliExtCnt_;
      }
      /**
       * <code>optional uint32 cliExtCnt = 36;</code>
       */
      public Builder setCliExtCnt(int value) {
        bitField1_ |= 0x00000008;
        cliExtCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cliExtCnt = 36;</code>
       */
      public Builder clearCliExtCnt() {
        bitField1_ = (bitField1_ & ~0x00000008);
        cliExtCnt_ = 0;
        onChanged();
        return this;
      }

      private int srvExtCnt_ ;
      /**
       * <code>optional uint32 srvExtCnt = 37;</code>
       */
      public boolean hasSrvExtCnt() {
        return ((bitField1_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional uint32 srvExtCnt = 37;</code>
       */
      public int getSrvExtCnt() {
        return srvExtCnt_;
      }
      /**
       * <code>optional uint32 srvExtCnt = 37;</code>
       */
      public Builder setSrvExtCnt(int value) {
        bitField1_ |= 0x00000010;
        srvExtCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvExtCnt = 37;</code>
       */
      public Builder clearSrvExtCnt() {
        bitField1_ = (bitField1_ & ~0x00000010);
        srvExtCnt_ = 0;
        onChanged();
        return this;
      }

      private int cliHandSkLen_ ;
      /**
       * <code>optional uint32 cliHandSkLen = 38;</code>
       */
      public boolean hasCliHandSkLen() {
        return ((bitField1_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional uint32 cliHandSkLen = 38;</code>
       */
      public int getCliHandSkLen() {
        return cliHandSkLen_;
      }
      /**
       * <code>optional uint32 cliHandSkLen = 38;</code>
       */
      public Builder setCliHandSkLen(int value) {
        bitField1_ |= 0x00000020;
        cliHandSkLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cliHandSkLen = 38;</code>
       */
      public Builder clearCliHandSkLen() {
        bitField1_ = (bitField1_ & ~0x00000020);
        cliHandSkLen_ = 0;
        onChanged();
        return this;
      }

      private int srvHandSkLen_ ;
      /**
       * <code>optional uint32 srvHandSkLen = 39;</code>
       */
      public boolean hasSrvHandSkLen() {
        return ((bitField1_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional uint32 srvHandSkLen = 39;</code>
       */
      public int getSrvHandSkLen() {
        return srvHandSkLen_;
      }
      /**
       * <code>optional uint32 srvHandSkLen = 39;</code>
       */
      public Builder setSrvHandSkLen(int value) {
        bitField1_ |= 0x00000040;
        srvHandSkLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvHandSkLen = 39;</code>
       */
      public Builder clearSrvHandSkLen() {
        bitField1_ = (bitField1_ & ~0x00000040);
        srvHandSkLen_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliExt_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliExt = 40;</code>
       */
      public boolean hasCliExt() {
        return ((bitField1_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional bytes cliExt = 40;</code>
       */
      public com.google.protobuf.ByteString getCliExt() {
        return cliExt_;
      }
      /**
       * <code>optional bytes cliExt = 40;</code>
       */
      public Builder setCliExt(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000080;
        cliExt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliExt = 40;</code>
       */
      public Builder clearCliExt() {
        bitField1_ = (bitField1_ & ~0x00000080);
        cliExt_ = getDefaultInstance().getCliExt();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvExt_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvExt = 41;</code>
       */
      public boolean hasSrvExt() {
        return ((bitField1_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional bytes srvExt = 41;</code>
       */
      public com.google.protobuf.ByteString getSrvExt() {
        return srvExt_;
      }
      /**
       * <code>optional bytes srvExt = 41;</code>
       */
      public Builder setSrvExt(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000100;
        srvExt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvExt = 41;</code>
       */
      public Builder clearSrvExt() {
        bitField1_ = (bitField1_ & ~0x00000100);
        srvExt_ = getDefaultInstance().getSrvExt();
        onChanged();
        return this;
      }

      private int cliExtGrease_ ;
      /**
       * <code>optional uint32 cliExtGrease = 42;</code>
       */
      public boolean hasCliExtGrease() {
        return ((bitField1_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>optional uint32 cliExtGrease = 42;</code>
       */
      public int getCliExtGrease() {
        return cliExtGrease_;
      }
      /**
       * <code>optional uint32 cliExtGrease = 42;</code>
       */
      public Builder setCliExtGrease(int value) {
        bitField1_ |= 0x00000200;
        cliExtGrease_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cliExtGrease = 42;</code>
       */
      public Builder clearCliExtGrease() {
        bitField1_ = (bitField1_ & ~0x00000200);
        cliExtGrease_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliJA3_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliJA3 = 43;</code>
       */
      public boolean hasCliJA3() {
        return ((bitField1_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>optional bytes cliJA3 = 43;</code>
       */
      public com.google.protobuf.ByteString getCliJA3() {
        return cliJA3_;
      }
      /**
       * <code>optional bytes cliJA3 = 43;</code>
       */
      public Builder setCliJA3(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000400;
        cliJA3_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliJA3 = 43;</code>
       */
      public Builder clearCliJA3() {
        bitField1_ = (bitField1_ & ~0x00000400);
        cliJA3_ = getDefaultInstance().getCliJA3();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvJA3_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvJA3 = 44;</code>
       */
      public boolean hasSrvJA3() {
        return ((bitField1_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>optional bytes srvJA3 = 44;</code>
       */
      public com.google.protobuf.ByteString getSrvJA3() {
        return srvJA3_;
      }
      /**
       * <code>optional bytes srvJA3 = 44;</code>
       */
      public Builder setSrvJA3(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000800;
        srvJA3_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvJA3 = 44;</code>
       */
      public Builder clearSrvJA3() {
        bitField1_ = (bitField1_ & ~0x00000800);
        srvJA3_ = getDefaultInstance().getSrvJA3();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliSessTicket_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliSessTicket = 45;</code>
       */
      public boolean hasCliSessTicket() {
        return ((bitField1_ & 0x00001000) == 0x00001000);
      }
      /**
       * <code>optional bytes cliSessTicket = 45;</code>
       */
      public com.google.protobuf.ByteString getCliSessTicket() {
        return cliSessTicket_;
      }
      /**
       * <code>optional bytes cliSessTicket = 45;</code>
       */
      public Builder setCliSessTicket(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00001000;
        cliSessTicket_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliSessTicket = 45;</code>
       */
      public Builder clearCliSessTicket() {
        bitField1_ = (bitField1_ & ~0x00001000);
        cliSessTicket_ = getDefaultInstance().getCliSessTicket();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvSessTicket_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvSessTicket = 46;</code>
       */
      public boolean hasSrvSessTicket() {
        return ((bitField1_ & 0x00002000) == 0x00002000);
      }
      /**
       * <code>optional bytes srvSessTicket = 46;</code>
       */
      public com.google.protobuf.ByteString getSrvSessTicket() {
        return srvSessTicket_;
      }
      /**
       * <code>optional bytes srvSessTicket = 46;</code>
       */
      public Builder setSrvSessTicket(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00002000;
        srvSessTicket_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvSessTicket = 46;</code>
       */
      public Builder clearSrvSessTicket() {
        bitField1_ = (bitField1_ & ~0x00002000);
        srvSessTicket_ = getDefaultInstance().getSrvSessTicket();
        onChanged();
        return this;
      }

      private int authTag_ ;
      /**
       * <code>optional uint32 AuthTag = 47;</code>
       */
      public boolean hasAuthTag() {
        return ((bitField1_ & 0x00004000) == 0x00004000);
      }
      /**
       * <code>optional uint32 AuthTag = 47;</code>
       */
      public int getAuthTag() {
        return authTag_;
      }
      /**
       * <code>optional uint32 AuthTag = 47;</code>
       */
      public Builder setAuthTag(int value) {
        bitField1_ |= 0x00004000;
        authTag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 AuthTag = 47;</code>
       */
      public Builder clearAuthTag() {
        bitField1_ = (bitField1_ & ~0x00004000);
        authTag_ = 0;
        onChanged();
        return this;
      }

      private int cliCertCnt_ ;
      /**
       * <code>optional uint32 cliCertCnt = 48;</code>
       */
      public boolean hasCliCertCnt() {
        return ((bitField1_ & 0x00008000) == 0x00008000);
      }
      /**
       * <code>optional uint32 cliCertCnt = 48;</code>
       */
      public int getCliCertCnt() {
        return cliCertCnt_;
      }
      /**
       * <code>optional uint32 cliCertCnt = 48;</code>
       */
      public Builder setCliCertCnt(int value) {
        bitField1_ |= 0x00008000;
        cliCertCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cliCertCnt = 48;</code>
       */
      public Builder clearCliCertCnt() {
        bitField1_ = (bitField1_ & ~0x00008000);
        cliCertCnt_ = 0;
        onChanged();
        return this;
      }

      private int srvCertCnt_ ;
      /**
       * <code>optional uint32 srvCertCnt = 49;</code>
       */
      public boolean hasSrvCertCnt() {
        return ((bitField1_ & 0x00010000) == 0x00010000);
      }
      /**
       * <code>optional uint32 srvCertCnt = 49;</code>
       */
      public int getSrvCertCnt() {
        return srvCertCnt_;
      }
      /**
       * <code>optional uint32 srvCertCnt = 49;</code>
       */
      public Builder setSrvCertCnt(int value) {
        bitField1_ |= 0x00010000;
        srvCertCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvCertCnt = 49;</code>
       */
      public Builder clearSrvCertCnt() {
        bitField1_ = (bitField1_ & ~0x00010000);
        srvCertCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ecGroupsCli_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ecGroupsCli = 50;</code>
       */
      public boolean hasEcGroupsCli() {
        return ((bitField1_ & 0x00020000) == 0x00020000);
      }
      /**
       * <code>optional bytes ecGroupsCli = 50;</code>
       */
      public com.google.protobuf.ByteString getEcGroupsCli() {
        return ecGroupsCli_;
      }
      /**
       * <code>optional bytes ecGroupsCli = 50;</code>
       */
      public Builder setEcGroupsCli(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00020000;
        ecGroupsCli_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ecGroupsCli = 50;</code>
       */
      public Builder clearEcGroupsCli() {
        bitField1_ = (bitField1_ & ~0x00020000);
        ecGroupsCli_ = getDefaultInstance().getEcGroupsCli();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ecPoiForByServ_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ecPoiForByServ = 51;</code>
       */
      public boolean hasEcPoiForByServ() {
        return ((bitField1_ & 0x00040000) == 0x00040000);
      }
      /**
       * <code>optional bytes ecPoiForByServ = 51;</code>
       */
      public com.google.protobuf.ByteString getEcPoiForByServ() {
        return ecPoiForByServ_;
      }
      /**
       * <code>optional bytes ecPoiForByServ = 51;</code>
       */
      public Builder setEcPoiForByServ(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00040000;
        ecPoiForByServ_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ecPoiForByServ = 51;</code>
       */
      public Builder clearEcPoiForByServ() {
        bitField1_ = (bitField1_ & ~0x00040000);
        ecPoiForByServ_ = getDefaultInstance().getEcPoiForByServ();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString etags_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes etags = 52;</code>
       */
      public boolean hasEtags() {
        return ((bitField1_ & 0x00080000) == 0x00080000);
      }
      /**
       * <code>optional bytes etags = 52;</code>
       */
      public com.google.protobuf.ByteString getEtags() {
        return etags_;
      }
      /**
       * <code>optional bytes etags = 52;</code>
       */
      public Builder setEtags(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00080000;
        etags_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes etags = 52;</code>
       */
      public Builder clearEtags() {
        bitField1_ = (bitField1_ & ~0x00080000);
        etags_ = getDefaultInstance().getEtags();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ttags_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ttags = 53;</code>
       */
      public boolean hasTtags() {
        return ((bitField1_ & 0x00100000) == 0x00100000);
      }
      /**
       * <code>optional bytes ttags = 53;</code>
       */
      public com.google.protobuf.ByteString getTtags() {
        return ttags_;
      }
      /**
       * <code>optional bytes ttags = 53;</code>
       */
      public Builder setTtags(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00100000;
        ttags_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ttags = 53;</code>
       */
      public Builder clearTtags() {
        bitField1_ = (bitField1_ & ~0x00100000);
        ttags_ = getDefaultInstance().getTtags();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliSesIDLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliSesIDLen = 54;</code>
       */
      public boolean hasCliSesIDLen() {
        return ((bitField1_ & 0x00200000) == 0x00200000);
      }
      /**
       * <code>optional bytes cliSesIDLen = 54;</code>
       */
      public com.google.protobuf.ByteString getCliSesIDLen() {
        return cliSesIDLen_;
      }
      /**
       * <code>optional bytes cliSesIDLen = 54;</code>
       */
      public Builder setCliSesIDLen(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00200000;
        cliSesIDLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliSesIDLen = 54;</code>
       */
      public Builder clearCliSesIDLen() {
        bitField1_ = (bitField1_ & ~0x00200000);
        cliSesIDLen_ = getDefaultInstance().getCliSesIDLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvSesIDLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvSesIDLen = 55;</code>
       */
      public boolean hasSrvSesIDLen() {
        return ((bitField1_ & 0x00400000) == 0x00400000);
      }
      /**
       * <code>optional bytes srvSesIDLen = 55;</code>
       */
      public com.google.protobuf.ByteString getSrvSesIDLen() {
        return srvSesIDLen_;
      }
      /**
       * <code>optional bytes srvSesIDLen = 55;</code>
       */
      public Builder setSrvSesIDLen(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00400000;
        srvSesIDLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvSesIDLen = 55;</code>
       */
      public Builder clearSrvSesIDLen() {
        bitField1_ = (bitField1_ & ~0x00400000);
        srvSesIDLen_ = getDefaultInstance().getSrvSesIDLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvKeyExcLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvKeyExcLen = 56;</code>
       */
      public boolean hasSrvKeyExcLen() {
        return ((bitField1_ & 0x00800000) == 0x00800000);
      }
      /**
       * <code>optional bytes srvKeyExcLen = 56;</code>
       */
      public com.google.protobuf.ByteString getSrvKeyExcLen() {
        return srvKeyExcLen_;
      }
      /**
       * <code>optional bytes srvKeyExcLen = 56;</code>
       */
      public Builder setSrvKeyExcLen(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00800000;
        srvKeyExcLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvKeyExcLen = 56;</code>
       */
      public Builder clearSrvKeyExcLen() {
        bitField1_ = (bitField1_ & ~0x00800000);
        srvKeyExcLen_ = getDefaultInstance().getSrvKeyExcLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString eCDHCurType_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ECDHCurType = 57;</code>
       */
      public boolean hasECDHCurType() {
        return ((bitField1_ & 0x01000000) == 0x01000000);
      }
      /**
       * <code>optional bytes ECDHCurType = 57;</code>
       */
      public com.google.protobuf.ByteString getECDHCurType() {
        return eCDHCurType_;
      }
      /**
       * <code>optional bytes ECDHCurType = 57;</code>
       */
      public Builder setECDHCurType(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x01000000;
        eCDHCurType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ECDHCurType = 57;</code>
       */
      public Builder clearECDHCurType() {
        bitField1_ = (bitField1_ & ~0x01000000);
        eCDHCurType_ = getDefaultInstance().getECDHCurType();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString eCDHSig_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ECDHSig = 58;</code>
       */
      public boolean hasECDHSig() {
        return ((bitField1_ & 0x02000000) == 0x02000000);
      }
      /**
       * <code>optional bytes ECDHSig = 58;</code>
       */
      public com.google.protobuf.ByteString getECDHSig() {
        return eCDHSig_;
      }
      /**
       * <code>optional bytes ECDHSig = 58;</code>
       */
      public Builder setECDHSig(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x02000000;
        eCDHSig_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ECDHSig = 58;</code>
       */
      public Builder clearECDHSig() {
        bitField1_ = (bitField1_ & ~0x02000000);
        eCDHSig_ = getDefaultInstance().getECDHSig();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHEPLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHEPLen = 59;</code>
       */
      public boolean hasDHEPLen() {
        return ((bitField1_ & 0x04000000) == 0x04000000);
      }
      /**
       * <code>optional bytes DHEPLen = 59;</code>
       */
      public com.google.protobuf.ByteString getDHEPLen() {
        return dHEPLen_;
      }
      /**
       * <code>optional bytes DHEPLen = 59;</code>
       */
      public Builder setDHEPLen(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x04000000;
        dHEPLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHEPLen = 59;</code>
       */
      public Builder clearDHEPLen() {
        bitField1_ = (bitField1_ & ~0x04000000);
        dHEPLen_ = getDefaultInstance().getDHEPLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHEGLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHEGLen = 60;</code>
       */
      public boolean hasDHEGLen() {
        return ((bitField1_ & 0x08000000) == 0x08000000);
      }
      /**
       * <code>optional bytes DHEGLen = 60;</code>
       */
      public com.google.protobuf.ByteString getDHEGLen() {
        return dHEGLen_;
      }
      /**
       * <code>optional bytes DHEGLen = 60;</code>
       */
      public Builder setDHEGLen(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x08000000;
        dHEGLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHEGLen = 60;</code>
       */
      public Builder clearDHEGLen() {
        bitField1_ = (bitField1_ & ~0x08000000);
        dHEGLen_ = getDefaultInstance().getDHEGLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliKeyExcLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliKeyExcLen = 61;</code>
       */
      public boolean hasCliKeyExcLen() {
        return ((bitField1_ & 0x10000000) == 0x10000000);
      }
      /**
       * <code>optional bytes cliKeyExcLen = 61;</code>
       */
      public com.google.protobuf.ByteString getCliKeyExcLen() {
        return cliKeyExcLen_;
      }
      /**
       * <code>optional bytes cliKeyExcLen = 61;</code>
       */
      public Builder setCliKeyExcLen(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x10000000;
        cliKeyExcLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliKeyExcLen = 61;</code>
       */
      public Builder clearCliKeyExcLen() {
        bitField1_ = (bitField1_ & ~0x10000000);
        cliKeyExcLen_ = getDefaultInstance().getCliKeyExcLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString encPubKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes encPubKey = 62;</code>
       */
      public boolean hasEncPubKey() {
        return ((bitField1_ & 0x20000000) == 0x20000000);
      }
      /**
       * <code>optional bytes encPubKey = 62;</code>
       */
      public com.google.protobuf.ByteString getEncPubKey() {
        return encPubKey_;
      }
      /**
       * <code>optional bytes encPubKey = 62;</code>
       */
      public Builder setEncPubKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x20000000;
        encPubKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes encPubKey = 62;</code>
       */
      public Builder clearEncPubKey() {
        bitField1_ = (bitField1_ & ~0x20000000);
        encPubKey_ = getDefaultInstance().getEncPubKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString encPubKeyLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes encPubKeyLen = 63;</code>
       */
      public boolean hasEncPubKeyLen() {
        return ((bitField1_ & 0x40000000) == 0x40000000);
      }
      /**
       * <code>optional bytes encPubKeyLen = 63;</code>
       */
      public com.google.protobuf.ByteString getEncPubKeyLen() {
        return encPubKeyLen_;
      }
      /**
       * <code>optional bytes encPubKeyLen = 63;</code>
       */
      public Builder setEncPubKeyLen(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x40000000;
        encPubKeyLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes encPubKeyLen = 63;</code>
       */
      public Builder clearEncPubKeyLen() {
        bitField1_ = (bitField1_ & ~0x40000000);
        encPubKeyLen_ = getDefaultInstance().getEncPubKeyLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliExtLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliExtLen = 64;</code>
       */
      public boolean hasCliExtLen() {
        return ((bitField1_ & 0x80000000) == 0x80000000);
      }
      /**
       * <code>optional bytes cliExtLen = 64;</code>
       */
      public com.google.protobuf.ByteString getCliExtLen() {
        return cliExtLen_;
      }
      /**
       * <code>optional bytes cliExtLen = 64;</code>
       */
      public Builder setCliExtLen(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x80000000;
        cliExtLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliExtLen = 64;</code>
       */
      public Builder clearCliExtLen() {
        bitField1_ = (bitField1_ & ~0x80000000);
        cliExtLen_ = getDefaultInstance().getCliExtLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvExtLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvExtLen = 65;</code>
       */
      public boolean hasSrvExtLen() {
        return ((bitField2_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional bytes srvExtLen = 65;</code>
       */
      public com.google.protobuf.ByteString getSrvExtLen() {
        return srvExtLen_;
      }
      /**
       * <code>optional bytes srvExtLen = 65;</code>
       */
      public Builder setSrvExtLen(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00000001;
        srvExtLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvExtLen = 65;</code>
       */
      public Builder clearSrvExtLen() {
        bitField2_ = (bitField2_ & ~0x00000001);
        srvExtLen_ = getDefaultInstance().getSrvExtLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString eCDHPubKeyLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ECDHPubKeyLen = 66;</code>
       */
      public boolean hasECDHPubKeyLen() {
        return ((bitField2_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional bytes ECDHPubKeyLen = 66;</code>
       */
      public com.google.protobuf.ByteString getECDHPubKeyLen() {
        return eCDHPubKeyLen_;
      }
      /**
       * <code>optional bytes ECDHPubKeyLen = 66;</code>
       */
      public Builder setECDHPubKeyLen(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00000002;
        eCDHPubKeyLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ECDHPubKeyLen = 66;</code>
       */
      public Builder clearECDHPubKeyLen() {
        bitField2_ = (bitField2_ & ~0x00000002);
        eCDHPubKeyLen_ = getDefaultInstance().getECDHPubKeyLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString namType_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes namType = 67;</code>
       */
      public boolean hasNamType() {
        return ((bitField2_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional bytes namType = 67;</code>
       */
      public com.google.protobuf.ByteString getNamType() {
        return namType_;
      }
      /**
       * <code>optional bytes namType = 67;</code>
       */
      public Builder setNamType(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00000004;
        namType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes namType = 67;</code>
       */
      public Builder clearNamType() {
        bitField2_ = (bitField2_ & ~0x00000004);
        namType_ = getDefaultInstance().getNamType();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString namLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes namLen = 68;</code>
       */
      public boolean hasNamLen() {
        return ((bitField2_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional bytes namLen = 68;</code>
       */
      public com.google.protobuf.ByteString getNamLen() {
        return namLen_;
      }
      /**
       * <code>optional bytes namLen = 68;</code>
       */
      public Builder setNamLen(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00000008;
        namLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes namLen = 68;</code>
       */
      public Builder clearNamLen() {
        bitField2_ = (bitField2_ & ~0x00000008);
        namLen_ = getDefaultInstance().getNamLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ticDat_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ticDat = 69;</code>
       */
      public boolean hasTicDat() {
        return ((bitField2_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional bytes ticDat = 69;</code>
       */
      public com.google.protobuf.ByteString getTicDat() {
        return ticDat_;
      }
      /**
       * <code>optional bytes ticDat = 69;</code>
       */
      public Builder setTicDat(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00000010;
        ticDat_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ticDat = 69;</code>
       */
      public Builder clearTicDat() {
        bitField2_ = (bitField2_ & ~0x00000010);
        ticDat_ = getDefaultInstance().getTicDat();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvCipSui_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvCipSui = 70;</code>
       */
      public boolean hasSrvCipSui() {
        return ((bitField2_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional bytes srvCipSui = 70;</code>
       */
      public com.google.protobuf.ByteString getSrvCipSui() {
        return srvCipSui_;
      }
      /**
       * <code>optional bytes srvCipSui = 70;</code>
       */
      public Builder setSrvCipSui(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00000020;
        srvCipSui_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvCipSui = 70;</code>
       */
      public Builder clearSrvCipSui() {
        bitField2_ = (bitField2_ & ~0x00000020);
        srvCipSui_ = getDefaultInstance().getSrvCipSui();
        onChanged();
        return this;
      }

      private int cipSuiNum_ ;
      /**
       * <code>optional uint32 cipSuiNum = 71;</code>
       */
      public boolean hasCipSuiNum() {
        return ((bitField2_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional uint32 cipSuiNum = 71;</code>
       */
      public int getCipSuiNum() {
        return cipSuiNum_;
      }
      /**
       * <code>optional uint32 cipSuiNum = 71;</code>
       */
      public Builder setCipSuiNum(int value) {
        bitField2_ |= 0x00000040;
        cipSuiNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cipSuiNum = 71;</code>
       */
      public Builder clearCipSuiNum() {
        bitField2_ = (bitField2_ & ~0x00000040);
        cipSuiNum_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString eCDHSigHash_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ECDHSigHash = 72;</code>
       */
      public boolean hasECDHSigHash() {
        return ((bitField2_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional bytes ECDHSigHash = 72;</code>
       */
      public com.google.protobuf.ByteString getECDHSigHash() {
        return eCDHSigHash_;
      }
      /**
       * <code>optional bytes ECDHSigHash = 72;</code>
       */
      public Builder setECDHSigHash(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00000080;
        eCDHSigHash_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ECDHSigHash = 72;</code>
       */
      public Builder clearECDHSigHash() {
        bitField2_ = (bitField2_ & ~0x00000080);
        eCDHSigHash_ = getDefaultInstance().getECDHSigHash();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHESigHash_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHESigHash = 73;</code>
       */
      public boolean hasDHESigHash() {
        return ((bitField2_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional bytes DHESigHash = 73;</code>
       */
      public com.google.protobuf.ByteString getDHESigHash() {
        return dHESigHash_;
      }
      /**
       * <code>optional bytes DHESigHash = 73;</code>
       */
      public Builder setDHESigHash(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00000100;
        dHESigHash_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHESigHash = 73;</code>
       */
      public Builder clearDHESigHash() {
        bitField2_ = (bitField2_ & ~0x00000100);
        dHESigHash_ = getDefaultInstance().getDHESigHash();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString rSASigHash_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes RSASigHash = 74;</code>
       */
      public boolean hasRSASigHash() {
        return ((bitField2_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>optional bytes RSASigHash = 74;</code>
       */
      public com.google.protobuf.ByteString getRSASigHash() {
        return rSASigHash_;
      }
      /**
       * <code>optional bytes RSASigHash = 74;</code>
       */
      public Builder setRSASigHash(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00000200;
        rSASigHash_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes RSASigHash = 74;</code>
       */
      public Builder clearRSASigHash() {
        bitField2_ = (bitField2_ & ~0x00000200);
        rSASigHash_ = getDefaultInstance().getRSASigHash();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString greaseFlag_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes greaseFlag = 75;</code>
       */
      public boolean hasGreaseFlag() {
        return ((bitField2_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>optional bytes greaseFlag = 75;</code>
       */
      public com.google.protobuf.ByteString getGreaseFlag() {
        return greaseFlag_;
      }
      /**
       * <code>optional bytes greaseFlag = 75;</code>
       */
      public Builder setGreaseFlag(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00000400;
        greaseFlag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes greaseFlag = 75;</code>
       */
      public Builder clearGreaseFlag() {
        bitField2_ = (bitField2_ & ~0x00000400);
        greaseFlag_ = getDefaultInstance().getGreaseFlag();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString rSAModLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes RSAModLen = 76;</code>
       */
      public boolean hasRSAModLen() {
        return ((bitField2_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>optional bytes RSAModLen = 76;</code>
       */
      public com.google.protobuf.ByteString getRSAModLen() {
        return rSAModLen_;
      }
      /**
       * <code>optional bytes RSAModLen = 76;</code>
       */
      public Builder setRSAModLen(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00000800;
        rSAModLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes RSAModLen = 76;</code>
       */
      public Builder clearRSAModLen() {
        bitField2_ = (bitField2_ & ~0x00000800);
        rSAModLen_ = getDefaultInstance().getRSAModLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString rSAExpLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes RSAExpLen = 77;</code>
       */
      public boolean hasRSAExpLen() {
        return ((bitField2_ & 0x00001000) == 0x00001000);
      }
      /**
       * <code>optional bytes RSAExpLen = 77;</code>
       */
      public com.google.protobuf.ByteString getRSAExpLen() {
        return rSAExpLen_;
      }
      /**
       * <code>optional bytes RSAExpLen = 77;</code>
       */
      public Builder setRSAExpLen(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00001000;
        rSAExpLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes RSAExpLen = 77;</code>
       */
      public Builder clearRSAExpLen() {
        bitField2_ = (bitField2_ & ~0x00001000);
        rSAExpLen_ = getDefaultInstance().getRSAExpLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString rSASig_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes RSASig = 78;</code>
       */
      public boolean hasRSASig() {
        return ((bitField2_ & 0x00002000) == 0x00002000);
      }
      /**
       * <code>optional bytes RSASig = 78;</code>
       */
      public com.google.protobuf.ByteString getRSASig() {
        return rSASig_;
      }
      /**
       * <code>optional bytes RSASig = 78;</code>
       */
      public Builder setRSASig(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00002000;
        rSASig_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes RSASig = 78;</code>
       */
      public Builder clearRSASig() {
        bitField2_ = (bitField2_ & ~0x00002000);
        rSASig_ = getDefaultInstance().getRSASig();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHESig_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHESig = 79;</code>
       */
      public boolean hasDHESig() {
        return ((bitField2_ & 0x00004000) == 0x00004000);
      }
      /**
       * <code>optional bytes DHESig = 79;</code>
       */
      public com.google.protobuf.ByteString getDHESig() {
        return dHESig_;
      }
      /**
       * <code>optional bytes DHESig = 79;</code>
       */
      public Builder setDHESig(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00004000;
        dHESig_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHESig = 79;</code>
       */
      public Builder clearDHESig() {
        bitField2_ = (bitField2_ & ~0x00004000);
        dHESig_ = getDefaultInstance().getDHESig();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHEPubKeyLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHEPubKeyLen = 80;</code>
       */
      public boolean hasDHEPubKeyLen() {
        return ((bitField2_ & 0x00008000) == 0x00008000);
      }
      /**
       * <code>optional bytes DHEPubKeyLen = 80;</code>
       */
      public com.google.protobuf.ByteString getDHEPubKeyLen() {
        return dHEPubKeyLen_;
      }
      /**
       * <code>optional bytes DHEPubKeyLen = 80;</code>
       */
      public Builder setDHEPubKeyLen(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00008000;
        dHEPubKeyLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHEPubKeyLen = 80;</code>
       */
      public Builder clearDHEPubKeyLen() {
        bitField2_ = (bitField2_ & ~0x00008000);
        dHEPubKeyLen_ = getDefaultInstance().getDHEPubKeyLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHEPubKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHEPubKey = 81;</code>
       */
      public boolean hasDHEPubKey() {
        return ((bitField2_ & 0x00010000) == 0x00010000);
      }
      /**
       * <code>optional bytes DHEPubKey = 81;</code>
       */
      public com.google.protobuf.ByteString getDHEPubKey() {
        return dHEPubKey_;
      }
      /**
       * <code>optional bytes DHEPubKey = 81;</code>
       */
      public Builder setDHEPubKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00010000;
        dHEPubKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHEPubKey = 81;</code>
       */
      public Builder clearDHEPubKey() {
        bitField2_ = (bitField2_ & ~0x00010000);
        dHEPubKey_ = getDefaultInstance().getDHEPubKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString sigAlgType_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes SigAlgType = 82;</code>
       */
      public boolean hasSigAlgType() {
        return ((bitField2_ & 0x00020000) == 0x00020000);
      }
      /**
       * <code>optional bytes SigAlgType = 82;</code>
       */
      public com.google.protobuf.ByteString getSigAlgType() {
        return sigAlgType_;
      }
      /**
       * <code>optional bytes SigAlgType = 82;</code>
       */
      public Builder setSigAlgType(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00020000;
        sigAlgType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes SigAlgType = 82;</code>
       */
      public Builder clearSigAlgType() {
        bitField2_ = (bitField2_ & ~0x00020000);
        sigAlgType_ = getDefaultInstance().getSigAlgType();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString sigAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes sigAlg = 83;</code>
       */
      public boolean hasSigAlg() {
        return ((bitField2_ & 0x00040000) == 0x00040000);
      }
      /**
       * <code>optional bytes sigAlg = 83;</code>
       */
      public com.google.protobuf.ByteString getSigAlg() {
        return sigAlg_;
      }
      /**
       * <code>optional bytes sigAlg = 83;</code>
       */
      public Builder setSigAlg(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00040000;
        sigAlg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes sigAlg = 83;</code>
       */
      public Builder clearSigAlg() {
        bitField2_ = (bitField2_ & ~0x00040000);
        sigAlg_ = getDefaultInstance().getSigAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString sigHashAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes SigHashAlg = 84;</code>
       */
      public boolean hasSigHashAlg() {
        return ((bitField2_ & 0x00080000) == 0x00080000);
      }
      /**
       * <code>optional bytes SigHashAlg = 84;</code>
       */
      public com.google.protobuf.ByteString getSigHashAlg() {
        return sigHashAlg_;
      }
      /**
       * <code>optional bytes SigHashAlg = 84;</code>
       */
      public Builder setSigHashAlg(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00080000;
        sigHashAlg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes SigHashAlg = 84;</code>
       */
      public Builder clearSigHashAlg() {
        bitField2_ = (bitField2_ & ~0x00080000);
        sigHashAlg_ = getDefaultInstance().getSigHashAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString jOY_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes JOY = 85;</code>
       */
      public boolean hasJOY() {
        return ((bitField2_ & 0x00100000) == 0x00100000);
      }
      /**
       * <code>optional bytes JOY = 85;</code>
       */
      public com.google.protobuf.ByteString getJOY() {
        return jOY_;
      }
      /**
       * <code>optional bytes JOY = 85;</code>
       */
      public Builder setJOY(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00100000;
        jOY_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes JOY = 85;</code>
       */
      public Builder clearJOY() {
        bitField2_ = (bitField2_ & ~0x00100000);
        jOY_ = getDefaultInstance().getJOY();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString jOYS_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes JOYS = 86;</code>
       */
      public boolean hasJOYS() {
        return ((bitField2_ & 0x00200000) == 0x00200000);
      }
      /**
       * <code>optional bytes JOYS = 86;</code>
       */
      public com.google.protobuf.ByteString getJOYS() {
        return jOYS_;
      }
      /**
       * <code>optional bytes JOYS = 86;</code>
       */
      public Builder setJOYS(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00200000;
        jOYS_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes JOYS = 86;</code>
       */
      public Builder clearJOYS() {
        bitField2_ = (bitField2_ & ~0x00200000);
        jOYS_ = getDefaultInstance().getJOYS();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString sTARTTLS_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes STARTTLS = 87;</code>
       */
      public boolean hasSTARTTLS() {
        return ((bitField2_ & 0x00400000) == 0x00400000);
      }
      /**
       * <code>optional bytes STARTTLS = 87;</code>
       */
      public com.google.protobuf.ByteString getSTARTTLS() {
        return sTARTTLS_;
      }
      /**
       * <code>optional bytes STARTTLS = 87;</code>
       */
      public Builder setSTARTTLS(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00400000;
        sTARTTLS_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes STARTTLS = 87;</code>
       */
      public Builder clearSTARTTLS() {
        bitField2_ = (bitField2_ & ~0x00400000);
        sTARTTLS_ = getDefaultInstance().getSTARTTLS();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString certNonFlag_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes certNonFlag = 88;</code>
       */
      public boolean hasCertNonFlag() {
        return ((bitField2_ & 0x00800000) == 0x00800000);
      }
      /**
       * <code>optional bytes certNonFlag = 88;</code>
       */
      public com.google.protobuf.ByteString getCertNonFlag() {
        return certNonFlag_;
      }
      /**
       * <code>optional bytes certNonFlag = 88;</code>
       */
      public Builder setCertNonFlag(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00800000;
        certNonFlag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes certNonFlag = 88;</code>
       */
      public Builder clearCertNonFlag() {
        bitField2_ = (bitField2_ & ~0x00800000);
        certNonFlag_ = getDefaultInstance().getCertNonFlag();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString joyFp_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes JoyFp = 89;</code>
       */
      public boolean hasJoyFp() {
        return ((bitField2_ & 0x01000000) == 0x01000000);
      }
      /**
       * <code>optional bytes JoyFp = 89;</code>
       */
      public com.google.protobuf.ByteString getJoyFp() {
        return joyFp_;
      }
      /**
       * <code>optional bytes JoyFp = 89;</code>
       */
      public Builder setJoyFp(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x01000000;
        joyFp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes JoyFp = 89;</code>
       */
      public Builder clearJoyFp() {
        bitField2_ = (bitField2_ & ~0x01000000);
        joyFp_ = getDefaultInstance().getJoyFp();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString certIntactFlag_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes certIntactFlag = 90;</code>
       */
      public boolean hasCertIntactFlag() {
        return ((bitField2_ & 0x02000000) == 0x02000000);
      }
      /**
       * <code>optional bytes certIntactFlag = 90;</code>
       */
      public com.google.protobuf.ByteString getCertIntactFlag() {
        return certIntactFlag_;
      }
      /**
       * <code>optional bytes certIntactFlag = 90;</code>
       */
      public Builder setCertIntactFlag(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x02000000;
        certIntactFlag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes certIntactFlag = 90;</code>
       */
      public Builder clearCertIntactFlag() {
        bitField2_ = (bitField2_ & ~0x02000000);
        certIntactFlag_ = getDefaultInstance().getCertIntactFlag();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString certPath_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes certPath = 91;</code>
       */
      public boolean hasCertPath() {
        return ((bitField2_ & 0x04000000) == 0x04000000);
      }
      /**
       * <code>optional bytes certPath = 91;</code>
       */
      public com.google.protobuf.ByteString getCertPath() {
        return certPath_;
      }
      /**
       * <code>optional bytes certPath = 91;</code>
       */
      public Builder setCertPath(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x04000000;
        certPath_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes certPath = 91;</code>
       */
      public Builder clearCertPath() {
        bitField2_ = (bitField2_ & ~0x04000000);
        certPath_ = getDefaultInstance().getCertPath();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString sessSecFlag_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes sessSecFlag = 92;</code>
       */
      public boolean hasSessSecFlag() {
        return ((bitField2_ & 0x08000000) == 0x08000000);
      }
      /**
       * <code>optional bytes sessSecFlag = 92;</code>
       */
      public com.google.protobuf.ByteString getSessSecFlag() {
        return sessSecFlag_;
      }
      /**
       * <code>optional bytes sessSecFlag = 92;</code>
       */
      public Builder setSessSecFlag(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x08000000;
        sessSecFlag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes sessSecFlag = 92;</code>
       */
      public Builder clearSessSecFlag() {
        bitField2_ = (bitField2_ & ~0x08000000);
        sessSecFlag_ = getDefaultInstance().getSessSecFlag();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString fullText_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes fullText = 93;</code>
       */
      public boolean hasFullText() {
        return ((bitField2_ & 0x10000000) == 0x10000000);
      }
      /**
       * <code>optional bytes fullText = 93;</code>
       */
      public com.google.protobuf.ByteString getFullText() {
        return fullText_;
      }
      /**
       * <code>optional bytes fullText = 93;</code>
       */
      public Builder setFullText(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x10000000;
        fullText_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes fullText = 93;</code>
       */
      public Builder clearFullText() {
        bitField2_ = (bitField2_ & ~0x10000000);
        fullText_ = getDefaultInstance().getFullText();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliCertHashes_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliCertHashes = 94;</code>
       */
      public boolean hasCliCertHashes() {
        return ((bitField2_ & 0x20000000) == 0x20000000);
      }
      /**
       * <code>optional bytes cliCertHashes = 94;</code>
       */
      public com.google.protobuf.ByteString getCliCertHashes() {
        return cliCertHashes_;
      }
      /**
       * <code>optional bytes cliCertHashes = 94;</code>
       */
      public Builder setCliCertHashes(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x20000000;
        cliCertHashes_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliCertHashes = 94;</code>
       */
      public Builder clearCliCertHashes() {
        bitField2_ = (bitField2_ & ~0x20000000);
        cliCertHashes_ = getDefaultInstance().getCliCertHashes();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvCertHashes_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvCertHashes = 95;</code>
       */
      public boolean hasSrvCertHashes() {
        return ((bitField2_ & 0x40000000) == 0x40000000);
      }
      /**
       * <code>optional bytes srvCertHashes = 95;</code>
       */
      public com.google.protobuf.ByteString getSrvCertHashes() {
        return srvCertHashes_;
      }
      /**
       * <code>optional bytes srvCertHashes = 95;</code>
       */
      public Builder setSrvCertHashes(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x40000000;
        srvCertHashes_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvCertHashes = 95;</code>
       */
      public Builder clearSrvCertHashes() {
        bitField2_ = (bitField2_ & ~0x40000000);
        srvCertHashes_ = getDefaultInstance().getSrvCertHashes();
        onChanged();
        return this;
      }

      private int cliCertNum_ ;
      /**
       * <code>optional uint32 cliCertNum = 96;</code>
       */
      public boolean hasCliCertNum() {
        return ((bitField2_ & 0x80000000) == 0x80000000);
      }
      /**
       * <code>optional uint32 cliCertNum = 96;</code>
       */
      public int getCliCertNum() {
        return cliCertNum_;
      }
      /**
       * <code>optional uint32 cliCertNum = 96;</code>
       */
      public Builder setCliCertNum(int value) {
        bitField2_ |= 0x80000000;
        cliCertNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cliCertNum = 96;</code>
       */
      public Builder clearCliCertNum() {
        bitField2_ = (bitField2_ & ~0x80000000);
        cliCertNum_ = 0;
        onChanged();
        return this;
      }

      private int srvCertNum_ ;
      /**
       * <code>optional uint32 srvCertNum = 97;</code>
       */
      public boolean hasSrvCertNum() {
        return ((bitField3_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional uint32 srvCertNum = 97;</code>
       */
      public int getSrvCertNum() {
        return srvCertNum_;
      }
      /**
       * <code>optional uint32 srvCertNum = 97;</code>
       */
      public Builder setSrvCertNum(int value) {
        bitField3_ |= 0x00000001;
        srvCertNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvCertNum = 97;</code>
       */
      public Builder clearSrvCertNum() {
        bitField3_ = (bitField3_ & ~0x00000001);
        srvCertNum_ = 0;
        onChanged();
        return this;
      }

      private int certExist_ ;
      /**
       * <code>optional uint32 certExist = 98;</code>
       */
      public boolean hasCertExist() {
        return ((bitField3_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional uint32 certExist = 98;</code>
       */
      public int getCertExist() {
        return certExist_;
      }
      /**
       * <code>optional uint32 certExist = 98;</code>
       */
      public Builder setCertExist(int value) {
        bitField3_ |= 0x00000002;
        certExist_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 certExist = 98;</code>
       */
      public Builder clearCertExist() {
        bitField3_ = (bitField3_ & ~0x00000002);
        certExist_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString extendEcGroupsClient_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes extendEcGroupsClient = 99;</code>
       */
      public boolean hasExtendEcGroupsClient() {
        return ((bitField3_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional bytes extendEcGroupsClient = 99;</code>
       */
      public com.google.protobuf.ByteString getExtendEcGroupsClient() {
        return extendEcGroupsClient_;
      }
      /**
       * <code>optional bytes extendEcGroupsClient = 99;</code>
       */
      public Builder setExtendEcGroupsClient(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField3_ |= 0x00000004;
        extendEcGroupsClient_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes extendEcGroupsClient = 99;</code>
       */
      public Builder clearExtendEcGroupsClient() {
        bitField3_ = (bitField3_ & ~0x00000004);
        extendEcGroupsClient_ = getDefaultInstance().getExtendEcGroupsClient();
        onChanged();
        return this;
      }

      private int leafCertDaysRemaining_ ;
      /**
       * <code>optional uint32 leafCertDaysRemaining = 100;</code>
       */
      public boolean hasLeafCertDaysRemaining() {
        return ((bitField3_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional uint32 leafCertDaysRemaining = 100;</code>
       */
      public int getLeafCertDaysRemaining() {
        return leafCertDaysRemaining_;
      }
      /**
       * <code>optional uint32 leafCertDaysRemaining = 100;</code>
       */
      public Builder setLeafCertDaysRemaining(int value) {
        bitField3_ |= 0x00000008;
        leafCertDaysRemaining_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 leafCertDaysRemaining = 100;</code>
       */
      public Builder clearLeafCertDaysRemaining() {
        bitField3_ = (bitField3_ & ~0x00000008);
        leafCertDaysRemaining_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Ssl_TlsInfo)
    }

    // @@protoc_insertion_point(class_scope:Ssl_TlsInfo)
    private static final SslTlsInfo.Ssl_TlsInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new SslTlsInfo.Ssl_TlsInfo();
    }

    public static SslTlsInfo.Ssl_TlsInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Ssl_TlsInfo>
        PARSER = new com.google.protobuf.AbstractParser<Ssl_TlsInfo>() {
      @java.lang.Override
      public Ssl_TlsInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Ssl_TlsInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Ssl_TlsInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Ssl_TlsInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public SslTlsInfo.Ssl_TlsInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Ssl_TlsInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Ssl_TlsInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\021Ssl_TlsInfo.proto\"\347\017\n\013Ssl_TlsInfo\022\017\n\007c" +
      "onType\030\001 \001(\r\022\016\n\006aleLev\030\002 \001(\r\022\016\n\006aleDes\030\003" +
      " \001(\r\022\023\n\013handShaType\030\004 \001(\r\022\016\n\006cliVer\030\005 \001(" +
      "\r\022\025\n\rcliGMTUniTime\030\006 \001(\004\022\017\n\007cliRand\030\007 \001(" +
      "\014\022\020\n\010cliSesID\030\010 \001(\014\022\021\n\tcliCipSui\030\t \001(\014\022\021" +
      "\n\tcliComMet\030\n \001(\014\022\016\n\006srvVer\030\013 \001(\r\022\017\n\007srv" +
      "Name\030\014 \001(\014\022\023\n\013srvNameAttr\030\r \001(\r\022\025\n\rsrvGM" +
      "TUniTime\030\016 \001(\004\022\017\n\007srvRand\030\017 \001(\014\022\020\n\010srvSe" +
      "sID\030\020 \001(\014\022\023\n\013srvComprMet\030\021 \001(\014\022\022\n\nsrvCer" +
      "tLen\030\022 \001(\r\022\023\n\013certResType\030\023 \001(\r\022\022\n\ncliCe" +
      "rtLen\030\024 \001(\r\022\031\n\021RSAModOfSrvKeyExc\030\025 \001(\014\022\031" +
      "\n\021RSAExpOfSrvKeyExc\030\026 \001(\004\022\030\n\020DHModOfSrvK" +
      "eyExc\030\027 \001(\014\022\030\n\020DHGenOfSrvKeyExc\030\030 \001(\014\022\023\n" +
      "\013srvDHPubKey\030\031 \001(\014\022\033\n\023preMasKeyEncryByRS" +
      "A\030\032 \001(\014\022\023\n\013cliDHPubKey\030\033 \001(\014\022\024\n\014extTypeI" +
      "nSSL\030\034 \001(\r\022\027\n\017cliEllCurPoiFor\030\035 \001(\r\022\021\n\tc" +
      "liEllCur\030\036 \001(\r\022\027\n\017srvEllCurPoiFor\030\037 \001(\r\022" +
      "\021\n\tsrvEllCur\030  \001(\r\022\031\n\021srvEllCurDHPubKey\030" +
      "! \001(\014\022\031\n\021cliEllCurDHPubKey\030\" \001(\014\022\026\n\016srvG" +
      "MTUni_Time\030# \001(\004\022\021\n\tcliExtCnt\030$ \001(\r\022\021\n\ts" +
      "rvExtCnt\030% \001(\r\022\024\n\014cliHandSkLen\030& \001(\r\022\024\n\014" +
      "srvHandSkLen\030\' \001(\r\022\016\n\006cliExt\030( \001(\014\022\016\n\006sr" +
      "vExt\030) \001(\014\022\024\n\014cliExtGrease\030* \001(\r\022\016\n\006cliJ" +
      "A3\030+ \001(\014\022\016\n\006srvJA3\030, \001(\014\022\025\n\rcliSessTicke" +
      "t\030- \001(\014\022\025\n\rsrvSessTicket\030. \001(\014\022\017\n\007AuthTa" +
      "g\030/ \001(\r\022\022\n\ncliCertCnt\0300 \001(\r\022\022\n\nsrvCertCn" +
      "t\0301 \001(\r\022\023\n\013ecGroupsCli\0302 \001(\014\022\026\n\016ecPoiFor" +
      "ByServ\0303 \001(\014\022\r\n\005etags\0304 \001(\014\022\r\n\005ttags\0305 \001" +
      "(\014\022\023\n\013cliSesIDLen\0306 \001(\014\022\023\n\013srvSesIDLen\0307" +
      " \001(\014\022\024\n\014srvKeyExcLen\0308 \001(\014\022\023\n\013ECDHCurTyp" +
      "e\0309 \001(\014\022\017\n\007ECDHSig\030: \001(\014\022\017\n\007DHEPLen\030; \001(" +
      "\014\022\017\n\007DHEGLen\030< \001(\014\022\024\n\014cliKeyExcLen\030= \001(\014" +
      "\022\021\n\tencPubKey\030> \001(\014\022\024\n\014encPubKeyLen\030? \001(" +
      "\014\022\021\n\tcliExtLen\030@ \001(\014\022\021\n\tsrvExtLen\030A \001(\014\022" +
      "\025\n\rECDHPubKeyLen\030B \001(\014\022\017\n\007namType\030C \001(\014\022" +
      "\016\n\006namLen\030D \001(\014\022\016\n\006ticDat\030E \001(\014\022\021\n\tsrvCi" +
      "pSui\030F \001(\014\022\021\n\tcipSuiNum\030G \001(\r\022\023\n\013ECDHSig" +
      "Hash\030H \001(\014\022\022\n\nDHESigHash\030I \001(\014\022\022\n\nRSASig" +
      "Hash\030J \001(\014\022\022\n\ngreaseFlag\030K \001(\014\022\021\n\tRSAMod" +
      "Len\030L \001(\014\022\021\n\tRSAExpLen\030M \001(\014\022\016\n\006RSASig\030N" +
      " \001(\014\022\016\n\006DHESig\030O \001(\014\022\024\n\014DHEPubKeyLen\030P \001" +
      "(\014\022\021\n\tDHEPubKey\030Q \001(\014\022\022\n\nSigAlgType\030R \001(" +
      "\014\022\016\n\006sigAlg\030S \001(\014\022\022\n\nSigHashAlg\030T \001(\014\022\013\n" +
      "\003JOY\030U \001(\014\022\014\n\004JOYS\030V \001(\014\022\020\n\010STARTTLS\030W \001" +
      "(\014\022\023\n\013certNonFlag\030X \001(\014\022\r\n\005JoyFp\030Y \001(\014\022\026" +
      "\n\016certIntactFlag\030Z \001(\014\022\020\n\010certPath\030[ \001(\014" +
      "\022\023\n\013sessSecFlag\030\\ \001(\014\022\020\n\010fullText\030] \001(\014\022" +
      "\025\n\rcliCertHashes\030^ \001(\014\022\025\n\rsrvCertHashes\030" +
      "_ \001(\014\022\022\n\ncliCertNum\030` \001(\r\022\022\n\nsrvCertNum\030" +
      "a \001(\r\022\021\n\tcertExist\030b \001(\r\022\034\n\024extendEcGrou" +
      "psClient\030c \001(\014\022\035\n\025leafCertDaysRemaining\030" +
      "d \001(\r"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_Ssl_TlsInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Ssl_TlsInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Ssl_TlsInfo_descriptor,
        new java.lang.String[] { "ConType", "AleLev", "AleDes", "HandShaType", "CliVer", "CliGMTUniTime", "CliRand", "CliSesID", "CliCipSui", "CliComMet", "SrvVer", "SrvName", "SrvNameAttr", "SrvGMTUniTime14", "SrvRand", "SrvSesID", "SrvComprMet", "SrvCertLen", "CertResType", "CliCertLen", "RSAModOfSrvKeyExc", "RSAExpOfSrvKeyExc", "DHModOfSrvKeyExc", "DHGenOfSrvKeyExc", "SrvDHPubKey", "PreMasKeyEncryByRSA", "CliDHPubKey", "ExtTypeInSSL", "CliEllCurPoiFor", "CliEllCur", "SrvEllCurPoiFor", "SrvEllCur", "SrvEllCurDHPubKey", "CliEllCurDHPubKey", "SrvGMTUniTime35", "CliExtCnt", "SrvExtCnt", "CliHandSkLen", "SrvHandSkLen", "CliExt", "SrvExt", "CliExtGrease", "CliJA3", "SrvJA3", "CliSessTicket", "SrvSessTicket", "AuthTag", "CliCertCnt", "SrvCertCnt", "EcGroupsCli", "EcPoiForByServ", "Etags", "Ttags", "CliSesIDLen", "SrvSesIDLen", "SrvKeyExcLen", "ECDHCurType", "ECDHSig", "DHEPLen", "DHEGLen", "CliKeyExcLen", "EncPubKey", "EncPubKeyLen", "CliExtLen", "SrvExtLen", "ECDHPubKeyLen", "NamType", "NamLen", "TicDat", "SrvCipSui", "CipSuiNum", "ECDHSigHash", "DHESigHash", "RSASigHash", "GreaseFlag", "RSAModLen", "RSAExpLen", "RSASig", "DHESig", "DHEPubKeyLen", "DHEPubKey", "SigAlgType", "SigAlg", "SigHashAlg", "JOY", "JOYS", "STARTTLS", "CertNonFlag", "JoyFp", "CertIntactFlag", "CertPath", "SessSecFlag", "FullText", "CliCertHashes", "SrvCertHashes", "CliCertNum", "SrvCertNum", "CertExist", "ExtendEcGroupsClient", "LeafCertDaysRemaining", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
