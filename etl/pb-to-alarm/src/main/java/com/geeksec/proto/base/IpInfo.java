// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: IP_INFO.proto
package com.geeksec.proto.base;

public final class IpInfo {
  private IpInfo() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface IP_INFOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:IP_INFO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * IP地址
     * </pre>
     *
     * <code>required string ip = 1;</code>
     */
    boolean hasIp();
    /**
     * <pre>
     * IP地址
     * </pre>
     *
     * <code>required string ip = 1;</code>
     */
    java.lang.String getIp();
    /**
     * <pre>
     * IP地址
     * </pre>
     *
     * <code>required string ip = 1;</code>
     */
    com.google.protobuf.ByteString
        getIpBytes();

    /**
     * <pre>
     * 端口
     * </pre>
     *
     * <code>required uint32 port = 2;</code>
     */
    boolean hasPort();
    /**
     * <pre>
     * 端口
     * </pre>
     *
     * <code>required uint32 port = 2;</code>
     */
    int getPort();

    /**
     * <pre>
     * 国家名
     * </pre>
     *
     * <code>required string ip_country = 3;</code>
     */
    boolean hasIpCountry();
    /**
     * <pre>
     * 国家名
     * </pre>
     *
     * <code>required string ip_country = 3;</code>
     */
    java.lang.String getIpCountry();
    /**
     * <pre>
     * 国家名
     * </pre>
     *
     * <code>required string ip_country = 3;</code>
     */
    com.google.protobuf.ByteString
        getIpCountryBytes();

    /**
     * <pre>
     * 省份/洲名
     * </pre>
     *
     * <code>required string ip_stat = 4;</code>
     */
    boolean hasIpStat();
    /**
     * <pre>
     * 省份/洲名
     * </pre>
     *
     * <code>required string ip_stat = 4;</code>
     */
    java.lang.String getIpStat();
    /**
     * <pre>
     * 省份/洲名
     * </pre>
     *
     * <code>required string ip_stat = 4;</code>
     */
    com.google.protobuf.ByteString
        getIpStatBytes();

    /**
     * <pre>
     * 城市名
     * </pre>
     *
     * <code>required string ip_city = 5;</code>
     */
    boolean hasIpCity();
    /**
     * <pre>
     * 城市名
     * </pre>
     *
     * <code>required string ip_city = 5;</code>
     */
    java.lang.String getIpCity();
    /**
     * <pre>
     * 城市名
     * </pre>
     *
     * <code>required string ip_city = 5;</code>
     */
    com.google.protobuf.ByteString
        getIpCityBytes();

    /**
     * <pre>
     * 机构名
     * </pre>
     *
     * <code>required string ip_org = 6;</code>
     */
    boolean hasIpOrg();
    /**
     * <pre>
     * 机构名
     * </pre>
     *
     * <code>required string ip_org = 6;</code>
     */
    java.lang.String getIpOrg();
    /**
     * <pre>
     * 机构名
     * </pre>
     *
     * <code>required string ip_org = 6;</code>
     */
    com.google.protobuf.ByteString
        getIpOrgBytes();

    /**
     * <pre>
     * 纬度
     * </pre>
     *
     * <code>required double ip_longitude = 7;</code>
     */
    boolean hasIpLongitude();
    /**
     * <pre>
     * 纬度
     * </pre>
     *
     * <code>required double ip_longitude = 7;</code>
     */
    double getIpLongitude();

    /**
     * <pre>
     * 经度
     * </pre>
     *
     * <code>required double ip_latitude = 8;</code>
     */
    boolean hasIpLatitude();
    /**
     * <pre>
     * 经度
     * </pre>
     *
     * <code>required double ip_latitude = 8;</code>
     */
    double getIpLatitude();

    /**
     * <pre>
     * 运营商
     * </pre>
     *
     * <code>required string ip_isp = 9;</code>
     */
    boolean hasIpIsp();
    /**
     * <pre>
     * 运营商
     * </pre>
     *
     * <code>required string ip_isp = 9;</code>
     */
    java.lang.String getIpIsp();
    /**
     * <pre>
     * 运营商
     * </pre>
     *
     * <code>required string ip_isp = 9;</code>
     */
    com.google.protobuf.ByteString
        getIpIspBytes();

    /**
     * <pre>
     * AS信息
     * </pre>
     *
     * <code>required string ip_asn = 10;</code>
     */
    boolean hasIpAsn();
    /**
     * <pre>
     * AS信息
     * </pre>
     *
     * <code>required string ip_asn = 10;</code>
     */
    java.lang.String getIpAsn();
    /**
     * <pre>
     * AS信息
     * </pre>
     *
     * <code>required string ip_asn = 10;</code>
     */
    com.google.protobuf.ByteString
        getIpAsnBytes();

    /**
     * <pre>
     * 标签
     * </pre>
     *
     * <code>optional string ip_tag = 11;</code>
     */
    boolean hasIpTag();
    /**
     * <pre>
     * 标签
     * </pre>
     *
     * <code>optional string ip_tag = 11;</code>
     */
    java.lang.String getIpTag();
    /**
     * <pre>
     * 标签
     * </pre>
     *
     * <code>optional string ip_tag = 11;</code>
     */
    com.google.protobuf.ByteString
        getIpTagBytes();
  }
  /**
   * Protobuf type {@code IP_INFO}
   */
  public  static final class IP_INFO extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:IP_INFO)
      IP_INFOOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use IP_INFO.newBuilder() to construct.
    private IP_INFO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private IP_INFO() {
      ip_ = "";
      port_ = 0;
      ipCountry_ = "";
      ipStat_ = "";
      ipCity_ = "";
      ipOrg_ = "";
      ipLongitude_ = 0D;
      ipLatitude_ = 0D;
      ipIsp_ = "";
      ipAsn_ = "";
      ipTag_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private IP_INFO(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              ip_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              port_ = input.readUInt32();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              ipCountry_ = bs;
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              ipStat_ = bs;
              break;
            }
            case 42: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000010;
              ipCity_ = bs;
              break;
            }
            case 50: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000020;
              ipOrg_ = bs;
              break;
            }
            case 57: {
              bitField0_ |= 0x00000040;
              ipLongitude_ = input.readDouble();
              break;
            }
            case 65: {
              bitField0_ |= 0x00000080;
              ipLatitude_ = input.readDouble();
              break;
            }
            case 74: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000100;
              ipIsp_ = bs;
              break;
            }
            case 82: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000200;
              ipAsn_ = bs;
              break;
            }
            case 90: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000400;
              ipTag_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return IpInfo.internal_static_IP_INFO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return IpInfo.internal_static_IP_INFO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              IpInfo.IP_INFO.class, IpInfo.IP_INFO.Builder.class);
    }

    private int bitField0_;
    public static final int IP_FIELD_NUMBER = 1;
    private volatile java.lang.Object ip_;
    /**
     * <pre>
     * IP地址
     * </pre>
     *
     * <code>required string ip = 1;</code>
     */
    public boolean hasIp() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <pre>
     * IP地址
     * </pre>
     *
     * <code>required string ip = 1;</code>
     */
    public java.lang.String getIp() {
      java.lang.Object ref = ip_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ip_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * IP地址
     * </pre>
     *
     * <code>required string ip = 1;</code>
     */
    public com.google.protobuf.ByteString
        getIpBytes() {
      java.lang.Object ref = ip_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ip_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PORT_FIELD_NUMBER = 2;
    private int port_;
    /**
     * <pre>
     * 端口
     * </pre>
     *
     * <code>required uint32 port = 2;</code>
     */
    public boolean hasPort() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <pre>
     * 端口
     * </pre>
     *
     * <code>required uint32 port = 2;</code>
     */
    public int getPort() {
      return port_;
    }

    public static final int IP_COUNTRY_FIELD_NUMBER = 3;
    private volatile java.lang.Object ipCountry_;
    /**
     * <pre>
     * 国家名
     * </pre>
     *
     * <code>required string ip_country = 3;</code>
     */
    public boolean hasIpCountry() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <pre>
     * 国家名
     * </pre>
     *
     * <code>required string ip_country = 3;</code>
     */
    public java.lang.String getIpCountry() {
      java.lang.Object ref = ipCountry_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ipCountry_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 国家名
     * </pre>
     *
     * <code>required string ip_country = 3;</code>
     */
    public com.google.protobuf.ByteString
        getIpCountryBytes() {
      java.lang.Object ref = ipCountry_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ipCountry_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IP_STAT_FIELD_NUMBER = 4;
    private volatile java.lang.Object ipStat_;
    /**
     * <pre>
     * 省份/洲名
     * </pre>
     *
     * <code>required string ip_stat = 4;</code>
     */
    public boolean hasIpStat() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <pre>
     * 省份/洲名
     * </pre>
     *
     * <code>required string ip_stat = 4;</code>
     */
    public java.lang.String getIpStat() {
      java.lang.Object ref = ipStat_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ipStat_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 省份/洲名
     * </pre>
     *
     * <code>required string ip_stat = 4;</code>
     */
    public com.google.protobuf.ByteString
        getIpStatBytes() {
      java.lang.Object ref = ipStat_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ipStat_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IP_CITY_FIELD_NUMBER = 5;
    private volatile java.lang.Object ipCity_;
    /**
     * <pre>
     * 城市名
     * </pre>
     *
     * <code>required string ip_city = 5;</code>
     */
    public boolean hasIpCity() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <pre>
     * 城市名
     * </pre>
     *
     * <code>required string ip_city = 5;</code>
     */
    public java.lang.String getIpCity() {
      java.lang.Object ref = ipCity_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ipCity_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 城市名
     * </pre>
     *
     * <code>required string ip_city = 5;</code>
     */
    public com.google.protobuf.ByteString
        getIpCityBytes() {
      java.lang.Object ref = ipCity_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ipCity_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IP_ORG_FIELD_NUMBER = 6;
    private volatile java.lang.Object ipOrg_;
    /**
     * <pre>
     * 机构名
     * </pre>
     *
     * <code>required string ip_org = 6;</code>
     */
    public boolean hasIpOrg() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <pre>
     * 机构名
     * </pre>
     *
     * <code>required string ip_org = 6;</code>
     */
    public java.lang.String getIpOrg() {
      java.lang.Object ref = ipOrg_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ipOrg_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 机构名
     * </pre>
     *
     * <code>required string ip_org = 6;</code>
     */
    public com.google.protobuf.ByteString
        getIpOrgBytes() {
      java.lang.Object ref = ipOrg_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ipOrg_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IP_LONGITUDE_FIELD_NUMBER = 7;
    private double ipLongitude_;
    /**
     * <pre>
     * 纬度
     * </pre>
     *
     * <code>required double ip_longitude = 7;</code>
     */
    public boolean hasIpLongitude() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <pre>
     * 纬度
     * </pre>
     *
     * <code>required double ip_longitude = 7;</code>
     */
    public double getIpLongitude() {
      return ipLongitude_;
    }

    public static final int IP_LATITUDE_FIELD_NUMBER = 8;
    private double ipLatitude_;
    /**
     * <pre>
     * 经度
     * </pre>
     *
     * <code>required double ip_latitude = 8;</code>
     */
    public boolean hasIpLatitude() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <pre>
     * 经度
     * </pre>
     *
     * <code>required double ip_latitude = 8;</code>
     */
    public double getIpLatitude() {
      return ipLatitude_;
    }

    public static final int IP_ISP_FIELD_NUMBER = 9;
    private volatile java.lang.Object ipIsp_;
    /**
     * <pre>
     * 运营商
     * </pre>
     *
     * <code>required string ip_isp = 9;</code>
     */
    public boolean hasIpIsp() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <pre>
     * 运营商
     * </pre>
     *
     * <code>required string ip_isp = 9;</code>
     */
    public java.lang.String getIpIsp() {
      java.lang.Object ref = ipIsp_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ipIsp_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 运营商
     * </pre>
     *
     * <code>required string ip_isp = 9;</code>
     */
    public com.google.protobuf.ByteString
        getIpIspBytes() {
      java.lang.Object ref = ipIsp_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ipIsp_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IP_ASN_FIELD_NUMBER = 10;
    private volatile java.lang.Object ipAsn_;
    /**
     * <pre>
     * AS信息
     * </pre>
     *
     * <code>required string ip_asn = 10;</code>
     */
    public boolean hasIpAsn() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <pre>
     * AS信息
     * </pre>
     *
     * <code>required string ip_asn = 10;</code>
     */
    public java.lang.String getIpAsn() {
      java.lang.Object ref = ipAsn_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ipAsn_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * AS信息
     * </pre>
     *
     * <code>required string ip_asn = 10;</code>
     */
    public com.google.protobuf.ByteString
        getIpAsnBytes() {
      java.lang.Object ref = ipAsn_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ipAsn_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IP_TAG_FIELD_NUMBER = 11;
    private volatile java.lang.Object ipTag_;
    /**
     * <pre>
     * 标签
     * </pre>
     *
     * <code>optional string ip_tag = 11;</code>
     */
    public boolean hasIpTag() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <pre>
     * 标签
     * </pre>
     *
     * <code>optional string ip_tag = 11;</code>
     */
    public java.lang.String getIpTag() {
      java.lang.Object ref = ipTag_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ipTag_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 标签
     * </pre>
     *
     * <code>optional string ip_tag = 11;</code>
     */
    public com.google.protobuf.ByteString
        getIpTagBytes() {
      java.lang.Object ref = ipTag_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ipTag_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasIp()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasPort()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIpCountry()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIpStat()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIpCity()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIpOrg()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIpLongitude()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIpLatitude()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIpIsp()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIpAsn()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, ip_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeUInt32(2, port_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, ipCountry_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, ipStat_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, ipCity_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, ipOrg_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeDouble(7, ipLongitude_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeDouble(8, ipLatitude_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, ipIsp_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 10, ipAsn_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 11, ipTag_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, ip_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, port_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, ipCountry_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, ipStat_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, ipCity_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, ipOrg_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(7, ipLongitude_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(8, ipLatitude_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, ipIsp_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, ipAsn_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, ipTag_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof IpInfo.IP_INFO)) {
        return super.equals(obj);
      }
      IpInfo.IP_INFO other = (IpInfo.IP_INFO) obj;

      boolean result = true;
      result = result && (hasIp() == other.hasIp());
      if (hasIp()) {
        result = result && getIp()
            .equals(other.getIp());
      }
      result = result && (hasPort() == other.hasPort());
      if (hasPort()) {
        result = result && (getPort()
            == other.getPort());
      }
      result = result && (hasIpCountry() == other.hasIpCountry());
      if (hasIpCountry()) {
        result = result && getIpCountry()
            .equals(other.getIpCountry());
      }
      result = result && (hasIpStat() == other.hasIpStat());
      if (hasIpStat()) {
        result = result && getIpStat()
            .equals(other.getIpStat());
      }
      result = result && (hasIpCity() == other.hasIpCity());
      if (hasIpCity()) {
        result = result && getIpCity()
            .equals(other.getIpCity());
      }
      result = result && (hasIpOrg() == other.hasIpOrg());
      if (hasIpOrg()) {
        result = result && getIpOrg()
            .equals(other.getIpOrg());
      }
      result = result && (hasIpLongitude() == other.hasIpLongitude());
      if (hasIpLongitude()) {
        result = result && (
            java.lang.Double.doubleToLongBits(getIpLongitude())
            == java.lang.Double.doubleToLongBits(
                other.getIpLongitude()));
      }
      result = result && (hasIpLatitude() == other.hasIpLatitude());
      if (hasIpLatitude()) {
        result = result && (
            java.lang.Double.doubleToLongBits(getIpLatitude())
            == java.lang.Double.doubleToLongBits(
                other.getIpLatitude()));
      }
      result = result && (hasIpIsp() == other.hasIpIsp());
      if (hasIpIsp()) {
        result = result && getIpIsp()
            .equals(other.getIpIsp());
      }
      result = result && (hasIpAsn() == other.hasIpAsn());
      if (hasIpAsn()) {
        result = result && getIpAsn()
            .equals(other.getIpAsn());
      }
      result = result && (hasIpTag() == other.hasIpTag());
      if (hasIpTag()) {
        result = result && getIpTag()
            .equals(other.getIpTag());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasIp()) {
        hash = (37 * hash) + IP_FIELD_NUMBER;
        hash = (53 * hash) + getIp().hashCode();
      }
      if (hasPort()) {
        hash = (37 * hash) + PORT_FIELD_NUMBER;
        hash = (53 * hash) + getPort();
      }
      if (hasIpCountry()) {
        hash = (37 * hash) + IP_COUNTRY_FIELD_NUMBER;
        hash = (53 * hash) + getIpCountry().hashCode();
      }
      if (hasIpStat()) {
        hash = (37 * hash) + IP_STAT_FIELD_NUMBER;
        hash = (53 * hash) + getIpStat().hashCode();
      }
      if (hasIpCity()) {
        hash = (37 * hash) + IP_CITY_FIELD_NUMBER;
        hash = (53 * hash) + getIpCity().hashCode();
      }
      if (hasIpOrg()) {
        hash = (37 * hash) + IP_ORG_FIELD_NUMBER;
        hash = (53 * hash) + getIpOrg().hashCode();
      }
      if (hasIpLongitude()) {
        hash = (37 * hash) + IP_LONGITUDE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            java.lang.Double.doubleToLongBits(getIpLongitude()));
      }
      if (hasIpLatitude()) {
        hash = (37 * hash) + IP_LATITUDE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            java.lang.Double.doubleToLongBits(getIpLatitude()));
      }
      if (hasIpIsp()) {
        hash = (37 * hash) + IP_ISP_FIELD_NUMBER;
        hash = (53 * hash) + getIpIsp().hashCode();
      }
      if (hasIpAsn()) {
        hash = (37 * hash) + IP_ASN_FIELD_NUMBER;
        hash = (53 * hash) + getIpAsn().hashCode();
      }
      if (hasIpTag()) {
        hash = (37 * hash) + IP_TAG_FIELD_NUMBER;
        hash = (53 * hash) + getIpTag().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static IpInfo.IP_INFO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IpInfo.IP_INFO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IpInfo.IP_INFO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IpInfo.IP_INFO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IpInfo.IP_INFO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IpInfo.IP_INFO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IpInfo.IP_INFO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static IpInfo.IP_INFO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static IpInfo.IP_INFO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static IpInfo.IP_INFO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static IpInfo.IP_INFO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static IpInfo.IP_INFO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(IpInfo.IP_INFO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code IP_INFO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:IP_INFO)
        IpInfo.IP_INFOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return IpInfo.internal_static_IP_INFO_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return IpInfo.internal_static_IP_INFO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                IpInfo.IP_INFO.class, IpInfo.IP_INFO.Builder.class);
      }

      // Construct using IpInfo.IP_INFO.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        ip_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        port_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        ipCountry_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        ipStat_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        ipCity_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        ipOrg_ = "";
        bitField0_ = (bitField0_ & ~0x00000020);
        ipLongitude_ = 0D;
        bitField0_ = (bitField0_ & ~0x00000040);
        ipLatitude_ = 0D;
        bitField0_ = (bitField0_ & ~0x00000080);
        ipIsp_ = "";
        bitField0_ = (bitField0_ & ~0x00000100);
        ipAsn_ = "";
        bitField0_ = (bitField0_ & ~0x00000200);
        ipTag_ = "";
        bitField0_ = (bitField0_ & ~0x00000400);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return IpInfo.internal_static_IP_INFO_descriptor;
      }

      @java.lang.Override
      public IpInfo.IP_INFO getDefaultInstanceForType() {
        return IpInfo.IP_INFO.getDefaultInstance();
      }

      @java.lang.Override
      public IpInfo.IP_INFO build() {
        IpInfo.IP_INFO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public IpInfo.IP_INFO buildPartial() {
        IpInfo.IP_INFO result = new IpInfo.IP_INFO(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.ip_ = ip_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.port_ = port_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.ipCountry_ = ipCountry_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.ipStat_ = ipStat_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.ipCity_ = ipCity_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.ipOrg_ = ipOrg_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.ipLongitude_ = ipLongitude_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.ipLatitude_ = ipLatitude_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.ipIsp_ = ipIsp_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000200;
        }
        result.ipAsn_ = ipAsn_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000400;
        }
        result.ipTag_ = ipTag_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof IpInfo.IP_INFO) {
          return mergeFrom((IpInfo.IP_INFO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(IpInfo.IP_INFO other) {
        if (other == IpInfo.IP_INFO.getDefaultInstance()) return this;
        if (other.hasIp()) {
          bitField0_ |= 0x00000001;
          ip_ = other.ip_;
          onChanged();
        }
        if (other.hasPort()) {
          setPort(other.getPort());
        }
        if (other.hasIpCountry()) {
          bitField0_ |= 0x00000004;
          ipCountry_ = other.ipCountry_;
          onChanged();
        }
        if (other.hasIpStat()) {
          bitField0_ |= 0x00000008;
          ipStat_ = other.ipStat_;
          onChanged();
        }
        if (other.hasIpCity()) {
          bitField0_ |= 0x00000010;
          ipCity_ = other.ipCity_;
          onChanged();
        }
        if (other.hasIpOrg()) {
          bitField0_ |= 0x00000020;
          ipOrg_ = other.ipOrg_;
          onChanged();
        }
        if (other.hasIpLongitude()) {
          setIpLongitude(other.getIpLongitude());
        }
        if (other.hasIpLatitude()) {
          setIpLatitude(other.getIpLatitude());
        }
        if (other.hasIpIsp()) {
          bitField0_ |= 0x00000100;
          ipIsp_ = other.ipIsp_;
          onChanged();
        }
        if (other.hasIpAsn()) {
          bitField0_ |= 0x00000200;
          ipAsn_ = other.ipAsn_;
          onChanged();
        }
        if (other.hasIpTag()) {
          bitField0_ |= 0x00000400;
          ipTag_ = other.ipTag_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        if (!hasIp()) {
          return false;
        }
        if (!hasPort()) {
          return false;
        }
        if (!hasIpCountry()) {
          return false;
        }
        if (!hasIpStat()) {
          return false;
        }
        if (!hasIpCity()) {
          return false;
        }
        if (!hasIpOrg()) {
          return false;
        }
        if (!hasIpLongitude()) {
          return false;
        }
        if (!hasIpLatitude()) {
          return false;
        }
        if (!hasIpIsp()) {
          return false;
        }
        if (!hasIpAsn()) {
          return false;
        }
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        IpInfo.IP_INFO parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (IpInfo.IP_INFO) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object ip_ = "";
      /**
       * <pre>
       * IP地址
       * </pre>
       *
       * <code>required string ip = 1;</code>
       */
      public boolean hasIp() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <pre>
       * IP地址
       * </pre>
       *
       * <code>required string ip = 1;</code>
       */
      public java.lang.String getIp() {
        java.lang.Object ref = ip_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ip_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * IP地址
       * </pre>
       *
       * <code>required string ip = 1;</code>
       */
      public com.google.protobuf.ByteString
          getIpBytes() {
        java.lang.Object ref = ip_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ip_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * IP地址
       * </pre>
       *
       * <code>required string ip = 1;</code>
       */
      public Builder setIp(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        ip_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * IP地址
       * </pre>
       *
       * <code>required string ip = 1;</code>
       */
      public Builder clearIp() {
        bitField0_ = (bitField0_ & ~0x00000001);
        ip_ = getDefaultInstance().getIp();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * IP地址
       * </pre>
       *
       * <code>required string ip = 1;</code>
       */
      public Builder setIpBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        ip_ = value;
        onChanged();
        return this;
      }

      private int port_ ;
      /**
       * <pre>
       * 端口
       * </pre>
       *
       * <code>required uint32 port = 2;</code>
       */
      public boolean hasPort() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <pre>
       * 端口
       * </pre>
       *
       * <code>required uint32 port = 2;</code>
       */
      public int getPort() {
        return port_;
      }
      /**
       * <pre>
       * 端口
       * </pre>
       *
       * <code>required uint32 port = 2;</code>
       */
      public Builder setPort(int value) {
        bitField0_ |= 0x00000002;
        port_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 端口
       * </pre>
       *
       * <code>required uint32 port = 2;</code>
       */
      public Builder clearPort() {
        bitField0_ = (bitField0_ & ~0x00000002);
        port_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object ipCountry_ = "";
      /**
       * <pre>
       * 国家名
       * </pre>
       *
       * <code>required string ip_country = 3;</code>
       */
      public boolean hasIpCountry() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <pre>
       * 国家名
       * </pre>
       *
       * <code>required string ip_country = 3;</code>
       */
      public java.lang.String getIpCountry() {
        java.lang.Object ref = ipCountry_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ipCountry_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 国家名
       * </pre>
       *
       * <code>required string ip_country = 3;</code>
       */
      public com.google.protobuf.ByteString
          getIpCountryBytes() {
        java.lang.Object ref = ipCountry_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ipCountry_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 国家名
       * </pre>
       *
       * <code>required string ip_country = 3;</code>
       */
      public Builder setIpCountry(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        ipCountry_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 国家名
       * </pre>
       *
       * <code>required string ip_country = 3;</code>
       */
      public Builder clearIpCountry() {
        bitField0_ = (bitField0_ & ~0x00000004);
        ipCountry_ = getDefaultInstance().getIpCountry();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 国家名
       * </pre>
       *
       * <code>required string ip_country = 3;</code>
       */
      public Builder setIpCountryBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        ipCountry_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object ipStat_ = "";
      /**
       * <pre>
       * 省份/洲名
       * </pre>
       *
       * <code>required string ip_stat = 4;</code>
       */
      public boolean hasIpStat() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <pre>
       * 省份/洲名
       * </pre>
       *
       * <code>required string ip_stat = 4;</code>
       */
      public java.lang.String getIpStat() {
        java.lang.Object ref = ipStat_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ipStat_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 省份/洲名
       * </pre>
       *
       * <code>required string ip_stat = 4;</code>
       */
      public com.google.protobuf.ByteString
          getIpStatBytes() {
        java.lang.Object ref = ipStat_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ipStat_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 省份/洲名
       * </pre>
       *
       * <code>required string ip_stat = 4;</code>
       */
      public Builder setIpStat(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        ipStat_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 省份/洲名
       * </pre>
       *
       * <code>required string ip_stat = 4;</code>
       */
      public Builder clearIpStat() {
        bitField0_ = (bitField0_ & ~0x00000008);
        ipStat_ = getDefaultInstance().getIpStat();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 省份/洲名
       * </pre>
       *
       * <code>required string ip_stat = 4;</code>
       */
      public Builder setIpStatBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        ipStat_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object ipCity_ = "";
      /**
       * <pre>
       * 城市名
       * </pre>
       *
       * <code>required string ip_city = 5;</code>
       */
      public boolean hasIpCity() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <pre>
       * 城市名
       * </pre>
       *
       * <code>required string ip_city = 5;</code>
       */
      public java.lang.String getIpCity() {
        java.lang.Object ref = ipCity_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ipCity_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 城市名
       * </pre>
       *
       * <code>required string ip_city = 5;</code>
       */
      public com.google.protobuf.ByteString
          getIpCityBytes() {
        java.lang.Object ref = ipCity_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ipCity_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 城市名
       * </pre>
       *
       * <code>required string ip_city = 5;</code>
       */
      public Builder setIpCity(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        ipCity_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 城市名
       * </pre>
       *
       * <code>required string ip_city = 5;</code>
       */
      public Builder clearIpCity() {
        bitField0_ = (bitField0_ & ~0x00000010);
        ipCity_ = getDefaultInstance().getIpCity();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 城市名
       * </pre>
       *
       * <code>required string ip_city = 5;</code>
       */
      public Builder setIpCityBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        ipCity_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object ipOrg_ = "";
      /**
       * <pre>
       * 机构名
       * </pre>
       *
       * <code>required string ip_org = 6;</code>
       */
      public boolean hasIpOrg() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <pre>
       * 机构名
       * </pre>
       *
       * <code>required string ip_org = 6;</code>
       */
      public java.lang.String getIpOrg() {
        java.lang.Object ref = ipOrg_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ipOrg_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 机构名
       * </pre>
       *
       * <code>required string ip_org = 6;</code>
       */
      public com.google.protobuf.ByteString
          getIpOrgBytes() {
        java.lang.Object ref = ipOrg_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ipOrg_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 机构名
       * </pre>
       *
       * <code>required string ip_org = 6;</code>
       */
      public Builder setIpOrg(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        ipOrg_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 机构名
       * </pre>
       *
       * <code>required string ip_org = 6;</code>
       */
      public Builder clearIpOrg() {
        bitField0_ = (bitField0_ & ~0x00000020);
        ipOrg_ = getDefaultInstance().getIpOrg();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 机构名
       * </pre>
       *
       * <code>required string ip_org = 6;</code>
       */
      public Builder setIpOrgBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        ipOrg_ = value;
        onChanged();
        return this;
      }

      private double ipLongitude_ ;
      /**
       * <pre>
       * 纬度
       * </pre>
       *
       * <code>required double ip_longitude = 7;</code>
       */
      public boolean hasIpLongitude() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <pre>
       * 纬度
       * </pre>
       *
       * <code>required double ip_longitude = 7;</code>
       */
      public double getIpLongitude() {
        return ipLongitude_;
      }
      /**
       * <pre>
       * 纬度
       * </pre>
       *
       * <code>required double ip_longitude = 7;</code>
       */
      public Builder setIpLongitude(double value) {
        bitField0_ |= 0x00000040;
        ipLongitude_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 纬度
       * </pre>
       *
       * <code>required double ip_longitude = 7;</code>
       */
      public Builder clearIpLongitude() {
        bitField0_ = (bitField0_ & ~0x00000040);
        ipLongitude_ = 0D;
        onChanged();
        return this;
      }

      private double ipLatitude_ ;
      /**
       * <pre>
       * 经度
       * </pre>
       *
       * <code>required double ip_latitude = 8;</code>
       */
      public boolean hasIpLatitude() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <pre>
       * 经度
       * </pre>
       *
       * <code>required double ip_latitude = 8;</code>
       */
      public double getIpLatitude() {
        return ipLatitude_;
      }
      /**
       * <pre>
       * 经度
       * </pre>
       *
       * <code>required double ip_latitude = 8;</code>
       */
      public Builder setIpLatitude(double value) {
        bitField0_ |= 0x00000080;
        ipLatitude_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 经度
       * </pre>
       *
       * <code>required double ip_latitude = 8;</code>
       */
      public Builder clearIpLatitude() {
        bitField0_ = (bitField0_ & ~0x00000080);
        ipLatitude_ = 0D;
        onChanged();
        return this;
      }

      private java.lang.Object ipIsp_ = "";
      /**
       * <pre>
       * 运营商
       * </pre>
       *
       * <code>required string ip_isp = 9;</code>
       */
      public boolean hasIpIsp() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <pre>
       * 运营商
       * </pre>
       *
       * <code>required string ip_isp = 9;</code>
       */
      public java.lang.String getIpIsp() {
        java.lang.Object ref = ipIsp_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ipIsp_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 运营商
       * </pre>
       *
       * <code>required string ip_isp = 9;</code>
       */
      public com.google.protobuf.ByteString
          getIpIspBytes() {
        java.lang.Object ref = ipIsp_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ipIsp_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 运营商
       * </pre>
       *
       * <code>required string ip_isp = 9;</code>
       */
      public Builder setIpIsp(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        ipIsp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 运营商
       * </pre>
       *
       * <code>required string ip_isp = 9;</code>
       */
      public Builder clearIpIsp() {
        bitField0_ = (bitField0_ & ~0x00000100);
        ipIsp_ = getDefaultInstance().getIpIsp();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 运营商
       * </pre>
       *
       * <code>required string ip_isp = 9;</code>
       */
      public Builder setIpIspBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        ipIsp_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object ipAsn_ = "";
      /**
       * <pre>
       * AS信息
       * </pre>
       *
       * <code>required string ip_asn = 10;</code>
       */
      public boolean hasIpAsn() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <pre>
       * AS信息
       * </pre>
       *
       * <code>required string ip_asn = 10;</code>
       */
      public java.lang.String getIpAsn() {
        java.lang.Object ref = ipAsn_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ipAsn_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * AS信息
       * </pre>
       *
       * <code>required string ip_asn = 10;</code>
       */
      public com.google.protobuf.ByteString
          getIpAsnBytes() {
        java.lang.Object ref = ipAsn_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ipAsn_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * AS信息
       * </pre>
       *
       * <code>required string ip_asn = 10;</code>
       */
      public Builder setIpAsn(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
        ipAsn_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * AS信息
       * </pre>
       *
       * <code>required string ip_asn = 10;</code>
       */
      public Builder clearIpAsn() {
        bitField0_ = (bitField0_ & ~0x00000200);
        ipAsn_ = getDefaultInstance().getIpAsn();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * AS信息
       * </pre>
       *
       * <code>required string ip_asn = 10;</code>
       */
      public Builder setIpAsnBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
        ipAsn_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object ipTag_ = "";
      /**
       * <pre>
       * 标签
       * </pre>
       *
       * <code>optional string ip_tag = 11;</code>
       */
      public boolean hasIpTag() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <pre>
       * 标签
       * </pre>
       *
       * <code>optional string ip_tag = 11;</code>
       */
      public java.lang.String getIpTag() {
        java.lang.Object ref = ipTag_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ipTag_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 标签
       * </pre>
       *
       * <code>optional string ip_tag = 11;</code>
       */
      public com.google.protobuf.ByteString
          getIpTagBytes() {
        java.lang.Object ref = ipTag_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ipTag_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 标签
       * </pre>
       *
       * <code>optional string ip_tag = 11;</code>
       */
      public Builder setIpTag(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        ipTag_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标签
       * </pre>
       *
       * <code>optional string ip_tag = 11;</code>
       */
      public Builder clearIpTag() {
        bitField0_ = (bitField0_ & ~0x00000400);
        ipTag_ = getDefaultInstance().getIpTag();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标签
       * </pre>
       *
       * <code>optional string ip_tag = 11;</code>
       */
      public Builder setIpTagBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        ipTag_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:IP_INFO)
    }

    // @@protoc_insertion_point(class_scope:IP_INFO)
    private static final IpInfo.IP_INFO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new IpInfo.IP_INFO();
    }

    public static IpInfo.IP_INFO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<IP_INFO>
        PARSER = new com.google.protobuf.AbstractParser<IP_INFO>() {
      @java.lang.Override
      public IP_INFO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new IP_INFO(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<IP_INFO> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IP_INFO> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public IpInfo.IP_INFO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_IP_INFO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_IP_INFO_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rIP_INFO.proto\"\304\001\n\007IP_INFO\022\n\n\002ip\030\001 \002(\t\022" +
      "\014\n\004port\030\002 \002(\r\022\022\n\nip_country\030\003 \002(\t\022\017\n\007ip_" +
      "stat\030\004 \002(\t\022\017\n\007ip_city\030\005 \002(\t\022\016\n\006ip_org\030\006 " +
      "\002(\t\022\024\n\014ip_longitude\030\007 \002(\001\022\023\n\013ip_latitude" +
      "\030\010 \002(\001\022\016\n\006ip_isp\030\t \002(\t\022\016\n\006ip_asn\030\n \002(\t\022\016" +
      "\n\006ip_tag\030\013 \001(\tB\010B\006IpInfo"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_IP_INFO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_IP_INFO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_IP_INFO_descriptor,
        new java.lang.String[] { "Ip", "Port", "IpCountry", "IpStat", "IpCity", "IpOrg", "IpLongitude", "IpLatitude", "IpIsp", "IpAsn", "IpTag", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
