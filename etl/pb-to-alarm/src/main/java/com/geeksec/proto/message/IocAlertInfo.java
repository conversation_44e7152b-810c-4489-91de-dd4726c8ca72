// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: IOC_ALERT_INFO.proto
package com.geeksec.proto.message;

public final class IocAlertInfo {
  private IocAlertInfo() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface IOC_ALERT_INFOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:IOC_ALERT_INFO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *	IOC编号	
     * </pre>
     *
     * <code>required string ioc_id = 1;</code>
     */
    boolean hasIocId();
    /**
     * <pre>
     *	IOC编号	
     * </pre>
     *
     * <code>required string ioc_id = 1;</code>
     */
    java.lang.String getIocId();
    /**
     * <pre>
     *	IOC编号	
     * </pre>
     *
     * <code>required string ioc_id = 1;</code>
     */
    com.google.protobuf.ByteString
        getIocIdBytes();

    /**
     * <pre>
     *	IOC内容	
     * </pre>
     *
     * <code>required string ioc_value = 2;</code>
     */
    boolean hasIocValue();
    /**
     * <pre>
     *	IOC内容	
     * </pre>
     *
     * <code>required string ioc_value = 2;</code>
     */
    java.lang.String getIocValue();
    /**
     * <pre>
     *	IOC内容	
     * </pre>
     *
     * <code>required string ioc_value = 2;</code>
     */
    com.google.protobuf.ByteString
        getIocValueBytes();

    /**
     * <pre>
     *	IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
     * </pre>
     *
     * <code>required string ioc_category = 3;</code>
     */
    boolean hasIocCategory();
    /**
     * <pre>
     *	IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
     * </pre>
     *
     * <code>required string ioc_category = 3;</code>
     */
    java.lang.String getIocCategory();
    /**
     * <pre>
     *	IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
     * </pre>
     *
     * <code>required string ioc_category = 3;</code>
     */
    com.google.protobuf.ByteString
        getIocCategoryBytes();

    /**
     * <pre>
     *	IOC发布时间	
     * </pre>
     *
     * <code>required uint64 ioc_public_date = 4;</code>
     */
    boolean hasIocPublicDate();
    /**
     * <pre>
     *	IOC发布时间	
     * </pre>
     *
     * <code>required uint64 ioc_public_date = 4;</code>
     */
    long getIocPublicDate();

    /**
     * <pre>
     *	IOC告警名称	
     * </pre>
     *
     * <code>required string ioc_alert_name = 5;</code>
     */
    boolean hasIocAlertName();
    /**
     * <pre>
     *	IOC告警名称	
     * </pre>
     *
     * <code>required string ioc_alert_name = 5;</code>
     */
    java.lang.String getIocAlertName();
    /**
     * <pre>
     *	IOC告警名称	
     * </pre>
     *
     * <code>required string ioc_alert_name = 5;</code>
     */
    com.google.protobuf.ByteString
        getIocAlertNameBytes();

    /**
     * <pre>
     *	IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
     * </pre>
     *
     * <code>required string ioc_current_status = 6;</code>
     */
    boolean hasIocCurrentStatus();
    /**
     * <pre>
     *	IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
     * </pre>
     *
     * <code>required string ioc_current_status = 6;</code>
     */
    java.lang.String getIocCurrentStatus();
    /**
     * <pre>
     *	IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
     * </pre>
     *
     * <code>required string ioc_current_status = 6;</code>
     */
    com.google.protobuf.ByteString
        getIocCurrentStatusBytes();

    /**
     * <pre>
     *	IOC热点状态	True/False 
     * </pre>
     *
     * <code>optional bool ioc_hot = 7;</code>
     */
    boolean hasIocHot();
    /**
     * <pre>
     *	IOC热点状态	True/False 
     * </pre>
     *
     * <code>optional bool ioc_hot = 7;</code>
     */
    boolean getIocHot();

    /**
     * <pre>
     *	首次发现时间	情报的首次发现时间
     * </pre>
     *
     * <code>optional string ioc_first_seen = 8;</code>
     */
    boolean hasIocFirstSeen();
    /**
     * <pre>
     *	首次发现时间	情报的首次发现时间
     * </pre>
     *
     * <code>optional string ioc_first_seen = 8;</code>
     */
    java.lang.String getIocFirstSeen();
    /**
     * <pre>
     *	首次发现时间	情报的首次发现时间
     * </pre>
     *
     * <code>optional string ioc_first_seen = 8;</code>
     */
    com.google.protobuf.ByteString
        getIocFirstSeenBytes();

    /**
     * <pre>
     *	最近检测时间	最后一次检测到攻击的时间
     * </pre>
     *
     * <code>required string ioc_last_detection = 9;</code>
     */
    boolean hasIocLastDetection();
    /**
     * <pre>
     *	最近检测时间	最后一次检测到攻击的时间
     * </pre>
     *
     * <code>required string ioc_last_detection = 9;</code>
     */
    java.lang.String getIocLastDetection();
    /**
     * <pre>
     *	最近检测时间	最后一次检测到攻击的时间
     * </pre>
     *
     * <code>required string ioc_last_detection = 9;</code>
     */
    com.google.protobuf.ByteString
        getIocLastDetectionBytes();

    /**
     * <pre>
     *	参考文档报告	
     * </pre>
     *
     * <code>optional string ioc_refer = 10;</code>
     */
    boolean hasIocRefer();
    /**
     * <pre>
     *	参考文档报告	
     * </pre>
     *
     * <code>optional string ioc_refer = 10;</code>
     */
    java.lang.String getIocRefer();
    /**
     * <pre>
     *	参考文档报告	
     * </pre>
     *
     * <code>optional string ioc_refer = 10;</code>
     */
    com.google.protobuf.ByteString
        getIocReferBytes();

    /**
     * <pre>
     *	报告发布时间	
     * </pre>
     *
     * <code>optional string ioc_report_data = 11;</code>
     */
    boolean hasIocReportData();
    /**
     * <pre>
     *	报告发布时间	
     * </pre>
     *
     * <code>optional string ioc_report_data = 11;</code>
     */
    java.lang.String getIocReportData();
    /**
     * <pre>
     *	报告发布时间	
     * </pre>
     *
     * <code>optional string ioc_report_data = 11;</code>
     */
    com.google.protobuf.ByteString
        getIocReportDataBytes();

    /**
     * <pre>
     *	报告发布厂商	
     * </pre>
     *
     * <code>optional string ioc_report_vendor = 12;</code>
     */
    boolean hasIocReportVendor();
    /**
     * <pre>
     *	报告发布厂商	
     * </pre>
     *
     * <code>optional string ioc_report_vendor = 12;</code>
     */
    java.lang.String getIocReportVendor();
    /**
     * <pre>
     *	报告发布厂商	
     * </pre>
     *
     * <code>optional string ioc_report_vendor = 12;</code>
     */
    com.google.protobuf.ByteString
        getIocReportVendorBytes();

    /**
     * <pre>
     *	IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
     * </pre>
     *
     * <code>required string ioc_type = 13;</code>
     */
    boolean hasIocType();
    /**
     * <pre>
     *	IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
     * </pre>
     *
     * <code>required string ioc_type = 13;</code>
     */
    java.lang.String getIocType();
    /**
     * <pre>
     *	IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
     * </pre>
     *
     * <code>required string ioc_type = 13;</code>
     */
    com.google.protobuf.ByteString
        getIocTypeBytes();

    /**
     * <pre>
     *	定向攻击标识	True/False 
     * </pre>
     *
     * <code>required bool ioc_targeted = 14;</code>
     */
    boolean hasIocTargeted();
    /**
     * <pre>
     *	定向攻击标识	True/False 
     * </pre>
     *
     * <code>required bool ioc_targeted = 14;</code>
     */
    boolean getIocTargeted();

    /**
     * <pre>
     *	恶意代码家族	
     * </pre>
     *
     * <code>optional string ioc_malicious_family = 15;</code>
     */
    boolean hasIocMaliciousFamily();
    /**
     * <pre>
     *	恶意代码家族	
     * </pre>
     *
     * <code>optional string ioc_malicious_family = 15;</code>
     */
    java.lang.String getIocMaliciousFamily();
    /**
     * <pre>
     *	恶意代码家族	
     * </pre>
     *
     * <code>optional string ioc_malicious_family = 15;</code>
     */
    com.google.protobuf.ByteString
        getIocMaliciousFamilyBytes();

    /**
     * <pre>
     *	APT组织名称	对应actor、primary_name
     * </pre>
     *
     * <code>optional string ioc_apt_campaign = 16;</code>
     */
    boolean hasIocAptCampaign();
    /**
     * <pre>
     *	APT组织名称	对应actor、primary_name
     * </pre>
     *
     * <code>optional string ioc_apt_campaign = 16;</code>
     */
    java.lang.String getIocAptCampaign();
    /**
     * <pre>
     *	APT组织名称	对应actor、primary_name
     * </pre>
     *
     * <code>optional string ioc_apt_campaign = 16;</code>
     */
    com.google.protobuf.ByteString
        getIocAptCampaignBytes();

    /**
     * <pre>
     *	APT组织别名	
     * </pre>
     *
     * <code>optional string ioc_apt_alias = 17;</code>
     */
    boolean hasIocAptAlias();
    /**
     * <pre>
     *	APT组织别名	
     * </pre>
     *
     * <code>optional string ioc_apt_alias = 17;</code>
     */
    java.lang.String getIocAptAlias();
    /**
     * <pre>
     *	APT组织别名	
     * </pre>
     *
     * <code>optional string ioc_apt_alias = 17;</code>
     */
    com.google.protobuf.ByteString
        getIocAptAliasBytes();

    /**
     * <pre>
     *	APT所属国家	
     * </pre>
     *
     * <code>optional string ioc_apt_country = 18;</code>
     */
    boolean hasIocAptCountry();
    /**
     * <pre>
     *	APT所属国家	
     * </pre>
     *
     * <code>optional string ioc_apt_country = 18;</code>
     */
    java.lang.String getIocAptCountry();
    /**
     * <pre>
     *	APT所属国家	
     * </pre>
     *
     * <code>optional string ioc_apt_country = 18;</code>
     */
    com.google.protobuf.ByteString
        getIocAptCountryBytes();

    /**
     * <pre>
     *	APT行动名称	
     * </pre>
     *
     * <code>optional string ioc_apt_mission = 19;</code>
     */
    boolean hasIocAptMission();
    /**
     * <pre>
     *	APT行动名称	
     * </pre>
     *
     * <code>optional string ioc_apt_mission = 19;</code>
     */
    java.lang.String getIocAptMission();
    /**
     * <pre>
     *	APT行动名称	
     * </pre>
     *
     * <code>optional string ioc_apt_mission = 19;</code>
     */
    com.google.protobuf.ByteString
        getIocAptMissionBytes();

    /**
     * <pre>
     *	远控工具	
     * </pre>
     *
     * <code>optional string ioc_rat = 20;</code>
     */
    boolean hasIocRat();
    /**
     * <pre>
     *	远控工具	
     * </pre>
     *
     * <code>optional string ioc_rat = 20;</code>
     */
    java.lang.String getIocRat();
    /**
     * <pre>
     *	远控工具	
     * </pre>
     *
     * <code>optional string ioc_rat = 20;</code>
     */
    com.google.protobuf.ByteString
        getIocRatBytes();

    /**
     * <pre>
     *	攻击手法	WEB攻击渗透、…
     * </pre>
     *
     * <code>optional string ioc_attack_method = 21;</code>
     */
    boolean hasIocAttackMethod();
    /**
     * <pre>
     *	攻击手法	WEB攻击渗透、…
     * </pre>
     *
     * <code>optional string ioc_attack_method = 21;</code>
     */
    java.lang.String getIocAttackMethod();
    /**
     * <pre>
     *	攻击手法	WEB攻击渗透、…
     * </pre>
     *
     * <code>optional string ioc_attack_method = 21;</code>
     */
    com.google.protobuf.ByteString
        getIocAttackMethodBytes();

    /**
     * <pre>
     *	关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioc_vul = 22;</code>
     */
    boolean hasIocVul();
    /**
     * <pre>
     *	关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioc_vul = 22;</code>
     */
    java.lang.String getIocVul();
    /**
     * <pre>
     *	关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioc_vul = 22;</code>
     */
    com.google.protobuf.ByteString
        getIocVulBytes();

    /**
     * <pre>
     *	影响行业	
     * </pre>
     *
     * <code>optional string ioc_affected_sector = 23;</code>
     */
    boolean hasIocAffectedSector();
    /**
     * <pre>
     *	影响行业	
     * </pre>
     *
     * <code>optional string ioc_affected_sector = 23;</code>
     */
    java.lang.String getIocAffectedSector();
    /**
     * <pre>
     *	影响行业	
     * </pre>
     *
     * <code>optional string ioc_affected_sector = 23;</code>
     */
    com.google.protobuf.ByteString
        getIocAffectedSectorBytes();

    /**
     * <pre>
     *	影响平台	
     * </pre>
     *
     * <code>optional string ioc_affected_product = 24;</code>
     */
    boolean hasIocAffectedProduct();
    /**
     * <pre>
     *	影响平台	
     * </pre>
     *
     * <code>optional string ioc_affected_product = 24;</code>
     */
    java.lang.String getIocAffectedProduct();
    /**
     * <pre>
     *	影响平台	
     * </pre>
     *
     * <code>optional string ioc_affected_product = 24;</code>
     */
    com.google.protobuf.ByteString
        getIocAffectedProductBytes();

    /**
     * <pre>
     *	威胁详情描述	"威胁详情：漏洞详情：修复方案："
     * </pre>
     *
     * <code>required string ioc_detail_info = 25;</code>
     */
    boolean hasIocDetailInfo();
    /**
     * <pre>
     *	威胁详情描述	"威胁详情：漏洞详情：修复方案："
     * </pre>
     *
     * <code>required string ioc_detail_info = 25;</code>
     */
    java.lang.String getIocDetailInfo();
    /**
     * <pre>
     *	威胁详情描述	"威胁详情：漏洞详情：修复方案："
     * </pre>
     *
     * <code>required string ioc_detail_info = 25;</code>
     */
    com.google.protobuf.ByteString
        getIocDetailInfoBytes();
  }
  /**
   * Protobuf type {@code IOC_ALERT_INFO}
   */
  public  static final class IOC_ALERT_INFO extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:IOC_ALERT_INFO)
      IOC_ALERT_INFOOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use IOC_ALERT_INFO.newBuilder() to construct.
    private IOC_ALERT_INFO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private IOC_ALERT_INFO() {
      iocId_ = "";
      iocValue_ = "";
      iocCategory_ = "";
      iocPublicDate_ = 0L;
      iocAlertName_ = "";
      iocCurrentStatus_ = "";
      iocHot_ = false;
      iocFirstSeen_ = "";
      iocLastDetection_ = "";
      iocRefer_ = "";
      iocReportData_ = "";
      iocReportVendor_ = "";
      iocType_ = "";
      iocTargeted_ = false;
      iocMaliciousFamily_ = "";
      iocAptCampaign_ = "";
      iocAptAlias_ = "";
      iocAptCountry_ = "";
      iocAptMission_ = "";
      iocRat_ = "";
      iocAttackMethod_ = "";
      iocVul_ = "";
      iocAffectedSector_ = "";
      iocAffectedProduct_ = "";
      iocDetailInfo_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private IOC_ALERT_INFO(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              iocId_ = bs;
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              iocValue_ = bs;
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              iocCategory_ = bs;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              iocPublicDate_ = input.readUInt64();
              break;
            }
            case 42: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000010;
              iocAlertName_ = bs;
              break;
            }
            case 50: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000020;
              iocCurrentStatus_ = bs;
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              iocHot_ = input.readBool();
              break;
            }
            case 66: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000080;
              iocFirstSeen_ = bs;
              break;
            }
            case 74: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000100;
              iocLastDetection_ = bs;
              break;
            }
            case 82: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000200;
              iocRefer_ = bs;
              break;
            }
            case 90: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000400;
              iocReportData_ = bs;
              break;
            }
            case 98: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000800;
              iocReportVendor_ = bs;
              break;
            }
            case 106: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00001000;
              iocType_ = bs;
              break;
            }
            case 112: {
              bitField0_ |= 0x00002000;
              iocTargeted_ = input.readBool();
              break;
            }
            case 122: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00004000;
              iocMaliciousFamily_ = bs;
              break;
            }
            case 130: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00008000;
              iocAptCampaign_ = bs;
              break;
            }
            case 138: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00010000;
              iocAptAlias_ = bs;
              break;
            }
            case 146: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00020000;
              iocAptCountry_ = bs;
              break;
            }
            case 154: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00040000;
              iocAptMission_ = bs;
              break;
            }
            case 162: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00080000;
              iocRat_ = bs;
              break;
            }
            case 170: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00100000;
              iocAttackMethod_ = bs;
              break;
            }
            case 178: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00200000;
              iocVul_ = bs;
              break;
            }
            case 186: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00400000;
              iocAffectedSector_ = bs;
              break;
            }
            case 194: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00800000;
              iocAffectedProduct_ = bs;
              break;
            }
            case 202: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x01000000;
              iocDetailInfo_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return IocAlertInfo.internal_static_IOC_ALERT_INFO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return IocAlertInfo.internal_static_IOC_ALERT_INFO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              IocAlertInfo.IOC_ALERT_INFO.class, IocAlertInfo.IOC_ALERT_INFO.Builder.class);
    }

    private int bitField0_;
    public static final int IOC_ID_FIELD_NUMBER = 1;
    private volatile java.lang.Object iocId_;
    /**
     * <pre>
     *	IOC编号	
     * </pre>
     *
     * <code>required string ioc_id = 1;</code>
     */
    public boolean hasIocId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <pre>
     *	IOC编号	
     * </pre>
     *
     * <code>required string ioc_id = 1;</code>
     */
    public java.lang.String getIocId() {
      java.lang.Object ref = iocId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocId_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	IOC编号	
     * </pre>
     *
     * <code>required string ioc_id = 1;</code>
     */
    public com.google.protobuf.ByteString
        getIocIdBytes() {
      java.lang.Object ref = iocId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_VALUE_FIELD_NUMBER = 2;
    private volatile java.lang.Object iocValue_;
    /**
     * <pre>
     *	IOC内容	
     * </pre>
     *
     * <code>required string ioc_value = 2;</code>
     */
    public boolean hasIocValue() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <pre>
     *	IOC内容	
     * </pre>
     *
     * <code>required string ioc_value = 2;</code>
     */
    public java.lang.String getIocValue() {
      java.lang.Object ref = iocValue_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocValue_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	IOC内容	
     * </pre>
     *
     * <code>required string ioc_value = 2;</code>
     */
    public com.google.protobuf.ByteString
        getIocValueBytes() {
      java.lang.Object ref = iocValue_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocValue_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_CATEGORY_FIELD_NUMBER = 3;
    private volatile java.lang.Object iocCategory_;
    /**
     * <pre>
     *	IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
     * </pre>
     *
     * <code>required string ioc_category = 3;</code>
     */
    public boolean hasIocCategory() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <pre>
     *	IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
     * </pre>
     *
     * <code>required string ioc_category = 3;</code>
     */
    public java.lang.String getIocCategory() {
      java.lang.Object ref = iocCategory_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocCategory_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
     * </pre>
     *
     * <code>required string ioc_category = 3;</code>
     */
    public com.google.protobuf.ByteString
        getIocCategoryBytes() {
      java.lang.Object ref = iocCategory_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocCategory_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_PUBLIC_DATE_FIELD_NUMBER = 4;
    private long iocPublicDate_;
    /**
     * <pre>
     *	IOC发布时间	
     * </pre>
     *
     * <code>required uint64 ioc_public_date = 4;</code>
     */
    public boolean hasIocPublicDate() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <pre>
     *	IOC发布时间	
     * </pre>
     *
     * <code>required uint64 ioc_public_date = 4;</code>
     */
    public long getIocPublicDate() {
      return iocPublicDate_;
    }

    public static final int IOC_ALERT_NAME_FIELD_NUMBER = 5;
    private volatile java.lang.Object iocAlertName_;
    /**
     * <pre>
     *	IOC告警名称	
     * </pre>
     *
     * <code>required string ioc_alert_name = 5;</code>
     */
    public boolean hasIocAlertName() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <pre>
     *	IOC告警名称	
     * </pre>
     *
     * <code>required string ioc_alert_name = 5;</code>
     */
    public java.lang.String getIocAlertName() {
      java.lang.Object ref = iocAlertName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocAlertName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	IOC告警名称	
     * </pre>
     *
     * <code>required string ioc_alert_name = 5;</code>
     */
    public com.google.protobuf.ByteString
        getIocAlertNameBytes() {
      java.lang.Object ref = iocAlertName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocAlertName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_CURRENT_STATUS_FIELD_NUMBER = 6;
    private volatile java.lang.Object iocCurrentStatus_;
    /**
     * <pre>
     *	IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
     * </pre>
     *
     * <code>required string ioc_current_status = 6;</code>
     */
    public boolean hasIocCurrentStatus() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <pre>
     *	IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
     * </pre>
     *
     * <code>required string ioc_current_status = 6;</code>
     */
    public java.lang.String getIocCurrentStatus() {
      java.lang.Object ref = iocCurrentStatus_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocCurrentStatus_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
     * </pre>
     *
     * <code>required string ioc_current_status = 6;</code>
     */
    public com.google.protobuf.ByteString
        getIocCurrentStatusBytes() {
      java.lang.Object ref = iocCurrentStatus_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocCurrentStatus_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_HOT_FIELD_NUMBER = 7;
    private boolean iocHot_;
    /**
     * <pre>
     *	IOC热点状态	True/False 
     * </pre>
     *
     * <code>optional bool ioc_hot = 7;</code>
     */
    public boolean hasIocHot() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <pre>
     *	IOC热点状态	True/False 
     * </pre>
     *
     * <code>optional bool ioc_hot = 7;</code>
     */
    public boolean getIocHot() {
      return iocHot_;
    }

    public static final int IOC_FIRST_SEEN_FIELD_NUMBER = 8;
    private volatile java.lang.Object iocFirstSeen_;
    /**
     * <pre>
     *	首次发现时间	情报的首次发现时间
     * </pre>
     *
     * <code>optional string ioc_first_seen = 8;</code>
     */
    public boolean hasIocFirstSeen() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <pre>
     *	首次发现时间	情报的首次发现时间
     * </pre>
     *
     * <code>optional string ioc_first_seen = 8;</code>
     */
    public java.lang.String getIocFirstSeen() {
      java.lang.Object ref = iocFirstSeen_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocFirstSeen_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	首次发现时间	情报的首次发现时间
     * </pre>
     *
     * <code>optional string ioc_first_seen = 8;</code>
     */
    public com.google.protobuf.ByteString
        getIocFirstSeenBytes() {
      java.lang.Object ref = iocFirstSeen_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocFirstSeen_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_LAST_DETECTION_FIELD_NUMBER = 9;
    private volatile java.lang.Object iocLastDetection_;
    /**
     * <pre>
     *	最近检测时间	最后一次检测到攻击的时间
     * </pre>
     *
     * <code>required string ioc_last_detection = 9;</code>
     */
    public boolean hasIocLastDetection() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <pre>
     *	最近检测时间	最后一次检测到攻击的时间
     * </pre>
     *
     * <code>required string ioc_last_detection = 9;</code>
     */
    public java.lang.String getIocLastDetection() {
      java.lang.Object ref = iocLastDetection_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocLastDetection_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	最近检测时间	最后一次检测到攻击的时间
     * </pre>
     *
     * <code>required string ioc_last_detection = 9;</code>
     */
    public com.google.protobuf.ByteString
        getIocLastDetectionBytes() {
      java.lang.Object ref = iocLastDetection_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocLastDetection_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_REFER_FIELD_NUMBER = 10;
    private volatile java.lang.Object iocRefer_;
    /**
     * <pre>
     *	参考文档报告	
     * </pre>
     *
     * <code>optional string ioc_refer = 10;</code>
     */
    public boolean hasIocRefer() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <pre>
     *	参考文档报告	
     * </pre>
     *
     * <code>optional string ioc_refer = 10;</code>
     */
    public java.lang.String getIocRefer() {
      java.lang.Object ref = iocRefer_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocRefer_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	参考文档报告	
     * </pre>
     *
     * <code>optional string ioc_refer = 10;</code>
     */
    public com.google.protobuf.ByteString
        getIocReferBytes() {
      java.lang.Object ref = iocRefer_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocRefer_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_REPORT_DATA_FIELD_NUMBER = 11;
    private volatile java.lang.Object iocReportData_;
    /**
     * <pre>
     *	报告发布时间	
     * </pre>
     *
     * <code>optional string ioc_report_data = 11;</code>
     */
    public boolean hasIocReportData() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <pre>
     *	报告发布时间	
     * </pre>
     *
     * <code>optional string ioc_report_data = 11;</code>
     */
    public java.lang.String getIocReportData() {
      java.lang.Object ref = iocReportData_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocReportData_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	报告发布时间	
     * </pre>
     *
     * <code>optional string ioc_report_data = 11;</code>
     */
    public com.google.protobuf.ByteString
        getIocReportDataBytes() {
      java.lang.Object ref = iocReportData_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocReportData_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_REPORT_VENDOR_FIELD_NUMBER = 12;
    private volatile java.lang.Object iocReportVendor_;
    /**
     * <pre>
     *	报告发布厂商	
     * </pre>
     *
     * <code>optional string ioc_report_vendor = 12;</code>
     */
    public boolean hasIocReportVendor() {
      return ((bitField0_ & 0x00000800) == 0x00000800);
    }
    /**
     * <pre>
     *	报告发布厂商	
     * </pre>
     *
     * <code>optional string ioc_report_vendor = 12;</code>
     */
    public java.lang.String getIocReportVendor() {
      java.lang.Object ref = iocReportVendor_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocReportVendor_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	报告发布厂商	
     * </pre>
     *
     * <code>optional string ioc_report_vendor = 12;</code>
     */
    public com.google.protobuf.ByteString
        getIocReportVendorBytes() {
      java.lang.Object ref = iocReportVendor_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocReportVendor_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_TYPE_FIELD_NUMBER = 13;
    private volatile java.lang.Object iocType_;
    /**
     * <pre>
     *	IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
     * </pre>
     *
     * <code>required string ioc_type = 13;</code>
     */
    public boolean hasIocType() {
      return ((bitField0_ & 0x00001000) == 0x00001000);
    }
    /**
     * <pre>
     *	IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
     * </pre>
     *
     * <code>required string ioc_type = 13;</code>
     */
    public java.lang.String getIocType() {
      java.lang.Object ref = iocType_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocType_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
     * </pre>
     *
     * <code>required string ioc_type = 13;</code>
     */
    public com.google.protobuf.ByteString
        getIocTypeBytes() {
      java.lang.Object ref = iocType_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_TARGETED_FIELD_NUMBER = 14;
    private boolean iocTargeted_;
    /**
     * <pre>
     *	定向攻击标识	True/False 
     * </pre>
     *
     * <code>required bool ioc_targeted = 14;</code>
     */
    public boolean hasIocTargeted() {
      return ((bitField0_ & 0x00002000) == 0x00002000);
    }
    /**
     * <pre>
     *	定向攻击标识	True/False 
     * </pre>
     *
     * <code>required bool ioc_targeted = 14;</code>
     */
    public boolean getIocTargeted() {
      return iocTargeted_;
    }

    public static final int IOC_MALICIOUS_FAMILY_FIELD_NUMBER = 15;
    private volatile java.lang.Object iocMaliciousFamily_;
    /**
     * <pre>
     *	恶意代码家族	
     * </pre>
     *
     * <code>optional string ioc_malicious_family = 15;</code>
     */
    public boolean hasIocMaliciousFamily() {
      return ((bitField0_ & 0x00004000) == 0x00004000);
    }
    /**
     * <pre>
     *	恶意代码家族	
     * </pre>
     *
     * <code>optional string ioc_malicious_family = 15;</code>
     */
    public java.lang.String getIocMaliciousFamily() {
      java.lang.Object ref = iocMaliciousFamily_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocMaliciousFamily_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	恶意代码家族	
     * </pre>
     *
     * <code>optional string ioc_malicious_family = 15;</code>
     */
    public com.google.protobuf.ByteString
        getIocMaliciousFamilyBytes() {
      java.lang.Object ref = iocMaliciousFamily_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocMaliciousFamily_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_APT_CAMPAIGN_FIELD_NUMBER = 16;
    private volatile java.lang.Object iocAptCampaign_;
    /**
     * <pre>
     *	APT组织名称	对应actor、primary_name
     * </pre>
     *
     * <code>optional string ioc_apt_campaign = 16;</code>
     */
    public boolean hasIocAptCampaign() {
      return ((bitField0_ & 0x00008000) == 0x00008000);
    }
    /**
     * <pre>
     *	APT组织名称	对应actor、primary_name
     * </pre>
     *
     * <code>optional string ioc_apt_campaign = 16;</code>
     */
    public java.lang.String getIocAptCampaign() {
      java.lang.Object ref = iocAptCampaign_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocAptCampaign_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	APT组织名称	对应actor、primary_name
     * </pre>
     *
     * <code>optional string ioc_apt_campaign = 16;</code>
     */
    public com.google.protobuf.ByteString
        getIocAptCampaignBytes() {
      java.lang.Object ref = iocAptCampaign_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocAptCampaign_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_APT_ALIAS_FIELD_NUMBER = 17;
    private volatile java.lang.Object iocAptAlias_;
    /**
     * <pre>
     *	APT组织别名	
     * </pre>
     *
     * <code>optional string ioc_apt_alias = 17;</code>
     */
    public boolean hasIocAptAlias() {
      return ((bitField0_ & 0x00010000) == 0x00010000);
    }
    /**
     * <pre>
     *	APT组织别名	
     * </pre>
     *
     * <code>optional string ioc_apt_alias = 17;</code>
     */
    public java.lang.String getIocAptAlias() {
      java.lang.Object ref = iocAptAlias_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocAptAlias_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	APT组织别名	
     * </pre>
     *
     * <code>optional string ioc_apt_alias = 17;</code>
     */
    public com.google.protobuf.ByteString
        getIocAptAliasBytes() {
      java.lang.Object ref = iocAptAlias_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocAptAlias_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_APT_COUNTRY_FIELD_NUMBER = 18;
    private volatile java.lang.Object iocAptCountry_;
    /**
     * <pre>
     *	APT所属国家	
     * </pre>
     *
     * <code>optional string ioc_apt_country = 18;</code>
     */
    public boolean hasIocAptCountry() {
      return ((bitField0_ & 0x00020000) == 0x00020000);
    }
    /**
     * <pre>
     *	APT所属国家	
     * </pre>
     *
     * <code>optional string ioc_apt_country = 18;</code>
     */
    public java.lang.String getIocAptCountry() {
      java.lang.Object ref = iocAptCountry_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocAptCountry_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	APT所属国家	
     * </pre>
     *
     * <code>optional string ioc_apt_country = 18;</code>
     */
    public com.google.protobuf.ByteString
        getIocAptCountryBytes() {
      java.lang.Object ref = iocAptCountry_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocAptCountry_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_APT_MISSION_FIELD_NUMBER = 19;
    private volatile java.lang.Object iocAptMission_;
    /**
     * <pre>
     *	APT行动名称	
     * </pre>
     *
     * <code>optional string ioc_apt_mission = 19;</code>
     */
    public boolean hasIocAptMission() {
      return ((bitField0_ & 0x00040000) == 0x00040000);
    }
    /**
     * <pre>
     *	APT行动名称	
     * </pre>
     *
     * <code>optional string ioc_apt_mission = 19;</code>
     */
    public java.lang.String getIocAptMission() {
      java.lang.Object ref = iocAptMission_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocAptMission_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	APT行动名称	
     * </pre>
     *
     * <code>optional string ioc_apt_mission = 19;</code>
     */
    public com.google.protobuf.ByteString
        getIocAptMissionBytes() {
      java.lang.Object ref = iocAptMission_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocAptMission_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_RAT_FIELD_NUMBER = 20;
    private volatile java.lang.Object iocRat_;
    /**
     * <pre>
     *	远控工具	
     * </pre>
     *
     * <code>optional string ioc_rat = 20;</code>
     */
    public boolean hasIocRat() {
      return ((bitField0_ & 0x00080000) == 0x00080000);
    }
    /**
     * <pre>
     *	远控工具	
     * </pre>
     *
     * <code>optional string ioc_rat = 20;</code>
     */
    public java.lang.String getIocRat() {
      java.lang.Object ref = iocRat_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocRat_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	远控工具	
     * </pre>
     *
     * <code>optional string ioc_rat = 20;</code>
     */
    public com.google.protobuf.ByteString
        getIocRatBytes() {
      java.lang.Object ref = iocRat_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocRat_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_ATTACK_METHOD_FIELD_NUMBER = 21;
    private volatile java.lang.Object iocAttackMethod_;
    /**
     * <pre>
     *	攻击手法	WEB攻击渗透、…
     * </pre>
     *
     * <code>optional string ioc_attack_method = 21;</code>
     */
    public boolean hasIocAttackMethod() {
      return ((bitField0_ & 0x00100000) == 0x00100000);
    }
    /**
     * <pre>
     *	攻击手法	WEB攻击渗透、…
     * </pre>
     *
     * <code>optional string ioc_attack_method = 21;</code>
     */
    public java.lang.String getIocAttackMethod() {
      java.lang.Object ref = iocAttackMethod_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocAttackMethod_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	攻击手法	WEB攻击渗透、…
     * </pre>
     *
     * <code>optional string ioc_attack_method = 21;</code>
     */
    public com.google.protobuf.ByteString
        getIocAttackMethodBytes() {
      java.lang.Object ref = iocAttackMethod_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocAttackMethod_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_VUL_FIELD_NUMBER = 22;
    private volatile java.lang.Object iocVul_;
    /**
     * <pre>
     *	关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioc_vul = 22;</code>
     */
    public boolean hasIocVul() {
      return ((bitField0_ & 0x00200000) == 0x00200000);
    }
    /**
     * <pre>
     *	关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioc_vul = 22;</code>
     */
    public java.lang.String getIocVul() {
      java.lang.Object ref = iocVul_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocVul_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioc_vul = 22;</code>
     */
    public com.google.protobuf.ByteString
        getIocVulBytes() {
      java.lang.Object ref = iocVul_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocVul_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_AFFECTED_SECTOR_FIELD_NUMBER = 23;
    private volatile java.lang.Object iocAffectedSector_;
    /**
     * <pre>
     *	影响行业	
     * </pre>
     *
     * <code>optional string ioc_affected_sector = 23;</code>
     */
    public boolean hasIocAffectedSector() {
      return ((bitField0_ & 0x00400000) == 0x00400000);
    }
    /**
     * <pre>
     *	影响行业	
     * </pre>
     *
     * <code>optional string ioc_affected_sector = 23;</code>
     */
    public java.lang.String getIocAffectedSector() {
      java.lang.Object ref = iocAffectedSector_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocAffectedSector_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	影响行业	
     * </pre>
     *
     * <code>optional string ioc_affected_sector = 23;</code>
     */
    public com.google.protobuf.ByteString
        getIocAffectedSectorBytes() {
      java.lang.Object ref = iocAffectedSector_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocAffectedSector_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_AFFECTED_PRODUCT_FIELD_NUMBER = 24;
    private volatile java.lang.Object iocAffectedProduct_;
    /**
     * <pre>
     *	影响平台	
     * </pre>
     *
     * <code>optional string ioc_affected_product = 24;</code>
     */
    public boolean hasIocAffectedProduct() {
      return ((bitField0_ & 0x00800000) == 0x00800000);
    }
    /**
     * <pre>
     *	影响平台	
     * </pre>
     *
     * <code>optional string ioc_affected_product = 24;</code>
     */
    public java.lang.String getIocAffectedProduct() {
      java.lang.Object ref = iocAffectedProduct_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocAffectedProduct_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	影响平台	
     * </pre>
     *
     * <code>optional string ioc_affected_product = 24;</code>
     */
    public com.google.protobuf.ByteString
        getIocAffectedProductBytes() {
      java.lang.Object ref = iocAffectedProduct_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocAffectedProduct_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_DETAIL_INFO_FIELD_NUMBER = 25;
    private volatile java.lang.Object iocDetailInfo_;
    /**
     * <pre>
     *	威胁详情描述	"威胁详情：漏洞详情：修复方案："
     * </pre>
     *
     * <code>required string ioc_detail_info = 25;</code>
     */
    public boolean hasIocDetailInfo() {
      return ((bitField0_ & 0x01000000) == 0x01000000);
    }
    /**
     * <pre>
     *	威胁详情描述	"威胁详情：漏洞详情：修复方案："
     * </pre>
     *
     * <code>required string ioc_detail_info = 25;</code>
     */
    public java.lang.String getIocDetailInfo() {
      java.lang.Object ref = iocDetailInfo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocDetailInfo_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *	威胁详情描述	"威胁详情：漏洞详情：修复方案："
     * </pre>
     *
     * <code>required string ioc_detail_info = 25;</code>
     */
    public com.google.protobuf.ByteString
        getIocDetailInfoBytes() {
      java.lang.Object ref = iocDetailInfo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocDetailInfo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasIocId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIocValue()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIocCategory()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIocPublicDate()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIocAlertName()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIocCurrentStatus()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIocLastDetection()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIocType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIocTargeted()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIocDetailInfo()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, iocId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, iocValue_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, iocCategory_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeUInt64(4, iocPublicDate_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, iocAlertName_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, iocCurrentStatus_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeBool(7, iocHot_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 8, iocFirstSeen_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, iocLastDetection_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 10, iocRefer_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 11, iocReportData_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 12, iocReportVendor_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 13, iocType_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        output.writeBool(14, iocTargeted_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 15, iocMaliciousFamily_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 16, iocAptCampaign_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 17, iocAptAlias_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 18, iocAptCountry_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 19, iocAptMission_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 20, iocRat_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 21, iocAttackMethod_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 22, iocVul_);
      }
      if (((bitField0_ & 0x00400000) == 0x00400000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 23, iocAffectedSector_);
      }
      if (((bitField0_ & 0x00800000) == 0x00800000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 24, iocAffectedProduct_);
      }
      if (((bitField0_ & 0x01000000) == 0x01000000)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 25, iocDetailInfo_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, iocId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, iocValue_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, iocCategory_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(4, iocPublicDate_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, iocAlertName_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, iocCurrentStatus_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(7, iocHot_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, iocFirstSeen_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, iocLastDetection_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, iocRefer_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, iocReportData_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, iocReportVendor_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, iocType_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(14, iocTargeted_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(15, iocMaliciousFamily_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(16, iocAptCampaign_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(17, iocAptAlias_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(18, iocAptCountry_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(19, iocAptMission_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(20, iocRat_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(21, iocAttackMethod_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(22, iocVul_);
      }
      if (((bitField0_ & 0x00400000) == 0x00400000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(23, iocAffectedSector_);
      }
      if (((bitField0_ & 0x00800000) == 0x00800000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(24, iocAffectedProduct_);
      }
      if (((bitField0_ & 0x01000000) == 0x01000000)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(25, iocDetailInfo_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof IocAlertInfo.IOC_ALERT_INFO)) {
        return super.equals(obj);
      }
      IocAlertInfo.IOC_ALERT_INFO other = (IocAlertInfo.IOC_ALERT_INFO) obj;

      boolean result = true;
      result = result && (hasIocId() == other.hasIocId());
      if (hasIocId()) {
        result = result && getIocId()
            .equals(other.getIocId());
      }
      result = result && (hasIocValue() == other.hasIocValue());
      if (hasIocValue()) {
        result = result && getIocValue()
            .equals(other.getIocValue());
      }
      result = result && (hasIocCategory() == other.hasIocCategory());
      if (hasIocCategory()) {
        result = result && getIocCategory()
            .equals(other.getIocCategory());
      }
      result = result && (hasIocPublicDate() == other.hasIocPublicDate());
      if (hasIocPublicDate()) {
        result = result && (getIocPublicDate()
            == other.getIocPublicDate());
      }
      result = result && (hasIocAlertName() == other.hasIocAlertName());
      if (hasIocAlertName()) {
        result = result && getIocAlertName()
            .equals(other.getIocAlertName());
      }
      result = result && (hasIocCurrentStatus() == other.hasIocCurrentStatus());
      if (hasIocCurrentStatus()) {
        result = result && getIocCurrentStatus()
            .equals(other.getIocCurrentStatus());
      }
      result = result && (hasIocHot() == other.hasIocHot());
      if (hasIocHot()) {
        result = result && (getIocHot()
            == other.getIocHot());
      }
      result = result && (hasIocFirstSeen() == other.hasIocFirstSeen());
      if (hasIocFirstSeen()) {
        result = result && getIocFirstSeen()
            .equals(other.getIocFirstSeen());
      }
      result = result && (hasIocLastDetection() == other.hasIocLastDetection());
      if (hasIocLastDetection()) {
        result = result && getIocLastDetection()
            .equals(other.getIocLastDetection());
      }
      result = result && (hasIocRefer() == other.hasIocRefer());
      if (hasIocRefer()) {
        result = result && getIocRefer()
            .equals(other.getIocRefer());
      }
      result = result && (hasIocReportData() == other.hasIocReportData());
      if (hasIocReportData()) {
        result = result && getIocReportData()
            .equals(other.getIocReportData());
      }
      result = result && (hasIocReportVendor() == other.hasIocReportVendor());
      if (hasIocReportVendor()) {
        result = result && getIocReportVendor()
            .equals(other.getIocReportVendor());
      }
      result = result && (hasIocType() == other.hasIocType());
      if (hasIocType()) {
        result = result && getIocType()
            .equals(other.getIocType());
      }
      result = result && (hasIocTargeted() == other.hasIocTargeted());
      if (hasIocTargeted()) {
        result = result && (getIocTargeted()
            == other.getIocTargeted());
      }
      result = result && (hasIocMaliciousFamily() == other.hasIocMaliciousFamily());
      if (hasIocMaliciousFamily()) {
        result = result && getIocMaliciousFamily()
            .equals(other.getIocMaliciousFamily());
      }
      result = result && (hasIocAptCampaign() == other.hasIocAptCampaign());
      if (hasIocAptCampaign()) {
        result = result && getIocAptCampaign()
            .equals(other.getIocAptCampaign());
      }
      result = result && (hasIocAptAlias() == other.hasIocAptAlias());
      if (hasIocAptAlias()) {
        result = result && getIocAptAlias()
            .equals(other.getIocAptAlias());
      }
      result = result && (hasIocAptCountry() == other.hasIocAptCountry());
      if (hasIocAptCountry()) {
        result = result && getIocAptCountry()
            .equals(other.getIocAptCountry());
      }
      result = result && (hasIocAptMission() == other.hasIocAptMission());
      if (hasIocAptMission()) {
        result = result && getIocAptMission()
            .equals(other.getIocAptMission());
      }
      result = result && (hasIocRat() == other.hasIocRat());
      if (hasIocRat()) {
        result = result && getIocRat()
            .equals(other.getIocRat());
      }
      result = result && (hasIocAttackMethod() == other.hasIocAttackMethod());
      if (hasIocAttackMethod()) {
        result = result && getIocAttackMethod()
            .equals(other.getIocAttackMethod());
      }
      result = result && (hasIocVul() == other.hasIocVul());
      if (hasIocVul()) {
        result = result && getIocVul()
            .equals(other.getIocVul());
      }
      result = result && (hasIocAffectedSector() == other.hasIocAffectedSector());
      if (hasIocAffectedSector()) {
        result = result && getIocAffectedSector()
            .equals(other.getIocAffectedSector());
      }
      result = result && (hasIocAffectedProduct() == other.hasIocAffectedProduct());
      if (hasIocAffectedProduct()) {
        result = result && getIocAffectedProduct()
            .equals(other.getIocAffectedProduct());
      }
      result = result && (hasIocDetailInfo() == other.hasIocDetailInfo());
      if (hasIocDetailInfo()) {
        result = result && getIocDetailInfo()
            .equals(other.getIocDetailInfo());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasIocId()) {
        hash = (37 * hash) + IOC_ID_FIELD_NUMBER;
        hash = (53 * hash) + getIocId().hashCode();
      }
      if (hasIocValue()) {
        hash = (37 * hash) + IOC_VALUE_FIELD_NUMBER;
        hash = (53 * hash) + getIocValue().hashCode();
      }
      if (hasIocCategory()) {
        hash = (37 * hash) + IOC_CATEGORY_FIELD_NUMBER;
        hash = (53 * hash) + getIocCategory().hashCode();
      }
      if (hasIocPublicDate()) {
        hash = (37 * hash) + IOC_PUBLIC_DATE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getIocPublicDate());
      }
      if (hasIocAlertName()) {
        hash = (37 * hash) + IOC_ALERT_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getIocAlertName().hashCode();
      }
      if (hasIocCurrentStatus()) {
        hash = (37 * hash) + IOC_CURRENT_STATUS_FIELD_NUMBER;
        hash = (53 * hash) + getIocCurrentStatus().hashCode();
      }
      if (hasIocHot()) {
        hash = (37 * hash) + IOC_HOT_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIocHot());
      }
      if (hasIocFirstSeen()) {
        hash = (37 * hash) + IOC_FIRST_SEEN_FIELD_NUMBER;
        hash = (53 * hash) + getIocFirstSeen().hashCode();
      }
      if (hasIocLastDetection()) {
        hash = (37 * hash) + IOC_LAST_DETECTION_FIELD_NUMBER;
        hash = (53 * hash) + getIocLastDetection().hashCode();
      }
      if (hasIocRefer()) {
        hash = (37 * hash) + IOC_REFER_FIELD_NUMBER;
        hash = (53 * hash) + getIocRefer().hashCode();
      }
      if (hasIocReportData()) {
        hash = (37 * hash) + IOC_REPORT_DATA_FIELD_NUMBER;
        hash = (53 * hash) + getIocReportData().hashCode();
      }
      if (hasIocReportVendor()) {
        hash = (37 * hash) + IOC_REPORT_VENDOR_FIELD_NUMBER;
        hash = (53 * hash) + getIocReportVendor().hashCode();
      }
      if (hasIocType()) {
        hash = (37 * hash) + IOC_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getIocType().hashCode();
      }
      if (hasIocTargeted()) {
        hash = (37 * hash) + IOC_TARGETED_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIocTargeted());
      }
      if (hasIocMaliciousFamily()) {
        hash = (37 * hash) + IOC_MALICIOUS_FAMILY_FIELD_NUMBER;
        hash = (53 * hash) + getIocMaliciousFamily().hashCode();
      }
      if (hasIocAptCampaign()) {
        hash = (37 * hash) + IOC_APT_CAMPAIGN_FIELD_NUMBER;
        hash = (53 * hash) + getIocAptCampaign().hashCode();
      }
      if (hasIocAptAlias()) {
        hash = (37 * hash) + IOC_APT_ALIAS_FIELD_NUMBER;
        hash = (53 * hash) + getIocAptAlias().hashCode();
      }
      if (hasIocAptCountry()) {
        hash = (37 * hash) + IOC_APT_COUNTRY_FIELD_NUMBER;
        hash = (53 * hash) + getIocAptCountry().hashCode();
      }
      if (hasIocAptMission()) {
        hash = (37 * hash) + IOC_APT_MISSION_FIELD_NUMBER;
        hash = (53 * hash) + getIocAptMission().hashCode();
      }
      if (hasIocRat()) {
        hash = (37 * hash) + IOC_RAT_FIELD_NUMBER;
        hash = (53 * hash) + getIocRat().hashCode();
      }
      if (hasIocAttackMethod()) {
        hash = (37 * hash) + IOC_ATTACK_METHOD_FIELD_NUMBER;
        hash = (53 * hash) + getIocAttackMethod().hashCode();
      }
      if (hasIocVul()) {
        hash = (37 * hash) + IOC_VUL_FIELD_NUMBER;
        hash = (53 * hash) + getIocVul().hashCode();
      }
      if (hasIocAffectedSector()) {
        hash = (37 * hash) + IOC_AFFECTED_SECTOR_FIELD_NUMBER;
        hash = (53 * hash) + getIocAffectedSector().hashCode();
      }
      if (hasIocAffectedProduct()) {
        hash = (37 * hash) + IOC_AFFECTED_PRODUCT_FIELD_NUMBER;
        hash = (53 * hash) + getIocAffectedProduct().hashCode();
      }
      if (hasIocDetailInfo()) {
        hash = (37 * hash) + IOC_DETAIL_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getIocDetailInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static IocAlertInfo.IOC_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IocAlertInfo.IOC_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IocAlertInfo.IOC_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IocAlertInfo.IOC_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IocAlertInfo.IOC_ALERT_INFO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IocAlertInfo.IOC_ALERT_INFO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IocAlertInfo.IOC_ALERT_INFO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static IocAlertInfo.IOC_ALERT_INFO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static IocAlertInfo.IOC_ALERT_INFO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static IocAlertInfo.IOC_ALERT_INFO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static IocAlertInfo.IOC_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static IocAlertInfo.IOC_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(IocAlertInfo.IOC_ALERT_INFO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code IOC_ALERT_INFO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:IOC_ALERT_INFO)
        IocAlertInfo.IOC_ALERT_INFOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return IocAlertInfo.internal_static_IOC_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return IocAlertInfo.internal_static_IOC_ALERT_INFO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                IocAlertInfo.IOC_ALERT_INFO.class, IocAlertInfo.IOC_ALERT_INFO.Builder.class);
      }

      // Construct using IocAlertInfo.IOC_ALERT_INFO.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        iocId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        iocValue_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        iocCategory_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        iocPublicDate_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        iocAlertName_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        iocCurrentStatus_ = "";
        bitField0_ = (bitField0_ & ~0x00000020);
        iocHot_ = false;
        bitField0_ = (bitField0_ & ~0x00000040);
        iocFirstSeen_ = "";
        bitField0_ = (bitField0_ & ~0x00000080);
        iocLastDetection_ = "";
        bitField0_ = (bitField0_ & ~0x00000100);
        iocRefer_ = "";
        bitField0_ = (bitField0_ & ~0x00000200);
        iocReportData_ = "";
        bitField0_ = (bitField0_ & ~0x00000400);
        iocReportVendor_ = "";
        bitField0_ = (bitField0_ & ~0x00000800);
        iocType_ = "";
        bitField0_ = (bitField0_ & ~0x00001000);
        iocTargeted_ = false;
        bitField0_ = (bitField0_ & ~0x00002000);
        iocMaliciousFamily_ = "";
        bitField0_ = (bitField0_ & ~0x00004000);
        iocAptCampaign_ = "";
        bitField0_ = (bitField0_ & ~0x00008000);
        iocAptAlias_ = "";
        bitField0_ = (bitField0_ & ~0x00010000);
        iocAptCountry_ = "";
        bitField0_ = (bitField0_ & ~0x00020000);
        iocAptMission_ = "";
        bitField0_ = (bitField0_ & ~0x00040000);
        iocRat_ = "";
        bitField0_ = (bitField0_ & ~0x00080000);
        iocAttackMethod_ = "";
        bitField0_ = (bitField0_ & ~0x00100000);
        iocVul_ = "";
        bitField0_ = (bitField0_ & ~0x00200000);
        iocAffectedSector_ = "";
        bitField0_ = (bitField0_ & ~0x00400000);
        iocAffectedProduct_ = "";
        bitField0_ = (bitField0_ & ~0x00800000);
        iocDetailInfo_ = "";
        bitField0_ = (bitField0_ & ~0x01000000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return IocAlertInfo.internal_static_IOC_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      public IocAlertInfo.IOC_ALERT_INFO getDefaultInstanceForType() {
        return IocAlertInfo.IOC_ALERT_INFO.getDefaultInstance();
      }

      @java.lang.Override
      public IocAlertInfo.IOC_ALERT_INFO build() {
        IocAlertInfo.IOC_ALERT_INFO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public IocAlertInfo.IOC_ALERT_INFO buildPartial() {
        IocAlertInfo.IOC_ALERT_INFO result = new IocAlertInfo.IOC_ALERT_INFO(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.iocId_ = iocId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.iocValue_ = iocValue_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.iocCategory_ = iocCategory_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.iocPublicDate_ = iocPublicDate_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.iocAlertName_ = iocAlertName_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.iocCurrentStatus_ = iocCurrentStatus_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.iocHot_ = iocHot_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.iocFirstSeen_ = iocFirstSeen_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.iocLastDetection_ = iocLastDetection_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000200;
        }
        result.iocRefer_ = iocRefer_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000400;
        }
        result.iocReportData_ = iocReportData_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000800;
        }
        result.iocReportVendor_ = iocReportVendor_;
        if (((from_bitField0_ & 0x00001000) == 0x00001000)) {
          to_bitField0_ |= 0x00001000;
        }
        result.iocType_ = iocType_;
        if (((from_bitField0_ & 0x00002000) == 0x00002000)) {
          to_bitField0_ |= 0x00002000;
        }
        result.iocTargeted_ = iocTargeted_;
        if (((from_bitField0_ & 0x00004000) == 0x00004000)) {
          to_bitField0_ |= 0x00004000;
        }
        result.iocMaliciousFamily_ = iocMaliciousFamily_;
        if (((from_bitField0_ & 0x00008000) == 0x00008000)) {
          to_bitField0_ |= 0x00008000;
        }
        result.iocAptCampaign_ = iocAptCampaign_;
        if (((from_bitField0_ & 0x00010000) == 0x00010000)) {
          to_bitField0_ |= 0x00010000;
        }
        result.iocAptAlias_ = iocAptAlias_;
        if (((from_bitField0_ & 0x00020000) == 0x00020000)) {
          to_bitField0_ |= 0x00020000;
        }
        result.iocAptCountry_ = iocAptCountry_;
        if (((from_bitField0_ & 0x00040000) == 0x00040000)) {
          to_bitField0_ |= 0x00040000;
        }
        result.iocAptMission_ = iocAptMission_;
        if (((from_bitField0_ & 0x00080000) == 0x00080000)) {
          to_bitField0_ |= 0x00080000;
        }
        result.iocRat_ = iocRat_;
        if (((from_bitField0_ & 0x00100000) == 0x00100000)) {
          to_bitField0_ |= 0x00100000;
        }
        result.iocAttackMethod_ = iocAttackMethod_;
        if (((from_bitField0_ & 0x00200000) == 0x00200000)) {
          to_bitField0_ |= 0x00200000;
        }
        result.iocVul_ = iocVul_;
        if (((from_bitField0_ & 0x00400000) == 0x00400000)) {
          to_bitField0_ |= 0x00400000;
        }
        result.iocAffectedSector_ = iocAffectedSector_;
        if (((from_bitField0_ & 0x00800000) == 0x00800000)) {
          to_bitField0_ |= 0x00800000;
        }
        result.iocAffectedProduct_ = iocAffectedProduct_;
        if (((from_bitField0_ & 0x01000000) == 0x01000000)) {
          to_bitField0_ |= 0x01000000;
        }
        result.iocDetailInfo_ = iocDetailInfo_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof IocAlertInfo.IOC_ALERT_INFO) {
          return mergeFrom((IocAlertInfo.IOC_ALERT_INFO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(IocAlertInfo.IOC_ALERT_INFO other) {
        if (other == IocAlertInfo.IOC_ALERT_INFO.getDefaultInstance()) return this;
        if (other.hasIocId()) {
          bitField0_ |= 0x00000001;
          iocId_ = other.iocId_;
          onChanged();
        }
        if (other.hasIocValue()) {
          bitField0_ |= 0x00000002;
          iocValue_ = other.iocValue_;
          onChanged();
        }
        if (other.hasIocCategory()) {
          bitField0_ |= 0x00000004;
          iocCategory_ = other.iocCategory_;
          onChanged();
        }
        if (other.hasIocPublicDate()) {
          setIocPublicDate(other.getIocPublicDate());
        }
        if (other.hasIocAlertName()) {
          bitField0_ |= 0x00000010;
          iocAlertName_ = other.iocAlertName_;
          onChanged();
        }
        if (other.hasIocCurrentStatus()) {
          bitField0_ |= 0x00000020;
          iocCurrentStatus_ = other.iocCurrentStatus_;
          onChanged();
        }
        if (other.hasIocHot()) {
          setIocHot(other.getIocHot());
        }
        if (other.hasIocFirstSeen()) {
          bitField0_ |= 0x00000080;
          iocFirstSeen_ = other.iocFirstSeen_;
          onChanged();
        }
        if (other.hasIocLastDetection()) {
          bitField0_ |= 0x00000100;
          iocLastDetection_ = other.iocLastDetection_;
          onChanged();
        }
        if (other.hasIocRefer()) {
          bitField0_ |= 0x00000200;
          iocRefer_ = other.iocRefer_;
          onChanged();
        }
        if (other.hasIocReportData()) {
          bitField0_ |= 0x00000400;
          iocReportData_ = other.iocReportData_;
          onChanged();
        }
        if (other.hasIocReportVendor()) {
          bitField0_ |= 0x00000800;
          iocReportVendor_ = other.iocReportVendor_;
          onChanged();
        }
        if (other.hasIocType()) {
          bitField0_ |= 0x00001000;
          iocType_ = other.iocType_;
          onChanged();
        }
        if (other.hasIocTargeted()) {
          setIocTargeted(other.getIocTargeted());
        }
        if (other.hasIocMaliciousFamily()) {
          bitField0_ |= 0x00004000;
          iocMaliciousFamily_ = other.iocMaliciousFamily_;
          onChanged();
        }
        if (other.hasIocAptCampaign()) {
          bitField0_ |= 0x00008000;
          iocAptCampaign_ = other.iocAptCampaign_;
          onChanged();
        }
        if (other.hasIocAptAlias()) {
          bitField0_ |= 0x00010000;
          iocAptAlias_ = other.iocAptAlias_;
          onChanged();
        }
        if (other.hasIocAptCountry()) {
          bitField0_ |= 0x00020000;
          iocAptCountry_ = other.iocAptCountry_;
          onChanged();
        }
        if (other.hasIocAptMission()) {
          bitField0_ |= 0x00040000;
          iocAptMission_ = other.iocAptMission_;
          onChanged();
        }
        if (other.hasIocRat()) {
          bitField0_ |= 0x00080000;
          iocRat_ = other.iocRat_;
          onChanged();
        }
        if (other.hasIocAttackMethod()) {
          bitField0_ |= 0x00100000;
          iocAttackMethod_ = other.iocAttackMethod_;
          onChanged();
        }
        if (other.hasIocVul()) {
          bitField0_ |= 0x00200000;
          iocVul_ = other.iocVul_;
          onChanged();
        }
        if (other.hasIocAffectedSector()) {
          bitField0_ |= 0x00400000;
          iocAffectedSector_ = other.iocAffectedSector_;
          onChanged();
        }
        if (other.hasIocAffectedProduct()) {
          bitField0_ |= 0x00800000;
          iocAffectedProduct_ = other.iocAffectedProduct_;
          onChanged();
        }
        if (other.hasIocDetailInfo()) {
          bitField0_ |= 0x01000000;
          iocDetailInfo_ = other.iocDetailInfo_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        if (!hasIocId()) {
          return false;
        }
        if (!hasIocValue()) {
          return false;
        }
        if (!hasIocCategory()) {
          return false;
        }
        if (!hasIocPublicDate()) {
          return false;
        }
        if (!hasIocAlertName()) {
          return false;
        }
        if (!hasIocCurrentStatus()) {
          return false;
        }
        if (!hasIocLastDetection()) {
          return false;
        }
        if (!hasIocType()) {
          return false;
        }
        if (!hasIocTargeted()) {
          return false;
        }
        if (!hasIocDetailInfo()) {
          return false;
        }
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        IocAlertInfo.IOC_ALERT_INFO parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (IocAlertInfo.IOC_ALERT_INFO) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object iocId_ = "";
      /**
       * <pre>
       *	IOC编号	
       * </pre>
       *
       * <code>required string ioc_id = 1;</code>
       */
      public boolean hasIocId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <pre>
       *	IOC编号	
       * </pre>
       *
       * <code>required string ioc_id = 1;</code>
       */
      public java.lang.String getIocId() {
        java.lang.Object ref = iocId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	IOC编号	
       * </pre>
       *
       * <code>required string ioc_id = 1;</code>
       */
      public com.google.protobuf.ByteString
          getIocIdBytes() {
        java.lang.Object ref = iocId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	IOC编号	
       * </pre>
       *
       * <code>required string ioc_id = 1;</code>
       */
      public Builder setIocId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        iocId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	IOC编号	
       * </pre>
       *
       * <code>required string ioc_id = 1;</code>
       */
      public Builder clearIocId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        iocId_ = getDefaultInstance().getIocId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	IOC编号	
       * </pre>
       *
       * <code>required string ioc_id = 1;</code>
       */
      public Builder setIocIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        iocId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iocValue_ = "";
      /**
       * <pre>
       *	IOC内容	
       * </pre>
       *
       * <code>required string ioc_value = 2;</code>
       */
      public boolean hasIocValue() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <pre>
       *	IOC内容	
       * </pre>
       *
       * <code>required string ioc_value = 2;</code>
       */
      public java.lang.String getIocValue() {
        java.lang.Object ref = iocValue_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocValue_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	IOC内容	
       * </pre>
       *
       * <code>required string ioc_value = 2;</code>
       */
      public com.google.protobuf.ByteString
          getIocValueBytes() {
        java.lang.Object ref = iocValue_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocValue_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	IOC内容	
       * </pre>
       *
       * <code>required string ioc_value = 2;</code>
       */
      public Builder setIocValue(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        iocValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	IOC内容	
       * </pre>
       *
       * <code>required string ioc_value = 2;</code>
       */
      public Builder clearIocValue() {
        bitField0_ = (bitField0_ & ~0x00000002);
        iocValue_ = getDefaultInstance().getIocValue();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	IOC内容	
       * </pre>
       *
       * <code>required string ioc_value = 2;</code>
       */
      public Builder setIocValueBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        iocValue_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iocCategory_ = "";
      /**
       * <pre>
       *	IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
       * </pre>
       *
       * <code>required string ioc_category = 3;</code>
       */
      public boolean hasIocCategory() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <pre>
       *	IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
       * </pre>
       *
       * <code>required string ioc_category = 3;</code>
       */
      public java.lang.String getIocCategory() {
        java.lang.Object ref = iocCategory_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocCategory_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
       * </pre>
       *
       * <code>required string ioc_category = 3;</code>
       */
      public com.google.protobuf.ByteString
          getIocCategoryBytes() {
        java.lang.Object ref = iocCategory_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocCategory_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
       * </pre>
       *
       * <code>required string ioc_category = 3;</code>
       */
      public Builder setIocCategory(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        iocCategory_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
       * </pre>
       *
       * <code>required string ioc_category = 3;</code>
       */
      public Builder clearIocCategory() {
        bitField0_ = (bitField0_ & ~0x00000004);
        iocCategory_ = getDefaultInstance().getIocCategory();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
       * </pre>
       *
       * <code>required string ioc_category = 3;</code>
       */
      public Builder setIocCategoryBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        iocCategory_ = value;
        onChanged();
        return this;
      }

      private long iocPublicDate_ ;
      /**
       * <pre>
       *	IOC发布时间	
       * </pre>
       *
       * <code>required uint64 ioc_public_date = 4;</code>
       */
      public boolean hasIocPublicDate() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <pre>
       *	IOC发布时间	
       * </pre>
       *
       * <code>required uint64 ioc_public_date = 4;</code>
       */
      public long getIocPublicDate() {
        return iocPublicDate_;
      }
      /**
       * <pre>
       *	IOC发布时间	
       * </pre>
       *
       * <code>required uint64 ioc_public_date = 4;</code>
       */
      public Builder setIocPublicDate(long value) {
        bitField0_ |= 0x00000008;
        iocPublicDate_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	IOC发布时间	
       * </pre>
       *
       * <code>required uint64 ioc_public_date = 4;</code>
       */
      public Builder clearIocPublicDate() {
        bitField0_ = (bitField0_ & ~0x00000008);
        iocPublicDate_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object iocAlertName_ = "";
      /**
       * <pre>
       *	IOC告警名称	
       * </pre>
       *
       * <code>required string ioc_alert_name = 5;</code>
       */
      public boolean hasIocAlertName() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <pre>
       *	IOC告警名称	
       * </pre>
       *
       * <code>required string ioc_alert_name = 5;</code>
       */
      public java.lang.String getIocAlertName() {
        java.lang.Object ref = iocAlertName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocAlertName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	IOC告警名称	
       * </pre>
       *
       * <code>required string ioc_alert_name = 5;</code>
       */
      public com.google.protobuf.ByteString
          getIocAlertNameBytes() {
        java.lang.Object ref = iocAlertName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocAlertName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	IOC告警名称	
       * </pre>
       *
       * <code>required string ioc_alert_name = 5;</code>
       */
      public Builder setIocAlertName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        iocAlertName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	IOC告警名称	
       * </pre>
       *
       * <code>required string ioc_alert_name = 5;</code>
       */
      public Builder clearIocAlertName() {
        bitField0_ = (bitField0_ & ~0x00000010);
        iocAlertName_ = getDefaultInstance().getIocAlertName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	IOC告警名称	
       * </pre>
       *
       * <code>required string ioc_alert_name = 5;</code>
       */
      public Builder setIocAlertNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        iocAlertName_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iocCurrentStatus_ = "";
      /**
       * <pre>
       *	IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
       * </pre>
       *
       * <code>required string ioc_current_status = 6;</code>
       */
      public boolean hasIocCurrentStatus() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <pre>
       *	IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
       * </pre>
       *
       * <code>required string ioc_current_status = 6;</code>
       */
      public java.lang.String getIocCurrentStatus() {
        java.lang.Object ref = iocCurrentStatus_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocCurrentStatus_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
       * </pre>
       *
       * <code>required string ioc_current_status = 6;</code>
       */
      public com.google.protobuf.ByteString
          getIocCurrentStatusBytes() {
        java.lang.Object ref = iocCurrentStatus_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocCurrentStatus_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
       * </pre>
       *
       * <code>required string ioc_current_status = 6;</code>
       */
      public Builder setIocCurrentStatus(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        iocCurrentStatus_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
       * </pre>
       *
       * <code>required string ioc_current_status = 6;</code>
       */
      public Builder clearIocCurrentStatus() {
        bitField0_ = (bitField0_ & ~0x00000020);
        iocCurrentStatus_ = getDefaultInstance().getIocCurrentStatus();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
       * </pre>
       *
       * <code>required string ioc_current_status = 6;</code>
       */
      public Builder setIocCurrentStatusBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        iocCurrentStatus_ = value;
        onChanged();
        return this;
      }

      private boolean iocHot_ ;
      /**
       * <pre>
       *	IOC热点状态	True/False 
       * </pre>
       *
       * <code>optional bool ioc_hot = 7;</code>
       */
      public boolean hasIocHot() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <pre>
       *	IOC热点状态	True/False 
       * </pre>
       *
       * <code>optional bool ioc_hot = 7;</code>
       */
      public boolean getIocHot() {
        return iocHot_;
      }
      /**
       * <pre>
       *	IOC热点状态	True/False 
       * </pre>
       *
       * <code>optional bool ioc_hot = 7;</code>
       */
      public Builder setIocHot(boolean value) {
        bitField0_ |= 0x00000040;
        iocHot_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	IOC热点状态	True/False 
       * </pre>
       *
       * <code>optional bool ioc_hot = 7;</code>
       */
      public Builder clearIocHot() {
        bitField0_ = (bitField0_ & ~0x00000040);
        iocHot_ = false;
        onChanged();
        return this;
      }

      private java.lang.Object iocFirstSeen_ = "";
      /**
       * <pre>
       *	首次发现时间	情报的首次发现时间
       * </pre>
       *
       * <code>optional string ioc_first_seen = 8;</code>
       */
      public boolean hasIocFirstSeen() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <pre>
       *	首次发现时间	情报的首次发现时间
       * </pre>
       *
       * <code>optional string ioc_first_seen = 8;</code>
       */
      public java.lang.String getIocFirstSeen() {
        java.lang.Object ref = iocFirstSeen_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocFirstSeen_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	首次发现时间	情报的首次发现时间
       * </pre>
       *
       * <code>optional string ioc_first_seen = 8;</code>
       */
      public com.google.protobuf.ByteString
          getIocFirstSeenBytes() {
        java.lang.Object ref = iocFirstSeen_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocFirstSeen_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	首次发现时间	情报的首次发现时间
       * </pre>
       *
       * <code>optional string ioc_first_seen = 8;</code>
       */
      public Builder setIocFirstSeen(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        iocFirstSeen_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	首次发现时间	情报的首次发现时间
       * </pre>
       *
       * <code>optional string ioc_first_seen = 8;</code>
       */
      public Builder clearIocFirstSeen() {
        bitField0_ = (bitField0_ & ~0x00000080);
        iocFirstSeen_ = getDefaultInstance().getIocFirstSeen();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	首次发现时间	情报的首次发现时间
       * </pre>
       *
       * <code>optional string ioc_first_seen = 8;</code>
       */
      public Builder setIocFirstSeenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        iocFirstSeen_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iocLastDetection_ = "";
      /**
       * <pre>
       *	最近检测时间	最后一次检测到攻击的时间
       * </pre>
       *
       * <code>required string ioc_last_detection = 9;</code>
       */
      public boolean hasIocLastDetection() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <pre>
       *	最近检测时间	最后一次检测到攻击的时间
       * </pre>
       *
       * <code>required string ioc_last_detection = 9;</code>
       */
      public java.lang.String getIocLastDetection() {
        java.lang.Object ref = iocLastDetection_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocLastDetection_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	最近检测时间	最后一次检测到攻击的时间
       * </pre>
       *
       * <code>required string ioc_last_detection = 9;</code>
       */
      public com.google.protobuf.ByteString
          getIocLastDetectionBytes() {
        java.lang.Object ref = iocLastDetection_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocLastDetection_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	最近检测时间	最后一次检测到攻击的时间
       * </pre>
       *
       * <code>required string ioc_last_detection = 9;</code>
       */
      public Builder setIocLastDetection(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        iocLastDetection_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	最近检测时间	最后一次检测到攻击的时间
       * </pre>
       *
       * <code>required string ioc_last_detection = 9;</code>
       */
      public Builder clearIocLastDetection() {
        bitField0_ = (bitField0_ & ~0x00000100);
        iocLastDetection_ = getDefaultInstance().getIocLastDetection();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	最近检测时间	最后一次检测到攻击的时间
       * </pre>
       *
       * <code>required string ioc_last_detection = 9;</code>
       */
      public Builder setIocLastDetectionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        iocLastDetection_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iocRefer_ = "";
      /**
       * <pre>
       *	参考文档报告	
       * </pre>
       *
       * <code>optional string ioc_refer = 10;</code>
       */
      public boolean hasIocRefer() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <pre>
       *	参考文档报告	
       * </pre>
       *
       * <code>optional string ioc_refer = 10;</code>
       */
      public java.lang.String getIocRefer() {
        java.lang.Object ref = iocRefer_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocRefer_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	参考文档报告	
       * </pre>
       *
       * <code>optional string ioc_refer = 10;</code>
       */
      public com.google.protobuf.ByteString
          getIocReferBytes() {
        java.lang.Object ref = iocRefer_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocRefer_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	参考文档报告	
       * </pre>
       *
       * <code>optional string ioc_refer = 10;</code>
       */
      public Builder setIocRefer(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
        iocRefer_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	参考文档报告	
       * </pre>
       *
       * <code>optional string ioc_refer = 10;</code>
       */
      public Builder clearIocRefer() {
        bitField0_ = (bitField0_ & ~0x00000200);
        iocRefer_ = getDefaultInstance().getIocRefer();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	参考文档报告	
       * </pre>
       *
       * <code>optional string ioc_refer = 10;</code>
       */
      public Builder setIocReferBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
        iocRefer_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iocReportData_ = "";
      /**
       * <pre>
       *	报告发布时间	
       * </pre>
       *
       * <code>optional string ioc_report_data = 11;</code>
       */
      public boolean hasIocReportData() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <pre>
       *	报告发布时间	
       * </pre>
       *
       * <code>optional string ioc_report_data = 11;</code>
       */
      public java.lang.String getIocReportData() {
        java.lang.Object ref = iocReportData_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocReportData_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	报告发布时间	
       * </pre>
       *
       * <code>optional string ioc_report_data = 11;</code>
       */
      public com.google.protobuf.ByteString
          getIocReportDataBytes() {
        java.lang.Object ref = iocReportData_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocReportData_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	报告发布时间	
       * </pre>
       *
       * <code>optional string ioc_report_data = 11;</code>
       */
      public Builder setIocReportData(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        iocReportData_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	报告发布时间	
       * </pre>
       *
       * <code>optional string ioc_report_data = 11;</code>
       */
      public Builder clearIocReportData() {
        bitField0_ = (bitField0_ & ~0x00000400);
        iocReportData_ = getDefaultInstance().getIocReportData();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	报告发布时间	
       * </pre>
       *
       * <code>optional string ioc_report_data = 11;</code>
       */
      public Builder setIocReportDataBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        iocReportData_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iocReportVendor_ = "";
      /**
       * <pre>
       *	报告发布厂商	
       * </pre>
       *
       * <code>optional string ioc_report_vendor = 12;</code>
       */
      public boolean hasIocReportVendor() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <pre>
       *	报告发布厂商	
       * </pre>
       *
       * <code>optional string ioc_report_vendor = 12;</code>
       */
      public java.lang.String getIocReportVendor() {
        java.lang.Object ref = iocReportVendor_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocReportVendor_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	报告发布厂商	
       * </pre>
       *
       * <code>optional string ioc_report_vendor = 12;</code>
       */
      public com.google.protobuf.ByteString
          getIocReportVendorBytes() {
        java.lang.Object ref = iocReportVendor_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocReportVendor_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	报告发布厂商	
       * </pre>
       *
       * <code>optional string ioc_report_vendor = 12;</code>
       */
      public Builder setIocReportVendor(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        iocReportVendor_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	报告发布厂商	
       * </pre>
       *
       * <code>optional string ioc_report_vendor = 12;</code>
       */
      public Builder clearIocReportVendor() {
        bitField0_ = (bitField0_ & ~0x00000800);
        iocReportVendor_ = getDefaultInstance().getIocReportVendor();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	报告发布厂商	
       * </pre>
       *
       * <code>optional string ioc_report_vendor = 12;</code>
       */
      public Builder setIocReportVendorBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        iocReportVendor_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iocType_ = "";
      /**
       * <pre>
       *	IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
       * </pre>
       *
       * <code>required string ioc_type = 13;</code>
       */
      public boolean hasIocType() {
        return ((bitField0_ & 0x00001000) == 0x00001000);
      }
      /**
       * <pre>
       *	IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
       * </pre>
       *
       * <code>required string ioc_type = 13;</code>
       */
      public java.lang.String getIocType() {
        java.lang.Object ref = iocType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocType_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
       * </pre>
       *
       * <code>required string ioc_type = 13;</code>
       */
      public com.google.protobuf.ByteString
          getIocTypeBytes() {
        java.lang.Object ref = iocType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
       * </pre>
       *
       * <code>required string ioc_type = 13;</code>
       */
      public Builder setIocType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00001000;
        iocType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
       * </pre>
       *
       * <code>required string ioc_type = 13;</code>
       */
      public Builder clearIocType() {
        bitField0_ = (bitField0_ & ~0x00001000);
        iocType_ = getDefaultInstance().getIocType();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
       * </pre>
       *
       * <code>required string ioc_type = 13;</code>
       */
      public Builder setIocTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00001000;
        iocType_ = value;
        onChanged();
        return this;
      }

      private boolean iocTargeted_ ;
      /**
       * <pre>
       *	定向攻击标识	True/False 
       * </pre>
       *
       * <code>required bool ioc_targeted = 14;</code>
       */
      public boolean hasIocTargeted() {
        return ((bitField0_ & 0x00002000) == 0x00002000);
      }
      /**
       * <pre>
       *	定向攻击标识	True/False 
       * </pre>
       *
       * <code>required bool ioc_targeted = 14;</code>
       */
      public boolean getIocTargeted() {
        return iocTargeted_;
      }
      /**
       * <pre>
       *	定向攻击标识	True/False 
       * </pre>
       *
       * <code>required bool ioc_targeted = 14;</code>
       */
      public Builder setIocTargeted(boolean value) {
        bitField0_ |= 0x00002000;
        iocTargeted_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	定向攻击标识	True/False 
       * </pre>
       *
       * <code>required bool ioc_targeted = 14;</code>
       */
      public Builder clearIocTargeted() {
        bitField0_ = (bitField0_ & ~0x00002000);
        iocTargeted_ = false;
        onChanged();
        return this;
      }

      private java.lang.Object iocMaliciousFamily_ = "";
      /**
       * <pre>
       *	恶意代码家族	
       * </pre>
       *
       * <code>optional string ioc_malicious_family = 15;</code>
       */
      public boolean hasIocMaliciousFamily() {
        return ((bitField0_ & 0x00004000) == 0x00004000);
      }
      /**
       * <pre>
       *	恶意代码家族	
       * </pre>
       *
       * <code>optional string ioc_malicious_family = 15;</code>
       */
      public java.lang.String getIocMaliciousFamily() {
        java.lang.Object ref = iocMaliciousFamily_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocMaliciousFamily_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	恶意代码家族	
       * </pre>
       *
       * <code>optional string ioc_malicious_family = 15;</code>
       */
      public com.google.protobuf.ByteString
          getIocMaliciousFamilyBytes() {
        java.lang.Object ref = iocMaliciousFamily_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocMaliciousFamily_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	恶意代码家族	
       * </pre>
       *
       * <code>optional string ioc_malicious_family = 15;</code>
       */
      public Builder setIocMaliciousFamily(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00004000;
        iocMaliciousFamily_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	恶意代码家族	
       * </pre>
       *
       * <code>optional string ioc_malicious_family = 15;</code>
       */
      public Builder clearIocMaliciousFamily() {
        bitField0_ = (bitField0_ & ~0x00004000);
        iocMaliciousFamily_ = getDefaultInstance().getIocMaliciousFamily();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	恶意代码家族	
       * </pre>
       *
       * <code>optional string ioc_malicious_family = 15;</code>
       */
      public Builder setIocMaliciousFamilyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00004000;
        iocMaliciousFamily_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iocAptCampaign_ = "";
      /**
       * <pre>
       *	APT组织名称	对应actor、primary_name
       * </pre>
       *
       * <code>optional string ioc_apt_campaign = 16;</code>
       */
      public boolean hasIocAptCampaign() {
        return ((bitField0_ & 0x00008000) == 0x00008000);
      }
      /**
       * <pre>
       *	APT组织名称	对应actor、primary_name
       * </pre>
       *
       * <code>optional string ioc_apt_campaign = 16;</code>
       */
      public java.lang.String getIocAptCampaign() {
        java.lang.Object ref = iocAptCampaign_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocAptCampaign_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	APT组织名称	对应actor、primary_name
       * </pre>
       *
       * <code>optional string ioc_apt_campaign = 16;</code>
       */
      public com.google.protobuf.ByteString
          getIocAptCampaignBytes() {
        java.lang.Object ref = iocAptCampaign_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocAptCampaign_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	APT组织名称	对应actor、primary_name
       * </pre>
       *
       * <code>optional string ioc_apt_campaign = 16;</code>
       */
      public Builder setIocAptCampaign(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00008000;
        iocAptCampaign_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	APT组织名称	对应actor、primary_name
       * </pre>
       *
       * <code>optional string ioc_apt_campaign = 16;</code>
       */
      public Builder clearIocAptCampaign() {
        bitField0_ = (bitField0_ & ~0x00008000);
        iocAptCampaign_ = getDefaultInstance().getIocAptCampaign();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	APT组织名称	对应actor、primary_name
       * </pre>
       *
       * <code>optional string ioc_apt_campaign = 16;</code>
       */
      public Builder setIocAptCampaignBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00008000;
        iocAptCampaign_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iocAptAlias_ = "";
      /**
       * <pre>
       *	APT组织别名	
       * </pre>
       *
       * <code>optional string ioc_apt_alias = 17;</code>
       */
      public boolean hasIocAptAlias() {
        return ((bitField0_ & 0x00010000) == 0x00010000);
      }
      /**
       * <pre>
       *	APT组织别名	
       * </pre>
       *
       * <code>optional string ioc_apt_alias = 17;</code>
       */
      public java.lang.String getIocAptAlias() {
        java.lang.Object ref = iocAptAlias_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocAptAlias_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	APT组织别名	
       * </pre>
       *
       * <code>optional string ioc_apt_alias = 17;</code>
       */
      public com.google.protobuf.ByteString
          getIocAptAliasBytes() {
        java.lang.Object ref = iocAptAlias_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocAptAlias_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	APT组织别名	
       * </pre>
       *
       * <code>optional string ioc_apt_alias = 17;</code>
       */
      public Builder setIocAptAlias(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00010000;
        iocAptAlias_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	APT组织别名	
       * </pre>
       *
       * <code>optional string ioc_apt_alias = 17;</code>
       */
      public Builder clearIocAptAlias() {
        bitField0_ = (bitField0_ & ~0x00010000);
        iocAptAlias_ = getDefaultInstance().getIocAptAlias();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	APT组织别名	
       * </pre>
       *
       * <code>optional string ioc_apt_alias = 17;</code>
       */
      public Builder setIocAptAliasBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00010000;
        iocAptAlias_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iocAptCountry_ = "";
      /**
       * <pre>
       *	APT所属国家	
       * </pre>
       *
       * <code>optional string ioc_apt_country = 18;</code>
       */
      public boolean hasIocAptCountry() {
        return ((bitField0_ & 0x00020000) == 0x00020000);
      }
      /**
       * <pre>
       *	APT所属国家	
       * </pre>
       *
       * <code>optional string ioc_apt_country = 18;</code>
       */
      public java.lang.String getIocAptCountry() {
        java.lang.Object ref = iocAptCountry_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocAptCountry_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	APT所属国家	
       * </pre>
       *
       * <code>optional string ioc_apt_country = 18;</code>
       */
      public com.google.protobuf.ByteString
          getIocAptCountryBytes() {
        java.lang.Object ref = iocAptCountry_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocAptCountry_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	APT所属国家	
       * </pre>
       *
       * <code>optional string ioc_apt_country = 18;</code>
       */
      public Builder setIocAptCountry(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00020000;
        iocAptCountry_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	APT所属国家	
       * </pre>
       *
       * <code>optional string ioc_apt_country = 18;</code>
       */
      public Builder clearIocAptCountry() {
        bitField0_ = (bitField0_ & ~0x00020000);
        iocAptCountry_ = getDefaultInstance().getIocAptCountry();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	APT所属国家	
       * </pre>
       *
       * <code>optional string ioc_apt_country = 18;</code>
       */
      public Builder setIocAptCountryBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00020000;
        iocAptCountry_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iocAptMission_ = "";
      /**
       * <pre>
       *	APT行动名称	
       * </pre>
       *
       * <code>optional string ioc_apt_mission = 19;</code>
       */
      public boolean hasIocAptMission() {
        return ((bitField0_ & 0x00040000) == 0x00040000);
      }
      /**
       * <pre>
       *	APT行动名称	
       * </pre>
       *
       * <code>optional string ioc_apt_mission = 19;</code>
       */
      public java.lang.String getIocAptMission() {
        java.lang.Object ref = iocAptMission_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocAptMission_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	APT行动名称	
       * </pre>
       *
       * <code>optional string ioc_apt_mission = 19;</code>
       */
      public com.google.protobuf.ByteString
          getIocAptMissionBytes() {
        java.lang.Object ref = iocAptMission_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocAptMission_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	APT行动名称	
       * </pre>
       *
       * <code>optional string ioc_apt_mission = 19;</code>
       */
      public Builder setIocAptMission(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00040000;
        iocAptMission_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	APT行动名称	
       * </pre>
       *
       * <code>optional string ioc_apt_mission = 19;</code>
       */
      public Builder clearIocAptMission() {
        bitField0_ = (bitField0_ & ~0x00040000);
        iocAptMission_ = getDefaultInstance().getIocAptMission();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	APT行动名称	
       * </pre>
       *
       * <code>optional string ioc_apt_mission = 19;</code>
       */
      public Builder setIocAptMissionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00040000;
        iocAptMission_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iocRat_ = "";
      /**
       * <pre>
       *	远控工具	
       * </pre>
       *
       * <code>optional string ioc_rat = 20;</code>
       */
      public boolean hasIocRat() {
        return ((bitField0_ & 0x00080000) == 0x00080000);
      }
      /**
       * <pre>
       *	远控工具	
       * </pre>
       *
       * <code>optional string ioc_rat = 20;</code>
       */
      public java.lang.String getIocRat() {
        java.lang.Object ref = iocRat_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocRat_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	远控工具	
       * </pre>
       *
       * <code>optional string ioc_rat = 20;</code>
       */
      public com.google.protobuf.ByteString
          getIocRatBytes() {
        java.lang.Object ref = iocRat_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocRat_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	远控工具	
       * </pre>
       *
       * <code>optional string ioc_rat = 20;</code>
       */
      public Builder setIocRat(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00080000;
        iocRat_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	远控工具	
       * </pre>
       *
       * <code>optional string ioc_rat = 20;</code>
       */
      public Builder clearIocRat() {
        bitField0_ = (bitField0_ & ~0x00080000);
        iocRat_ = getDefaultInstance().getIocRat();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	远控工具	
       * </pre>
       *
       * <code>optional string ioc_rat = 20;</code>
       */
      public Builder setIocRatBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00080000;
        iocRat_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iocAttackMethod_ = "";
      /**
       * <pre>
       *	攻击手法	WEB攻击渗透、…
       * </pre>
       *
       * <code>optional string ioc_attack_method = 21;</code>
       */
      public boolean hasIocAttackMethod() {
        return ((bitField0_ & 0x00100000) == 0x00100000);
      }
      /**
       * <pre>
       *	攻击手法	WEB攻击渗透、…
       * </pre>
       *
       * <code>optional string ioc_attack_method = 21;</code>
       */
      public java.lang.String getIocAttackMethod() {
        java.lang.Object ref = iocAttackMethod_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocAttackMethod_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	攻击手法	WEB攻击渗透、…
       * </pre>
       *
       * <code>optional string ioc_attack_method = 21;</code>
       */
      public com.google.protobuf.ByteString
          getIocAttackMethodBytes() {
        java.lang.Object ref = iocAttackMethod_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocAttackMethod_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	攻击手法	WEB攻击渗透、…
       * </pre>
       *
       * <code>optional string ioc_attack_method = 21;</code>
       */
      public Builder setIocAttackMethod(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00100000;
        iocAttackMethod_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	攻击手法	WEB攻击渗透、…
       * </pre>
       *
       * <code>optional string ioc_attack_method = 21;</code>
       */
      public Builder clearIocAttackMethod() {
        bitField0_ = (bitField0_ & ~0x00100000);
        iocAttackMethod_ = getDefaultInstance().getIocAttackMethod();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	攻击手法	WEB攻击渗透、…
       * </pre>
       *
       * <code>optional string ioc_attack_method = 21;</code>
       */
      public Builder setIocAttackMethodBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00100000;
        iocAttackMethod_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iocVul_ = "";
      /**
       * <pre>
       *	关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioc_vul = 22;</code>
       */
      public boolean hasIocVul() {
        return ((bitField0_ & 0x00200000) == 0x00200000);
      }
      /**
       * <pre>
       *	关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioc_vul = 22;</code>
       */
      public java.lang.String getIocVul() {
        java.lang.Object ref = iocVul_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocVul_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioc_vul = 22;</code>
       */
      public com.google.protobuf.ByteString
          getIocVulBytes() {
        java.lang.Object ref = iocVul_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocVul_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioc_vul = 22;</code>
       */
      public Builder setIocVul(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00200000;
        iocVul_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioc_vul = 22;</code>
       */
      public Builder clearIocVul() {
        bitField0_ = (bitField0_ & ~0x00200000);
        iocVul_ = getDefaultInstance().getIocVul();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioc_vul = 22;</code>
       */
      public Builder setIocVulBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00200000;
        iocVul_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iocAffectedSector_ = "";
      /**
       * <pre>
       *	影响行业	
       * </pre>
       *
       * <code>optional string ioc_affected_sector = 23;</code>
       */
      public boolean hasIocAffectedSector() {
        return ((bitField0_ & 0x00400000) == 0x00400000);
      }
      /**
       * <pre>
       *	影响行业	
       * </pre>
       *
       * <code>optional string ioc_affected_sector = 23;</code>
       */
      public java.lang.String getIocAffectedSector() {
        java.lang.Object ref = iocAffectedSector_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocAffectedSector_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	影响行业	
       * </pre>
       *
       * <code>optional string ioc_affected_sector = 23;</code>
       */
      public com.google.protobuf.ByteString
          getIocAffectedSectorBytes() {
        java.lang.Object ref = iocAffectedSector_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocAffectedSector_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	影响行业	
       * </pre>
       *
       * <code>optional string ioc_affected_sector = 23;</code>
       */
      public Builder setIocAffectedSector(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00400000;
        iocAffectedSector_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	影响行业	
       * </pre>
       *
       * <code>optional string ioc_affected_sector = 23;</code>
       */
      public Builder clearIocAffectedSector() {
        bitField0_ = (bitField0_ & ~0x00400000);
        iocAffectedSector_ = getDefaultInstance().getIocAffectedSector();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	影响行业	
       * </pre>
       *
       * <code>optional string ioc_affected_sector = 23;</code>
       */
      public Builder setIocAffectedSectorBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00400000;
        iocAffectedSector_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iocAffectedProduct_ = "";
      /**
       * <pre>
       *	影响平台	
       * </pre>
       *
       * <code>optional string ioc_affected_product = 24;</code>
       */
      public boolean hasIocAffectedProduct() {
        return ((bitField0_ & 0x00800000) == 0x00800000);
      }
      /**
       * <pre>
       *	影响平台	
       * </pre>
       *
       * <code>optional string ioc_affected_product = 24;</code>
       */
      public java.lang.String getIocAffectedProduct() {
        java.lang.Object ref = iocAffectedProduct_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocAffectedProduct_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	影响平台	
       * </pre>
       *
       * <code>optional string ioc_affected_product = 24;</code>
       */
      public com.google.protobuf.ByteString
          getIocAffectedProductBytes() {
        java.lang.Object ref = iocAffectedProduct_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocAffectedProduct_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	影响平台	
       * </pre>
       *
       * <code>optional string ioc_affected_product = 24;</code>
       */
      public Builder setIocAffectedProduct(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00800000;
        iocAffectedProduct_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	影响平台	
       * </pre>
       *
       * <code>optional string ioc_affected_product = 24;</code>
       */
      public Builder clearIocAffectedProduct() {
        bitField0_ = (bitField0_ & ~0x00800000);
        iocAffectedProduct_ = getDefaultInstance().getIocAffectedProduct();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	影响平台	
       * </pre>
       *
       * <code>optional string ioc_affected_product = 24;</code>
       */
      public Builder setIocAffectedProductBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00800000;
        iocAffectedProduct_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object iocDetailInfo_ = "";
      /**
       * <pre>
       *	威胁详情描述	"威胁详情：漏洞详情：修复方案："
       * </pre>
       *
       * <code>required string ioc_detail_info = 25;</code>
       */
      public boolean hasIocDetailInfo() {
        return ((bitField0_ & 0x01000000) == 0x01000000);
      }
      /**
       * <pre>
       *	威胁详情描述	"威胁详情：漏洞详情：修复方案："
       * </pre>
       *
       * <code>required string ioc_detail_info = 25;</code>
       */
      public java.lang.String getIocDetailInfo() {
        java.lang.Object ref = iocDetailInfo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocDetailInfo_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *	威胁详情描述	"威胁详情：漏洞详情：修复方案："
       * </pre>
       *
       * <code>required string ioc_detail_info = 25;</code>
       */
      public com.google.protobuf.ByteString
          getIocDetailInfoBytes() {
        java.lang.Object ref = iocDetailInfo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocDetailInfo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *	威胁详情描述	"威胁详情：漏洞详情：修复方案："
       * </pre>
       *
       * <code>required string ioc_detail_info = 25;</code>
       */
      public Builder setIocDetailInfo(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x01000000;
        iocDetailInfo_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁详情描述	"威胁详情：漏洞详情：修复方案："
       * </pre>
       *
       * <code>required string ioc_detail_info = 25;</code>
       */
      public Builder clearIocDetailInfo() {
        bitField0_ = (bitField0_ & ~0x01000000);
        iocDetailInfo_ = getDefaultInstance().getIocDetailInfo();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *	威胁详情描述	"威胁详情：漏洞详情：修复方案："
       * </pre>
       *
       * <code>required string ioc_detail_info = 25;</code>
       */
      public Builder setIocDetailInfoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x01000000;
        iocDetailInfo_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:IOC_ALERT_INFO)
    }

    // @@protoc_insertion_point(class_scope:IOC_ALERT_INFO)
    private static final IocAlertInfo.IOC_ALERT_INFO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new IocAlertInfo.IOC_ALERT_INFO();
    }

    public static IocAlertInfo.IOC_ALERT_INFO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<IOC_ALERT_INFO>
        PARSER = new com.google.protobuf.AbstractParser<IOC_ALERT_INFO>() {
      @java.lang.Override
      public IOC_ALERT_INFO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new IOC_ALERT_INFO(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<IOC_ALERT_INFO> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IOC_ALERT_INFO> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public IocAlertInfo.IOC_ALERT_INFO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_IOC_ALERT_INFO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_IOC_ALERT_INFO_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\024IOC_ALERT_INFO.proto\"\334\004\n\016IOC_ALERT_INF" +
      "O\022\016\n\006ioc_id\030\001 \002(\t\022\021\n\tioc_value\030\002 \002(\t\022\024\n\014" +
      "ioc_category\030\003 \002(\t\022\027\n\017ioc_public_date\030\004 " +
      "\002(\004\022\026\n\016ioc_alert_name\030\005 \002(\t\022\032\n\022ioc_curre" +
      "nt_status\030\006 \002(\t\022\017\n\007ioc_hot\030\007 \001(\010\022\026\n\016ioc_" +
      "first_seen\030\010 \001(\t\022\032\n\022ioc_last_detection\030\t" +
      " \002(\t\022\021\n\tioc_refer\030\n \001(\t\022\027\n\017ioc_report_da" +
      "ta\030\013 \001(\t\022\031\n\021ioc_report_vendor\030\014 \001(\t\022\020\n\010i" +
      "oc_type\030\r \002(\t\022\024\n\014ioc_targeted\030\016 \002(\010\022\034\n\024i" +
      "oc_malicious_family\030\017 \001(\t\022\030\n\020ioc_apt_cam" +
      "paign\030\020 \001(\t\022\025\n\rioc_apt_alias\030\021 \001(\t\022\027\n\017io" +
      "c_apt_country\030\022 \001(\t\022\027\n\017ioc_apt_mission\030\023" +
      " \001(\t\022\017\n\007ioc_rat\030\024 \001(\t\022\031\n\021ioc_attack_meth" +
      "od\030\025 \001(\t\022\017\n\007ioc_vul\030\026 \001(\t\022\033\n\023ioc_affecte" +
      "d_sector\030\027 \001(\t\022\034\n\024ioc_affected_product\030\030" +
      " \001(\t\022\027\n\017ioc_detail_info\030\031 \002(\tB\016B\014IocAler" +
      "tInfo"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_IOC_ALERT_INFO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_IOC_ALERT_INFO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_IOC_ALERT_INFO_descriptor,
        new java.lang.String[] { "IocId", "IocValue", "IocCategory", "IocPublicDate", "IocAlertName", "IocCurrentStatus", "IocHot", "IocFirstSeen", "IocLastDetection", "IocRefer", "IocReportData", "IocReportVendor", "IocType", "IocTargeted", "IocMaliciousFamily", "IocAptCampaign", "IocAptAlias", "IocAptCountry", "IocAptMission", "IocRat", "IocAttackMethod", "IocVul", "IocAffectedSector", "IocAffectedProduct", "IocDetailInfo", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
