// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: X509CerInfo.proto
package com.geeksec.proto.output;

public final class X509CerInfoOuterClass {
  private X509CerInfoOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface X509CerInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:X509CerInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional uint64 ProtabID = 1;</code>
     */
    boolean hasProtabID();
    /**
     * <code>optional uint64 ProtabID = 1;</code>
     */
    long getProtabID();

    /**
     * <code>optional uint32 ver = 2;</code>
     */
    boolean hasVer();
    /**
     * <code>optional uint32 ver = 2;</code>
     */
    int getVer();

    /**
     * <code>optional bytes srvNum = 3;</code>
     */
    boolean hasSrvNum();
    /**
     * <code>optional bytes srvNum = 3;</code>
     */
    com.google.protobuf.ByteString getSrvNum();

    /**
     * <code>optional uint32 issDataLen = 4;</code>
     */
    boolean hasIssDataLen();
    /**
     * <code>optional uint32 issDataLen = 4;</code>
     */
    int getIssDataLen();

    /**
     * <code>optional bytes issComName = 5;</code>
     */
    boolean hasIssComName();
    /**
     * <code>optional bytes issComName = 5;</code>
     */
    com.google.protobuf.ByteString getIssComName();

    /**
     * <code>optional bytes issConName = 6;</code>
     */
    boolean hasIssConName();
    /**
     * <code>optional bytes issConName = 6;</code>
     */
    com.google.protobuf.ByteString getIssConName();

    /**
     * <code>optional bytes issLoaName = 7;</code>
     */
    boolean hasIssLoaName();
    /**
     * <code>optional bytes issLoaName = 7;</code>
     */
    com.google.protobuf.ByteString getIssLoaName();

    /**
     * <code>optional bytes issStaOrProName = 8;</code>
     */
    boolean hasIssStaOrProName();
    /**
     * <code>optional bytes issStaOrProName = 8;</code>
     */
    com.google.protobuf.ByteString getIssStaOrProName();

    /**
     * <code>optional bytes issStrAddr = 9;</code>
     */
    boolean hasIssStrAddr();
    /**
     * <code>optional bytes issStrAddr = 9;</code>
     */
    com.google.protobuf.ByteString getIssStrAddr();

    /**
     * <code>optional bytes issOrgName = 10;</code>
     */
    boolean hasIssOrgName();
    /**
     * <code>optional bytes issOrgName = 10;</code>
     */
    com.google.protobuf.ByteString getIssOrgName();

    /**
     * <code>optional bytes issOrgUniName = 11;</code>
     */
    boolean hasIssOrgUniName();
    /**
     * <code>optional bytes issOrgUniName = 11;</code>
     */
    com.google.protobuf.ByteString getIssOrgUniName();

    /**
     * <code>optional bytes issPosOffBox = 12;</code>
     */
    boolean hasIssPosOffBox();
    /**
     * <code>optional bytes issPosOffBox = 12;</code>
     */
    com.google.protobuf.ByteString getIssPosOffBox();

    /**
     * <code>optional bytes subComName = 13;</code>
     */
    boolean hasSubComName();
    /**
     * <code>optional bytes subComName = 13;</code>
     */
    com.google.protobuf.ByteString getSubComName();

    /**
     * <code>optional bytes subConName = 14;</code>
     */
    boolean hasSubConName();
    /**
     * <code>optional bytes subConName = 14;</code>
     */
    com.google.protobuf.ByteString getSubConName();

    /**
     * <code>optional bytes subLoaName = 15;</code>
     */
    boolean hasSubLoaName();
    /**
     * <code>optional bytes subLoaName = 15;</code>
     */
    com.google.protobuf.ByteString getSubLoaName();

    /**
     * <code>optional bytes subStaOrProName = 16;</code>
     */
    boolean hasSubStaOrProName();
    /**
     * <code>optional bytes subStaOrProName = 16;</code>
     */
    com.google.protobuf.ByteString getSubStaOrProName();

    /**
     * <code>optional bytes subStrAddr = 17;</code>
     */
    boolean hasSubStrAddr();
    /**
     * <code>optional bytes subStrAddr = 17;</code>
     */
    com.google.protobuf.ByteString getSubStrAddr();

    /**
     * <code>optional bytes subOrgName = 18;</code>
     */
    boolean hasSubOrgName();
    /**
     * <code>optional bytes subOrgName = 18;</code>
     */
    com.google.protobuf.ByteString getSubOrgName();

    /**
     * <code>optional bytes subOrgUniName = 19;</code>
     */
    boolean hasSubOrgUniName();
    /**
     * <code>optional bytes subOrgUniName = 19;</code>
     */
    com.google.protobuf.ByteString getSubOrgUniName();

    /**
     * <code>optional bytes subPosOffBox = 20;</code>
     */
    boolean hasSubPosOffBox();
    /**
     * <code>optional bytes subPosOffBox = 20;</code>
     */
    com.google.protobuf.ByteString getSubPosOffBox();

    /**
     * <code>optional uint64 valNotBef = 21;</code>
     */
    boolean hasValNotBef();
    /**
     * <code>optional uint64 valNotBef = 21;</code>
     */
    long getValNotBef();

    /**
     * <code>optional uint64 valNotAft = 22;</code>
     */
    boolean hasValNotAft();
    /**
     * <code>optional uint64 valNotAft = 22;</code>
     */
    long getValNotAft();

    /**
     * <code>optional bytes RSAMod = 23;</code>
     */
    boolean hasRSAMod();
    /**
     * <code>optional bytes RSAMod = 23;</code>
     */
    com.google.protobuf.ByteString getRSAMod();

    /**
     * <code>optional bytes RSAExp = 24;</code>
     */
    boolean hasRSAExp();
    /**
     * <code>optional bytes RSAExp = 24;</code>
     */
    com.google.protobuf.ByteString getRSAExp();

    /**
     * <code>optional bytes DHPriMod = 25;</code>
     */
    boolean hasDHPriMod();
    /**
     * <code>optional bytes DHPriMod = 25;</code>
     */
    com.google.protobuf.ByteString getDHPriMod();

    /**
     * <code>optional bytes DHPGen = 26;</code>
     */
    boolean hasDHPGen();
    /**
     * <code>optional bytes DHPGen = 26;</code>
     */
    com.google.protobuf.ByteString getDHPGen();

    /**
     * <code>optional bytes DHPubKey = 27;</code>
     */
    boolean hasDHPubKey();
    /**
     * <code>optional bytes DHPubKey = 27;</code>
     */
    com.google.protobuf.ByteString getDHPubKey();

    /**
     * <code>optional bytes DSAPubKeyP = 28;</code>
     */
    boolean hasDSAPubKeyP();
    /**
     * <code>optional bytes DSAPubKeyP = 28;</code>
     */
    com.google.protobuf.ByteString getDSAPubKeyP();

    /**
     * <code>optional bytes DSAPubKeyQ = 29;</code>
     */
    boolean hasDSAPubKeyQ();
    /**
     * <code>optional bytes DSAPubKeyQ = 29;</code>
     */
    com.google.protobuf.ByteString getDSAPubKeyQ();

    /**
     * <code>optional bytes DSAPubKeyG = 30;</code>
     */
    boolean hasDSAPubKeyG();
    /**
     * <code>optional bytes DSAPubKeyG = 30;</code>
     */
    com.google.protobuf.ByteString getDSAPubKeyG();

    /**
     * <code>optional bytes sigAlg = 31;</code>
     */
    boolean hasSigAlg();
    /**
     * <code>optional bytes sigAlg = 31;</code>
     */
    com.google.protobuf.ByteString getSigAlg();

    /**
     * <code>optional bytes sigVal = 32;</code>
     */
    boolean hasSigVal();
    /**
     * <code>optional bytes sigVal = 32;</code>
     */
    com.google.protobuf.ByteString getSigVal();

    /**
     * <code>optional bytes authKeyID = 33;</code>
     */
    boolean hasAuthKeyID();
    /**
     * <code>optional bytes authKeyID = 33;</code>
     */
    com.google.protobuf.ByteString getAuthKeyID();

    /**
     * <code>optional bytes subKeyID = 34;</code>
     */
    boolean hasSubKeyID();
    /**
     * <code>optional bytes subKeyID = 34;</code>
     */
    com.google.protobuf.ByteString getSubKeyID();

    /**
     * <code>optional bytes keyUsage = 35;</code>
     */
    boolean hasKeyUsage();
    /**
     * <code>optional bytes keyUsage = 35;</code>
     */
    com.google.protobuf.ByteString getKeyUsage();

    /**
     * <code>optional uint64 priKeyUsaPerNotBef = 36;</code>
     */
    boolean hasPriKeyUsaPerNotBef();
    /**
     * <code>optional uint64 priKeyUsaPerNotBef = 36;</code>
     */
    long getPriKeyUsaPerNotBef();

    /**
     * <code>optional uint64 priKeyUsaPerNotAft = 37;</code>
     */
    boolean hasPriKeyUsaPerNotAft();
    /**
     * <code>optional uint64 priKeyUsaPerNotAft = 37;</code>
     */
    long getPriKeyUsaPerNotAft();

    /**
     * <code>optional bytes certPol = 38;</code>
     */
    boolean hasCertPol();
    /**
     * <code>optional bytes certPol = 38;</code>
     */
    com.google.protobuf.ByteString getCertPol();

    /**
     * <code>optional bytes subAltDNS = 39;</code>
     */
    boolean hasSubAltDNS();
    /**
     * <code>optional bytes subAltDNS = 39;</code>
     */
    com.google.protobuf.ByteString getSubAltDNS();

    /**
     * <code>optional bytes subAltIP = 40;</code>
     */
    boolean hasSubAltIP();
    /**
     * <code>optional bytes subAltIP = 40;</code>
     */
    com.google.protobuf.ByteString getSubAltIP();

    /**
     * <code>optional bytes subAltName = 41;</code>
     */
    boolean hasSubAltName();
    /**
     * <code>optional bytes subAltName = 41;</code>
     */
    com.google.protobuf.ByteString getSubAltName();

    /**
     * <code>optional bytes issAltNameSys = 42;</code>
     */
    boolean hasIssAltNameSys();
    /**
     * <code>optional bytes issAltNameSys = 42;</code>
     */
    com.google.protobuf.ByteString getIssAltNameSys();

    /**
     * <code>optional bytes issAltIP = 43;</code>
     */
    boolean hasIssAltIP();
    /**
     * <code>optional bytes issAltIP = 43;</code>
     */
    com.google.protobuf.ByteString getIssAltIP();

    /**
     * <code>optional bytes issAltName = 44;</code>
     */
    boolean hasIssAltName();
    /**
     * <code>optional bytes issAltName = 44;</code>
     */
    com.google.protobuf.ByteString getIssAltName();

    /**
     * <code>optional bytes subDirAtt = 45;</code>
     */
    boolean hasSubDirAtt();
    /**
     * <code>optional bytes subDirAtt = 45;</code>
     */
    com.google.protobuf.ByteString getSubDirAtt();

    /**
     * <code>optional bytes extKeyUsage = 46;</code>
     */
    boolean hasExtKeyUsage();
    /**
     * <code>optional bytes extKeyUsage = 46;</code>
     */
    com.google.protobuf.ByteString getExtKeyUsage();

    /**
     * <code>optional bytes certRevListSrc = 47;</code>
     */
    boolean hasCertRevListSrc();
    /**
     * <code>optional bytes certRevListSrc = 47;</code>
     */
    com.google.protobuf.ByteString getCertRevListSrc();

    /**
     * <code>optional bytes certAuthInfAccMet = 48;</code>
     */
    boolean hasCertAuthInfAccMet();
    /**
     * <code>optional bytes certAuthInfAccMet = 48;</code>
     */
    com.google.protobuf.ByteString getCertAuthInfAccMet();

    /**
     * <code>optional bytes certAuthInfAccLoc = 49;</code>
     */
    boolean hasCertAuthInfAccLoc();
    /**
     * <code>optional bytes certAuthInfAccLoc = 49;</code>
     */
    com.google.protobuf.ByteString getCertAuthInfAccLoc();

    /**
     * <code>optional uint32 extCnt = 50;</code>
     */
    boolean hasExtCnt();
    /**
     * <code>optional uint32 extCnt = 50;</code>
     */
    int getExtCnt();

    /**
     * <code>optional bytes Protabname = 51;</code>
     */
    boolean hasProtabname();
    /**
     * <code>optional bytes Protabname = 51;</code>
     */
    com.google.protobuf.ByteString getProtabname();

    /**
     * <code>optional bytes issuer = 52;</code>
     */
    boolean hasIssuer();
    /**
     * <code>optional bytes issuer = 52;</code>
     */
    com.google.protobuf.ByteString getIssuer();

    /**
     * <code>optional bytes subject = 53;</code>
     */
    boolean hasSubject();
    /**
     * <code>optional bytes subject = 53;</code>
     */
    com.google.protobuf.ByteString getSubject();

    /**
     * <code>optional uint32 daysRem = 54;</code>
     */
    boolean hasDaysRem();
    /**
     * <code>optional uint32 daysRem = 54;</code>
     */
    int getDaysRem();

    /**
     * <code>optional bytes pubkey = 55;</code>
     */
    boolean hasPubkey();
    /**
     * <code>optional bytes pubkey = 55;</code>
     */
    com.google.protobuf.ByteString getPubkey();

    /**
     * <code>optional bytes fpAlg = 56;</code>
     */
    boolean hasFpAlg();
    /**
     * <code>optional bytes fpAlg = 56;</code>
     */
    com.google.protobuf.ByteString getFpAlg();

    /**
     * <code>optional bytes hash = 57;</code>
     */
    boolean hasHash();
    /**
     * <code>optional bytes hash = 57;</code>
     */
    com.google.protobuf.ByteString getHash();

    /**
     * <code>optional bytes extSet = 58;</code>
     */
    boolean hasExtSet();
    /**
     * <code>optional bytes extSet = 58;</code>
     */
    com.google.protobuf.ByteString getExtSet();

    /**
     * <code>optional uint32 daysTotal = 59;</code>
     */
    boolean hasDaysTotal();
    /**
     * <code>optional uint32 daysTotal = 59;</code>
     */
    int getDaysTotal();

    /**
     * <code>optional uint32 subAltDNSCnt = 60;</code>
     */
    boolean hasSubAltDNSCnt();
    /**
     * <code>optional uint32 subAltDNSCnt = 60;</code>
     */
    int getSubAltDNSCnt();

    /**
     * <code>optional bytes authinfo = 61;</code>
     */
    boolean hasAuthinfo();
    /**
     * <code>optional bytes authinfo = 61;</code>
     */
    com.google.protobuf.ByteString getAuthinfo();

    /**
     * <code>optional bytes basicConsCA = 62;</code>
     */
    boolean hasBasicConsCA();
    /**
     * <code>optional bytes basicConsCA = 62;</code>
     */
    com.google.protobuf.ByteString getBasicConsCA();

    /**
     * <code>optional bytes basicConsPathLen = 63;</code>
     */
    boolean hasBasicConsPathLen();
    /**
     * <code>optional bytes basicConsPathLen = 63;</code>
     */
    com.google.protobuf.ByteString getBasicConsPathLen();

    /**
     * <code>optional bytes basicCons = 64;</code>
     */
    boolean hasBasicCons();
    /**
     * <code>optional bytes basicCons = 64;</code>
     */
    com.google.protobuf.ByteString getBasicCons();

    /**
     * <code>optional bytes KeyPur = 65;</code>
     */
    boolean hasKeyPur();
    /**
     * <code>optional bytes KeyPur = 65;</code>
     */
    com.google.protobuf.ByteString getKeyPur();

    /**
     * <code>optional uint32 Certype = 66;</code>
     */
    boolean hasCertype();
    /**
     * <code>optional uint32 Certype = 66;</code>
     */
    int getCertype();

    /**
     * <code>optional bytes certFullText = 67;</code>
     */
    boolean hasCertFullText();
    /**
     * <code>optional bytes certFullText = 67;</code>
     */
    com.google.protobuf.ByteString getCertFullText();

    /**
     * <code>optional uint32 alternativeIpCount = 68;</code>
     */
    boolean hasAlternativeIpCount();
    /**
     * <code>optional uint32 alternativeIpCount = 68;</code>
     */
    int getAlternativeIpCount();

    /**
     * <code>optional uint32 source = 69;</code>
     */
    boolean hasSource();
    /**
     * <code>optional uint32 source = 69;</code>
     */
    int getSource();
  }
  /**
   * Protobuf type {@code X509CerInfo}
   */
  public  static final class X509CerInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:X509CerInfo)
      X509CerInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use X509CerInfo.newBuilder() to construct.
    private X509CerInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private X509CerInfo() {
      protabID_ = 0L;
      ver_ = 0;
      srvNum_ = com.google.protobuf.ByteString.EMPTY;
      issDataLen_ = 0;
      issComName_ = com.google.protobuf.ByteString.EMPTY;
      issConName_ = com.google.protobuf.ByteString.EMPTY;
      issLoaName_ = com.google.protobuf.ByteString.EMPTY;
      issStaOrProName_ = com.google.protobuf.ByteString.EMPTY;
      issStrAddr_ = com.google.protobuf.ByteString.EMPTY;
      issOrgName_ = com.google.protobuf.ByteString.EMPTY;
      issOrgUniName_ = com.google.protobuf.ByteString.EMPTY;
      issPosOffBox_ = com.google.protobuf.ByteString.EMPTY;
      subComName_ = com.google.protobuf.ByteString.EMPTY;
      subConName_ = com.google.protobuf.ByteString.EMPTY;
      subLoaName_ = com.google.protobuf.ByteString.EMPTY;
      subStaOrProName_ = com.google.protobuf.ByteString.EMPTY;
      subStrAddr_ = com.google.protobuf.ByteString.EMPTY;
      subOrgName_ = com.google.protobuf.ByteString.EMPTY;
      subOrgUniName_ = com.google.protobuf.ByteString.EMPTY;
      subPosOffBox_ = com.google.protobuf.ByteString.EMPTY;
      valNotBef_ = 0L;
      valNotAft_ = 0L;
      rSAMod_ = com.google.protobuf.ByteString.EMPTY;
      rSAExp_ = com.google.protobuf.ByteString.EMPTY;
      dHPriMod_ = com.google.protobuf.ByteString.EMPTY;
      dHPGen_ = com.google.protobuf.ByteString.EMPTY;
      dHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      dSAPubKeyP_ = com.google.protobuf.ByteString.EMPTY;
      dSAPubKeyQ_ = com.google.protobuf.ByteString.EMPTY;
      dSAPubKeyG_ = com.google.protobuf.ByteString.EMPTY;
      sigAlg_ = com.google.protobuf.ByteString.EMPTY;
      sigVal_ = com.google.protobuf.ByteString.EMPTY;
      authKeyID_ = com.google.protobuf.ByteString.EMPTY;
      subKeyID_ = com.google.protobuf.ByteString.EMPTY;
      keyUsage_ = com.google.protobuf.ByteString.EMPTY;
      priKeyUsaPerNotBef_ = 0L;
      priKeyUsaPerNotAft_ = 0L;
      certPol_ = com.google.protobuf.ByteString.EMPTY;
      subAltDNS_ = com.google.protobuf.ByteString.EMPTY;
      subAltIP_ = com.google.protobuf.ByteString.EMPTY;
      subAltName_ = com.google.protobuf.ByteString.EMPTY;
      issAltNameSys_ = com.google.protobuf.ByteString.EMPTY;
      issAltIP_ = com.google.protobuf.ByteString.EMPTY;
      issAltName_ = com.google.protobuf.ByteString.EMPTY;
      subDirAtt_ = com.google.protobuf.ByteString.EMPTY;
      extKeyUsage_ = com.google.protobuf.ByteString.EMPTY;
      certRevListSrc_ = com.google.protobuf.ByteString.EMPTY;
      certAuthInfAccMet_ = com.google.protobuf.ByteString.EMPTY;
      certAuthInfAccLoc_ = com.google.protobuf.ByteString.EMPTY;
      extCnt_ = 0;
      protabname_ = com.google.protobuf.ByteString.EMPTY;
      issuer_ = com.google.protobuf.ByteString.EMPTY;
      subject_ = com.google.protobuf.ByteString.EMPTY;
      daysRem_ = 0;
      pubkey_ = com.google.protobuf.ByteString.EMPTY;
      fpAlg_ = com.google.protobuf.ByteString.EMPTY;
      hash_ = com.google.protobuf.ByteString.EMPTY;
      extSet_ = com.google.protobuf.ByteString.EMPTY;
      daysTotal_ = 0;
      subAltDNSCnt_ = 0;
      authinfo_ = com.google.protobuf.ByteString.EMPTY;
      basicConsCA_ = com.google.protobuf.ByteString.EMPTY;
      basicConsPathLen_ = com.google.protobuf.ByteString.EMPTY;
      basicCons_ = com.google.protobuf.ByteString.EMPTY;
      keyPur_ = com.google.protobuf.ByteString.EMPTY;
      certype_ = 0;
      certFullText_ = com.google.protobuf.ByteString.EMPTY;
      alternativeIpCount_ = 0;
      source_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private X509CerInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      int mutable_bitField1_ = 0;
      int mutable_bitField2_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              protabID_ = input.readUInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              ver_ = input.readUInt32();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              srvNum_ = input.readBytes();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              issDataLen_ = input.readUInt32();
              break;
            }
            case 42: {
              bitField0_ |= 0x00000010;
              issComName_ = input.readBytes();
              break;
            }
            case 50: {
              bitField0_ |= 0x00000020;
              issConName_ = input.readBytes();
              break;
            }
            case 58: {
              bitField0_ |= 0x00000040;
              issLoaName_ = input.readBytes();
              break;
            }
            case 66: {
              bitField0_ |= 0x00000080;
              issStaOrProName_ = input.readBytes();
              break;
            }
            case 74: {
              bitField0_ |= 0x00000100;
              issStrAddr_ = input.readBytes();
              break;
            }
            case 82: {
              bitField0_ |= 0x00000200;
              issOrgName_ = input.readBytes();
              break;
            }
            case 90: {
              bitField0_ |= 0x00000400;
              issOrgUniName_ = input.readBytes();
              break;
            }
            case 98: {
              bitField0_ |= 0x00000800;
              issPosOffBox_ = input.readBytes();
              break;
            }
            case 106: {
              bitField0_ |= 0x00001000;
              subComName_ = input.readBytes();
              break;
            }
            case 114: {
              bitField0_ |= 0x00002000;
              subConName_ = input.readBytes();
              break;
            }
            case 122: {
              bitField0_ |= 0x00004000;
              subLoaName_ = input.readBytes();
              break;
            }
            case 130: {
              bitField0_ |= 0x00008000;
              subStaOrProName_ = input.readBytes();
              break;
            }
            case 138: {
              bitField0_ |= 0x00010000;
              subStrAddr_ = input.readBytes();
              break;
            }
            case 146: {
              bitField0_ |= 0x00020000;
              subOrgName_ = input.readBytes();
              break;
            }
            case 154: {
              bitField0_ |= 0x00040000;
              subOrgUniName_ = input.readBytes();
              break;
            }
            case 162: {
              bitField0_ |= 0x00080000;
              subPosOffBox_ = input.readBytes();
              break;
            }
            case 168: {
              bitField0_ |= 0x00100000;
              valNotBef_ = input.readUInt64();
              break;
            }
            case 176: {
              bitField0_ |= 0x00200000;
              valNotAft_ = input.readUInt64();
              break;
            }
            case 186: {
              bitField0_ |= 0x00400000;
              rSAMod_ = input.readBytes();
              break;
            }
            case 194: {
              bitField0_ |= 0x00800000;
              rSAExp_ = input.readBytes();
              break;
            }
            case 202: {
              bitField0_ |= 0x01000000;
              dHPriMod_ = input.readBytes();
              break;
            }
            case 210: {
              bitField0_ |= 0x02000000;
              dHPGen_ = input.readBytes();
              break;
            }
            case 218: {
              bitField0_ |= 0x04000000;
              dHPubKey_ = input.readBytes();
              break;
            }
            case 226: {
              bitField0_ |= 0x08000000;
              dSAPubKeyP_ = input.readBytes();
              break;
            }
            case 234: {
              bitField0_ |= 0x10000000;
              dSAPubKeyQ_ = input.readBytes();
              break;
            }
            case 242: {
              bitField0_ |= 0x20000000;
              dSAPubKeyG_ = input.readBytes();
              break;
            }
            case 250: {
              bitField0_ |= 0x40000000;
              sigAlg_ = input.readBytes();
              break;
            }
            case 258: {
              bitField0_ |= 0x80000000;
              sigVal_ = input.readBytes();
              break;
            }
            case 266: {
              bitField1_ |= 0x00000001;
              authKeyID_ = input.readBytes();
              break;
            }
            case 274: {
              bitField1_ |= 0x00000002;
              subKeyID_ = input.readBytes();
              break;
            }
            case 282: {
              bitField1_ |= 0x00000004;
              keyUsage_ = input.readBytes();
              break;
            }
            case 288: {
              bitField1_ |= 0x00000008;
              priKeyUsaPerNotBef_ = input.readUInt64();
              break;
            }
            case 296: {
              bitField1_ |= 0x00000010;
              priKeyUsaPerNotAft_ = input.readUInt64();
              break;
            }
            case 306: {
              bitField1_ |= 0x00000020;
              certPol_ = input.readBytes();
              break;
            }
            case 314: {
              bitField1_ |= 0x00000040;
              subAltDNS_ = input.readBytes();
              break;
            }
            case 322: {
              bitField1_ |= 0x00000080;
              subAltIP_ = input.readBytes();
              break;
            }
            case 330: {
              bitField1_ |= 0x00000100;
              subAltName_ = input.readBytes();
              break;
            }
            case 338: {
              bitField1_ |= 0x00000200;
              issAltNameSys_ = input.readBytes();
              break;
            }
            case 346: {
              bitField1_ |= 0x00000400;
              issAltIP_ = input.readBytes();
              break;
            }
            case 354: {
              bitField1_ |= 0x00000800;
              issAltName_ = input.readBytes();
              break;
            }
            case 362: {
              bitField1_ |= 0x00001000;
              subDirAtt_ = input.readBytes();
              break;
            }
            case 370: {
              bitField1_ |= 0x00002000;
              extKeyUsage_ = input.readBytes();
              break;
            }
            case 378: {
              bitField1_ |= 0x00004000;
              certRevListSrc_ = input.readBytes();
              break;
            }
            case 386: {
              bitField1_ |= 0x00008000;
              certAuthInfAccMet_ = input.readBytes();
              break;
            }
            case 394: {
              bitField1_ |= 0x00010000;
              certAuthInfAccLoc_ = input.readBytes();
              break;
            }
            case 400: {
              bitField1_ |= 0x00020000;
              extCnt_ = input.readUInt32();
              break;
            }
            case 410: {
              bitField1_ |= 0x00040000;
              protabname_ = input.readBytes();
              break;
            }
            case 418: {
              bitField1_ |= 0x00080000;
              issuer_ = input.readBytes();
              break;
            }
            case 426: {
              bitField1_ |= 0x00100000;
              subject_ = input.readBytes();
              break;
            }
            case 432: {
              bitField1_ |= 0x00200000;
              daysRem_ = input.readUInt32();
              break;
            }
            case 442: {
              bitField1_ |= 0x00400000;
              pubkey_ = input.readBytes();
              break;
            }
            case 450: {
              bitField1_ |= 0x00800000;
              fpAlg_ = input.readBytes();
              break;
            }
            case 458: {
              bitField1_ |= 0x01000000;
              hash_ = input.readBytes();
              break;
            }
            case 466: {
              bitField1_ |= 0x02000000;
              extSet_ = input.readBytes();
              break;
            }
            case 472: {
              bitField1_ |= 0x04000000;
              daysTotal_ = input.readUInt32();
              break;
            }
            case 480: {
              bitField1_ |= 0x08000000;
              subAltDNSCnt_ = input.readUInt32();
              break;
            }
            case 490: {
              bitField1_ |= 0x10000000;
              authinfo_ = input.readBytes();
              break;
            }
            case 498: {
              bitField1_ |= 0x20000000;
              basicConsCA_ = input.readBytes();
              break;
            }
            case 506: {
              bitField1_ |= 0x40000000;
              basicConsPathLen_ = input.readBytes();
              break;
            }
            case 514: {
              bitField1_ |= 0x80000000;
              basicCons_ = input.readBytes();
              break;
            }
            case 522: {
              bitField2_ |= 0x00000001;
              keyPur_ = input.readBytes();
              break;
            }
            case 528: {
              bitField2_ |= 0x00000002;
              certype_ = input.readUInt32();
              break;
            }
            case 538: {
              bitField2_ |= 0x00000004;
              certFullText_ = input.readBytes();
              break;
            }
            case 544: {
              bitField2_ |= 0x00000008;
              alternativeIpCount_ = input.readUInt32();
              break;
            }
            case 552: {
              bitField2_ |= 0x00000010;
              source_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return X509CerInfoOuterClass.internal_static_X509CerInfo_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return X509CerInfoOuterClass.internal_static_X509CerInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              X509CerInfo.class, Builder.class);
    }

    private int bitField0_;
    private int bitField1_;
    private int bitField2_;
    public static final int PROTABID_FIELD_NUMBER = 1;
    private long protabID_;
    /**
     * <code>optional uint64 ProtabID = 1;</code>
     */
    public boolean hasProtabID() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional uint64 ProtabID = 1;</code>
     */
    public long getProtabID() {
      return protabID_;
    }

    public static final int VER_FIELD_NUMBER = 2;
    private int ver_;
    /**
     * <code>optional uint32 ver = 2;</code>
     */
    public boolean hasVer() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional uint32 ver = 2;</code>
     */
    public int getVer() {
      return ver_;
    }

    public static final int SRVNUM_FIELD_NUMBER = 3;
    private com.google.protobuf.ByteString srvNum_;
    /**
     * <code>optional bytes srvNum = 3;</code>
     */
    public boolean hasSrvNum() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional bytes srvNum = 3;</code>
     */
    public com.google.protobuf.ByteString getSrvNum() {
      return srvNum_;
    }

    public static final int ISSDATALEN_FIELD_NUMBER = 4;
    private int issDataLen_;
    /**
     * <code>optional uint32 issDataLen = 4;</code>
     */
    public boolean hasIssDataLen() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional uint32 issDataLen = 4;</code>
     */
    public int getIssDataLen() {
      return issDataLen_;
    }

    public static final int ISSCOMNAME_FIELD_NUMBER = 5;
    private com.google.protobuf.ByteString issComName_;
    /**
     * <code>optional bytes issComName = 5;</code>
     */
    public boolean hasIssComName() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional bytes issComName = 5;</code>
     */
    public com.google.protobuf.ByteString getIssComName() {
      return issComName_;
    }

    public static final int ISSCONNAME_FIELD_NUMBER = 6;
    private com.google.protobuf.ByteString issConName_;
    /**
     * <code>optional bytes issConName = 6;</code>
     */
    public boolean hasIssConName() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional bytes issConName = 6;</code>
     */
    public com.google.protobuf.ByteString getIssConName() {
      return issConName_;
    }

    public static final int ISSLOANAME_FIELD_NUMBER = 7;
    private com.google.protobuf.ByteString issLoaName_;
    /**
     * <code>optional bytes issLoaName = 7;</code>
     */
    public boolean hasIssLoaName() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional bytes issLoaName = 7;</code>
     */
    public com.google.protobuf.ByteString getIssLoaName() {
      return issLoaName_;
    }

    public static final int ISSSTAORPRONAME_FIELD_NUMBER = 8;
    private com.google.protobuf.ByteString issStaOrProName_;
    /**
     * <code>optional bytes issStaOrProName = 8;</code>
     */
    public boolean hasIssStaOrProName() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional bytes issStaOrProName = 8;</code>
     */
    public com.google.protobuf.ByteString getIssStaOrProName() {
      return issStaOrProName_;
    }

    public static final int ISSSTRADDR_FIELD_NUMBER = 9;
    private com.google.protobuf.ByteString issStrAddr_;
    /**
     * <code>optional bytes issStrAddr = 9;</code>
     */
    public boolean hasIssStrAddr() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional bytes issStrAddr = 9;</code>
     */
    public com.google.protobuf.ByteString getIssStrAddr() {
      return issStrAddr_;
    }

    public static final int ISSORGNAME_FIELD_NUMBER = 10;
    private com.google.protobuf.ByteString issOrgName_;
    /**
     * <code>optional bytes issOrgName = 10;</code>
     */
    public boolean hasIssOrgName() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>optional bytes issOrgName = 10;</code>
     */
    public com.google.protobuf.ByteString getIssOrgName() {
      return issOrgName_;
    }

    public static final int ISSORGUNINAME_FIELD_NUMBER = 11;
    private com.google.protobuf.ByteString issOrgUniName_;
    /**
     * <code>optional bytes issOrgUniName = 11;</code>
     */
    public boolean hasIssOrgUniName() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <code>optional bytes issOrgUniName = 11;</code>
     */
    public com.google.protobuf.ByteString getIssOrgUniName() {
      return issOrgUniName_;
    }

    public static final int ISSPOSOFFBOX_FIELD_NUMBER = 12;
    private com.google.protobuf.ByteString issPosOffBox_;
    /**
     * <code>optional bytes issPosOffBox = 12;</code>
     */
    public boolean hasIssPosOffBox() {
      return ((bitField0_ & 0x00000800) == 0x00000800);
    }
    /**
     * <code>optional bytes issPosOffBox = 12;</code>
     */
    public com.google.protobuf.ByteString getIssPosOffBox() {
      return issPosOffBox_;
    }

    public static final int SUBCOMNAME_FIELD_NUMBER = 13;
    private com.google.protobuf.ByteString subComName_;
    /**
     * <code>optional bytes subComName = 13;</code>
     */
    public boolean hasSubComName() {
      return ((bitField0_ & 0x00001000) == 0x00001000);
    }
    /**
     * <code>optional bytes subComName = 13;</code>
     */
    public com.google.protobuf.ByteString getSubComName() {
      return subComName_;
    }

    public static final int SUBCONNAME_FIELD_NUMBER = 14;
    private com.google.protobuf.ByteString subConName_;
    /**
     * <code>optional bytes subConName = 14;</code>
     */
    public boolean hasSubConName() {
      return ((bitField0_ & 0x00002000) == 0x00002000);
    }
    /**
     * <code>optional bytes subConName = 14;</code>
     */
    public com.google.protobuf.ByteString getSubConName() {
      return subConName_;
    }

    public static final int SUBLOANAME_FIELD_NUMBER = 15;
    private com.google.protobuf.ByteString subLoaName_;
    /**
     * <code>optional bytes subLoaName = 15;</code>
     */
    public boolean hasSubLoaName() {
      return ((bitField0_ & 0x00004000) == 0x00004000);
    }
    /**
     * <code>optional bytes subLoaName = 15;</code>
     */
    public com.google.protobuf.ByteString getSubLoaName() {
      return subLoaName_;
    }

    public static final int SUBSTAORPRONAME_FIELD_NUMBER = 16;
    private com.google.protobuf.ByteString subStaOrProName_;
    /**
     * <code>optional bytes subStaOrProName = 16;</code>
     */
    public boolean hasSubStaOrProName() {
      return ((bitField0_ & 0x00008000) == 0x00008000);
    }
    /**
     * <code>optional bytes subStaOrProName = 16;</code>
     */
    public com.google.protobuf.ByteString getSubStaOrProName() {
      return subStaOrProName_;
    }

    public static final int SUBSTRADDR_FIELD_NUMBER = 17;
    private com.google.protobuf.ByteString subStrAddr_;
    /**
     * <code>optional bytes subStrAddr = 17;</code>
     */
    public boolean hasSubStrAddr() {
      return ((bitField0_ & 0x00010000) == 0x00010000);
    }
    /**
     * <code>optional bytes subStrAddr = 17;</code>
     */
    public com.google.protobuf.ByteString getSubStrAddr() {
      return subStrAddr_;
    }

    public static final int SUBORGNAME_FIELD_NUMBER = 18;
    private com.google.protobuf.ByteString subOrgName_;
    /**
     * <code>optional bytes subOrgName = 18;</code>
     */
    public boolean hasSubOrgName() {
      return ((bitField0_ & 0x00020000) == 0x00020000);
    }
    /**
     * <code>optional bytes subOrgName = 18;</code>
     */
    public com.google.protobuf.ByteString getSubOrgName() {
      return subOrgName_;
    }

    public static final int SUBORGUNINAME_FIELD_NUMBER = 19;
    private com.google.protobuf.ByteString subOrgUniName_;
    /**
     * <code>optional bytes subOrgUniName = 19;</code>
     */
    public boolean hasSubOrgUniName() {
      return ((bitField0_ & 0x00040000) == 0x00040000);
    }
    /**
     * <code>optional bytes subOrgUniName = 19;</code>
     */
    public com.google.protobuf.ByteString getSubOrgUniName() {
      return subOrgUniName_;
    }

    public static final int SUBPOSOFFBOX_FIELD_NUMBER = 20;
    private com.google.protobuf.ByteString subPosOffBox_;
    /**
     * <code>optional bytes subPosOffBox = 20;</code>
     */
    public boolean hasSubPosOffBox() {
      return ((bitField0_ & 0x00080000) == 0x00080000);
    }
    /**
     * <code>optional bytes subPosOffBox = 20;</code>
     */
    public com.google.protobuf.ByteString getSubPosOffBox() {
      return subPosOffBox_;
    }

    public static final int VALNOTBEF_FIELD_NUMBER = 21;
    private long valNotBef_;
    /**
     * <code>optional uint64 valNotBef = 21;</code>
     */
    public boolean hasValNotBef() {
      return ((bitField0_ & 0x00100000) == 0x00100000);
    }
    /**
     * <code>optional uint64 valNotBef = 21;</code>
     */
    public long getValNotBef() {
      return valNotBef_;
    }

    public static final int VALNOTAFT_FIELD_NUMBER = 22;
    private long valNotAft_;
    /**
     * <code>optional uint64 valNotAft = 22;</code>
     */
    public boolean hasValNotAft() {
      return ((bitField0_ & 0x00200000) == 0x00200000);
    }
    /**
     * <code>optional uint64 valNotAft = 22;</code>
     */
    public long getValNotAft() {
      return valNotAft_;
    }

    public static final int RSAMOD_FIELD_NUMBER = 23;
    private com.google.protobuf.ByteString rSAMod_;
    /**
     * <code>optional bytes RSAMod = 23;</code>
     */
    public boolean hasRSAMod() {
      return ((bitField0_ & 0x00400000) == 0x00400000);
    }
    /**
     * <code>optional bytes RSAMod = 23;</code>
     */
    public com.google.protobuf.ByteString getRSAMod() {
      return rSAMod_;
    }

    public static final int RSAEXP_FIELD_NUMBER = 24;
    private com.google.protobuf.ByteString rSAExp_;
    /**
     * <code>optional bytes RSAExp = 24;</code>
     */
    public boolean hasRSAExp() {
      return ((bitField0_ & 0x00800000) == 0x00800000);
    }
    /**
     * <code>optional bytes RSAExp = 24;</code>
     */
    public com.google.protobuf.ByteString getRSAExp() {
      return rSAExp_;
    }

    public static final int DHPRIMOD_FIELD_NUMBER = 25;
    private com.google.protobuf.ByteString dHPriMod_;
    /**
     * <code>optional bytes DHPriMod = 25;</code>
     */
    public boolean hasDHPriMod() {
      return ((bitField0_ & 0x01000000) == 0x01000000);
    }
    /**
     * <code>optional bytes DHPriMod = 25;</code>
     */
    public com.google.protobuf.ByteString getDHPriMod() {
      return dHPriMod_;
    }

    public static final int DHPGEN_FIELD_NUMBER = 26;
    private com.google.protobuf.ByteString dHPGen_;
    /**
     * <code>optional bytes DHPGen = 26;</code>
     */
    public boolean hasDHPGen() {
      return ((bitField0_ & 0x02000000) == 0x02000000);
    }
    /**
     * <code>optional bytes DHPGen = 26;</code>
     */
    public com.google.protobuf.ByteString getDHPGen() {
      return dHPGen_;
    }

    public static final int DHPUBKEY_FIELD_NUMBER = 27;
    private com.google.protobuf.ByteString dHPubKey_;
    /**
     * <code>optional bytes DHPubKey = 27;</code>
     */
    public boolean hasDHPubKey() {
      return ((bitField0_ & 0x04000000) == 0x04000000);
    }
    /**
     * <code>optional bytes DHPubKey = 27;</code>
     */
    public com.google.protobuf.ByteString getDHPubKey() {
      return dHPubKey_;
    }

    public static final int DSAPUBKEYP_FIELD_NUMBER = 28;
    private com.google.protobuf.ByteString dSAPubKeyP_;
    /**
     * <code>optional bytes DSAPubKeyP = 28;</code>
     */
    public boolean hasDSAPubKeyP() {
      return ((bitField0_ & 0x08000000) == 0x08000000);
    }
    /**
     * <code>optional bytes DSAPubKeyP = 28;</code>
     */
    public com.google.protobuf.ByteString getDSAPubKeyP() {
      return dSAPubKeyP_;
    }

    public static final int DSAPUBKEYQ_FIELD_NUMBER = 29;
    private com.google.protobuf.ByteString dSAPubKeyQ_;
    /**
     * <code>optional bytes DSAPubKeyQ = 29;</code>
     */
    public boolean hasDSAPubKeyQ() {
      return ((bitField0_ & 0x10000000) == 0x10000000);
    }
    /**
     * <code>optional bytes DSAPubKeyQ = 29;</code>
     */
    public com.google.protobuf.ByteString getDSAPubKeyQ() {
      return dSAPubKeyQ_;
    }

    public static final int DSAPUBKEYG_FIELD_NUMBER = 30;
    private com.google.protobuf.ByteString dSAPubKeyG_;
    /**
     * <code>optional bytes DSAPubKeyG = 30;</code>
     */
    public boolean hasDSAPubKeyG() {
      return ((bitField0_ & 0x20000000) == 0x20000000);
    }
    /**
     * <code>optional bytes DSAPubKeyG = 30;</code>
     */
    public com.google.protobuf.ByteString getDSAPubKeyG() {
      return dSAPubKeyG_;
    }

    public static final int SIGALG_FIELD_NUMBER = 31;
    private com.google.protobuf.ByteString sigAlg_;
    /**
     * <code>optional bytes sigAlg = 31;</code>
     */
    public boolean hasSigAlg() {
      return ((bitField0_ & 0x40000000) == 0x40000000);
    }
    /**
     * <code>optional bytes sigAlg = 31;</code>
     */
    public com.google.protobuf.ByteString getSigAlg() {
      return sigAlg_;
    }

    public static final int SIGVAL_FIELD_NUMBER = 32;
    private com.google.protobuf.ByteString sigVal_;
    /**
     * <code>optional bytes sigVal = 32;</code>
     */
    public boolean hasSigVal() {
      return ((bitField0_ & 0x80000000) == 0x80000000);
    }
    /**
     * <code>optional bytes sigVal = 32;</code>
     */
    public com.google.protobuf.ByteString getSigVal() {
      return sigVal_;
    }

    public static final int AUTHKEYID_FIELD_NUMBER = 33;
    private com.google.protobuf.ByteString authKeyID_;
    /**
     * <code>optional bytes authKeyID = 33;</code>
     */
    public boolean hasAuthKeyID() {
      return ((bitField1_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional bytes authKeyID = 33;</code>
     */
    public com.google.protobuf.ByteString getAuthKeyID() {
      return authKeyID_;
    }

    public static final int SUBKEYID_FIELD_NUMBER = 34;
    private com.google.protobuf.ByteString subKeyID_;
    /**
     * <code>optional bytes subKeyID = 34;</code>
     */
    public boolean hasSubKeyID() {
      return ((bitField1_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional bytes subKeyID = 34;</code>
     */
    public com.google.protobuf.ByteString getSubKeyID() {
      return subKeyID_;
    }

    public static final int KEYUSAGE_FIELD_NUMBER = 35;
    private com.google.protobuf.ByteString keyUsage_;
    /**
     * <code>optional bytes keyUsage = 35;</code>
     */
    public boolean hasKeyUsage() {
      return ((bitField1_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional bytes keyUsage = 35;</code>
     */
    public com.google.protobuf.ByteString getKeyUsage() {
      return keyUsage_;
    }

    public static final int PRIKEYUSAPERNOTBEF_FIELD_NUMBER = 36;
    private long priKeyUsaPerNotBef_;
    /**
     * <code>optional uint64 priKeyUsaPerNotBef = 36;</code>
     */
    public boolean hasPriKeyUsaPerNotBef() {
      return ((bitField1_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional uint64 priKeyUsaPerNotBef = 36;</code>
     */
    public long getPriKeyUsaPerNotBef() {
      return priKeyUsaPerNotBef_;
    }

    public static final int PRIKEYUSAPERNOTAFT_FIELD_NUMBER = 37;
    private long priKeyUsaPerNotAft_;
    /**
     * <code>optional uint64 priKeyUsaPerNotAft = 37;</code>
     */
    public boolean hasPriKeyUsaPerNotAft() {
      return ((bitField1_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional uint64 priKeyUsaPerNotAft = 37;</code>
     */
    public long getPriKeyUsaPerNotAft() {
      return priKeyUsaPerNotAft_;
    }

    public static final int CERTPOL_FIELD_NUMBER = 38;
    private com.google.protobuf.ByteString certPol_;
    /**
     * <code>optional bytes certPol = 38;</code>
     */
    public boolean hasCertPol() {
      return ((bitField1_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional bytes certPol = 38;</code>
     */
    public com.google.protobuf.ByteString getCertPol() {
      return certPol_;
    }

    public static final int SUBALTDNS_FIELD_NUMBER = 39;
    private com.google.protobuf.ByteString subAltDNS_;
    /**
     * <code>optional bytes subAltDNS = 39;</code>
     */
    public boolean hasSubAltDNS() {
      return ((bitField1_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional bytes subAltDNS = 39;</code>
     */
    public com.google.protobuf.ByteString getSubAltDNS() {
      return subAltDNS_;
    }

    public static final int SUBALTIP_FIELD_NUMBER = 40;
    private com.google.protobuf.ByteString subAltIP_;
    /**
     * <code>optional bytes subAltIP = 40;</code>
     */
    public boolean hasSubAltIP() {
      return ((bitField1_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional bytes subAltIP = 40;</code>
     */
    public com.google.protobuf.ByteString getSubAltIP() {
      return subAltIP_;
    }

    public static final int SUBALTNAME_FIELD_NUMBER = 41;
    private com.google.protobuf.ByteString subAltName_;
    /**
     * <code>optional bytes subAltName = 41;</code>
     */
    public boolean hasSubAltName() {
      return ((bitField1_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional bytes subAltName = 41;</code>
     */
    public com.google.protobuf.ByteString getSubAltName() {
      return subAltName_;
    }

    public static final int ISSALTNAMESYS_FIELD_NUMBER = 42;
    private com.google.protobuf.ByteString issAltNameSys_;
    /**
     * <code>optional bytes issAltNameSys = 42;</code>
     */
    public boolean hasIssAltNameSys() {
      return ((bitField1_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>optional bytes issAltNameSys = 42;</code>
     */
    public com.google.protobuf.ByteString getIssAltNameSys() {
      return issAltNameSys_;
    }

    public static final int ISSALTIP_FIELD_NUMBER = 43;
    private com.google.protobuf.ByteString issAltIP_;
    /**
     * <code>optional bytes issAltIP = 43;</code>
     */
    public boolean hasIssAltIP() {
      return ((bitField1_ & 0x00000400) == 0x00000400);
    }
    /**
     * <code>optional bytes issAltIP = 43;</code>
     */
    public com.google.protobuf.ByteString getIssAltIP() {
      return issAltIP_;
    }

    public static final int ISSALTNAME_FIELD_NUMBER = 44;
    private com.google.protobuf.ByteString issAltName_;
    /**
     * <code>optional bytes issAltName = 44;</code>
     */
    public boolean hasIssAltName() {
      return ((bitField1_ & 0x00000800) == 0x00000800);
    }
    /**
     * <code>optional bytes issAltName = 44;</code>
     */
    public com.google.protobuf.ByteString getIssAltName() {
      return issAltName_;
    }

    public static final int SUBDIRATT_FIELD_NUMBER = 45;
    private com.google.protobuf.ByteString subDirAtt_;
    /**
     * <code>optional bytes subDirAtt = 45;</code>
     */
    public boolean hasSubDirAtt() {
      return ((bitField1_ & 0x00001000) == 0x00001000);
    }
    /**
     * <code>optional bytes subDirAtt = 45;</code>
     */
    public com.google.protobuf.ByteString getSubDirAtt() {
      return subDirAtt_;
    }

    public static final int EXTKEYUSAGE_FIELD_NUMBER = 46;
    private com.google.protobuf.ByteString extKeyUsage_;
    /**
     * <code>optional bytes extKeyUsage = 46;</code>
     */
    public boolean hasExtKeyUsage() {
      return ((bitField1_ & 0x00002000) == 0x00002000);
    }
    /**
     * <code>optional bytes extKeyUsage = 46;</code>
     */
    public com.google.protobuf.ByteString getExtKeyUsage() {
      return extKeyUsage_;
    }

    public static final int CERTREVLISTSRC_FIELD_NUMBER = 47;
    private com.google.protobuf.ByteString certRevListSrc_;
    /**
     * <code>optional bytes certRevListSrc = 47;</code>
     */
    public boolean hasCertRevListSrc() {
      return ((bitField1_ & 0x00004000) == 0x00004000);
    }
    /**
     * <code>optional bytes certRevListSrc = 47;</code>
     */
    public com.google.protobuf.ByteString getCertRevListSrc() {
      return certRevListSrc_;
    }

    public static final int CERTAUTHINFACCMET_FIELD_NUMBER = 48;
    private com.google.protobuf.ByteString certAuthInfAccMet_;
    /**
     * <code>optional bytes certAuthInfAccMet = 48;</code>
     */
    public boolean hasCertAuthInfAccMet() {
      return ((bitField1_ & 0x00008000) == 0x00008000);
    }
    /**
     * <code>optional bytes certAuthInfAccMet = 48;</code>
     */
    public com.google.protobuf.ByteString getCertAuthInfAccMet() {
      return certAuthInfAccMet_;
    }

    public static final int CERTAUTHINFACCLOC_FIELD_NUMBER = 49;
    private com.google.protobuf.ByteString certAuthInfAccLoc_;
    /**
     * <code>optional bytes certAuthInfAccLoc = 49;</code>
     */
    public boolean hasCertAuthInfAccLoc() {
      return ((bitField1_ & 0x00010000) == 0x00010000);
    }
    /**
     * <code>optional bytes certAuthInfAccLoc = 49;</code>
     */
    public com.google.protobuf.ByteString getCertAuthInfAccLoc() {
      return certAuthInfAccLoc_;
    }

    public static final int EXTCNT_FIELD_NUMBER = 50;
    private int extCnt_;
    /**
     * <code>optional uint32 extCnt = 50;</code>
     */
    public boolean hasExtCnt() {
      return ((bitField1_ & 0x00020000) == 0x00020000);
    }
    /**
     * <code>optional uint32 extCnt = 50;</code>
     */
    public int getExtCnt() {
      return extCnt_;
    }

    public static final int PROTABNAME_FIELD_NUMBER = 51;
    private com.google.protobuf.ByteString protabname_;
    /**
     * <code>optional bytes Protabname = 51;</code>
     */
    public boolean hasProtabname() {
      return ((bitField1_ & 0x00040000) == 0x00040000);
    }
    /**
     * <code>optional bytes Protabname = 51;</code>
     */
    public com.google.protobuf.ByteString getProtabname() {
      return protabname_;
    }

    public static final int ISSUER_FIELD_NUMBER = 52;
    private com.google.protobuf.ByteString issuer_;
    /**
     * <code>optional bytes issuer = 52;</code>
     */
    public boolean hasIssuer() {
      return ((bitField1_ & 0x00080000) == 0x00080000);
    }
    /**
     * <code>optional bytes issuer = 52;</code>
     */
    public com.google.protobuf.ByteString getIssuer() {
      return issuer_;
    }

    public static final int SUBJECT_FIELD_NUMBER = 53;
    private com.google.protobuf.ByteString subject_;
    /**
     * <code>optional bytes subject = 53;</code>
     */
    public boolean hasSubject() {
      return ((bitField1_ & 0x00100000) == 0x00100000);
    }
    /**
     * <code>optional bytes subject = 53;</code>
     */
    public com.google.protobuf.ByteString getSubject() {
      return subject_;
    }

    public static final int DAYSREM_FIELD_NUMBER = 54;
    private int daysRem_;
    /**
     * <code>optional uint32 daysRem = 54;</code>
     */
    public boolean hasDaysRem() {
      return ((bitField1_ & 0x00200000) == 0x00200000);
    }
    /**
     * <code>optional uint32 daysRem = 54;</code>
     */
    public int getDaysRem() {
      return daysRem_;
    }

    public static final int PUBKEY_FIELD_NUMBER = 55;
    private com.google.protobuf.ByteString pubkey_;
    /**
     * <code>optional bytes pubkey = 55;</code>
     */
    public boolean hasPubkey() {
      return ((bitField1_ & 0x00400000) == 0x00400000);
    }
    /**
     * <code>optional bytes pubkey = 55;</code>
     */
    public com.google.protobuf.ByteString getPubkey() {
      return pubkey_;
    }

    public static final int FPALG_FIELD_NUMBER = 56;
    private com.google.protobuf.ByteString fpAlg_;
    /**
     * <code>optional bytes fpAlg = 56;</code>
     */
    public boolean hasFpAlg() {
      return ((bitField1_ & 0x00800000) == 0x00800000);
    }
    /**
     * <code>optional bytes fpAlg = 56;</code>
     */
    public com.google.protobuf.ByteString getFpAlg() {
      return fpAlg_;
    }

    public static final int HASH_FIELD_NUMBER = 57;
    private com.google.protobuf.ByteString hash_;
    /**
     * <code>optional bytes hash = 57;</code>
     */
    public boolean hasHash() {
      return ((bitField1_ & 0x01000000) == 0x01000000);
    }
    /**
     * <code>optional bytes hash = 57;</code>
     */
    public com.google.protobuf.ByteString getHash() {
      return hash_;
    }

    public static final int EXTSET_FIELD_NUMBER = 58;
    private com.google.protobuf.ByteString extSet_;
    /**
     * <code>optional bytes extSet = 58;</code>
     */
    public boolean hasExtSet() {
      return ((bitField1_ & 0x02000000) == 0x02000000);
    }
    /**
     * <code>optional bytes extSet = 58;</code>
     */
    public com.google.protobuf.ByteString getExtSet() {
      return extSet_;
    }

    public static final int DAYSTOTAL_FIELD_NUMBER = 59;
    private int daysTotal_;
    /**
     * <code>optional uint32 daysTotal = 59;</code>
     */
    public boolean hasDaysTotal() {
      return ((bitField1_ & 0x04000000) == 0x04000000);
    }
    /**
     * <code>optional uint32 daysTotal = 59;</code>
     */
    public int getDaysTotal() {
      return daysTotal_;
    }

    public static final int SUBALTDNSCNT_FIELD_NUMBER = 60;
    private int subAltDNSCnt_;
    /**
     * <code>optional uint32 subAltDNSCnt = 60;</code>
     */
    public boolean hasSubAltDNSCnt() {
      return ((bitField1_ & 0x08000000) == 0x08000000);
    }
    /**
     * <code>optional uint32 subAltDNSCnt = 60;</code>
     */
    public int getSubAltDNSCnt() {
      return subAltDNSCnt_;
    }

    public static final int AUTHINFO_FIELD_NUMBER = 61;
    private com.google.protobuf.ByteString authinfo_;
    /**
     * <code>optional bytes authinfo = 61;</code>
     */
    public boolean hasAuthinfo() {
      return ((bitField1_ & 0x10000000) == 0x10000000);
    }
    /**
     * <code>optional bytes authinfo = 61;</code>
     */
    public com.google.protobuf.ByteString getAuthinfo() {
      return authinfo_;
    }

    public static final int BASICCONSCA_FIELD_NUMBER = 62;
    private com.google.protobuf.ByteString basicConsCA_;
    /**
     * <code>optional bytes basicConsCA = 62;</code>
     */
    public boolean hasBasicConsCA() {
      return ((bitField1_ & 0x20000000) == 0x20000000);
    }
    /**
     * <code>optional bytes basicConsCA = 62;</code>
     */
    public com.google.protobuf.ByteString getBasicConsCA() {
      return basicConsCA_;
    }

    public static final int BASICCONSPATHLEN_FIELD_NUMBER = 63;
    private com.google.protobuf.ByteString basicConsPathLen_;
    /**
     * <code>optional bytes basicConsPathLen = 63;</code>
     */
    public boolean hasBasicConsPathLen() {
      return ((bitField1_ & 0x40000000) == 0x40000000);
    }
    /**
     * <code>optional bytes basicConsPathLen = 63;</code>
     */
    public com.google.protobuf.ByteString getBasicConsPathLen() {
      return basicConsPathLen_;
    }

    public static final int BASICCONS_FIELD_NUMBER = 64;
    private com.google.protobuf.ByteString basicCons_;
    /**
     * <code>optional bytes basicCons = 64;</code>
     */
    public boolean hasBasicCons() {
      return ((bitField1_ & 0x80000000) == 0x80000000);
    }
    /**
     * <code>optional bytes basicCons = 64;</code>
     */
    public com.google.protobuf.ByteString getBasicCons() {
      return basicCons_;
    }

    public static final int KEYPUR_FIELD_NUMBER = 65;
    private com.google.protobuf.ByteString keyPur_;
    /**
     * <code>optional bytes KeyPur = 65;</code>
     */
    public boolean hasKeyPur() {
      return ((bitField2_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional bytes KeyPur = 65;</code>
     */
    public com.google.protobuf.ByteString getKeyPur() {
      return keyPur_;
    }

    public static final int CERTYPE_FIELD_NUMBER = 66;
    private int certype_;
    /**
     * <code>optional uint32 Certype = 66;</code>
     */
    public boolean hasCertype() {
      return ((bitField2_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional uint32 Certype = 66;</code>
     */
    public int getCertype() {
      return certype_;
    }

    public static final int CERTFULLTEXT_FIELD_NUMBER = 67;
    private com.google.protobuf.ByteString certFullText_;
    /**
     * <code>optional bytes certFullText = 67;</code>
     */
    public boolean hasCertFullText() {
      return ((bitField2_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional bytes certFullText = 67;</code>
     */
    public com.google.protobuf.ByteString getCertFullText() {
      return certFullText_;
    }

    public static final int ALTERNATIVEIPCOUNT_FIELD_NUMBER = 68;
    private int alternativeIpCount_;
    /**
     * <code>optional uint32 alternativeIpCount = 68;</code>
     */
    public boolean hasAlternativeIpCount() {
      return ((bitField2_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional uint32 alternativeIpCount = 68;</code>
     */
    public int getAlternativeIpCount() {
      return alternativeIpCount_;
    }

    public static final int SOURCE_FIELD_NUMBER = 69;
    private int source_;
    /**
     * <code>optional uint32 source = 69;</code>
     */
    public boolean hasSource() {
      return ((bitField2_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional uint32 source = 69;</code>
     */
    public int getSource() {
      return source_;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeUInt64(1, protabID_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeUInt32(2, ver_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, srvNum_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeUInt32(4, issDataLen_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeBytes(5, issComName_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeBytes(6, issConName_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeBytes(7, issLoaName_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeBytes(8, issStaOrProName_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        output.writeBytes(9, issStrAddr_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        output.writeBytes(10, issOrgName_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        output.writeBytes(11, issOrgUniName_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        output.writeBytes(12, issPosOffBox_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        output.writeBytes(13, subComName_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        output.writeBytes(14, subConName_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        output.writeBytes(15, subLoaName_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        output.writeBytes(16, subStaOrProName_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        output.writeBytes(17, subStrAddr_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        output.writeBytes(18, subOrgName_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        output.writeBytes(19, subOrgUniName_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        output.writeBytes(20, subPosOffBox_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        output.writeUInt64(21, valNotBef_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        output.writeUInt64(22, valNotAft_);
      }
      if (((bitField0_ & 0x00400000) == 0x00400000)) {
        output.writeBytes(23, rSAMod_);
      }
      if (((bitField0_ & 0x00800000) == 0x00800000)) {
        output.writeBytes(24, rSAExp_);
      }
      if (((bitField0_ & 0x01000000) == 0x01000000)) {
        output.writeBytes(25, dHPriMod_);
      }
      if (((bitField0_ & 0x02000000) == 0x02000000)) {
        output.writeBytes(26, dHPGen_);
      }
      if (((bitField0_ & 0x04000000) == 0x04000000)) {
        output.writeBytes(27, dHPubKey_);
      }
      if (((bitField0_ & 0x08000000) == 0x08000000)) {
        output.writeBytes(28, dSAPubKeyP_);
      }
      if (((bitField0_ & 0x10000000) == 0x10000000)) {
        output.writeBytes(29, dSAPubKeyQ_);
      }
      if (((bitField0_ & 0x20000000) == 0x20000000)) {
        output.writeBytes(30, dSAPubKeyG_);
      }
      if (((bitField0_ & 0x40000000) == 0x40000000)) {
        output.writeBytes(31, sigAlg_);
      }
      if (((bitField0_ & 0x80000000) == 0x80000000)) {
        output.writeBytes(32, sigVal_);
      }
      if (((bitField1_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(33, authKeyID_);
      }
      if (((bitField1_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(34, subKeyID_);
      }
      if (((bitField1_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(35, keyUsage_);
      }
      if (((bitField1_ & 0x00000008) == 0x00000008)) {
        output.writeUInt64(36, priKeyUsaPerNotBef_);
      }
      if (((bitField1_ & 0x00000010) == 0x00000010)) {
        output.writeUInt64(37, priKeyUsaPerNotAft_);
      }
      if (((bitField1_ & 0x00000020) == 0x00000020)) {
        output.writeBytes(38, certPol_);
      }
      if (((bitField1_ & 0x00000040) == 0x00000040)) {
        output.writeBytes(39, subAltDNS_);
      }
      if (((bitField1_ & 0x00000080) == 0x00000080)) {
        output.writeBytes(40, subAltIP_);
      }
      if (((bitField1_ & 0x00000100) == 0x00000100)) {
        output.writeBytes(41, subAltName_);
      }
      if (((bitField1_ & 0x00000200) == 0x00000200)) {
        output.writeBytes(42, issAltNameSys_);
      }
      if (((bitField1_ & 0x00000400) == 0x00000400)) {
        output.writeBytes(43, issAltIP_);
      }
      if (((bitField1_ & 0x00000800) == 0x00000800)) {
        output.writeBytes(44, issAltName_);
      }
      if (((bitField1_ & 0x00001000) == 0x00001000)) {
        output.writeBytes(45, subDirAtt_);
      }
      if (((bitField1_ & 0x00002000) == 0x00002000)) {
        output.writeBytes(46, extKeyUsage_);
      }
      if (((bitField1_ & 0x00004000) == 0x00004000)) {
        output.writeBytes(47, certRevListSrc_);
      }
      if (((bitField1_ & 0x00008000) == 0x00008000)) {
        output.writeBytes(48, certAuthInfAccMet_);
      }
      if (((bitField1_ & 0x00010000) == 0x00010000)) {
        output.writeBytes(49, certAuthInfAccLoc_);
      }
      if (((bitField1_ & 0x00020000) == 0x00020000)) {
        output.writeUInt32(50, extCnt_);
      }
      if (((bitField1_ & 0x00040000) == 0x00040000)) {
        output.writeBytes(51, protabname_);
      }
      if (((bitField1_ & 0x00080000) == 0x00080000)) {
        output.writeBytes(52, issuer_);
      }
      if (((bitField1_ & 0x00100000) == 0x00100000)) {
        output.writeBytes(53, subject_);
      }
      if (((bitField1_ & 0x00200000) == 0x00200000)) {
        output.writeUInt32(54, daysRem_);
      }
      if (((bitField1_ & 0x00400000) == 0x00400000)) {
        output.writeBytes(55, pubkey_);
      }
      if (((bitField1_ & 0x00800000) == 0x00800000)) {
        output.writeBytes(56, fpAlg_);
      }
      if (((bitField1_ & 0x01000000) == 0x01000000)) {
        output.writeBytes(57, hash_);
      }
      if (((bitField1_ & 0x02000000) == 0x02000000)) {
        output.writeBytes(58, extSet_);
      }
      if (((bitField1_ & 0x04000000) == 0x04000000)) {
        output.writeUInt32(59, daysTotal_);
      }
      if (((bitField1_ & 0x08000000) == 0x08000000)) {
        output.writeUInt32(60, subAltDNSCnt_);
      }
      if (((bitField1_ & 0x10000000) == 0x10000000)) {
        output.writeBytes(61, authinfo_);
      }
      if (((bitField1_ & 0x20000000) == 0x20000000)) {
        output.writeBytes(62, basicConsCA_);
      }
      if (((bitField1_ & 0x40000000) == 0x40000000)) {
        output.writeBytes(63, basicConsPathLen_);
      }
      if (((bitField1_ & 0x80000000) == 0x80000000)) {
        output.writeBytes(64, basicCons_);
      }
      if (((bitField2_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(65, keyPur_);
      }
      if (((bitField2_ & 0x00000002) == 0x00000002)) {
        output.writeUInt32(66, certype_);
      }
      if (((bitField2_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(67, certFullText_);
      }
      if (((bitField2_ & 0x00000008) == 0x00000008)) {
        output.writeUInt32(68, alternativeIpCount_);
      }
      if (((bitField2_ & 0x00000010) == 0x00000010)) {
        output.writeUInt32(69, source_);
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, protabID_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, ver_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, srvNum_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, issDataLen_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(5, issComName_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, issConName_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(7, issLoaName_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(8, issStaOrProName_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(9, issStrAddr_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(10, issOrgName_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(11, issOrgUniName_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(12, issPosOffBox_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(13, subComName_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(14, subConName_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(15, subLoaName_);
      }
      if (((bitField0_ & 0x00008000) == 0x00008000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(16, subStaOrProName_);
      }
      if (((bitField0_ & 0x00010000) == 0x00010000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(17, subStrAddr_);
      }
      if (((bitField0_ & 0x00020000) == 0x00020000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(18, subOrgName_);
      }
      if (((bitField0_ & 0x00040000) == 0x00040000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(19, subOrgUniName_);
      }
      if (((bitField0_ & 0x00080000) == 0x00080000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(20, subPosOffBox_);
      }
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(21, valNotBef_);
      }
      if (((bitField0_ & 0x00200000) == 0x00200000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(22, valNotAft_);
      }
      if (((bitField0_ & 0x00400000) == 0x00400000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(23, rSAMod_);
      }
      if (((bitField0_ & 0x00800000) == 0x00800000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(24, rSAExp_);
      }
      if (((bitField0_ & 0x01000000) == 0x01000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(25, dHPriMod_);
      }
      if (((bitField0_ & 0x02000000) == 0x02000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(26, dHPGen_);
      }
      if (((bitField0_ & 0x04000000) == 0x04000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(27, dHPubKey_);
      }
      if (((bitField0_ & 0x08000000) == 0x08000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(28, dSAPubKeyP_);
      }
      if (((bitField0_ & 0x10000000) == 0x10000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(29, dSAPubKeyQ_);
      }
      if (((bitField0_ & 0x20000000) == 0x20000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(30, dSAPubKeyG_);
      }
      if (((bitField0_ & 0x40000000) == 0x40000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(31, sigAlg_);
      }
      if (((bitField0_ & 0x80000000) == 0x80000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(32, sigVal_);
      }
      if (((bitField1_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(33, authKeyID_);
      }
      if (((bitField1_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(34, subKeyID_);
      }
      if (((bitField1_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(35, keyUsage_);
      }
      if (((bitField1_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(36, priKeyUsaPerNotBef_);
      }
      if (((bitField1_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(37, priKeyUsaPerNotAft_);
      }
      if (((bitField1_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(38, certPol_);
      }
      if (((bitField1_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(39, subAltDNS_);
      }
      if (((bitField1_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(40, subAltIP_);
      }
      if (((bitField1_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(41, subAltName_);
      }
      if (((bitField1_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(42, issAltNameSys_);
      }
      if (((bitField1_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(43, issAltIP_);
      }
      if (((bitField1_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(44, issAltName_);
      }
      if (((bitField1_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(45, subDirAtt_);
      }
      if (((bitField1_ & 0x00002000) == 0x00002000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(46, extKeyUsage_);
      }
      if (((bitField1_ & 0x00004000) == 0x00004000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(47, certRevListSrc_);
      }
      if (((bitField1_ & 0x00008000) == 0x00008000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(48, certAuthInfAccMet_);
      }
      if (((bitField1_ & 0x00010000) == 0x00010000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(49, certAuthInfAccLoc_);
      }
      if (((bitField1_ & 0x00020000) == 0x00020000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(50, extCnt_);
      }
      if (((bitField1_ & 0x00040000) == 0x00040000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(51, protabname_);
      }
      if (((bitField1_ & 0x00080000) == 0x00080000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(52, issuer_);
      }
      if (((bitField1_ & 0x00100000) == 0x00100000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(53, subject_);
      }
      if (((bitField1_ & 0x00200000) == 0x00200000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(54, daysRem_);
      }
      if (((bitField1_ & 0x00400000) == 0x00400000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(55, pubkey_);
      }
      if (((bitField1_ & 0x00800000) == 0x00800000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(56, fpAlg_);
      }
      if (((bitField1_ & 0x01000000) == 0x01000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(57, hash_);
      }
      if (((bitField1_ & 0x02000000) == 0x02000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(58, extSet_);
      }
      if (((bitField1_ & 0x04000000) == 0x04000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(59, daysTotal_);
      }
      if (((bitField1_ & 0x08000000) == 0x08000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(60, subAltDNSCnt_);
      }
      if (((bitField1_ & 0x10000000) == 0x10000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(61, authinfo_);
      }
      if (((bitField1_ & 0x20000000) == 0x20000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(62, basicConsCA_);
      }
      if (((bitField1_ & 0x40000000) == 0x40000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(63, basicConsPathLen_);
      }
      if (((bitField1_ & 0x80000000) == 0x80000000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(64, basicCons_);
      }
      if (((bitField2_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(65, keyPur_);
      }
      if (((bitField2_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(66, certype_);
      }
      if (((bitField2_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(67, certFullText_);
      }
      if (((bitField2_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(68, alternativeIpCount_);
      }
      if (((bitField2_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(69, source_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof X509CerInfo)) {
        return super.equals(obj);
      }
      X509CerInfo other = (X509CerInfo) obj;

      boolean result = true;
      result = result && (hasProtabID() == other.hasProtabID());
      if (hasProtabID()) {
        result = result && (getProtabID()
            == other.getProtabID());
      }
      result = result && (hasVer() == other.hasVer());
      if (hasVer()) {
        result = result && (getVer()
            == other.getVer());
      }
      result = result && (hasSrvNum() == other.hasSrvNum());
      if (hasSrvNum()) {
        result = result && getSrvNum()
            .equals(other.getSrvNum());
      }
      result = result && (hasIssDataLen() == other.hasIssDataLen());
      if (hasIssDataLen()) {
        result = result && (getIssDataLen()
            == other.getIssDataLen());
      }
      result = result && (hasIssComName() == other.hasIssComName());
      if (hasIssComName()) {
        result = result && getIssComName()
            .equals(other.getIssComName());
      }
      result = result && (hasIssConName() == other.hasIssConName());
      if (hasIssConName()) {
        result = result && getIssConName()
            .equals(other.getIssConName());
      }
      result = result && (hasIssLoaName() == other.hasIssLoaName());
      if (hasIssLoaName()) {
        result = result && getIssLoaName()
            .equals(other.getIssLoaName());
      }
      result = result && (hasIssStaOrProName() == other.hasIssStaOrProName());
      if (hasIssStaOrProName()) {
        result = result && getIssStaOrProName()
            .equals(other.getIssStaOrProName());
      }
      result = result && (hasIssStrAddr() == other.hasIssStrAddr());
      if (hasIssStrAddr()) {
        result = result && getIssStrAddr()
            .equals(other.getIssStrAddr());
      }
      result = result && (hasIssOrgName() == other.hasIssOrgName());
      if (hasIssOrgName()) {
        result = result && getIssOrgName()
            .equals(other.getIssOrgName());
      }
      result = result && (hasIssOrgUniName() == other.hasIssOrgUniName());
      if (hasIssOrgUniName()) {
        result = result && getIssOrgUniName()
            .equals(other.getIssOrgUniName());
      }
      result = result && (hasIssPosOffBox() == other.hasIssPosOffBox());
      if (hasIssPosOffBox()) {
        result = result && getIssPosOffBox()
            .equals(other.getIssPosOffBox());
      }
      result = result && (hasSubComName() == other.hasSubComName());
      if (hasSubComName()) {
        result = result && getSubComName()
            .equals(other.getSubComName());
      }
      result = result && (hasSubConName() == other.hasSubConName());
      if (hasSubConName()) {
        result = result && getSubConName()
            .equals(other.getSubConName());
      }
      result = result && (hasSubLoaName() == other.hasSubLoaName());
      if (hasSubLoaName()) {
        result = result && getSubLoaName()
            .equals(other.getSubLoaName());
      }
      result = result && (hasSubStaOrProName() == other.hasSubStaOrProName());
      if (hasSubStaOrProName()) {
        result = result && getSubStaOrProName()
            .equals(other.getSubStaOrProName());
      }
      result = result && (hasSubStrAddr() == other.hasSubStrAddr());
      if (hasSubStrAddr()) {
        result = result && getSubStrAddr()
            .equals(other.getSubStrAddr());
      }
      result = result && (hasSubOrgName() == other.hasSubOrgName());
      if (hasSubOrgName()) {
        result = result && getSubOrgName()
            .equals(other.getSubOrgName());
      }
      result = result && (hasSubOrgUniName() == other.hasSubOrgUniName());
      if (hasSubOrgUniName()) {
        result = result && getSubOrgUniName()
            .equals(other.getSubOrgUniName());
      }
      result = result && (hasSubPosOffBox() == other.hasSubPosOffBox());
      if (hasSubPosOffBox()) {
        result = result && getSubPosOffBox()
            .equals(other.getSubPosOffBox());
      }
      result = result && (hasValNotBef() == other.hasValNotBef());
      if (hasValNotBef()) {
        result = result && (getValNotBef()
            == other.getValNotBef());
      }
      result = result && (hasValNotAft() == other.hasValNotAft());
      if (hasValNotAft()) {
        result = result && (getValNotAft()
            == other.getValNotAft());
      }
      result = result && (hasRSAMod() == other.hasRSAMod());
      if (hasRSAMod()) {
        result = result && getRSAMod()
            .equals(other.getRSAMod());
      }
      result = result && (hasRSAExp() == other.hasRSAExp());
      if (hasRSAExp()) {
        result = result && getRSAExp()
            .equals(other.getRSAExp());
      }
      result = result && (hasDHPriMod() == other.hasDHPriMod());
      if (hasDHPriMod()) {
        result = result && getDHPriMod()
            .equals(other.getDHPriMod());
      }
      result = result && (hasDHPGen() == other.hasDHPGen());
      if (hasDHPGen()) {
        result = result && getDHPGen()
            .equals(other.getDHPGen());
      }
      result = result && (hasDHPubKey() == other.hasDHPubKey());
      if (hasDHPubKey()) {
        result = result && getDHPubKey()
            .equals(other.getDHPubKey());
      }
      result = result && (hasDSAPubKeyP() == other.hasDSAPubKeyP());
      if (hasDSAPubKeyP()) {
        result = result && getDSAPubKeyP()
            .equals(other.getDSAPubKeyP());
      }
      result = result && (hasDSAPubKeyQ() == other.hasDSAPubKeyQ());
      if (hasDSAPubKeyQ()) {
        result = result && getDSAPubKeyQ()
            .equals(other.getDSAPubKeyQ());
      }
      result = result && (hasDSAPubKeyG() == other.hasDSAPubKeyG());
      if (hasDSAPubKeyG()) {
        result = result && getDSAPubKeyG()
            .equals(other.getDSAPubKeyG());
      }
      result = result && (hasSigAlg() == other.hasSigAlg());
      if (hasSigAlg()) {
        result = result && getSigAlg()
            .equals(other.getSigAlg());
      }
      result = result && (hasSigVal() == other.hasSigVal());
      if (hasSigVal()) {
        result = result && getSigVal()
            .equals(other.getSigVal());
      }
      result = result && (hasAuthKeyID() == other.hasAuthKeyID());
      if (hasAuthKeyID()) {
        result = result && getAuthKeyID()
            .equals(other.getAuthKeyID());
      }
      result = result && (hasSubKeyID() == other.hasSubKeyID());
      if (hasSubKeyID()) {
        result = result && getSubKeyID()
            .equals(other.getSubKeyID());
      }
      result = result && (hasKeyUsage() == other.hasKeyUsage());
      if (hasKeyUsage()) {
        result = result && getKeyUsage()
            .equals(other.getKeyUsage());
      }
      result = result && (hasPriKeyUsaPerNotBef() == other.hasPriKeyUsaPerNotBef());
      if (hasPriKeyUsaPerNotBef()) {
        result = result && (getPriKeyUsaPerNotBef()
            == other.getPriKeyUsaPerNotBef());
      }
      result = result && (hasPriKeyUsaPerNotAft() == other.hasPriKeyUsaPerNotAft());
      if (hasPriKeyUsaPerNotAft()) {
        result = result && (getPriKeyUsaPerNotAft()
            == other.getPriKeyUsaPerNotAft());
      }
      result = result && (hasCertPol() == other.hasCertPol());
      if (hasCertPol()) {
        result = result && getCertPol()
            .equals(other.getCertPol());
      }
      result = result && (hasSubAltDNS() == other.hasSubAltDNS());
      if (hasSubAltDNS()) {
        result = result && getSubAltDNS()
            .equals(other.getSubAltDNS());
      }
      result = result && (hasSubAltIP() == other.hasSubAltIP());
      if (hasSubAltIP()) {
        result = result && getSubAltIP()
            .equals(other.getSubAltIP());
      }
      result = result && (hasSubAltName() == other.hasSubAltName());
      if (hasSubAltName()) {
        result = result && getSubAltName()
            .equals(other.getSubAltName());
      }
      result = result && (hasIssAltNameSys() == other.hasIssAltNameSys());
      if (hasIssAltNameSys()) {
        result = result && getIssAltNameSys()
            .equals(other.getIssAltNameSys());
      }
      result = result && (hasIssAltIP() == other.hasIssAltIP());
      if (hasIssAltIP()) {
        result = result && getIssAltIP()
            .equals(other.getIssAltIP());
      }
      result = result && (hasIssAltName() == other.hasIssAltName());
      if (hasIssAltName()) {
        result = result && getIssAltName()
            .equals(other.getIssAltName());
      }
      result = result && (hasSubDirAtt() == other.hasSubDirAtt());
      if (hasSubDirAtt()) {
        result = result && getSubDirAtt()
            .equals(other.getSubDirAtt());
      }
      result = result && (hasExtKeyUsage() == other.hasExtKeyUsage());
      if (hasExtKeyUsage()) {
        result = result && getExtKeyUsage()
            .equals(other.getExtKeyUsage());
      }
      result = result && (hasCertRevListSrc() == other.hasCertRevListSrc());
      if (hasCertRevListSrc()) {
        result = result && getCertRevListSrc()
            .equals(other.getCertRevListSrc());
      }
      result = result && (hasCertAuthInfAccMet() == other.hasCertAuthInfAccMet());
      if (hasCertAuthInfAccMet()) {
        result = result && getCertAuthInfAccMet()
            .equals(other.getCertAuthInfAccMet());
      }
      result = result && (hasCertAuthInfAccLoc() == other.hasCertAuthInfAccLoc());
      if (hasCertAuthInfAccLoc()) {
        result = result && getCertAuthInfAccLoc()
            .equals(other.getCertAuthInfAccLoc());
      }
      result = result && (hasExtCnt() == other.hasExtCnt());
      if (hasExtCnt()) {
        result = result && (getExtCnt()
            == other.getExtCnt());
      }
      result = result && (hasProtabname() == other.hasProtabname());
      if (hasProtabname()) {
        result = result && getProtabname()
            .equals(other.getProtabname());
      }
      result = result && (hasIssuer() == other.hasIssuer());
      if (hasIssuer()) {
        result = result && getIssuer()
            .equals(other.getIssuer());
      }
      result = result && (hasSubject() == other.hasSubject());
      if (hasSubject()) {
        result = result && getSubject()
            .equals(other.getSubject());
      }
      result = result && (hasDaysRem() == other.hasDaysRem());
      if (hasDaysRem()) {
        result = result && (getDaysRem()
            == other.getDaysRem());
      }
      result = result && (hasPubkey() == other.hasPubkey());
      if (hasPubkey()) {
        result = result && getPubkey()
            .equals(other.getPubkey());
      }
      result = result && (hasFpAlg() == other.hasFpAlg());
      if (hasFpAlg()) {
        result = result && getFpAlg()
            .equals(other.getFpAlg());
      }
      result = result && (hasHash() == other.hasHash());
      if (hasHash()) {
        result = result && getHash()
            .equals(other.getHash());
      }
      result = result && (hasExtSet() == other.hasExtSet());
      if (hasExtSet()) {
        result = result && getExtSet()
            .equals(other.getExtSet());
      }
      result = result && (hasDaysTotal() == other.hasDaysTotal());
      if (hasDaysTotal()) {
        result = result && (getDaysTotal()
            == other.getDaysTotal());
      }
      result = result && (hasSubAltDNSCnt() == other.hasSubAltDNSCnt());
      if (hasSubAltDNSCnt()) {
        result = result && (getSubAltDNSCnt()
            == other.getSubAltDNSCnt());
      }
      result = result && (hasAuthinfo() == other.hasAuthinfo());
      if (hasAuthinfo()) {
        result = result && getAuthinfo()
            .equals(other.getAuthinfo());
      }
      result = result && (hasBasicConsCA() == other.hasBasicConsCA());
      if (hasBasicConsCA()) {
        result = result && getBasicConsCA()
            .equals(other.getBasicConsCA());
      }
      result = result && (hasBasicConsPathLen() == other.hasBasicConsPathLen());
      if (hasBasicConsPathLen()) {
        result = result && getBasicConsPathLen()
            .equals(other.getBasicConsPathLen());
      }
      result = result && (hasBasicCons() == other.hasBasicCons());
      if (hasBasicCons()) {
        result = result && getBasicCons()
            .equals(other.getBasicCons());
      }
      result = result && (hasKeyPur() == other.hasKeyPur());
      if (hasKeyPur()) {
        result = result && getKeyPur()
            .equals(other.getKeyPur());
      }
      result = result && (hasCertype() == other.hasCertype());
      if (hasCertype()) {
        result = result && (getCertype()
            == other.getCertype());
      }
      result = result && (hasCertFullText() == other.hasCertFullText());
      if (hasCertFullText()) {
        result = result && getCertFullText()
            .equals(other.getCertFullText());
      }
      result = result && (hasAlternativeIpCount() == other.hasAlternativeIpCount());
      if (hasAlternativeIpCount()) {
        result = result && (getAlternativeIpCount()
            == other.getAlternativeIpCount());
      }
      result = result && (hasSource() == other.hasSource());
      if (hasSource()) {
        result = result && (getSource()
            == other.getSource());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasProtabID()) {
        hash = (37 * hash) + PROTABID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getProtabID());
      }
      if (hasVer()) {
        hash = (37 * hash) + VER_FIELD_NUMBER;
        hash = (53 * hash) + getVer();
      }
      if (hasSrvNum()) {
        hash = (37 * hash) + SRVNUM_FIELD_NUMBER;
        hash = (53 * hash) + getSrvNum().hashCode();
      }
      if (hasIssDataLen()) {
        hash = (37 * hash) + ISSDATALEN_FIELD_NUMBER;
        hash = (53 * hash) + getIssDataLen();
      }
      if (hasIssComName()) {
        hash = (37 * hash) + ISSCOMNAME_FIELD_NUMBER;
        hash = (53 * hash) + getIssComName().hashCode();
      }
      if (hasIssConName()) {
        hash = (37 * hash) + ISSCONNAME_FIELD_NUMBER;
        hash = (53 * hash) + getIssConName().hashCode();
      }
      if (hasIssLoaName()) {
        hash = (37 * hash) + ISSLOANAME_FIELD_NUMBER;
        hash = (53 * hash) + getIssLoaName().hashCode();
      }
      if (hasIssStaOrProName()) {
        hash = (37 * hash) + ISSSTAORPRONAME_FIELD_NUMBER;
        hash = (53 * hash) + getIssStaOrProName().hashCode();
      }
      if (hasIssStrAddr()) {
        hash = (37 * hash) + ISSSTRADDR_FIELD_NUMBER;
        hash = (53 * hash) + getIssStrAddr().hashCode();
      }
      if (hasIssOrgName()) {
        hash = (37 * hash) + ISSORGNAME_FIELD_NUMBER;
        hash = (53 * hash) + getIssOrgName().hashCode();
      }
      if (hasIssOrgUniName()) {
        hash = (37 * hash) + ISSORGUNINAME_FIELD_NUMBER;
        hash = (53 * hash) + getIssOrgUniName().hashCode();
      }
      if (hasIssPosOffBox()) {
        hash = (37 * hash) + ISSPOSOFFBOX_FIELD_NUMBER;
        hash = (53 * hash) + getIssPosOffBox().hashCode();
      }
      if (hasSubComName()) {
        hash = (37 * hash) + SUBCOMNAME_FIELD_NUMBER;
        hash = (53 * hash) + getSubComName().hashCode();
      }
      if (hasSubConName()) {
        hash = (37 * hash) + SUBCONNAME_FIELD_NUMBER;
        hash = (53 * hash) + getSubConName().hashCode();
      }
      if (hasSubLoaName()) {
        hash = (37 * hash) + SUBLOANAME_FIELD_NUMBER;
        hash = (53 * hash) + getSubLoaName().hashCode();
      }
      if (hasSubStaOrProName()) {
        hash = (37 * hash) + SUBSTAORPRONAME_FIELD_NUMBER;
        hash = (53 * hash) + getSubStaOrProName().hashCode();
      }
      if (hasSubStrAddr()) {
        hash = (37 * hash) + SUBSTRADDR_FIELD_NUMBER;
        hash = (53 * hash) + getSubStrAddr().hashCode();
      }
      if (hasSubOrgName()) {
        hash = (37 * hash) + SUBORGNAME_FIELD_NUMBER;
        hash = (53 * hash) + getSubOrgName().hashCode();
      }
      if (hasSubOrgUniName()) {
        hash = (37 * hash) + SUBORGUNINAME_FIELD_NUMBER;
        hash = (53 * hash) + getSubOrgUniName().hashCode();
      }
      if (hasSubPosOffBox()) {
        hash = (37 * hash) + SUBPOSOFFBOX_FIELD_NUMBER;
        hash = (53 * hash) + getSubPosOffBox().hashCode();
      }
      if (hasValNotBef()) {
        hash = (37 * hash) + VALNOTBEF_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getValNotBef());
      }
      if (hasValNotAft()) {
        hash = (37 * hash) + VALNOTAFT_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getValNotAft());
      }
      if (hasRSAMod()) {
        hash = (37 * hash) + RSAMOD_FIELD_NUMBER;
        hash = (53 * hash) + getRSAMod().hashCode();
      }
      if (hasRSAExp()) {
        hash = (37 * hash) + RSAEXP_FIELD_NUMBER;
        hash = (53 * hash) + getRSAExp().hashCode();
      }
      if (hasDHPriMod()) {
        hash = (37 * hash) + DHPRIMOD_FIELD_NUMBER;
        hash = (53 * hash) + getDHPriMod().hashCode();
      }
      if (hasDHPGen()) {
        hash = (37 * hash) + DHPGEN_FIELD_NUMBER;
        hash = (53 * hash) + getDHPGen().hashCode();
      }
      if (hasDHPubKey()) {
        hash = (37 * hash) + DHPUBKEY_FIELD_NUMBER;
        hash = (53 * hash) + getDHPubKey().hashCode();
      }
      if (hasDSAPubKeyP()) {
        hash = (37 * hash) + DSAPUBKEYP_FIELD_NUMBER;
        hash = (53 * hash) + getDSAPubKeyP().hashCode();
      }
      if (hasDSAPubKeyQ()) {
        hash = (37 * hash) + DSAPUBKEYQ_FIELD_NUMBER;
        hash = (53 * hash) + getDSAPubKeyQ().hashCode();
      }
      if (hasDSAPubKeyG()) {
        hash = (37 * hash) + DSAPUBKEYG_FIELD_NUMBER;
        hash = (53 * hash) + getDSAPubKeyG().hashCode();
      }
      if (hasSigAlg()) {
        hash = (37 * hash) + SIGALG_FIELD_NUMBER;
        hash = (53 * hash) + getSigAlg().hashCode();
      }
      if (hasSigVal()) {
        hash = (37 * hash) + SIGVAL_FIELD_NUMBER;
        hash = (53 * hash) + getSigVal().hashCode();
      }
      if (hasAuthKeyID()) {
        hash = (37 * hash) + AUTHKEYID_FIELD_NUMBER;
        hash = (53 * hash) + getAuthKeyID().hashCode();
      }
      if (hasSubKeyID()) {
        hash = (37 * hash) + SUBKEYID_FIELD_NUMBER;
        hash = (53 * hash) + getSubKeyID().hashCode();
      }
      if (hasKeyUsage()) {
        hash = (37 * hash) + KEYUSAGE_FIELD_NUMBER;
        hash = (53 * hash) + getKeyUsage().hashCode();
      }
      if (hasPriKeyUsaPerNotBef()) {
        hash = (37 * hash) + PRIKEYUSAPERNOTBEF_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPriKeyUsaPerNotBef());
      }
      if (hasPriKeyUsaPerNotAft()) {
        hash = (37 * hash) + PRIKEYUSAPERNOTAFT_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPriKeyUsaPerNotAft());
      }
      if (hasCertPol()) {
        hash = (37 * hash) + CERTPOL_FIELD_NUMBER;
        hash = (53 * hash) + getCertPol().hashCode();
      }
      if (hasSubAltDNS()) {
        hash = (37 * hash) + SUBALTDNS_FIELD_NUMBER;
        hash = (53 * hash) + getSubAltDNS().hashCode();
      }
      if (hasSubAltIP()) {
        hash = (37 * hash) + SUBALTIP_FIELD_NUMBER;
        hash = (53 * hash) + getSubAltIP().hashCode();
      }
      if (hasSubAltName()) {
        hash = (37 * hash) + SUBALTNAME_FIELD_NUMBER;
        hash = (53 * hash) + getSubAltName().hashCode();
      }
      if (hasIssAltNameSys()) {
        hash = (37 * hash) + ISSALTNAMESYS_FIELD_NUMBER;
        hash = (53 * hash) + getIssAltNameSys().hashCode();
      }
      if (hasIssAltIP()) {
        hash = (37 * hash) + ISSALTIP_FIELD_NUMBER;
        hash = (53 * hash) + getIssAltIP().hashCode();
      }
      if (hasIssAltName()) {
        hash = (37 * hash) + ISSALTNAME_FIELD_NUMBER;
        hash = (53 * hash) + getIssAltName().hashCode();
      }
      if (hasSubDirAtt()) {
        hash = (37 * hash) + SUBDIRATT_FIELD_NUMBER;
        hash = (53 * hash) + getSubDirAtt().hashCode();
      }
      if (hasExtKeyUsage()) {
        hash = (37 * hash) + EXTKEYUSAGE_FIELD_NUMBER;
        hash = (53 * hash) + getExtKeyUsage().hashCode();
      }
      if (hasCertRevListSrc()) {
        hash = (37 * hash) + CERTREVLISTSRC_FIELD_NUMBER;
        hash = (53 * hash) + getCertRevListSrc().hashCode();
      }
      if (hasCertAuthInfAccMet()) {
        hash = (37 * hash) + CERTAUTHINFACCMET_FIELD_NUMBER;
        hash = (53 * hash) + getCertAuthInfAccMet().hashCode();
      }
      if (hasCertAuthInfAccLoc()) {
        hash = (37 * hash) + CERTAUTHINFACCLOC_FIELD_NUMBER;
        hash = (53 * hash) + getCertAuthInfAccLoc().hashCode();
      }
      if (hasExtCnt()) {
        hash = (37 * hash) + EXTCNT_FIELD_NUMBER;
        hash = (53 * hash) + getExtCnt();
      }
      if (hasProtabname()) {
        hash = (37 * hash) + PROTABNAME_FIELD_NUMBER;
        hash = (53 * hash) + getProtabname().hashCode();
      }
      if (hasIssuer()) {
        hash = (37 * hash) + ISSUER_FIELD_NUMBER;
        hash = (53 * hash) + getIssuer().hashCode();
      }
      if (hasSubject()) {
        hash = (37 * hash) + SUBJECT_FIELD_NUMBER;
        hash = (53 * hash) + getSubject().hashCode();
      }
      if (hasDaysRem()) {
        hash = (37 * hash) + DAYSREM_FIELD_NUMBER;
        hash = (53 * hash) + getDaysRem();
      }
      if (hasPubkey()) {
        hash = (37 * hash) + PUBKEY_FIELD_NUMBER;
        hash = (53 * hash) + getPubkey().hashCode();
      }
      if (hasFpAlg()) {
        hash = (37 * hash) + FPALG_FIELD_NUMBER;
        hash = (53 * hash) + getFpAlg().hashCode();
      }
      if (hasHash()) {
        hash = (37 * hash) + HASH_FIELD_NUMBER;
        hash = (53 * hash) + getHash().hashCode();
      }
      if (hasExtSet()) {
        hash = (37 * hash) + EXTSET_FIELD_NUMBER;
        hash = (53 * hash) + getExtSet().hashCode();
      }
      if (hasDaysTotal()) {
        hash = (37 * hash) + DAYSTOTAL_FIELD_NUMBER;
        hash = (53 * hash) + getDaysTotal();
      }
      if (hasSubAltDNSCnt()) {
        hash = (37 * hash) + SUBALTDNSCNT_FIELD_NUMBER;
        hash = (53 * hash) + getSubAltDNSCnt();
      }
      if (hasAuthinfo()) {
        hash = (37 * hash) + AUTHINFO_FIELD_NUMBER;
        hash = (53 * hash) + getAuthinfo().hashCode();
      }
      if (hasBasicConsCA()) {
        hash = (37 * hash) + BASICCONSCA_FIELD_NUMBER;
        hash = (53 * hash) + getBasicConsCA().hashCode();
      }
      if (hasBasicConsPathLen()) {
        hash = (37 * hash) + BASICCONSPATHLEN_FIELD_NUMBER;
        hash = (53 * hash) + getBasicConsPathLen().hashCode();
      }
      if (hasBasicCons()) {
        hash = (37 * hash) + BASICCONS_FIELD_NUMBER;
        hash = (53 * hash) + getBasicCons().hashCode();
      }
      if (hasKeyPur()) {
        hash = (37 * hash) + KEYPUR_FIELD_NUMBER;
        hash = (53 * hash) + getKeyPur().hashCode();
      }
      if (hasCertype()) {
        hash = (37 * hash) + CERTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getCertype();
      }
      if (hasCertFullText()) {
        hash = (37 * hash) + CERTFULLTEXT_FIELD_NUMBER;
        hash = (53 * hash) + getCertFullText().hashCode();
      }
      if (hasAlternativeIpCount()) {
        hash = (37 * hash) + ALTERNATIVEIPCOUNT_FIELD_NUMBER;
        hash = (53 * hash) + getAlternativeIpCount();
      }
      if (hasSource()) {
        hash = (37 * hash) + SOURCE_FIELD_NUMBER;
        hash = (53 * hash) + getSource();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static X509CerInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static X509CerInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static X509CerInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static X509CerInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static X509CerInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static X509CerInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static X509CerInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static X509CerInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static X509CerInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static X509CerInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static X509CerInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static X509CerInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(X509CerInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code X509CerInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:X509CerInfo)
        X509CerInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return X509CerInfoOuterClass.internal_static_X509CerInfo_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return X509CerInfoOuterClass.internal_static_X509CerInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                X509CerInfo.class, Builder.class);
      }

      // Construct using X509CerInfoOuterClass.X509CerInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        protabID_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        ver_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        srvNum_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000004);
        issDataLen_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        issComName_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000010);
        issConName_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000020);
        issLoaName_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000040);
        issStaOrProName_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000080);
        issStrAddr_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000100);
        issOrgName_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000200);
        issOrgUniName_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000400);
        issPosOffBox_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000800);
        subComName_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00001000);
        subConName_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00002000);
        subLoaName_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00004000);
        subStaOrProName_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00008000);
        subStrAddr_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00010000);
        subOrgName_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00020000);
        subOrgUniName_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00040000);
        subPosOffBox_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00080000);
        valNotBef_ = 0L;
        bitField0_ = (bitField0_ & ~0x00100000);
        valNotAft_ = 0L;
        bitField0_ = (bitField0_ & ~0x00200000);
        rSAMod_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00400000);
        rSAExp_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00800000);
        dHPriMod_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x01000000);
        dHPGen_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x02000000);
        dHPubKey_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x04000000);
        dSAPubKeyP_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x08000000);
        dSAPubKeyQ_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x10000000);
        dSAPubKeyG_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x20000000);
        sigAlg_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x40000000);
        sigVal_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x80000000);
        authKeyID_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000001);
        subKeyID_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000002);
        keyUsage_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000004);
        priKeyUsaPerNotBef_ = 0L;
        bitField1_ = (bitField1_ & ~0x00000008);
        priKeyUsaPerNotAft_ = 0L;
        bitField1_ = (bitField1_ & ~0x00000010);
        certPol_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000020);
        subAltDNS_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000040);
        subAltIP_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000080);
        subAltName_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000100);
        issAltNameSys_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000200);
        issAltIP_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000400);
        issAltName_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00000800);
        subDirAtt_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00001000);
        extKeyUsage_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00002000);
        certRevListSrc_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00004000);
        certAuthInfAccMet_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00008000);
        certAuthInfAccLoc_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00010000);
        extCnt_ = 0;
        bitField1_ = (bitField1_ & ~0x00020000);
        protabname_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00040000);
        issuer_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00080000);
        subject_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00100000);
        daysRem_ = 0;
        bitField1_ = (bitField1_ & ~0x00200000);
        pubkey_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00400000);
        fpAlg_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x00800000);
        hash_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x01000000);
        extSet_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x02000000);
        daysTotal_ = 0;
        bitField1_ = (bitField1_ & ~0x04000000);
        subAltDNSCnt_ = 0;
        bitField1_ = (bitField1_ & ~0x08000000);
        authinfo_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x10000000);
        basicConsCA_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x20000000);
        basicConsPathLen_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x40000000);
        basicCons_ = com.google.protobuf.ByteString.EMPTY;
        bitField1_ = (bitField1_ & ~0x80000000);
        keyPur_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00000001);
        certype_ = 0;
        bitField2_ = (bitField2_ & ~0x00000002);
        certFullText_ = com.google.protobuf.ByteString.EMPTY;
        bitField2_ = (bitField2_ & ~0x00000004);
        alternativeIpCount_ = 0;
        bitField2_ = (bitField2_ & ~0x00000008);
        source_ = 0;
        bitField2_ = (bitField2_ & ~0x00000010);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return X509CerInfoOuterClass.internal_static_X509CerInfo_descriptor;
      }

      @java.lang.Override
      public X509CerInfoOuterClass.X509CerInfo getDefaultInstanceForType() {
        return X509CerInfoOuterClass.X509CerInfo.getDefaultInstance();
      }

      @java.lang.Override
      public X509CerInfoOuterClass.X509CerInfo build() {
        X509CerInfoOuterClass.X509CerInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public X509CerInfoOuterClass.X509CerInfo buildPartial() {
        X509CerInfoOuterClass.X509CerInfo result = new X509CerInfoOuterClass.X509CerInfo(this);
        int from_bitField0_ = bitField0_;
        int from_bitField1_ = bitField1_;
        int from_bitField2_ = bitField2_;
        int to_bitField0_ = 0;
        int to_bitField1_ = 0;
        int to_bitField2_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.protabID_ = protabID_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.ver_ = ver_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.srvNum_ = srvNum_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.issDataLen_ = issDataLen_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.issComName_ = issComName_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.issConName_ = issConName_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.issLoaName_ = issLoaName_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.issStaOrProName_ = issStaOrProName_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.issStrAddr_ = issStrAddr_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000200;
        }
        result.issOrgName_ = issOrgName_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000400;
        }
        result.issOrgUniName_ = issOrgUniName_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000800;
        }
        result.issPosOffBox_ = issPosOffBox_;
        if (((from_bitField0_ & 0x00001000) == 0x00001000)) {
          to_bitField0_ |= 0x00001000;
        }
        result.subComName_ = subComName_;
        if (((from_bitField0_ & 0x00002000) == 0x00002000)) {
          to_bitField0_ |= 0x00002000;
        }
        result.subConName_ = subConName_;
        if (((from_bitField0_ & 0x00004000) == 0x00004000)) {
          to_bitField0_ |= 0x00004000;
        }
        result.subLoaName_ = subLoaName_;
        if (((from_bitField0_ & 0x00008000) == 0x00008000)) {
          to_bitField0_ |= 0x00008000;
        }
        result.subStaOrProName_ = subStaOrProName_;
        if (((from_bitField0_ & 0x00010000) == 0x00010000)) {
          to_bitField0_ |= 0x00010000;
        }
        result.subStrAddr_ = subStrAddr_;
        if (((from_bitField0_ & 0x00020000) == 0x00020000)) {
          to_bitField0_ |= 0x00020000;
        }
        result.subOrgName_ = subOrgName_;
        if (((from_bitField0_ & 0x00040000) == 0x00040000)) {
          to_bitField0_ |= 0x00040000;
        }
        result.subOrgUniName_ = subOrgUniName_;
        if (((from_bitField0_ & 0x00080000) == 0x00080000)) {
          to_bitField0_ |= 0x00080000;
        }
        result.subPosOffBox_ = subPosOffBox_;
        if (((from_bitField0_ & 0x00100000) == 0x00100000)) {
          to_bitField0_ |= 0x00100000;
        }
        result.valNotBef_ = valNotBef_;
        if (((from_bitField0_ & 0x00200000) == 0x00200000)) {
          to_bitField0_ |= 0x00200000;
        }
        result.valNotAft_ = valNotAft_;
        if (((from_bitField0_ & 0x00400000) == 0x00400000)) {
          to_bitField0_ |= 0x00400000;
        }
        result.rSAMod_ = rSAMod_;
        if (((from_bitField0_ & 0x00800000) == 0x00800000)) {
          to_bitField0_ |= 0x00800000;
        }
        result.rSAExp_ = rSAExp_;
        if (((from_bitField0_ & 0x01000000) == 0x01000000)) {
          to_bitField0_ |= 0x01000000;
        }
        result.dHPriMod_ = dHPriMod_;
        if (((from_bitField0_ & 0x02000000) == 0x02000000)) {
          to_bitField0_ |= 0x02000000;
        }
        result.dHPGen_ = dHPGen_;
        if (((from_bitField0_ & 0x04000000) == 0x04000000)) {
          to_bitField0_ |= 0x04000000;
        }
        result.dHPubKey_ = dHPubKey_;
        if (((from_bitField0_ & 0x08000000) == 0x08000000)) {
          to_bitField0_ |= 0x08000000;
        }
        result.dSAPubKeyP_ = dSAPubKeyP_;
        if (((from_bitField0_ & 0x10000000) == 0x10000000)) {
          to_bitField0_ |= 0x10000000;
        }
        result.dSAPubKeyQ_ = dSAPubKeyQ_;
        if (((from_bitField0_ & 0x20000000) == 0x20000000)) {
          to_bitField0_ |= 0x20000000;
        }
        result.dSAPubKeyG_ = dSAPubKeyG_;
        if (((from_bitField0_ & 0x40000000) == 0x40000000)) {
          to_bitField0_ |= 0x40000000;
        }
        result.sigAlg_ = sigAlg_;
        if (((from_bitField0_ & 0x80000000) == 0x80000000)) {
          to_bitField0_ |= 0x80000000;
        }
        result.sigVal_ = sigVal_;
        if (((from_bitField1_ & 0x00000001) == 0x00000001)) {
          to_bitField1_ |= 0x00000001;
        }
        result.authKeyID_ = authKeyID_;
        if (((from_bitField1_ & 0x00000002) == 0x00000002)) {
          to_bitField1_ |= 0x00000002;
        }
        result.subKeyID_ = subKeyID_;
        if (((from_bitField1_ & 0x00000004) == 0x00000004)) {
          to_bitField1_ |= 0x00000004;
        }
        result.keyUsage_ = keyUsage_;
        if (((from_bitField1_ & 0x00000008) == 0x00000008)) {
          to_bitField1_ |= 0x00000008;
        }
        result.priKeyUsaPerNotBef_ = priKeyUsaPerNotBef_;
        if (((from_bitField1_ & 0x00000010) == 0x00000010)) {
          to_bitField1_ |= 0x00000010;
        }
        result.priKeyUsaPerNotAft_ = priKeyUsaPerNotAft_;
        if (((from_bitField1_ & 0x00000020) == 0x00000020)) {
          to_bitField1_ |= 0x00000020;
        }
        result.certPol_ = certPol_;
        if (((from_bitField1_ & 0x00000040) == 0x00000040)) {
          to_bitField1_ |= 0x00000040;
        }
        result.subAltDNS_ = subAltDNS_;
        if (((from_bitField1_ & 0x00000080) == 0x00000080)) {
          to_bitField1_ |= 0x00000080;
        }
        result.subAltIP_ = subAltIP_;
        if (((from_bitField1_ & 0x00000100) == 0x00000100)) {
          to_bitField1_ |= 0x00000100;
        }
        result.subAltName_ = subAltName_;
        if (((from_bitField1_ & 0x00000200) == 0x00000200)) {
          to_bitField1_ |= 0x00000200;
        }
        result.issAltNameSys_ = issAltNameSys_;
        if (((from_bitField1_ & 0x00000400) == 0x00000400)) {
          to_bitField1_ |= 0x00000400;
        }
        result.issAltIP_ = issAltIP_;
        if (((from_bitField1_ & 0x00000800) == 0x00000800)) {
          to_bitField1_ |= 0x00000800;
        }
        result.issAltName_ = issAltName_;
        if (((from_bitField1_ & 0x00001000) == 0x00001000)) {
          to_bitField1_ |= 0x00001000;
        }
        result.subDirAtt_ = subDirAtt_;
        if (((from_bitField1_ & 0x00002000) == 0x00002000)) {
          to_bitField1_ |= 0x00002000;
        }
        result.extKeyUsage_ = extKeyUsage_;
        if (((from_bitField1_ & 0x00004000) == 0x00004000)) {
          to_bitField1_ |= 0x00004000;
        }
        result.certRevListSrc_ = certRevListSrc_;
        if (((from_bitField1_ & 0x00008000) == 0x00008000)) {
          to_bitField1_ |= 0x00008000;
        }
        result.certAuthInfAccMet_ = certAuthInfAccMet_;
        if (((from_bitField1_ & 0x00010000) == 0x00010000)) {
          to_bitField1_ |= 0x00010000;
        }
        result.certAuthInfAccLoc_ = certAuthInfAccLoc_;
        if (((from_bitField1_ & 0x00020000) == 0x00020000)) {
          to_bitField1_ |= 0x00020000;
        }
        result.extCnt_ = extCnt_;
        if (((from_bitField1_ & 0x00040000) == 0x00040000)) {
          to_bitField1_ |= 0x00040000;
        }
        result.protabname_ = protabname_;
        if (((from_bitField1_ & 0x00080000) == 0x00080000)) {
          to_bitField1_ |= 0x00080000;
        }
        result.issuer_ = issuer_;
        if (((from_bitField1_ & 0x00100000) == 0x00100000)) {
          to_bitField1_ |= 0x00100000;
        }
        result.subject_ = subject_;
        if (((from_bitField1_ & 0x00200000) == 0x00200000)) {
          to_bitField1_ |= 0x00200000;
        }
        result.daysRem_ = daysRem_;
        if (((from_bitField1_ & 0x00400000) == 0x00400000)) {
          to_bitField1_ |= 0x00400000;
        }
        result.pubkey_ = pubkey_;
        if (((from_bitField1_ & 0x00800000) == 0x00800000)) {
          to_bitField1_ |= 0x00800000;
        }
        result.fpAlg_ = fpAlg_;
        if (((from_bitField1_ & 0x01000000) == 0x01000000)) {
          to_bitField1_ |= 0x01000000;
        }
        result.hash_ = hash_;
        if (((from_bitField1_ & 0x02000000) == 0x02000000)) {
          to_bitField1_ |= 0x02000000;
        }
        result.extSet_ = extSet_;
        if (((from_bitField1_ & 0x04000000) == 0x04000000)) {
          to_bitField1_ |= 0x04000000;
        }
        result.daysTotal_ = daysTotal_;
        if (((from_bitField1_ & 0x08000000) == 0x08000000)) {
          to_bitField1_ |= 0x08000000;
        }
        result.subAltDNSCnt_ = subAltDNSCnt_;
        if (((from_bitField1_ & 0x10000000) == 0x10000000)) {
          to_bitField1_ |= 0x10000000;
        }
        result.authinfo_ = authinfo_;
        if (((from_bitField1_ & 0x20000000) == 0x20000000)) {
          to_bitField1_ |= 0x20000000;
        }
        result.basicConsCA_ = basicConsCA_;
        if (((from_bitField1_ & 0x40000000) == 0x40000000)) {
          to_bitField1_ |= 0x40000000;
        }
        result.basicConsPathLen_ = basicConsPathLen_;
        if (((from_bitField1_ & 0x80000000) == 0x80000000)) {
          to_bitField1_ |= 0x80000000;
        }
        result.basicCons_ = basicCons_;
        if (((from_bitField2_ & 0x00000001) == 0x00000001)) {
          to_bitField2_ |= 0x00000001;
        }
        result.keyPur_ = keyPur_;
        if (((from_bitField2_ & 0x00000002) == 0x00000002)) {
          to_bitField2_ |= 0x00000002;
        }
        result.certype_ = certype_;
        if (((from_bitField2_ & 0x00000004) == 0x00000004)) {
          to_bitField2_ |= 0x00000004;
        }
        result.certFullText_ = certFullText_;
        if (((from_bitField2_ & 0x00000008) == 0x00000008)) {
          to_bitField2_ |= 0x00000008;
        }
        result.alternativeIpCount_ = alternativeIpCount_;
        if (((from_bitField2_ & 0x00000010) == 0x00000010)) {
          to_bitField2_ |= 0x00000010;
        }
        result.source_ = source_;
        result.bitField0_ = to_bitField0_;
        result.bitField1_ = to_bitField1_;
        result.bitField2_ = to_bitField2_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof X509CerInfoOuterClass.X509CerInfo) {
          return mergeFrom((X509CerInfoOuterClass.X509CerInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(X509CerInfoOuterClass.X509CerInfo other) {
        if (other == X509CerInfoOuterClass.X509CerInfo.getDefaultInstance()) return this;
        if (other.hasProtabID()) {
          setProtabID(other.getProtabID());
        }
        if (other.hasVer()) {
          setVer(other.getVer());
        }
        if (other.hasSrvNum()) {
          setSrvNum(other.getSrvNum());
        }
        if (other.hasIssDataLen()) {
          setIssDataLen(other.getIssDataLen());
        }
        if (other.hasIssComName()) {
          setIssComName(other.getIssComName());
        }
        if (other.hasIssConName()) {
          setIssConName(other.getIssConName());
        }
        if (other.hasIssLoaName()) {
          setIssLoaName(other.getIssLoaName());
        }
        if (other.hasIssStaOrProName()) {
          setIssStaOrProName(other.getIssStaOrProName());
        }
        if (other.hasIssStrAddr()) {
          setIssStrAddr(other.getIssStrAddr());
        }
        if (other.hasIssOrgName()) {
          setIssOrgName(other.getIssOrgName());
        }
        if (other.hasIssOrgUniName()) {
          setIssOrgUniName(other.getIssOrgUniName());
        }
        if (other.hasIssPosOffBox()) {
          setIssPosOffBox(other.getIssPosOffBox());
        }
        if (other.hasSubComName()) {
          setSubComName(other.getSubComName());
        }
        if (other.hasSubConName()) {
          setSubConName(other.getSubConName());
        }
        if (other.hasSubLoaName()) {
          setSubLoaName(other.getSubLoaName());
        }
        if (other.hasSubStaOrProName()) {
          setSubStaOrProName(other.getSubStaOrProName());
        }
        if (other.hasSubStrAddr()) {
          setSubStrAddr(other.getSubStrAddr());
        }
        if (other.hasSubOrgName()) {
          setSubOrgName(other.getSubOrgName());
        }
        if (other.hasSubOrgUniName()) {
          setSubOrgUniName(other.getSubOrgUniName());
        }
        if (other.hasSubPosOffBox()) {
          setSubPosOffBox(other.getSubPosOffBox());
        }
        if (other.hasValNotBef()) {
          setValNotBef(other.getValNotBef());
        }
        if (other.hasValNotAft()) {
          setValNotAft(other.getValNotAft());
        }
        if (other.hasRSAMod()) {
          setRSAMod(other.getRSAMod());
        }
        if (other.hasRSAExp()) {
          setRSAExp(other.getRSAExp());
        }
        if (other.hasDHPriMod()) {
          setDHPriMod(other.getDHPriMod());
        }
        if (other.hasDHPGen()) {
          setDHPGen(other.getDHPGen());
        }
        if (other.hasDHPubKey()) {
          setDHPubKey(other.getDHPubKey());
        }
        if (other.hasDSAPubKeyP()) {
          setDSAPubKeyP(other.getDSAPubKeyP());
        }
        if (other.hasDSAPubKeyQ()) {
          setDSAPubKeyQ(other.getDSAPubKeyQ());
        }
        if (other.hasDSAPubKeyG()) {
          setDSAPubKeyG(other.getDSAPubKeyG());
        }
        if (other.hasSigAlg()) {
          setSigAlg(other.getSigAlg());
        }
        if (other.hasSigVal()) {
          setSigVal(other.getSigVal());
        }
        if (other.hasAuthKeyID()) {
          setAuthKeyID(other.getAuthKeyID());
        }
        if (other.hasSubKeyID()) {
          setSubKeyID(other.getSubKeyID());
        }
        if (other.hasKeyUsage()) {
          setKeyUsage(other.getKeyUsage());
        }
        if (other.hasPriKeyUsaPerNotBef()) {
          setPriKeyUsaPerNotBef(other.getPriKeyUsaPerNotBef());
        }
        if (other.hasPriKeyUsaPerNotAft()) {
          setPriKeyUsaPerNotAft(other.getPriKeyUsaPerNotAft());
        }
        if (other.hasCertPol()) {
          setCertPol(other.getCertPol());
        }
        if (other.hasSubAltDNS()) {
          setSubAltDNS(other.getSubAltDNS());
        }
        if (other.hasSubAltIP()) {
          setSubAltIP(other.getSubAltIP());
        }
        if (other.hasSubAltName()) {
          setSubAltName(other.getSubAltName());
        }
        if (other.hasIssAltNameSys()) {
          setIssAltNameSys(other.getIssAltNameSys());
        }
        if (other.hasIssAltIP()) {
          setIssAltIP(other.getIssAltIP());
        }
        if (other.hasIssAltName()) {
          setIssAltName(other.getIssAltName());
        }
        if (other.hasSubDirAtt()) {
          setSubDirAtt(other.getSubDirAtt());
        }
        if (other.hasExtKeyUsage()) {
          setExtKeyUsage(other.getExtKeyUsage());
        }
        if (other.hasCertRevListSrc()) {
          setCertRevListSrc(other.getCertRevListSrc());
        }
        if (other.hasCertAuthInfAccMet()) {
          setCertAuthInfAccMet(other.getCertAuthInfAccMet());
        }
        if (other.hasCertAuthInfAccLoc()) {
          setCertAuthInfAccLoc(other.getCertAuthInfAccLoc());
        }
        if (other.hasExtCnt()) {
          setExtCnt(other.getExtCnt());
        }
        if (other.hasProtabname()) {
          setProtabname(other.getProtabname());
        }
        if (other.hasIssuer()) {
          setIssuer(other.getIssuer());
        }
        if (other.hasSubject()) {
          setSubject(other.getSubject());
        }
        if (other.hasDaysRem()) {
          setDaysRem(other.getDaysRem());
        }
        if (other.hasPubkey()) {
          setPubkey(other.getPubkey());
        }
        if (other.hasFpAlg()) {
          setFpAlg(other.getFpAlg());
        }
        if (other.hasHash()) {
          setHash(other.getHash());
        }
        if (other.hasExtSet()) {
          setExtSet(other.getExtSet());
        }
        if (other.hasDaysTotal()) {
          setDaysTotal(other.getDaysTotal());
        }
        if (other.hasSubAltDNSCnt()) {
          setSubAltDNSCnt(other.getSubAltDNSCnt());
        }
        if (other.hasAuthinfo()) {
          setAuthinfo(other.getAuthinfo());
        }
        if (other.hasBasicConsCA()) {
          setBasicConsCA(other.getBasicConsCA());
        }
        if (other.hasBasicConsPathLen()) {
          setBasicConsPathLen(other.getBasicConsPathLen());
        }
        if (other.hasBasicCons()) {
          setBasicCons(other.getBasicCons());
        }
        if (other.hasKeyPur()) {
          setKeyPur(other.getKeyPur());
        }
        if (other.hasCertype()) {
          setCertype(other.getCertype());
        }
        if (other.hasCertFullText()) {
          setCertFullText(other.getCertFullText());
        }
        if (other.hasAlternativeIpCount()) {
          setAlternativeIpCount(other.getAlternativeIpCount());
        }
        if (other.hasSource()) {
          setSource(other.getSource());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        X509CerInfoOuterClass.X509CerInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (X509CerInfoOuterClass.X509CerInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;
      private int bitField1_;
      private int bitField2_;

      private long protabID_ ;
      /**
       * <code>optional uint64 ProtabID = 1;</code>
       */
      public boolean hasProtabID() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional uint64 ProtabID = 1;</code>
       */
      public long getProtabID() {
        return protabID_;
      }
      /**
       * <code>optional uint64 ProtabID = 1;</code>
       */
      public Builder setProtabID(long value) {
        bitField0_ |= 0x00000001;
        protabID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 ProtabID = 1;</code>
       */
      public Builder clearProtabID() {
        bitField0_ = (bitField0_ & ~0x00000001);
        protabID_ = 0L;
        onChanged();
        return this;
      }

      private int ver_ ;
      /**
       * <code>optional uint32 ver = 2;</code>
       */
      public boolean hasVer() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional uint32 ver = 2;</code>
       */
      public int getVer() {
        return ver_;
      }
      /**
       * <code>optional uint32 ver = 2;</code>
       */
      public Builder setVer(int value) {
        bitField0_ |= 0x00000002;
        ver_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ver = 2;</code>
       */
      public Builder clearVer() {
        bitField0_ = (bitField0_ & ~0x00000002);
        ver_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvNum_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvNum = 3;</code>
       */
      public boolean hasSrvNum() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional bytes srvNum = 3;</code>
       */
      public com.google.protobuf.ByteString getSrvNum() {
        return srvNum_;
      }
      /**
       * <code>optional bytes srvNum = 3;</code>
       */
      public Builder setSrvNum(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        srvNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvNum = 3;</code>
       */
      public Builder clearSrvNum() {
        bitField0_ = (bitField0_ & ~0x00000004);
        srvNum_ = getDefaultInstance().getSrvNum();
        onChanged();
        return this;
      }

      private int issDataLen_ ;
      /**
       * <code>optional uint32 issDataLen = 4;</code>
       */
      public boolean hasIssDataLen() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional uint32 issDataLen = 4;</code>
       */
      public int getIssDataLen() {
        return issDataLen_;
      }
      /**
       * <code>optional uint32 issDataLen = 4;</code>
       */
      public Builder setIssDataLen(int value) {
        bitField0_ |= 0x00000008;
        issDataLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 issDataLen = 4;</code>
       */
      public Builder clearIssDataLen() {
        bitField0_ = (bitField0_ & ~0x00000008);
        issDataLen_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issComName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issComName = 5;</code>
       */
      public boolean hasIssComName() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional bytes issComName = 5;</code>
       */
      public com.google.protobuf.ByteString getIssComName() {
        return issComName_;
      }
      /**
       * <code>optional bytes issComName = 5;</code>
       */
      public Builder setIssComName(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        issComName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issComName = 5;</code>
       */
      public Builder clearIssComName() {
        bitField0_ = (bitField0_ & ~0x00000010);
        issComName_ = getDefaultInstance().getIssComName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issConName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issConName = 6;</code>
       */
      public boolean hasIssConName() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional bytes issConName = 6;</code>
       */
      public com.google.protobuf.ByteString getIssConName() {
        return issConName_;
      }
      /**
       * <code>optional bytes issConName = 6;</code>
       */
      public Builder setIssConName(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        issConName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issConName = 6;</code>
       */
      public Builder clearIssConName() {
        bitField0_ = (bitField0_ & ~0x00000020);
        issConName_ = getDefaultInstance().getIssConName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issLoaName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issLoaName = 7;</code>
       */
      public boolean hasIssLoaName() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional bytes issLoaName = 7;</code>
       */
      public com.google.protobuf.ByteString getIssLoaName() {
        return issLoaName_;
      }
      /**
       * <code>optional bytes issLoaName = 7;</code>
       */
      public Builder setIssLoaName(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        issLoaName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issLoaName = 7;</code>
       */
      public Builder clearIssLoaName() {
        bitField0_ = (bitField0_ & ~0x00000040);
        issLoaName_ = getDefaultInstance().getIssLoaName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issStaOrProName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issStaOrProName = 8;</code>
       */
      public boolean hasIssStaOrProName() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional bytes issStaOrProName = 8;</code>
       */
      public com.google.protobuf.ByteString getIssStaOrProName() {
        return issStaOrProName_;
      }
      /**
       * <code>optional bytes issStaOrProName = 8;</code>
       */
      public Builder setIssStaOrProName(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        issStaOrProName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issStaOrProName = 8;</code>
       */
      public Builder clearIssStaOrProName() {
        bitField0_ = (bitField0_ & ~0x00000080);
        issStaOrProName_ = getDefaultInstance().getIssStaOrProName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issStrAddr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issStrAddr = 9;</code>
       */
      public boolean hasIssStrAddr() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional bytes issStrAddr = 9;</code>
       */
      public com.google.protobuf.ByteString getIssStrAddr() {
        return issStrAddr_;
      }
      /**
       * <code>optional bytes issStrAddr = 9;</code>
       */
      public Builder setIssStrAddr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        issStrAddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issStrAddr = 9;</code>
       */
      public Builder clearIssStrAddr() {
        bitField0_ = (bitField0_ & ~0x00000100);
        issStrAddr_ = getDefaultInstance().getIssStrAddr();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issOrgName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issOrgName = 10;</code>
       */
      public boolean hasIssOrgName() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>optional bytes issOrgName = 10;</code>
       */
      public com.google.protobuf.ByteString getIssOrgName() {
        return issOrgName_;
      }
      /**
       * <code>optional bytes issOrgName = 10;</code>
       */
      public Builder setIssOrgName(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
        issOrgName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issOrgName = 10;</code>
       */
      public Builder clearIssOrgName() {
        bitField0_ = (bitField0_ & ~0x00000200);
        issOrgName_ = getDefaultInstance().getIssOrgName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issOrgUniName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issOrgUniName = 11;</code>
       */
      public boolean hasIssOrgUniName() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>optional bytes issOrgUniName = 11;</code>
       */
      public com.google.protobuf.ByteString getIssOrgUniName() {
        return issOrgUniName_;
      }
      /**
       * <code>optional bytes issOrgUniName = 11;</code>
       */
      public Builder setIssOrgUniName(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        issOrgUniName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issOrgUniName = 11;</code>
       */
      public Builder clearIssOrgUniName() {
        bitField0_ = (bitField0_ & ~0x00000400);
        issOrgUniName_ = getDefaultInstance().getIssOrgUniName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issPosOffBox_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issPosOffBox = 12;</code>
       */
      public boolean hasIssPosOffBox() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>optional bytes issPosOffBox = 12;</code>
       */
      public com.google.protobuf.ByteString getIssPosOffBox() {
        return issPosOffBox_;
      }
      /**
       * <code>optional bytes issPosOffBox = 12;</code>
       */
      public Builder setIssPosOffBox(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        issPosOffBox_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issPosOffBox = 12;</code>
       */
      public Builder clearIssPosOffBox() {
        bitField0_ = (bitField0_ & ~0x00000800);
        issPosOffBox_ = getDefaultInstance().getIssPosOffBox();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subComName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subComName = 13;</code>
       */
      public boolean hasSubComName() {
        return ((bitField0_ & 0x00001000) == 0x00001000);
      }
      /**
       * <code>optional bytes subComName = 13;</code>
       */
      public com.google.protobuf.ByteString getSubComName() {
        return subComName_;
      }
      /**
       * <code>optional bytes subComName = 13;</code>
       */
      public Builder setSubComName(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00001000;
        subComName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subComName = 13;</code>
       */
      public Builder clearSubComName() {
        bitField0_ = (bitField0_ & ~0x00001000);
        subComName_ = getDefaultInstance().getSubComName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subConName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subConName = 14;</code>
       */
      public boolean hasSubConName() {
        return ((bitField0_ & 0x00002000) == 0x00002000);
      }
      /**
       * <code>optional bytes subConName = 14;</code>
       */
      public com.google.protobuf.ByteString getSubConName() {
        return subConName_;
      }
      /**
       * <code>optional bytes subConName = 14;</code>
       */
      public Builder setSubConName(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00002000;
        subConName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subConName = 14;</code>
       */
      public Builder clearSubConName() {
        bitField0_ = (bitField0_ & ~0x00002000);
        subConName_ = getDefaultInstance().getSubConName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subLoaName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subLoaName = 15;</code>
       */
      public boolean hasSubLoaName() {
        return ((bitField0_ & 0x00004000) == 0x00004000);
      }
      /**
       * <code>optional bytes subLoaName = 15;</code>
       */
      public com.google.protobuf.ByteString getSubLoaName() {
        return subLoaName_;
      }
      /**
       * <code>optional bytes subLoaName = 15;</code>
       */
      public Builder setSubLoaName(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00004000;
        subLoaName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subLoaName = 15;</code>
       */
      public Builder clearSubLoaName() {
        bitField0_ = (bitField0_ & ~0x00004000);
        subLoaName_ = getDefaultInstance().getSubLoaName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subStaOrProName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subStaOrProName = 16;</code>
       */
      public boolean hasSubStaOrProName() {
        return ((bitField0_ & 0x00008000) == 0x00008000);
      }
      /**
       * <code>optional bytes subStaOrProName = 16;</code>
       */
      public com.google.protobuf.ByteString getSubStaOrProName() {
        return subStaOrProName_;
      }
      /**
       * <code>optional bytes subStaOrProName = 16;</code>
       */
      public Builder setSubStaOrProName(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00008000;
        subStaOrProName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subStaOrProName = 16;</code>
       */
      public Builder clearSubStaOrProName() {
        bitField0_ = (bitField0_ & ~0x00008000);
        subStaOrProName_ = getDefaultInstance().getSubStaOrProName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subStrAddr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subStrAddr = 17;</code>
       */
      public boolean hasSubStrAddr() {
        return ((bitField0_ & 0x00010000) == 0x00010000);
      }
      /**
       * <code>optional bytes subStrAddr = 17;</code>
       */
      public com.google.protobuf.ByteString getSubStrAddr() {
        return subStrAddr_;
      }
      /**
       * <code>optional bytes subStrAddr = 17;</code>
       */
      public Builder setSubStrAddr(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00010000;
        subStrAddr_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subStrAddr = 17;</code>
       */
      public Builder clearSubStrAddr() {
        bitField0_ = (bitField0_ & ~0x00010000);
        subStrAddr_ = getDefaultInstance().getSubStrAddr();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subOrgName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subOrgName = 18;</code>
       */
      public boolean hasSubOrgName() {
        return ((bitField0_ & 0x00020000) == 0x00020000);
      }
      /**
       * <code>optional bytes subOrgName = 18;</code>
       */
      public com.google.protobuf.ByteString getSubOrgName() {
        return subOrgName_;
      }
      /**
       * <code>optional bytes subOrgName = 18;</code>
       */
      public Builder setSubOrgName(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00020000;
        subOrgName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subOrgName = 18;</code>
       */
      public Builder clearSubOrgName() {
        bitField0_ = (bitField0_ & ~0x00020000);
        subOrgName_ = getDefaultInstance().getSubOrgName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subOrgUniName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subOrgUniName = 19;</code>
       */
      public boolean hasSubOrgUniName() {
        return ((bitField0_ & 0x00040000) == 0x00040000);
      }
      /**
       * <code>optional bytes subOrgUniName = 19;</code>
       */
      public com.google.protobuf.ByteString getSubOrgUniName() {
        return subOrgUniName_;
      }
      /**
       * <code>optional bytes subOrgUniName = 19;</code>
       */
      public Builder setSubOrgUniName(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00040000;
        subOrgUniName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subOrgUniName = 19;</code>
       */
      public Builder clearSubOrgUniName() {
        bitField0_ = (bitField0_ & ~0x00040000);
        subOrgUniName_ = getDefaultInstance().getSubOrgUniName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subPosOffBox_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subPosOffBox = 20;</code>
       */
      public boolean hasSubPosOffBox() {
        return ((bitField0_ & 0x00080000) == 0x00080000);
      }
      /**
       * <code>optional bytes subPosOffBox = 20;</code>
       */
      public com.google.protobuf.ByteString getSubPosOffBox() {
        return subPosOffBox_;
      }
      /**
       * <code>optional bytes subPosOffBox = 20;</code>
       */
      public Builder setSubPosOffBox(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00080000;
        subPosOffBox_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subPosOffBox = 20;</code>
       */
      public Builder clearSubPosOffBox() {
        bitField0_ = (bitField0_ & ~0x00080000);
        subPosOffBox_ = getDefaultInstance().getSubPosOffBox();
        onChanged();
        return this;
      }

      private long valNotBef_ ;
      /**
       * <code>optional uint64 valNotBef = 21;</code>
       */
      public boolean hasValNotBef() {
        return ((bitField0_ & 0x00100000) == 0x00100000);
      }
      /**
       * <code>optional uint64 valNotBef = 21;</code>
       */
      public long getValNotBef() {
        return valNotBef_;
      }
      /**
       * <code>optional uint64 valNotBef = 21;</code>
       */
      public Builder setValNotBef(long value) {
        bitField0_ |= 0x00100000;
        valNotBef_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 valNotBef = 21;</code>
       */
      public Builder clearValNotBef() {
        bitField0_ = (bitField0_ & ~0x00100000);
        valNotBef_ = 0L;
        onChanged();
        return this;
      }

      private long valNotAft_ ;
      /**
       * <code>optional uint64 valNotAft = 22;</code>
       */
      public boolean hasValNotAft() {
        return ((bitField0_ & 0x00200000) == 0x00200000);
      }
      /**
       * <code>optional uint64 valNotAft = 22;</code>
       */
      public long getValNotAft() {
        return valNotAft_;
      }
      /**
       * <code>optional uint64 valNotAft = 22;</code>
       */
      public Builder setValNotAft(long value) {
        bitField0_ |= 0x00200000;
        valNotAft_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 valNotAft = 22;</code>
       */
      public Builder clearValNotAft() {
        bitField0_ = (bitField0_ & ~0x00200000);
        valNotAft_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString rSAMod_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes RSAMod = 23;</code>
       */
      public boolean hasRSAMod() {
        return ((bitField0_ & 0x00400000) == 0x00400000);
      }
      /**
       * <code>optional bytes RSAMod = 23;</code>
       */
      public com.google.protobuf.ByteString getRSAMod() {
        return rSAMod_;
      }
      /**
       * <code>optional bytes RSAMod = 23;</code>
       */
      public Builder setRSAMod(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00400000;
        rSAMod_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes RSAMod = 23;</code>
       */
      public Builder clearRSAMod() {
        bitField0_ = (bitField0_ & ~0x00400000);
        rSAMod_ = getDefaultInstance().getRSAMod();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString rSAExp_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes RSAExp = 24;</code>
       */
      public boolean hasRSAExp() {
        return ((bitField0_ & 0x00800000) == 0x00800000);
      }
      /**
       * <code>optional bytes RSAExp = 24;</code>
       */
      public com.google.protobuf.ByteString getRSAExp() {
        return rSAExp_;
      }
      /**
       * <code>optional bytes RSAExp = 24;</code>
       */
      public Builder setRSAExp(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00800000;
        rSAExp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes RSAExp = 24;</code>
       */
      public Builder clearRSAExp() {
        bitField0_ = (bitField0_ & ~0x00800000);
        rSAExp_ = getDefaultInstance().getRSAExp();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHPriMod_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHPriMod = 25;</code>
       */
      public boolean hasDHPriMod() {
        return ((bitField0_ & 0x01000000) == 0x01000000);
      }
      /**
       * <code>optional bytes DHPriMod = 25;</code>
       */
      public com.google.protobuf.ByteString getDHPriMod() {
        return dHPriMod_;
      }
      /**
       * <code>optional bytes DHPriMod = 25;</code>
       */
      public Builder setDHPriMod(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x01000000;
        dHPriMod_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHPriMod = 25;</code>
       */
      public Builder clearDHPriMod() {
        bitField0_ = (bitField0_ & ~0x01000000);
        dHPriMod_ = getDefaultInstance().getDHPriMod();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHPGen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHPGen = 26;</code>
       */
      public boolean hasDHPGen() {
        return ((bitField0_ & 0x02000000) == 0x02000000);
      }
      /**
       * <code>optional bytes DHPGen = 26;</code>
       */
      public com.google.protobuf.ByteString getDHPGen() {
        return dHPGen_;
      }
      /**
       * <code>optional bytes DHPGen = 26;</code>
       */
      public Builder setDHPGen(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x02000000;
        dHPGen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHPGen = 26;</code>
       */
      public Builder clearDHPGen() {
        bitField0_ = (bitField0_ & ~0x02000000);
        dHPGen_ = getDefaultInstance().getDHPGen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHPubKey = 27;</code>
       */
      public boolean hasDHPubKey() {
        return ((bitField0_ & 0x04000000) == 0x04000000);
      }
      /**
       * <code>optional bytes DHPubKey = 27;</code>
       */
      public com.google.protobuf.ByteString getDHPubKey() {
        return dHPubKey_;
      }
      /**
       * <code>optional bytes DHPubKey = 27;</code>
       */
      public Builder setDHPubKey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x04000000;
        dHPubKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHPubKey = 27;</code>
       */
      public Builder clearDHPubKey() {
        bitField0_ = (bitField0_ & ~0x04000000);
        dHPubKey_ = getDefaultInstance().getDHPubKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dSAPubKeyP_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DSAPubKeyP = 28;</code>
       */
      public boolean hasDSAPubKeyP() {
        return ((bitField0_ & 0x08000000) == 0x08000000);
      }
      /**
       * <code>optional bytes DSAPubKeyP = 28;</code>
       */
      public com.google.protobuf.ByteString getDSAPubKeyP() {
        return dSAPubKeyP_;
      }
      /**
       * <code>optional bytes DSAPubKeyP = 28;</code>
       */
      public Builder setDSAPubKeyP(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x08000000;
        dSAPubKeyP_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DSAPubKeyP = 28;</code>
       */
      public Builder clearDSAPubKeyP() {
        bitField0_ = (bitField0_ & ~0x08000000);
        dSAPubKeyP_ = getDefaultInstance().getDSAPubKeyP();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dSAPubKeyQ_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DSAPubKeyQ = 29;</code>
       */
      public boolean hasDSAPubKeyQ() {
        return ((bitField0_ & 0x10000000) == 0x10000000);
      }
      /**
       * <code>optional bytes DSAPubKeyQ = 29;</code>
       */
      public com.google.protobuf.ByteString getDSAPubKeyQ() {
        return dSAPubKeyQ_;
      }
      /**
       * <code>optional bytes DSAPubKeyQ = 29;</code>
       */
      public Builder setDSAPubKeyQ(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x10000000;
        dSAPubKeyQ_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DSAPubKeyQ = 29;</code>
       */
      public Builder clearDSAPubKeyQ() {
        bitField0_ = (bitField0_ & ~0x10000000);
        dSAPubKeyQ_ = getDefaultInstance().getDSAPubKeyQ();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dSAPubKeyG_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DSAPubKeyG = 30;</code>
       */
      public boolean hasDSAPubKeyG() {
        return ((bitField0_ & 0x20000000) == 0x20000000);
      }
      /**
       * <code>optional bytes DSAPubKeyG = 30;</code>
       */
      public com.google.protobuf.ByteString getDSAPubKeyG() {
        return dSAPubKeyG_;
      }
      /**
       * <code>optional bytes DSAPubKeyG = 30;</code>
       */
      public Builder setDSAPubKeyG(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x20000000;
        dSAPubKeyG_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DSAPubKeyG = 30;</code>
       */
      public Builder clearDSAPubKeyG() {
        bitField0_ = (bitField0_ & ~0x20000000);
        dSAPubKeyG_ = getDefaultInstance().getDSAPubKeyG();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString sigAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes sigAlg = 31;</code>
       */
      public boolean hasSigAlg() {
        return ((bitField0_ & 0x40000000) == 0x40000000);
      }
      /**
       * <code>optional bytes sigAlg = 31;</code>
       */
      public com.google.protobuf.ByteString getSigAlg() {
        return sigAlg_;
      }
      /**
       * <code>optional bytes sigAlg = 31;</code>
       */
      public Builder setSigAlg(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x40000000;
        sigAlg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes sigAlg = 31;</code>
       */
      public Builder clearSigAlg() {
        bitField0_ = (bitField0_ & ~0x40000000);
        sigAlg_ = getDefaultInstance().getSigAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString sigVal_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes sigVal = 32;</code>
       */
      public boolean hasSigVal() {
        return ((bitField0_ & 0x80000000) == 0x80000000);
      }
      /**
       * <code>optional bytes sigVal = 32;</code>
       */
      public com.google.protobuf.ByteString getSigVal() {
        return sigVal_;
      }
      /**
       * <code>optional bytes sigVal = 32;</code>
       */
      public Builder setSigVal(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x80000000;
        sigVal_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes sigVal = 32;</code>
       */
      public Builder clearSigVal() {
        bitField0_ = (bitField0_ & ~0x80000000);
        sigVal_ = getDefaultInstance().getSigVal();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString authKeyID_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes authKeyID = 33;</code>
       */
      public boolean hasAuthKeyID() {
        return ((bitField1_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional bytes authKeyID = 33;</code>
       */
      public com.google.protobuf.ByteString getAuthKeyID() {
        return authKeyID_;
      }
      /**
       * <code>optional bytes authKeyID = 33;</code>
       */
      public Builder setAuthKeyID(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000001;
        authKeyID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes authKeyID = 33;</code>
       */
      public Builder clearAuthKeyID() {
        bitField1_ = (bitField1_ & ~0x00000001);
        authKeyID_ = getDefaultInstance().getAuthKeyID();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subKeyID_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subKeyID = 34;</code>
       */
      public boolean hasSubKeyID() {
        return ((bitField1_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional bytes subKeyID = 34;</code>
       */
      public com.google.protobuf.ByteString getSubKeyID() {
        return subKeyID_;
      }
      /**
       * <code>optional bytes subKeyID = 34;</code>
       */
      public Builder setSubKeyID(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000002;
        subKeyID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subKeyID = 34;</code>
       */
      public Builder clearSubKeyID() {
        bitField1_ = (bitField1_ & ~0x00000002);
        subKeyID_ = getDefaultInstance().getSubKeyID();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString keyUsage_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes keyUsage = 35;</code>
       */
      public boolean hasKeyUsage() {
        return ((bitField1_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional bytes keyUsage = 35;</code>
       */
      public com.google.protobuf.ByteString getKeyUsage() {
        return keyUsage_;
      }
      /**
       * <code>optional bytes keyUsage = 35;</code>
       */
      public Builder setKeyUsage(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000004;
        keyUsage_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes keyUsage = 35;</code>
       */
      public Builder clearKeyUsage() {
        bitField1_ = (bitField1_ & ~0x00000004);
        keyUsage_ = getDefaultInstance().getKeyUsage();
        onChanged();
        return this;
      }

      private long priKeyUsaPerNotBef_ ;
      /**
       * <code>optional uint64 priKeyUsaPerNotBef = 36;</code>
       */
      public boolean hasPriKeyUsaPerNotBef() {
        return ((bitField1_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional uint64 priKeyUsaPerNotBef = 36;</code>
       */
      public long getPriKeyUsaPerNotBef() {
        return priKeyUsaPerNotBef_;
      }
      /**
       * <code>optional uint64 priKeyUsaPerNotBef = 36;</code>
       */
      public Builder setPriKeyUsaPerNotBef(long value) {
        bitField1_ |= 0x00000008;
        priKeyUsaPerNotBef_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 priKeyUsaPerNotBef = 36;</code>
       */
      public Builder clearPriKeyUsaPerNotBef() {
        bitField1_ = (bitField1_ & ~0x00000008);
        priKeyUsaPerNotBef_ = 0L;
        onChanged();
        return this;
      }

      private long priKeyUsaPerNotAft_ ;
      /**
       * <code>optional uint64 priKeyUsaPerNotAft = 37;</code>
       */
      public boolean hasPriKeyUsaPerNotAft() {
        return ((bitField1_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional uint64 priKeyUsaPerNotAft = 37;</code>
       */
      public long getPriKeyUsaPerNotAft() {
        return priKeyUsaPerNotAft_;
      }
      /**
       * <code>optional uint64 priKeyUsaPerNotAft = 37;</code>
       */
      public Builder setPriKeyUsaPerNotAft(long value) {
        bitField1_ |= 0x00000010;
        priKeyUsaPerNotAft_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 priKeyUsaPerNotAft = 37;</code>
       */
      public Builder clearPriKeyUsaPerNotAft() {
        bitField1_ = (bitField1_ & ~0x00000010);
        priKeyUsaPerNotAft_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString certPol_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes certPol = 38;</code>
       */
      public boolean hasCertPol() {
        return ((bitField1_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional bytes certPol = 38;</code>
       */
      public com.google.protobuf.ByteString getCertPol() {
        return certPol_;
      }
      /**
       * <code>optional bytes certPol = 38;</code>
       */
      public Builder setCertPol(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000020;
        certPol_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes certPol = 38;</code>
       */
      public Builder clearCertPol() {
        bitField1_ = (bitField1_ & ~0x00000020);
        certPol_ = getDefaultInstance().getCertPol();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subAltDNS_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subAltDNS = 39;</code>
       */
      public boolean hasSubAltDNS() {
        return ((bitField1_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional bytes subAltDNS = 39;</code>
       */
      public com.google.protobuf.ByteString getSubAltDNS() {
        return subAltDNS_;
      }
      /**
       * <code>optional bytes subAltDNS = 39;</code>
       */
      public Builder setSubAltDNS(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000040;
        subAltDNS_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subAltDNS = 39;</code>
       */
      public Builder clearSubAltDNS() {
        bitField1_ = (bitField1_ & ~0x00000040);
        subAltDNS_ = getDefaultInstance().getSubAltDNS();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subAltIP_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subAltIP = 40;</code>
       */
      public boolean hasSubAltIP() {
        return ((bitField1_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional bytes subAltIP = 40;</code>
       */
      public com.google.protobuf.ByteString getSubAltIP() {
        return subAltIP_;
      }
      /**
       * <code>optional bytes subAltIP = 40;</code>
       */
      public Builder setSubAltIP(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000080;
        subAltIP_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subAltIP = 40;</code>
       */
      public Builder clearSubAltIP() {
        bitField1_ = (bitField1_ & ~0x00000080);
        subAltIP_ = getDefaultInstance().getSubAltIP();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subAltName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subAltName = 41;</code>
       */
      public boolean hasSubAltName() {
        return ((bitField1_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional bytes subAltName = 41;</code>
       */
      public com.google.protobuf.ByteString getSubAltName() {
        return subAltName_;
      }
      /**
       * <code>optional bytes subAltName = 41;</code>
       */
      public Builder setSubAltName(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000100;
        subAltName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subAltName = 41;</code>
       */
      public Builder clearSubAltName() {
        bitField1_ = (bitField1_ & ~0x00000100);
        subAltName_ = getDefaultInstance().getSubAltName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issAltNameSys_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issAltNameSys = 42;</code>
       */
      public boolean hasIssAltNameSys() {
        return ((bitField1_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>optional bytes issAltNameSys = 42;</code>
       */
      public com.google.protobuf.ByteString getIssAltNameSys() {
        return issAltNameSys_;
      }
      /**
       * <code>optional bytes issAltNameSys = 42;</code>
       */
      public Builder setIssAltNameSys(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000200;
        issAltNameSys_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issAltNameSys = 42;</code>
       */
      public Builder clearIssAltNameSys() {
        bitField1_ = (bitField1_ & ~0x00000200);
        issAltNameSys_ = getDefaultInstance().getIssAltNameSys();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issAltIP_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issAltIP = 43;</code>
       */
      public boolean hasIssAltIP() {
        return ((bitField1_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>optional bytes issAltIP = 43;</code>
       */
      public com.google.protobuf.ByteString getIssAltIP() {
        return issAltIP_;
      }
      /**
       * <code>optional bytes issAltIP = 43;</code>
       */
      public Builder setIssAltIP(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000400;
        issAltIP_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issAltIP = 43;</code>
       */
      public Builder clearIssAltIP() {
        bitField1_ = (bitField1_ & ~0x00000400);
        issAltIP_ = getDefaultInstance().getIssAltIP();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issAltName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issAltName = 44;</code>
       */
      public boolean hasIssAltName() {
        return ((bitField1_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>optional bytes issAltName = 44;</code>
       */
      public com.google.protobuf.ByteString getIssAltName() {
        return issAltName_;
      }
      /**
       * <code>optional bytes issAltName = 44;</code>
       */
      public Builder setIssAltName(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00000800;
        issAltName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issAltName = 44;</code>
       */
      public Builder clearIssAltName() {
        bitField1_ = (bitField1_ & ~0x00000800);
        issAltName_ = getDefaultInstance().getIssAltName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subDirAtt_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subDirAtt = 45;</code>
       */
      public boolean hasSubDirAtt() {
        return ((bitField1_ & 0x00001000) == 0x00001000);
      }
      /**
       * <code>optional bytes subDirAtt = 45;</code>
       */
      public com.google.protobuf.ByteString getSubDirAtt() {
        return subDirAtt_;
      }
      /**
       * <code>optional bytes subDirAtt = 45;</code>
       */
      public Builder setSubDirAtt(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00001000;
        subDirAtt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subDirAtt = 45;</code>
       */
      public Builder clearSubDirAtt() {
        bitField1_ = (bitField1_ & ~0x00001000);
        subDirAtt_ = getDefaultInstance().getSubDirAtt();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString extKeyUsage_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes extKeyUsage = 46;</code>
       */
      public boolean hasExtKeyUsage() {
        return ((bitField1_ & 0x00002000) == 0x00002000);
      }
      /**
       * <code>optional bytes extKeyUsage = 46;</code>
       */
      public com.google.protobuf.ByteString getExtKeyUsage() {
        return extKeyUsage_;
      }
      /**
       * <code>optional bytes extKeyUsage = 46;</code>
       */
      public Builder setExtKeyUsage(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00002000;
        extKeyUsage_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes extKeyUsage = 46;</code>
       */
      public Builder clearExtKeyUsage() {
        bitField1_ = (bitField1_ & ~0x00002000);
        extKeyUsage_ = getDefaultInstance().getExtKeyUsage();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString certRevListSrc_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes certRevListSrc = 47;</code>
       */
      public boolean hasCertRevListSrc() {
        return ((bitField1_ & 0x00004000) == 0x00004000);
      }
      /**
       * <code>optional bytes certRevListSrc = 47;</code>
       */
      public com.google.protobuf.ByteString getCertRevListSrc() {
        return certRevListSrc_;
      }
      /**
       * <code>optional bytes certRevListSrc = 47;</code>
       */
      public Builder setCertRevListSrc(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00004000;
        certRevListSrc_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes certRevListSrc = 47;</code>
       */
      public Builder clearCertRevListSrc() {
        bitField1_ = (bitField1_ & ~0x00004000);
        certRevListSrc_ = getDefaultInstance().getCertRevListSrc();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString certAuthInfAccMet_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes certAuthInfAccMet = 48;</code>
       */
      public boolean hasCertAuthInfAccMet() {
        return ((bitField1_ & 0x00008000) == 0x00008000);
      }
      /**
       * <code>optional bytes certAuthInfAccMet = 48;</code>
       */
      public com.google.protobuf.ByteString getCertAuthInfAccMet() {
        return certAuthInfAccMet_;
      }
      /**
       * <code>optional bytes certAuthInfAccMet = 48;</code>
       */
      public Builder setCertAuthInfAccMet(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00008000;
        certAuthInfAccMet_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes certAuthInfAccMet = 48;</code>
       */
      public Builder clearCertAuthInfAccMet() {
        bitField1_ = (bitField1_ & ~0x00008000);
        certAuthInfAccMet_ = getDefaultInstance().getCertAuthInfAccMet();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString certAuthInfAccLoc_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes certAuthInfAccLoc = 49;</code>
       */
      public boolean hasCertAuthInfAccLoc() {
        return ((bitField1_ & 0x00010000) == 0x00010000);
      }
      /**
       * <code>optional bytes certAuthInfAccLoc = 49;</code>
       */
      public com.google.protobuf.ByteString getCertAuthInfAccLoc() {
        return certAuthInfAccLoc_;
      }
      /**
       * <code>optional bytes certAuthInfAccLoc = 49;</code>
       */
      public Builder setCertAuthInfAccLoc(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00010000;
        certAuthInfAccLoc_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes certAuthInfAccLoc = 49;</code>
       */
      public Builder clearCertAuthInfAccLoc() {
        bitField1_ = (bitField1_ & ~0x00010000);
        certAuthInfAccLoc_ = getDefaultInstance().getCertAuthInfAccLoc();
        onChanged();
        return this;
      }

      private int extCnt_ ;
      /**
       * <code>optional uint32 extCnt = 50;</code>
       */
      public boolean hasExtCnt() {
        return ((bitField1_ & 0x00020000) == 0x00020000);
      }
      /**
       * <code>optional uint32 extCnt = 50;</code>
       */
      public int getExtCnt() {
        return extCnt_;
      }
      /**
       * <code>optional uint32 extCnt = 50;</code>
       */
      public Builder setExtCnt(int value) {
        bitField1_ |= 0x00020000;
        extCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 extCnt = 50;</code>
       */
      public Builder clearExtCnt() {
        bitField1_ = (bitField1_ & ~0x00020000);
        extCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString protabname_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes Protabname = 51;</code>
       */
      public boolean hasProtabname() {
        return ((bitField1_ & 0x00040000) == 0x00040000);
      }
      /**
       * <code>optional bytes Protabname = 51;</code>
       */
      public com.google.protobuf.ByteString getProtabname() {
        return protabname_;
      }
      /**
       * <code>optional bytes Protabname = 51;</code>
       */
      public Builder setProtabname(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00040000;
        protabname_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes Protabname = 51;</code>
       */
      public Builder clearProtabname() {
        bitField1_ = (bitField1_ & ~0x00040000);
        protabname_ = getDefaultInstance().getProtabname();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issuer_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issuer = 52;</code>
       */
      public boolean hasIssuer() {
        return ((bitField1_ & 0x00080000) == 0x00080000);
      }
      /**
       * <code>optional bytes issuer = 52;</code>
       */
      public com.google.protobuf.ByteString getIssuer() {
        return issuer_;
      }
      /**
       * <code>optional bytes issuer = 52;</code>
       */
      public Builder setIssuer(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00080000;
        issuer_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issuer = 52;</code>
       */
      public Builder clearIssuer() {
        bitField1_ = (bitField1_ & ~0x00080000);
        issuer_ = getDefaultInstance().getIssuer();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subject_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subject = 53;</code>
       */
      public boolean hasSubject() {
        return ((bitField1_ & 0x00100000) == 0x00100000);
      }
      /**
       * <code>optional bytes subject = 53;</code>
       */
      public com.google.protobuf.ByteString getSubject() {
        return subject_;
      }
      /**
       * <code>optional bytes subject = 53;</code>
       */
      public Builder setSubject(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00100000;
        subject_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subject = 53;</code>
       */
      public Builder clearSubject() {
        bitField1_ = (bitField1_ & ~0x00100000);
        subject_ = getDefaultInstance().getSubject();
        onChanged();
        return this;
      }

      private int daysRem_ ;
      /**
       * <code>optional uint32 daysRem = 54;</code>
       */
      public boolean hasDaysRem() {
        return ((bitField1_ & 0x00200000) == 0x00200000);
      }
      /**
       * <code>optional uint32 daysRem = 54;</code>
       */
      public int getDaysRem() {
        return daysRem_;
      }
      /**
       * <code>optional uint32 daysRem = 54;</code>
       */
      public Builder setDaysRem(int value) {
        bitField1_ |= 0x00200000;
        daysRem_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 daysRem = 54;</code>
       */
      public Builder clearDaysRem() {
        bitField1_ = (bitField1_ & ~0x00200000);
        daysRem_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString pubkey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes pubkey = 55;</code>
       */
      public boolean hasPubkey() {
        return ((bitField1_ & 0x00400000) == 0x00400000);
      }
      /**
       * <code>optional bytes pubkey = 55;</code>
       */
      public com.google.protobuf.ByteString getPubkey() {
        return pubkey_;
      }
      /**
       * <code>optional bytes pubkey = 55;</code>
       */
      public Builder setPubkey(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00400000;
        pubkey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes pubkey = 55;</code>
       */
      public Builder clearPubkey() {
        bitField1_ = (bitField1_ & ~0x00400000);
        pubkey_ = getDefaultInstance().getPubkey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString fpAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes fpAlg = 56;</code>
       */
      public boolean hasFpAlg() {
        return ((bitField1_ & 0x00800000) == 0x00800000);
      }
      /**
       * <code>optional bytes fpAlg = 56;</code>
       */
      public com.google.protobuf.ByteString getFpAlg() {
        return fpAlg_;
      }
      /**
       * <code>optional bytes fpAlg = 56;</code>
       */
      public Builder setFpAlg(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x00800000;
        fpAlg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes fpAlg = 56;</code>
       */
      public Builder clearFpAlg() {
        bitField1_ = (bitField1_ & ~0x00800000);
        fpAlg_ = getDefaultInstance().getFpAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString hash_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes hash = 57;</code>
       */
      public boolean hasHash() {
        return ((bitField1_ & 0x01000000) == 0x01000000);
      }
      /**
       * <code>optional bytes hash = 57;</code>
       */
      public com.google.protobuf.ByteString getHash() {
        return hash_;
      }
      /**
       * <code>optional bytes hash = 57;</code>
       */
      public Builder setHash(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x01000000;
        hash_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes hash = 57;</code>
       */
      public Builder clearHash() {
        bitField1_ = (bitField1_ & ~0x01000000);
        hash_ = getDefaultInstance().getHash();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString extSet_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes extSet = 58;</code>
       */
      public boolean hasExtSet() {
        return ((bitField1_ & 0x02000000) == 0x02000000);
      }
      /**
       * <code>optional bytes extSet = 58;</code>
       */
      public com.google.protobuf.ByteString getExtSet() {
        return extSet_;
      }
      /**
       * <code>optional bytes extSet = 58;</code>
       */
      public Builder setExtSet(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x02000000;
        extSet_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes extSet = 58;</code>
       */
      public Builder clearExtSet() {
        bitField1_ = (bitField1_ & ~0x02000000);
        extSet_ = getDefaultInstance().getExtSet();
        onChanged();
        return this;
      }

      private int daysTotal_ ;
      /**
       * <code>optional uint32 daysTotal = 59;</code>
       */
      public boolean hasDaysTotal() {
        return ((bitField1_ & 0x04000000) == 0x04000000);
      }
      /**
       * <code>optional uint32 daysTotal = 59;</code>
       */
      public int getDaysTotal() {
        return daysTotal_;
      }
      /**
       * <code>optional uint32 daysTotal = 59;</code>
       */
      public Builder setDaysTotal(int value) {
        bitField1_ |= 0x04000000;
        daysTotal_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 daysTotal = 59;</code>
       */
      public Builder clearDaysTotal() {
        bitField1_ = (bitField1_ & ~0x04000000);
        daysTotal_ = 0;
        onChanged();
        return this;
      }

      private int subAltDNSCnt_ ;
      /**
       * <code>optional uint32 subAltDNSCnt = 60;</code>
       */
      public boolean hasSubAltDNSCnt() {
        return ((bitField1_ & 0x08000000) == 0x08000000);
      }
      /**
       * <code>optional uint32 subAltDNSCnt = 60;</code>
       */
      public int getSubAltDNSCnt() {
        return subAltDNSCnt_;
      }
      /**
       * <code>optional uint32 subAltDNSCnt = 60;</code>
       */
      public Builder setSubAltDNSCnt(int value) {
        bitField1_ |= 0x08000000;
        subAltDNSCnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 subAltDNSCnt = 60;</code>
       */
      public Builder clearSubAltDNSCnt() {
        bitField1_ = (bitField1_ & ~0x08000000);
        subAltDNSCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString authinfo_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes authinfo = 61;</code>
       */
      public boolean hasAuthinfo() {
        return ((bitField1_ & 0x10000000) == 0x10000000);
      }
      /**
       * <code>optional bytes authinfo = 61;</code>
       */
      public com.google.protobuf.ByteString getAuthinfo() {
        return authinfo_;
      }
      /**
       * <code>optional bytes authinfo = 61;</code>
       */
      public Builder setAuthinfo(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x10000000;
        authinfo_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes authinfo = 61;</code>
       */
      public Builder clearAuthinfo() {
        bitField1_ = (bitField1_ & ~0x10000000);
        authinfo_ = getDefaultInstance().getAuthinfo();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString basicConsCA_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes basicConsCA = 62;</code>
       */
      public boolean hasBasicConsCA() {
        return ((bitField1_ & 0x20000000) == 0x20000000);
      }
      /**
       * <code>optional bytes basicConsCA = 62;</code>
       */
      public com.google.protobuf.ByteString getBasicConsCA() {
        return basicConsCA_;
      }
      /**
       * <code>optional bytes basicConsCA = 62;</code>
       */
      public Builder setBasicConsCA(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x20000000;
        basicConsCA_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes basicConsCA = 62;</code>
       */
      public Builder clearBasicConsCA() {
        bitField1_ = (bitField1_ & ~0x20000000);
        basicConsCA_ = getDefaultInstance().getBasicConsCA();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString basicConsPathLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes basicConsPathLen = 63;</code>
       */
      public boolean hasBasicConsPathLen() {
        return ((bitField1_ & 0x40000000) == 0x40000000);
      }
      /**
       * <code>optional bytes basicConsPathLen = 63;</code>
       */
      public com.google.protobuf.ByteString getBasicConsPathLen() {
        return basicConsPathLen_;
      }
      /**
       * <code>optional bytes basicConsPathLen = 63;</code>
       */
      public Builder setBasicConsPathLen(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x40000000;
        basicConsPathLen_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes basicConsPathLen = 63;</code>
       */
      public Builder clearBasicConsPathLen() {
        bitField1_ = (bitField1_ & ~0x40000000);
        basicConsPathLen_ = getDefaultInstance().getBasicConsPathLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString basicCons_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes basicCons = 64;</code>
       */
      public boolean hasBasicCons() {
        return ((bitField1_ & 0x80000000) == 0x80000000);
      }
      /**
       * <code>optional bytes basicCons = 64;</code>
       */
      public com.google.protobuf.ByteString getBasicCons() {
        return basicCons_;
      }
      /**
       * <code>optional bytes basicCons = 64;</code>
       */
      public Builder setBasicCons(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField1_ |= 0x80000000;
        basicCons_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes basicCons = 64;</code>
       */
      public Builder clearBasicCons() {
        bitField1_ = (bitField1_ & ~0x80000000);
        basicCons_ = getDefaultInstance().getBasicCons();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString keyPur_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes KeyPur = 65;</code>
       */
      public boolean hasKeyPur() {
        return ((bitField2_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional bytes KeyPur = 65;</code>
       */
      public com.google.protobuf.ByteString getKeyPur() {
        return keyPur_;
      }
      /**
       * <code>optional bytes KeyPur = 65;</code>
       */
      public Builder setKeyPur(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00000001;
        keyPur_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes KeyPur = 65;</code>
       */
      public Builder clearKeyPur() {
        bitField2_ = (bitField2_ & ~0x00000001);
        keyPur_ = getDefaultInstance().getKeyPur();
        onChanged();
        return this;
      }

      private int certype_ ;
      /**
       * <code>optional uint32 Certype = 66;</code>
       */
      public boolean hasCertype() {
        return ((bitField2_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional uint32 Certype = 66;</code>
       */
      public int getCertype() {
        return certype_;
      }
      /**
       * <code>optional uint32 Certype = 66;</code>
       */
      public Builder setCertype(int value) {
        bitField2_ |= 0x00000002;
        certype_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 Certype = 66;</code>
       */
      public Builder clearCertype() {
        bitField2_ = (bitField2_ & ~0x00000002);
        certype_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString certFullText_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes certFullText = 67;</code>
       */
      public boolean hasCertFullText() {
        return ((bitField2_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional bytes certFullText = 67;</code>
       */
      public com.google.protobuf.ByteString getCertFullText() {
        return certFullText_;
      }
      /**
       * <code>optional bytes certFullText = 67;</code>
       */
      public Builder setCertFullText(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField2_ |= 0x00000004;
        certFullText_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes certFullText = 67;</code>
       */
      public Builder clearCertFullText() {
        bitField2_ = (bitField2_ & ~0x00000004);
        certFullText_ = getDefaultInstance().getCertFullText();
        onChanged();
        return this;
      }

      private int alternativeIpCount_ ;
      /**
       * <code>optional uint32 alternativeIpCount = 68;</code>
       */
      public boolean hasAlternativeIpCount() {
        return ((bitField2_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional uint32 alternativeIpCount = 68;</code>
       */
      public int getAlternativeIpCount() {
        return alternativeIpCount_;
      }
      /**
       * <code>optional uint32 alternativeIpCount = 68;</code>
       */
      public Builder setAlternativeIpCount(int value) {
        bitField2_ |= 0x00000008;
        alternativeIpCount_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 alternativeIpCount = 68;</code>
       */
      public Builder clearAlternativeIpCount() {
        bitField2_ = (bitField2_ & ~0x00000008);
        alternativeIpCount_ = 0;
        onChanged();
        return this;
      }

      private int source_ ;
      /**
       * <code>optional uint32 source = 69;</code>
       */
      public boolean hasSource() {
        return ((bitField2_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional uint32 source = 69;</code>
       */
      public int getSource() {
        return source_;
      }
      /**
       * <code>optional uint32 source = 69;</code>
       */
      public Builder setSource(int value) {
        bitField2_ |= 0x00000010;
        source_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 source = 69;</code>
       */
      public Builder clearSource() {
        bitField2_ = (bitField2_ & ~0x00000010);
        source_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:X509CerInfo)
    }

    // @@protoc_insertion_point(class_scope:X509CerInfo)
    private static final X509CerInfoOuterClass.X509CerInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new X509CerInfoOuterClass.X509CerInfo();
    }

    public static X509CerInfoOuterClass.X509CerInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<X509CerInfo>
        PARSER = new com.google.protobuf.AbstractParser<X509CerInfo>() {
      @java.lang.Override
      public X509CerInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new X509CerInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<X509CerInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<X509CerInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public X509CerInfoOuterClass.X509CerInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_X509CerInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_X509CerInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\021X509CerInfo.proto\"\331\n\n\013X509CerInfo\022\020\n\010P" +
      "rotabID\030\001 \001(\004\022\013\n\003ver\030\002 \001(\r\022\016\n\006srvNum\030\003 \001" +
      "(\014\022\022\n\nissDataLen\030\004 \001(\r\022\022\n\nissComName\030\005 \001" +
      "(\014\022\022\n\nissConName\030\006 \001(\014\022\022\n\nissLoaName\030\007 \001" +
      "(\014\022\027\n\017issStaOrProName\030\010 \001(\014\022\022\n\nissStrAdd" +
      "r\030\t \001(\014\022\022\n\nissOrgName\030\n \001(\014\022\025\n\rissOrgUni" +
      "Name\030\013 \001(\014\022\024\n\014issPosOffBox\030\014 \001(\014\022\022\n\nsubC" +
      "omName\030\r \001(\014\022\022\n\nsubConName\030\016 \001(\014\022\022\n\nsubL" +
      "oaName\030\017 \001(\014\022\027\n\017subStaOrProName\030\020 \001(\014\022\022\n" +
      "\nsubStrAddr\030\021 \001(\014\022\022\n\nsubOrgName\030\022 \001(\014\022\025\n" +
      "\rsubOrgUniName\030\023 \001(\014\022\024\n\014subPosOffBox\030\024 \001" +
      "(\014\022\021\n\tvalNotBef\030\025 \001(\004\022\021\n\tvalNotAft\030\026 \001(\004" +
      "\022\016\n\006RSAMod\030\027 \001(\014\022\016\n\006RSAExp\030\030 \001(\014\022\020\n\010DHPr" +
      "iMod\030\031 \001(\014\022\016\n\006DHPGen\030\032 \001(\014\022\020\n\010DHPubKey\030\033" +
      " \001(\014\022\022\n\nDSAPubKeyP\030\034 \001(\014\022\022\n\nDSAPubKeyQ\030\035" +
      " \001(\014\022\022\n\nDSAPubKeyG\030\036 \001(\014\022\016\n\006sigAlg\030\037 \001(\014" +
      "\022\016\n\006sigVal\030  \001(\014\022\021\n\tauthKeyID\030! \001(\014\022\020\n\010s" +
      "ubKeyID\030\" \001(\014\022\020\n\010keyUsage\030# \001(\014\022\032\n\022priKe" +
      "yUsaPerNotBef\030$ \001(\004\022\032\n\022priKeyUsaPerNotAf" +
      "t\030% \001(\004\022\017\n\007certPol\030& \001(\014\022\021\n\tsubAltDNS\030\' " +
      "\001(\014\022\020\n\010subAltIP\030( \001(\014\022\022\n\nsubAltName\030) \001(" +
      "\014\022\025\n\rissAltNameSys\030* \001(\014\022\020\n\010issAltIP\030+ \001" +
      "(\014\022\022\n\nissAltName\030, \001(\014\022\021\n\tsubDirAtt\030- \001(" +
      "\014\022\023\n\013extKeyUsage\030. \001(\014\022\026\n\016certRevListSrc" +
      "\030/ \001(\014\022\031\n\021certAuthInfAccMet\0300 \001(\014\022\031\n\021cer" +
      "tAuthInfAccLoc\0301 \001(\014\022\016\n\006extCnt\0302 \001(\r\022\022\n\n" +
      "Protabname\0303 \001(\014\022\016\n\006issuer\0304 \001(\014\022\017\n\007subj" +
      "ect\0305 \001(\014\022\017\n\007daysRem\0306 \001(\r\022\016\n\006pubkey\0307 \001" +
      "(\014\022\r\n\005fpAlg\0308 \001(\014\022\014\n\004hash\0309 \001(\014\022\016\n\006extSe" +
      "t\030: \001(\014\022\021\n\tdaysTotal\030; \001(\r\022\024\n\014subAltDNSC" +
      "nt\030< \001(\r\022\020\n\010authinfo\030= \001(\014\022\023\n\013basicConsC" +
      "A\030> \001(\014\022\030\n\020basicConsPathLen\030? \001(\014\022\021\n\tbas" +
      "icCons\030@ \001(\014\022\016\n\006KeyPur\030A \001(\014\022\017\n\007Certype\030" +
      "B \001(\r\022\024\n\014certFullText\030C \001(\014\022\032\n\022alternati" +
      "veIpCount\030D \001(\r\022\016\n\006source\030E \001(\r"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_X509CerInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_X509CerInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_X509CerInfo_descriptor,
        new java.lang.String[] { "ProtabID", "Ver", "SrvNum", "IssDataLen", "IssComName", "IssConName", "IssLoaName", "IssStaOrProName", "IssStrAddr", "IssOrgName", "IssOrgUniName", "IssPosOffBox", "SubComName", "SubConName", "SubLoaName", "SubStaOrProName", "SubStrAddr", "SubOrgName", "SubOrgUniName", "SubPosOffBox", "ValNotBef", "ValNotAft", "RSAMod", "RSAExp", "DHPriMod", "DHPGen", "DHPubKey", "DSAPubKeyP", "DSAPubKeyQ", "DSAPubKeyG", "SigAlg", "SigVal", "AuthKeyID", "SubKeyID", "KeyUsage", "PriKeyUsaPerNotBef", "PriKeyUsaPerNotAft", "CertPol", "SubAltDNS", "SubAltIP", "SubAltName", "IssAltNameSys", "IssAltIP", "IssAltName", "SubDirAtt", "ExtKeyUsage", "CertRevListSrc", "CertAuthInfAccMet", "CertAuthInfAccLoc", "ExtCnt", "Protabname", "Issuer", "Subject", "DaysRem", "Pubkey", "FpAlg", "Hash", "ExtSet", "DaysTotal", "SubAltDNSCnt", "Authinfo", "BasicConsCA", "BasicConsPathLen", "BasicCons", "KeyPur", "Certype", "CertFullText", "AlternativeIpCount", "Source", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
