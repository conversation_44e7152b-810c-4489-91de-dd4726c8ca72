package com.geeksec.common.utils;

/**
 * <AUTHOR>
 * @Description：
 */
public class InstanceofUtils {
    public static Integer longInstanceofInt(Object obj) {
        if (obj instanceof Long) {
            return ((Long) obj).intValue();
        } else {
            return (Integer) obj;
        }
    }

    public static Long intInstanceofLong(Object obj) {
        if (obj instanceof Integer) {
            return ((Integer) obj).longValue();
        } else {
            return (Long) obj;
        }
    }
}
