package com.geeksec.common.LabelUtils;

import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/3/16
 */

public class MysqlUtils {

    protected static final Logger LOG = LoggerFactory.getLogger(MysqlUtils.class);

    //57所证书导入batch表
    public static int setMysqlCertInfo(String SHA1, boolean is_repeat,List<String> task_batch_info) throws SQLException {
        Connection connection = null;
        PreparedStatement select_preparedStatement = null;
        PreparedStatement update_preparedStatement = null;
        ResultSet selectResultSet = null;
        int rowsAffected_all = 0;
        try {
            connection = mysqlPool.getConnection();

            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formattedDateTime = now.format(formatter);
            //对于每个taskId和batchId,更新对应的值
            for (String task_batch : task_batch_info) {
                String[] task_batch_info_list = task_batch.split("-");
                String task_id = task_batch_info_list[0];
                String batch_id = task_batch_info_list[1];
                if (is_repeat) {
                    String select_query = "SELECT repeat_num,cert_finish FROM tb_cert_anay_batch WHERE task_id=? AND batch_id=? LIMIT 1";
                    select_preparedStatement = connection.prepareStatement(select_query);
                    select_preparedStatement.setInt(1, Integer.parseInt(task_id));
                    select_preparedStatement.setInt(2, Integer.parseInt(batch_id));
                    LOG.info("sql——{}",select_preparedStatement);
                    selectResultSet = select_preparedStatement.executeQuery();

                    Map<String, Object> cert_info = new HashMap<>();
                    while (selectResultSet.next()) {
                        ResultSetMetaData metaData = selectResultSet.getMetaData();
                        int columnCount = metaData.getColumnCount();
                        for (int i = 1; i <= columnCount; i++) {
                            String columnName = metaData.getColumnName(i);
                            Object value = selectResultSet.getObject(i);
                            cert_info.put(columnName, value);
                        }
                    }
                    Integer repeat_num = (Integer) cert_info.get("repeat_num") + 1;
                    Integer cert_finish = (Integer) cert_info.get("cert_finish") + 1;
                    String update_query = "UPDATE tb_cert_anay_batch SET repeat_num=?,cert_finish=?,updated_time=? WHERE task_id=? AND batch_id=?";
                    update_preparedStatement = connection.prepareStatement(update_query);
                    update_preparedStatement.setInt(1, repeat_num);
                    update_preparedStatement.setInt(2, cert_finish);
                    update_preparedStatement.setString(3, formattedDateTime);
                    update_preparedStatement.setInt(4, Integer.parseInt(task_id));
                    update_preparedStatement.setInt(5, Integer.parseInt(batch_id));
                    LOG.info("sql——{}",update_preparedStatement);
                    int rowsAffected = update_preparedStatement.executeUpdate();
                    rowsAffected_all+=rowsAffected;
                } else {
                    String select_query = "SELECT cert_finish FROM tb_cert_anay_batch WHERE task_id=? AND batch_id=? LIMIT 1";
                    select_preparedStatement = connection.prepareStatement(select_query);
                    select_preparedStatement.setInt(1, Integer.parseInt(task_id));
                    select_preparedStatement.setInt(2, Integer.parseInt(batch_id));
                    LOG.info("sql——{}",select_preparedStatement);
                    selectResultSet = select_preparedStatement.executeQuery();

                    Map<String, Object> cert_info = new HashMap<>();
                    while (selectResultSet.next()) {
                        ResultSetMetaData metaData = selectResultSet.getMetaData();
                        int columnCount = metaData.getColumnCount();
                        for (int i = 1; i <= columnCount; i++) {
                            String columnName = metaData.getColumnName(i);
                            Object value = selectResultSet.getObject(i);
                            cert_info.put(columnName, value);
                        }
                    }
                    Integer cert_finish = (Integer) cert_info.get("cert_finish") + 1;
                    String update_query = "UPDATE tb_cert_anay_batch SET cert_finish=?,updated_time=? WHERE task_id=? AND batch_id=?";

                    update_preparedStatement = connection.prepareStatement(update_query);
                    update_preparedStatement.setInt(1, cert_finish);
                    update_preparedStatement.setString(2, formattedDateTime);
                    update_preparedStatement.setInt(3, Integer.parseInt(task_id));
                    update_preparedStatement.setInt(4, Integer.parseInt(batch_id));
                    LOG.info("sql——{}",update_preparedStatement);
                    int rowsAffected = update_preparedStatement.executeUpdate();
                    rowsAffected_all+=rowsAffected;
                }
            }

            if (task_batch_info.size()!=rowsAffected_all){
                LOG.error("更新mysql证书检测结果不匹配错误，redis更新条数——{}——，mysql更新条数——{}——",task_batch_info.size(),rowsAffected_all);
                return -1;
            }
        } catch (Exception e) {
            LOG.error("mysql查询失败，error--->{},SHA1 is--->{}", e, SHA1);
            return -1;
        } finally {
            if (selectResultSet != null) {selectResultSet.close();}
            if (select_preparedStatement != null) {select_preparedStatement.close();}
            if (update_preparedStatement != null) {update_preparedStatement.close();}
            if (connection != null) {connection.close();}
        }
        return rowsAffected_all;
    }

    public static boolean get_mysql_not_white(List<String> attack_chain_list) throws SQLException {
        Connection connection = null;
        PreparedStatement select_preparedStatement = null;
        ResultSet selectResultSet = null;
        boolean mysql_not_white = false;
        try{
            connection = mysqlPool.getConnection();
            for(String attack_chain:attack_chain_list){
                String[] attack_chain_info = attack_chain.split("_");
                String victim = attack_chain_info[0];
                String attacker = attack_chain_info[1];
                String label = attack_chain_info[2];
                String select_query = "SELECT * FROM tb_alarm_white WHERE victim=? AND attacker=? AND label=? LIMIT 1";
                select_preparedStatement = connection.prepareStatement(select_query);
                select_preparedStatement.setString(1, victim);
                select_preparedStatement.setString(2, attacker);
                select_preparedStatement.setString(3, label);
                LOG.info("sql——{}",select_preparedStatement);
                selectResultSet = select_preparedStatement.executeQuery();

                Map<String, Object> alarm_white_info = new HashMap<>();
                while (selectResultSet.next()) {
                    ResultSetMetaData metaData = selectResultSet.getMetaData();
                    int columnCount = metaData.getColumnCount();
                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnName(i);
                        Object value = selectResultSet.getObject(i);
                        alarm_white_info.put(columnName, value);
                    }
                }
                if (alarm_white_info.size()==0){
                    // 查看是否存在受害者标记的情况
                    String victimCountQuery = "SELECT COUNT(*) FROM tb_alarm_white WHERE victim = ? AND label = ?";
                    select_preparedStatement = connection.prepareStatement(victimCountQuery);
                    select_preparedStatement.setString(1, victim);
                    select_preparedStatement.setString(2, label);
                    selectResultSet = select_preparedStatement.executeQuery();
                    int victimCountResult = 0;
                    if (selectResultSet.next()) {
                        victimCountResult = selectResultSet.getInt(1);
                    }

                    // 查看是否存在攻击者标记的情况
                    String attackerCountQuery = "SELECT COUNT(*) FROM tb_alarm_white WHERE attacker = ? AND label = ?";
                    select_preparedStatement = connection.prepareStatement(attackerCountQuery);
                    select_preparedStatement.setString(1, attacker);
                    select_preparedStatement.setString(2, label);
                    selectResultSet = select_preparedStatement.executeQuery();
                    int attackerCountResult = 0;
                    if (selectResultSet.next()) {
                        attackerCountResult = selectResultSet.getInt(1);
                    }
                    // 判断结果
                    if(victimCountResult<=1 && attackerCountResult<=1){
                        LOG.info("攻击者白名单规则匹配到——{}——，受害者白名单规则匹配到——{}——",attackerCountResult,victimCountResult);
                        mysql_not_white = true;
                        break;
                    }
                }
            }
        }catch (Exception e){
            LOG.error("查询mysql出现错误——{}——",e);
        }finally {
            if (selectResultSet != null) {selectResultSet.close();}
            if (select_preparedStatement != null) {select_preparedStatement.close();}
            if (connection != null) {connection.close();}
        }
        return mysql_not_white;
    }

    // 模型开关查询，查询tb_model_switch表，取其中的switch值，若为0，则进行检测，若不为0，则不检测
    public static Integer getModelSwitchInfo(String model_name) throws SQLException {
        Integer modelSwitchInfo = 1;
        Connection connection = null;
        PreparedStatement select_preparedStatement = null;
        ResultSet selectResultSet = null;
        try{
            connection = mysqlPool.getConnection();

            String select_query = "SELECT switch FROM tb_model_switch WHERE model_name=? LIMIT 1";
            select_preparedStatement = connection.prepareStatement(select_query);
            select_preparedStatement.setString(1, model_name);
            selectResultSet = select_preparedStatement.executeQuery();

            Map<String, Object> alarm_white_info = new HashMap<>();
            while (selectResultSet.next()) {
                ResultSetMetaData metaData = selectResultSet.getMetaData();
                int columnCount = metaData.getColumnCount();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = selectResultSet.getObject(i);
                    alarm_white_info.put(columnName, value);
                }
            }
            if (alarm_white_info.size()!=0){
                modelSwitchInfo = (Integer) alarm_white_info.get("switch");
            }else {
                LOG.info("--------------模型mysql表为空，默认开启--------------");
            }
        }catch (Exception e){
            LOG.error("查询mysql出现错误，依然开启:——{}——",e.toString());
        }finally {
            if (selectResultSet != null) {selectResultSet.close();}
            if (select_preparedStatement != null) {select_preparedStatement.close();}
            if (connection != null) {connection.close();}
        }

        return modelSwitchInfo;
    }


    // 日志外发查询，查询tb_alarm_output表，取其中的status值，若为0，则进行外发，若为1，则外发
    public static List<Map<String,Object>> getAlarmOutputStatus() throws SQLException {
        List<Map<String,Object>> tool_on = new ArrayList<>();
        Connection connection = null;
        PreparedStatement select_preparedStatement = null;
        ResultSet selectResultSet = null;
        try{
            connection = mysqlPool.getConnection();

            String select_query = "SELECT * FROM tb_alarm_output WHERE status=1";
            select_preparedStatement = connection.prepareStatement(select_query);
            selectResultSet = select_preparedStatement.executeQuery();
            LOG.info("sql——{}",select_preparedStatement);

            while (selectResultSet.next()) {
                ResultSetMetaData metaData = selectResultSet.getMetaData();
                int columnCount = metaData.getColumnCount();
                Map<String,Object> tool_info = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = selectResultSet.getObject(i);
                    tool_info.put(columnName,value);
                }
                tool_on.add(tool_info);
            }
            if (tool_on.size()!=0){
                LOG.info("告警外发的组件开启为：{}",tool_on);
            }else {
                LOG.info("--------------告警外发mysql表中status都为空--------------");
            }
        }catch (Exception e){
            LOG.error("查询mysql出现错误，报错内容:——{}——",e.toString());
        }finally {
            if (selectResultSet != null) {selectResultSet.close();}
            if (select_preparedStatement != null) {select_preparedStatement.close();}
            if (connection != null) {connection.close();}
        }

        return tool_on;
    }

    // 获取label对应的中文描述
    public static Map<String,String> getTagMap() throws SQLException {
        Map<String,String> tagMap = new HashMap<>();
        Connection connection = null;
        PreparedStatement select_preparedStatement = null;
        ResultSet selectResultSet = null;
        try{
            connection = mysqlPool.getConnection();

            String select_query = "SELECT tag_id,tag_text FROM tb_tag_info";
            select_preparedStatement = connection.prepareStatement(select_query);
            selectResultSet = select_preparedStatement.executeQuery();
            LOG.info("sql——{}",select_preparedStatement);

            while (selectResultSet.next()) {
                ResultSetMetaData metaData = selectResultSet.getMetaData();
                int columnCount = metaData.getColumnCount();
                Map<String,Object> tagInfo = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = selectResultSet.getObject(i);
                    tagInfo.put(columnName,value);
                }
                tagMap.put(tagInfo.get("tag_id").toString(), tagInfo.get("tag_text").toString());
            }
        }catch (Exception e){
            LOG.error("查询mysql出现错误，报错内容:——{}——",e.toString());
        }finally {
            if (selectResultSet != null) {selectResultSet.close();}
            if (select_preparedStatement != null) {select_preparedStatement.close();}
            if (connection != null) {connection.close();}
        }

        return tagMap;
    }

    public static Map<String, String> getModelMap() throws SQLException {
        Map<String,String> modelMap = new HashMap<>();
        Connection connection = null;
        PreparedStatement select_preparedStatement = null;
        ResultSet selectResultSet = null;
        try{
            connection = mysqlPool.getConnection();

            String select_query = "SELECT model_id,model_name FROM tb_model_info";
            select_preparedStatement = connection.prepareStatement(select_query);
            selectResultSet = select_preparedStatement.executeQuery();
            LOG.info("sql——{}",select_preparedStatement);

            while (selectResultSet.next()) {
                ResultSetMetaData metaData = selectResultSet.getMetaData();
                int columnCount = metaData.getColumnCount();
                Map<String,Object> modelInfo = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = selectResultSet.getObject(i);
                    modelInfo.put(columnName,value);
                }
                modelMap.put(modelInfo.get("model_id").toString(), modelInfo.get("model_name").toString());
            }
        }catch (Exception e){
            LOG.error("查询mysql出现错误，报错内容:——{}——",e.toString());
        }finally {
            if (selectResultSet != null) {selectResultSet.close();}
            if (select_preparedStatement != null) {select_preparedStatement.close();}
            if (connection != null) {connection.close();}
        }

        return modelMap;
    }

    public static Map<String, Object> getAlertLogProperties() throws SQLException {
        Map<String,Object> alertLogProperties = new HashMap<>();
        Connection connection = null;
        PreparedStatement select_preparedStatement = null;
        ResultSet selectResultSet = null;
        List<Map<String,Object>> alertLogPropertiesList = new ArrayList<>();
        try{
            connection = mysqlPool.getConnection();

            String selectQuery = "SELECT * FROM tb_alert_log_output";
            select_preparedStatement = connection.prepareStatement(selectQuery);
            selectResultSet = select_preparedStatement.executeQuery();
            LOG.info("sql——{}",select_preparedStatement);

            while (selectResultSet.next()) {
                ResultSetMetaData metaData = selectResultSet.getMetaData();
                int columnCount = metaData.getColumnCount();
                Map<String,Object> alertLogPropertiesTmp = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = selectResultSet.getObject(i);
                    alertLogPropertiesTmp.put(columnName,value);
                }
                alertLogPropertiesList.add(alertLogPropertiesTmp);
            }
            if (!alertLogPropertiesList.isEmpty()){
                alertLogProperties = alertLogPropertiesList.get(0);
                LOG.info("alert log告警外发的配置为：{}",alertLogProperties);
            }else {
                LOG.info("--------------alert log告警外发的配置mysql表无数据--------------");
            }
        }catch (Exception e){
            LOG.error("查询alert log告警外发出现错误，报错内容:——{}——",e.toString());
        }finally {
            if (selectResultSet != null) {selectResultSet.close();}
            if (select_preparedStatement != null) {select_preparedStatement.close();}
            if (connection != null) {connection.close();}
        }

        return alertLogProperties;
    }
}

