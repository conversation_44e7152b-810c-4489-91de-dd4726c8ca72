package com.geeksec.common.utils;

import com.geeksec.common.loader.PropertiesLoader;
import com.google.common.collect.Lists;
import com.vesoft.nebula.client.graph.NebulaPoolConfig;
import com.vesoft.nebula.client.graph.data.HostAddress;
import com.vesoft.nebula.client.graph.net.NebulaPool;
import com.vesoft.nebula.client.graph.net.Session;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @Description：
 */
public class NebulaUtils {
    private static final Logger logger = LoggerFactory.getLogger(NebulaUtils.class);

    private static NebulaPool NEBULA_POOL = null;
    private static PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");

    // Nebula Configs
    private static final int nebulaPoolMaxConnSize = 1000;
    private static final int nebulaPoolMinConnSize = 50;
    private static final int nebulaPoolIdleTime = 180000;
    private static final int nebulaPoolTimeout = 300000;
    private static String nebulaCluster = propertiesLoader.getProperty("nebula.graph.addr");
    private static String userName = propertiesLoader.getProperty("nebula.graph.username");
    private static String password = propertiesLoader.getProperty("nebula.graph.password");

    // 空间表名
    private static String space = propertiesLoader.getProperty("nebula.space.name");


    public static NebulaPoolConfig nebulaPoolConfig() {
        NebulaPoolConfig nebulaPoolConfig = new NebulaPoolConfig();
        nebulaPoolConfig.setMaxConnSize(nebulaPoolMaxConnSize);
        nebulaPoolConfig.setMinConnSize(nebulaPoolMinConnSize);
        nebulaPoolConfig.setIdleTime(nebulaPoolIdleTime);
        nebulaPoolConfig.setTimeout(nebulaPoolTimeout);
        return nebulaPoolConfig;
    }

    public static void nebulaPool(NebulaPoolConfig nebulaPoolConfig)
            throws UnknownHostException {
        List<HostAddress> addresses = null;
        try {
            String[] hostPorts;
            if (nebulaCluster.contains(",")) {
                hostPorts = StringUtils.split(nebulaCluster, ",");
                addresses = Lists.newArrayListWithExpectedSize(hostPorts.length);
            } else {
                hostPorts = new String[1];
                hostPorts[0] = nebulaCluster;
                addresses = new ArrayList<>(1);
            }

            for (String hostPort : hostPorts) {
                String[] linkElements = StringUtils.split(hostPort, ":");
                HostAddress hostAddress = new HostAddress(linkElements[0],
                        Integer.valueOf(linkElements[1]));
                addresses.add(hostAddress);
            }
        } catch (Exception e) {
            throw new RuntimeException("nebula数据库连接信息配置有误，正确格式：ip1:port1,ip2:port2 ");
        }
        NebulaPool pool = new NebulaPool();
        Boolean initResult = pool.init(addresses, nebulaPoolConfig);
        if (!initResult) {
            logger.error("poll init failed.");
        }
        NEBULA_POOL = pool;
    }

    public static NebulaPool getNebulaPool(NebulaPoolConfig nebulaPoolConfig) throws UnknownHostException {
        List<HostAddress> addresses = null;
        try {
            String[] hostPorts;
            if (nebulaCluster.contains(",")) {
                hostPorts = StringUtils.split(nebulaCluster, ",");
                addresses = Lists.newArrayListWithExpectedSize(hostPorts.length);
            } else {
                hostPorts = new String[1];
                hostPorts[0] = nebulaCluster;
                addresses = new ArrayList<>(1);
            }

            for (String hostPort : hostPorts) {
                String[] linkElements = StringUtils.split(hostPort, ":");
                HostAddress hostAddress = new HostAddress(linkElements[0],
                        Integer.valueOf(linkElements[1]));
                addresses.add(hostAddress);
            }
        } catch (Exception e) {
            throw new RuntimeException("nebula数据库连接信息配置有误，正确格式：ip1:port1,ip2:port2 ");
        }
        NebulaPool pool = new NebulaPool();
        Boolean initResult = pool.init(addresses, nebulaPoolConfig);
        if (!initResult) {
            logger.error("poll init failed.");
        }
        return pool;
    }

    public static Session getSession() {
        Session session = null;

        try {
            if (NEBULA_POOL == null) {
                nebulaPool(nebulaPoolConfig());
            }
            session = NEBULA_POOL.getSession(userName, password, false);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("初始化Nebula Graph Session 失败! error --->{}", e);
        }
        return session;
    }

}
