package com.geeksec.common.utils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Description：
 */
public class IndexContext {
    private static Long AccCount_task_0 = 0L;
    private static Long AccCount_task_1 = 0L;
    private static IndexContext instance = null;

    private IndexContext() {
    }

    public static IndexContext getInstance() {
        if (instance == null) {
            synchronized (IndexContext.class) {
                instance = new IndexContext();
            }
        }
        return instance;
    }


    public String IndexToNum(String Type, String es_key) {
        String Index = es_key;
        List<String> Tlist = Arrays.asList(es_key.split("_"));
        String TaskId = Tlist.get(1);

        Long Num = 10000000001L;
        if (TaskId.contains("0")) {
            if (Type.contains("3009")) {
                AccCount_task_0 += 1L;
            }
            Num += AccCount_task_0 / Num;
        } else if (TaskId.contains("1")) {
            AccCount_task_1 += 1;
            Num += AccCount_task_1 / Num;
        }
        return Index + "_" + String.valueOf(Num);
    }
    public  void IndexRefresh() {

    }
}
