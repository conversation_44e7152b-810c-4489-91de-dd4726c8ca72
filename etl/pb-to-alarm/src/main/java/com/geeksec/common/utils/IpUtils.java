package com.geeksec.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;

/**
 * <AUTHOR>
 * @Description：
 */
public class IpUtils {
    public static boolean isIpAddress(String str) {
        if (StringUtil.isNullOrEmpty(str)) {
            return false;
        }
        /**
         * 判断IP格式和范围
         */
        String rexp = "([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}";
        Pattern pat = Pattern.compile(rexp);
        Matcher mat = pat.matcher(str);
        boolean ipAddress = mat.find();
        return ipAddress;
    }

    public static boolean isIpv4Str(String str) {
        //1、判断是否是7-15位之间（0.0.0.0-***************.255）
        if (str.length() < 7 || str.length() > 15) {
            return false;
        }
        //2、判断是否能以小数点分成四段
        String[] ipArray = str.split("\\.");
        if (ipArray.length != 4) {
            return false;
        }
        for (int i = 0; i < ipArray.length; i++) {
            //3、判断每段是否都是数字
            try {
                int number = Integer.parseInt(ipArray[i]);
                //4.判断每段数字是否都在0-255之间
                if (number < 0 || number > 255) {
                    return false;
                }
            } catch (Exception e) {
                return false;
            }
        }
        return true;
    }

    public static boolean isIpv6Str(String str) {
        str = str.trim();
        if (str.equals("")) {
            return false;
        }
        Pattern pattern = Pattern.compile("((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))"
                + "|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9] [0-9])"
                + "(.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9] [0-9])){3})|:))|(([0-9A-Fa-f]{1,4}:){5}"
                + "(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9] [0-9])"
                + "(.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9] [0-9])){3})|:))|(([0-9A-Fa-f]{1,4}:){4}"
                + "(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4}) :((25[0-5]|2[0-4][0-9]|1[0-9][0-9]"
                + "|[1-9] [0-9])(.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9] [0-9])){3}))|:))|(([0-9A-Fa-f]{1,4}:)"
                + "{3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4][0-9]|1[0-9][0-9]"
                + "|[1-9] [0-9])(.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9] [0-9])){3}))|:))|(([0-9A-Fa-f]{1,4}:)"
                + "{2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4][0-9]|1[0-9][0-9]"
                + "|[1-9] [0-9])(.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9] [0-9])){3}))|:))|(([0-9A-Fa-f]{1,4}:)"
                + "{1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4][0-9]|1[0-9][0-9]"
                + "|[1-9] [0-9])(.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9] [0-9])){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})"
                + "|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9] [0-9])"
                + "(.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9] [0-9])){3}))|:)))(%.+)");
        return pattern.matcher(str).matches();
    }

}
