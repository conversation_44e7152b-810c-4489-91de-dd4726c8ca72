package com.geeksec.common.LabelUtils;

import com.geeksec.common.loader.PropertiesLoader;
import com.mysql.cj.jdbc.MysqlDataSource;
import java.sql.Connection;
import java.sql.SQLException;
import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @Date 2023/3/17
 */


public class mysqlPool {
    private static PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");

    public static String mysqlHost = propertiesLoader.getProperty("mysql.database.host","");

    public static String mysqlUser = propertiesLoader.getProperty("mysql.database.user","");

    public static String mysqlPassword = propertiesLoader.getProperty("mysql.database.password","");
    private static DataSource dataSource;

    static {
        MysqlDataSource mysqlDS = new MysqlDataSource();
        mysqlDS.setURL(mysqlHost);
        mysqlDS.setUser(mysqlUser);
        mysqlDS.setPassword(mysqlPassword);
        dataSource = mysqlDS;
    }

    public static Connection getConnection() throws SQLException {
        return dataSource.getConnection();
    }
}
