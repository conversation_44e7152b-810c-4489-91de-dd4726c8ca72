package com.geeksec.common.LabelUtils;

import com.geeksec.common.loader.PropertiesLoader;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.connector.jdbc.JdbcInputFormat;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description null.java
 * @Date 11:25$ 2025/5/22$
 **/
public class KnowledgeImportUtil {

    private static PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");
    public static String KNOWLEDGE_HOST = propertiesLoader.getProperty("mysql.knowledge.database.host");
    public static String USERNAME = propertiesLoader.getProperty("mysql.database.user");
    public static String PASSWORD = propertiesLoader.getProperty("mysql.database.password");
    public static List<String> loadMineDomainFromDB(StreamExecutionEnvironment env) {
        List<String> result = new ArrayList<>();

        String tagSql = "SELECT mine_domain FROM mine_domain; ";
        // MySQL连接配置
        JdbcInputFormat inputFormat = JdbcInputFormat.buildJdbcInputFormat()
                .setDrivername("com.mysql.cj.jdbc.Driver")
                .setDBUrl(KNOWLEDGE_HOST)
                .setUsername(USERNAME)
                .setPassword(PASSWORD)
                .setQuery(tagSql)
                .setRowTypeInfo(new RowTypeInfo(BasicTypeInfo.STRING_TYPE_INFO))
                .finish();

        SingleOutputStreamOperator<Row> orgTagInfoDataStreamSource = env.createInput(inputFormat)
                .flatMap(new RichFlatMapFunction<Row, Row>() {
                             @Override
                             public void flatMap(Row row, Collector<Row> collector) throws Exception {
                                 if (row != null) {
                                     String domain = row.getFieldAs(0);
                                     result.add(domain);
                                 }
                             }
                         }
                ).name("加载MineDomain知识库");
        return result;
    }

    public static List<String> loadCDNNamesFromDB(StreamExecutionEnvironment env) {
        List<String> result = new ArrayList<>();

        String tagSql = "SELECT cn FROM cdns; ";
        // MySQL连接配置
        JdbcInputFormat inputFormat = JdbcInputFormat.buildJdbcInputFormat()
                .setDrivername("com.mysql.cj.jdbc.Driver")
                .setDBUrl(KNOWLEDGE_HOST)
                .setUsername(USERNAME)
                .setPassword(PASSWORD)
                .setQuery(tagSql)
                .setRowTypeInfo(new RowTypeInfo(BasicTypeInfo.STRING_TYPE_INFO))
                .finish();

        SingleOutputStreamOperator<Row> orgTagInfoDataStreamSource = env.createInput(inputFormat)
                .flatMap(new RichFlatMapFunction<Row, Row>() {
                             @Override
                             public void flatMap(Row row, Collector<Row> collector) throws Exception {
                                 if (row != null) {
                                     String cdnName = row.getFieldAs(0);
                                     result.add(cdnName);
                                 }
                             }
                         }
                ).name("加载CDNNames知识库");
        return result;
    }
}
