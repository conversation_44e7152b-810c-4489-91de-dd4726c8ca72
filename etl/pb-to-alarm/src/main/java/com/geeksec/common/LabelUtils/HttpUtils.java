package com.geeksec.common.LabelUtils;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class HttpUtils {
    private static final Logger LOG = LoggerFactory.getLogger(HttpUtils.class);

    /**
     * 发送post请求
     */
    public static JSONObject sendPost(String url, JSONObject jsonObject) {
        String result = null;
        try {
            URL serverUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) serverUrl.openConnection();
            // 设置是否向connection输出，因为这个是post请求，参数要放在
            // http正文内，因此需要设为true
            conn.setDoOutput(Boolean.TRUE);
            conn.setDoInput(Boolean.TRUE);
            //请求方式是POST
            conn.setRequestMethod("POST");
            // Post 请求不能使用缓存
            conn.setUseCaches(false);
            conn.setRequestProperty("Content-type", "application/json");
            //必须设置false，否则会自动redirect到重定向后的地址
            conn.setInstanceFollowRedirects(false);
            //建立连接
            conn.connect();
            //设置请求体

            String s = JSONUtil.toJsonStr(jsonObject);
            System.out.println(s);
            //获取了返回值
            result = getReturn(conn, s);

//            OkHttpClient client = new OkHttpClient();
//            MediaType JSON = MediaType.parse("application/json; charset=utf-8");
//
//            String json = "{\"key1\":\"value1\", \"key2\":\"value2\"}";
//            RequestBody body = RequestBody.create(json, JSON);
//            Request request = new Request.Builder()
//                    .url("http://example.com/api/resource")
//                    .post(body)
//                    .build();

        } catch (IOException e) {
            LOG.error("发送post请求失败 ==>", e);
            return null;
        }
        //如果返回值是标准的JSON字符串可以像我这样给他进行转换
        return JSONObject.parseObject(result);
    }

    /**
     * 发送请求并获取返回值
     */
    public static String getReturn(HttpURLConnection connection, String param) throws IOException {
        StringBuffer buffer = new StringBuffer();
        byte[] bytes = param.getBytes();
        System.out.println(bytes);
        OutputStream outputStream = connection.getOutputStream();
        outputStream.write(bytes);
        outputStream.flush();
        outputStream.close();

        int code = connection.getResponseCode();
        if (code != 200) {
            InputStream raw = connection.getErrorStream();// HttpURLConnection 才有getErrorStream方法
            JSONObject wrong_result = new JSONObject();
            wrong_result.put("raw",raw);
            wrong_result.put("response_code",code);
            return wrong_result.toString();
        }
        //将返回的输入流转换成字符串
        InputStream inputStream = connection.getInputStream();
        InputStreamReader inputStreamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
        BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
        String str = null;
        while ((str = bufferedReader.readLine()) != null) {
            buffer.append(str);
        }

        return buffer.toString();
    }

    public static void OnlyPost(HttpURLConnection connection,String param) throws IOException{
        byte[] bytes = param.getBytes();
        System.out.println(bytes);
        OutputStream outputStream = connection.getOutputStream();
        outputStream.write(bytes);
        outputStream.flush();
        outputStream.close();
    }

    public static void sendPost_only(String url, JSONObject jsonObject) {
        try {
            URL serverUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) serverUrl.openConnection();
            // 设置是否向connection输出，因为这个是post请求，参数要放在
            // http正文内，因此需要设为true
            conn.setDoOutput(Boolean.TRUE);
            conn.setDoInput(Boolean.TRUE);
            //请求方式是POST
            conn.setRequestMethod("POST");
            // Post 请求不能使用缓存
            conn.setUseCaches(false);
            conn.setRequestProperty("Content-type", "application/json");
            //必须设置false，否则会自动redirect到重定向后的地址
            conn.setInstanceFollowRedirects(false);
            //建立连接
            conn.connect();
            //设置请求体

            String s = JSONUtil.toJsonStr(jsonObject);
            //获取了返回值
            OnlyPost(conn, s);
        } catch (IOException e) {
            LOG.error("发送post请求失败 ==>", e);
        }
    }
}
