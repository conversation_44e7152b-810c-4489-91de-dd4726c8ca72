package com.geeksec.common.utils;

import com.geeksec.analysisFunction.analysisEntity.nebula.FingerInfo;
import com.geeksec.common.loader.PropertiesLoader;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ProNameForMysql {

    private static final Logger logger = LoggerFactory.getLogger(ProNameForMysql.class);
    private static PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");
    private static String HOST = propertiesLoader.getProperty("mysql.database.host");
    private static String USERNAME = propertiesLoader.getProperty("mysql.database.user");
    private static String PASSWORD = propertiesLoader.getProperty("mysql.database.password");

    private static ProNameForMysql instance = null;
    private static Map<Integer, String> ProNameMap = new HashMap<Integer, String>();

    private ProNameForMysql() {
        Init();
    }

    public static void Init() {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver").newInstance();
            Connection conn = DriverManager.getConnection(HOST, USERNAME, PASSWORD);
            Statement stmt = conn.createStatement();

            String sql = "SELECT pro_id , pro_value FROM app_pro_value where type = 2 ";

            ResultSet rs = stmt.executeQuery(sql);

            while (rs.next()) {
                ProNameMap.put(rs.getInt(1), rs.getString(2));
            }
            conn.close();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 查询标签信息
     *
     * @return
     */
    public static List<FingerInfo> getMysqlFingerInfo() {
        try {
            List<FingerInfo> resultList = new ArrayList<>();
            Class.forName("com.mysql.cj.jdbc.Driver").newInstance();
            Connection conn = DriverManager.getConnection(HOST, USERNAME, PASSWORD);
            Statement stmt = conn.createStatement();
            String fingerSql = "SELECT finger_es,finger_content,ja3_hash,es_type,finger_type FROM tb_finger_info";
            ResultSet tagRs = stmt.executeQuery(fingerSql);

            while (tagRs.next()) {
                FingerInfo info = new FingerInfo();
                info.setFingerID(tagRs.getString(1));
                info.setFingerContent(tagRs.getString(2));
                info.setFingerJa3(tagRs.getString(3));
                info.setFingerDirection(tagRs.getString(4));
                info.setFingerType(tagRs.getString(5));
                resultList.add(info);
            }
            tagRs.close();
            stmt.close();
            conn.close();
            return resultList;
        } catch (Exception e) {
            System.out.println("查询fingerMap信息失败!");
        }
        logger.info("fingerMap查询失败");
        return null;
    }

    public static HashMap<String,String> get_finger_type_map(List<FingerInfo> FingerInfoList){
        HashMap<String,String> FINGER_MAP = new HashMap<>();
        for(FingerInfo fingerInfo:FingerInfoList){
            FINGER_MAP.put(fingerInfo.getFingerID(),fingerInfo.getFingerType());
        }
        return FINGER_MAP;
    }

    public static HashMap<String,String> get_finger_ja3_map(List<FingerInfo> FingerInfoList){
        HashMap<String,String> FINGER_JA3_MAP = new HashMap<>();
        for(FingerInfo fingerInfo:FingerInfoList){
            FINGER_JA3_MAP.put(fingerInfo.getFingerID(),fingerInfo.getFingerJa3());
        }
        return FINGER_JA3_MAP;
    }

    public static ProNameForMysql getInstance() {
        if (instance == null) {
            synchronized (ProNameForMysql.class) {
                instance = new ProNameForMysql();
            }
        }
        return instance;
    }

}
