package com.geeksec.common.utils;

/**
 * <AUTHOR>
 * @Description：
 */
public class TTL_Func {
    private int[]  m_TTL = new int[256];
    private static TTL_Func instance = null;
    private   TTL_Func() {
        int ttl = 1;
        for (int i = 0 ; i < 256 ; i++) {
            if (i > ttl) {
                if(ttl >= 0x80) {
                    ttl =0xff;
                } else {
                    ttl = ttl <<1;
                }
            }
            m_TTL[i] = ttl;
        }
    }
    public static TTL_Func getInstance() {
        if (instance == null) {
            synchronized (TTL_Func.class) {
                instance = new TTL_Func();
            }
        }
        return instance;
    }
    public int Judge(int IN_TTL, int OutDistance) {
        int BasicTTL = m_TTL[IN_TTL];
        OutDistance =BasicTTL - IN_TTL ;
        return BasicTTL;
    }
}
