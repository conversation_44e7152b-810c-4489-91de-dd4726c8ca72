package com.geeksec.common.utils;

import org.springframework.util.StringUtils;
import sun.net.util.IPAddressUtil;

/**
 * <AUTHOR>
 * @Description：IP utils
 */
public class IpNetUtils {

    //10.x.x.x/8
    final static byte SECTION_1 = 0x0A;
    //172.16.x.x/12
    final static byte SECTION_2 = (byte) 0xAC;
    final static byte SECTION_3 = (byte) 0x10;
    final static byte SECTION_4 = (byte) 0x1F;
    //192.168.x.x/16
    final static byte SECTION_5 = (byte) 0xC0;
    final static byte SECTION_6 = (byte) 0xA8;

    /**
     * 判断是否为内网IP
     *
     * @param ip
     * @return
     */
    public static boolean judgeInternal(String ip) {
        // 只有IPV4才认定为内网
        byte[] addrArr = IPAddressUtil.textToNumericFormatV4(ip);
        final byte b0 = addrArr[0];
        final byte b1 = addrArr[1];
        switch (b0) {
            case SECTION_1:
                return true;
            case SECTION_2:
                if (b1 >= SECTION_3 && b1 <= SECTION_4) {
                    return true;
                }
            case SECTION_5:
                switch (b1) {
                    case SECTION_6:
                        return true;
                    default:
                        return false;
                }
            default:
                return false;
        }
    }


    /**
     * 判断为IP version v4 否 则为v6
     */
    public static Boolean isValidIPV4(String ip) {
        if (StringUtils.hasText(ip)) {
            try{
                if (IPAddressUtil.isIPv4LiteralAddress(ip)) {
                    return true;
                } else if (IPAddressUtil.isIPv6LiteralAddress(ip)) {
                    return false;
                }
            }catch (Exception e) {
                return null;
            }
        } else {
            return null;
        }
        return false;
    }
}
