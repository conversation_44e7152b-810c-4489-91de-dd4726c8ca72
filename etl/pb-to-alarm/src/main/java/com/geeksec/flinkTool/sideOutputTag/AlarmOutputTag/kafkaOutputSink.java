package com.geeksec.flinkTool.sideOutputTag.AlarmOutputTag;

import static com.geeksec.flinkTool.descriptor.CustomDescriptor.subscribeKafkaDescriptor;
import static com.geeksec.task.LabelKafka2ES.PARALLELISM_2;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.webshell.KafkaConfig;
import com.geeksec.common.loader.PropertiesLoader;
import java.util.Map;
import java.util.Properties;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.common.state.BroadcastState;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.sink.TopicSelector;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 * @Date 2023/6/5
 */

public class kafkaOutputSink {

    private static Properties kafkaSinkProperties = new Properties();

    // 获取当前环境配置
    private static PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");
    public static String OUTPUT_TOPIC = "";
    public static String OUTPUT_BROKER_LIST = propertiesLoader.getProperty("kafka.broker.list");


    public static void AlarmKafkaSink(DataStream<JSONObject> alarmJsonStream, BroadcastStream<KafkaConfig> subscribeUnionStream, Map<String, Object> kafka_info) throws Exception {
        SingleOutputStreamOperator<JSONObject> alarmJsonScirbeStream = alarmJsonStream.connect(subscribeUnionStream).process(new BroadcastProcessFunction<JSONObject, KafkaConfig, JSONObject>() {

            @Override
            public void open(Configuration parameters) throws Exception {
                super.open(parameters);
            }

            @Override
            public void processElement(JSONObject jsonObject, BroadcastProcessFunction<JSONObject, KafkaConfig, JSONObject>.ReadOnlyContext readOnlyContext, Collector<JSONObject> collector) throws Exception {
                if (jsonObject != null) {
                    // 将Kafka外发配置通过广播流的方式添加到告警流的字段中
                    KafkaConfig config = (KafkaConfig) readOnlyContext.getBroadcastState(subscribeKafkaDescriptor).get("config-key");
                        if (ObjectUtils.allNotNull(config)) {
                            jsonObject.put("broker", config.getIp()+":"+config.getPort());
                            jsonObject.put("topic", config.getTopic());
                            OUTPUT_BROKER_LIST = config.getIp()+":"+config.getPort();
                            collector.collect(jsonObject);
                        } else {
                            jsonObject.put("broker", propertiesLoader.getProperty("kafka.broker.list"));
                            jsonObject.put("topic", propertiesLoader.getProperty("kafka.output.topic"));
                            collector.collect(jsonObject);
                    }
                }
            }

            @Override
            public void processBroadcastElement(KafkaConfig value, BroadcastProcessFunction<JSONObject, KafkaConfig, JSONObject>.Context context, Collector<JSONObject> collector) throws Exception {
                BroadcastState<String, KafkaConfig> state = context.getBroadcastState(subscribeKafkaDescriptor);
                state.put("config-key", value);
            }
        });
        kafkaSinkProperties.put("bootstrap.servers", OUTPUT_BROKER_LIST);
        kafkaSinkProperties.put("transaction.timeout.ms", "5000");
        KafkaSink<String> kafkaSink = KafkaSink.<String>builder()
                .setKafkaProducerConfig(kafkaSinkProperties)
                .setBootstrapServers(OUTPUT_BROKER_LIST)
                .setRecordSerializer(KafkaRecordSerializationSchema.builder()
                        // 根据topicselector读取topic配置动态改变外发的topic，Kafka服务器地址暂不能动态改变
                        .setTopicSelector(new TopicSelector<String>() {
                            @Override
                            public String apply(String s) {
                                JSONObject jsonObject = JSONObject.parseObject(s);
                                return jsonObject.getString("topic");
                            }
                        })
                        .setValueSerializationSchema(new SimpleStringSchema())
                        .build())
                .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                .build();
        DataStream<String> alarmJsonScirbeStr = alarmJsonScirbeStream.map(new MapFunction<JSONObject, String>() {
            @Override
            public String map(JSONObject jsonObject) throws Exception {
                return jsonObject.toString();
            }
        });
        alarmJsonScirbeStr.sinkTo(kafkaSink).name("kafka 外发告警日志").setParallelism(PARALLELISM_2);
    }


}
