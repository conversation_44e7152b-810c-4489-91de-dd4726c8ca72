package com.geeksec.flinkTool.labelKeySelector;

import java.util.List;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.types.Row;

/**
 * <AUTHOR>
 * @Date 2022/11/3
 */

public class SipDipFingerLabelKeySelector implements KeySelector<Row, List<String>> {
    @Override
    public List<String> getKey(Row row) throws Exception {
        List<String> SipDipFingerLabelKey = row.getFieldAs(1);
        return SipDipFingerLabelKey;
    }
}
