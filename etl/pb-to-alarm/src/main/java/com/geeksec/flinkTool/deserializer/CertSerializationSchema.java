package com.geeksec.flinkTool.deserializer;

import com.geeksec.analysisFunction.analysisEntity.protocolRelatedInfo.X509Cert;
import com.geeksec.common.utils.CertFormatUtil;
import java.io.IOException;
import java.security.cert.CertificateEncodingException;
import java.security.cert.X509Certificate;
import java.util.Date;
import java.util.LinkedHashMap;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @Author: GuanHao
 * @Date: 2022/6/13 11:24
 * @Description： <Functions List>
 */
//对于flink中获取的kafka的序列化数据，通过继承kafka的反序列化策略类KafkaDeserializationSchema，来实现对证书类的序列化数据的反序列化
public class CertSerializationSchema implements KafkaRecordDeserializationSchema<X509Cert> {
    private static final Logger LOG = LoggerFactory.getLogger(CertSerializationSchema.class);

    @Override
    public TypeInformation<X509Cert> getProducedType() {
        return TypeInformation.of(X509Cert.class);
    }

    @Override
    public void deserialize(ConsumerRecord<byte[], byte[]> record, Collector<X509Cert> collector) throws IOException {
        LOG.info("start millisecond is: {}", System.currentTimeMillis());
        X509Cert x509Cert;

        // 声明一个证书类
        byte[] bytes = record.value();

        byte[] bytes_key = record.key();
        // 内容解析：声明一个列表值为byte的value值为consumerRecord的value
        try {
            // try处理这个value值，如果是空数据就不做处理
            if (bytes.length == 0) {
                throw new IOException();
            }
            x509Cert = new X509Cert(bytes);
            // 证书解析
            byte[] cert = x509Cert.getCert();
            // 获取证书对象，调用java的X509证书方法，初始化一个X509证书对象
            X509Certificate certificate = CertFormatUtil.getCertificate(cert);//通过一个byte列表的cert，获取到X509Certificate的certificate

            byte[] encoded = new byte[0];
            try {
                encoded = certificate.getEncoded();//对证书对象进行编码，certificate类下面的一个方法
            } catch (CertificateEncodingException e) {
                encoded = "".getBytes();
            }
            x509Cert.setASN1MD5(CertFormatUtil.getDerCertHash(encoded, "MD5"));//通过编码后的encoded值，得到一个证书的MD5hash字段的值
            x509Cert.setASN1SHA1(CertFormatUtil.getDerCertHash(encoded, "SHA"));//通过编码后的encoded值，得到一个证书的SH1hash字段的值

            x509Cert.setCertID(x509Cert.getASN1SHA1());//证书的ID就是证书的ASN1SHA1值，给证书ID设置为ASN1SHA1值
            x509Cert.setVersion(certificate.getVersion());//获取到证书的版本信息
            try{
                x509Cert.setSubject(CertFormatUtil.getNameOIDMap(certificate,"subject"));
            }catch (Exception e){
                LOG.error("Subject解析失败");
                x509Cert.setSubject(new LinkedHashMap<>());
            }
            try{
                x509Cert.setIssuer(CertFormatUtil.getNameOIDMap(certificate,"issuer"));
            }catch (Exception e){
                LOG.error("Issuer解析失败");
                x509Cert.setIssuer(new LinkedHashMap<>());
            }

            LinkedHashMap<String, String> subject = x509Cert.getSubject();
            x509Cert.setCN(CertFormatUtil.readDataFromKeys(subject, "CN", ""));
            // 初始化两个日期
            Date notAfter = certificate.getNotAfter();
            Date notBefore = certificate.getNotBefore();
            x509Cert.setNotAfter(CertFormatUtil.timeFormat(notAfter));//使用日期
            x509Cert.setNotBefore(CertFormatUtil.timeFormat(notBefore));//到期
            x509Cert.setDuration((notAfter.getTime() - notBefore.getTime()) / 1000);//到期时间减去使用时间除以100
            x509Cert.setSerialNumber(certificate.getSerialNumber().toString());//得到序列号字符串
            LOG.info("Loading cert id is: {}", x509Cert.getASN1SHA1());
            collector.collect(x509Cert);
        } catch (Exception e) {
            LOG.error("证书解析异常");
            x509Cert = null;
            collector.collect(x509Cert);
        }
    }
}
