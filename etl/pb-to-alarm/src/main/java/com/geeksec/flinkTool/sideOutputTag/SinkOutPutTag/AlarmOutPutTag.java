package com.geeksec.flinkTool.sideOutputTag.SinkOutPutTag;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * <AUTHOR>
 * @Date 2022/12/29
 */

public class AlarmOutPutTag {
    public static final OutputTag<Row> Alarm_SIP_DIP_FINGER_ROW = new OutputTag<>("Alarm_SIP_DIP_FINGER_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Alarm_http_webLogin_info = new OutputTag<>("Alarm_http_webLogin_info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Alarm_Web_Login_Info = new OutputTag<>("Alarm_Web_Login_Info", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Alarm_Port_Scan_Row = new OutputTag<>("Alarm_Port_Scan_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Alarm_DNS_Tunnel_Row = new OutputTag<>("Alarm_DNS_Tunnel_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Alarm_Finger_Label_EdgeRow = new OutputTag<>("Alarm_Finger_Label_EdgeRow", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Alarm_DNSMine_EdgeRow = new OutputTag<>("Alarm_DNSMine_EdgeRow", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Alarm_Neoregeo_Row = new OutputTag<>("Alarm_Neoregeo_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Alarm_RDP_Row = new OutputTag<>("Alarm_RDP_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Alarm_Oracle_Row = new OutputTag<>("Alarm_Oracle_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Alarm_MYSQL_Row = new OutputTag<>("Alarm_MYSQL_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Alarm_SMB_Row = new OutputTag<>("Alarm_SMB_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Alarm_APP_SCAN_ROW = new OutputTag<>("Alarm_APP_SCAN_ROW", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Alarm_xRay_Row = new OutputTag<>("Alarm_xRay_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Alarm_suo5_Row = new OutputTag<>("Alarm_suo5_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Alarm_BeHinder_Row = new OutputTag<>("Alarm_BeHinder_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Alarm_AntSword_Row = new OutputTag<>("Alarm_AntSword_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Alarm_Tunnel_Row = new OutputTag<>("Alarm_Tunnel_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Alarm_SRCPC2_Row = new OutputTag<>("Alarm_SRCPC2_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Alarm_URCP_Row = new OutputTag<>("Alarm_URCP_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Alarm_WebShell_Row = new OutputTag<>("Alarm_WebShell_Row", TypeInformation.of(Row.class));
    public static final OutputTag<Row> Alarm_EncryptedTool_Row = new OutputTag<>("Alarm_EncryptedTool_Row", TypeInformation.of(Row.class));
}
