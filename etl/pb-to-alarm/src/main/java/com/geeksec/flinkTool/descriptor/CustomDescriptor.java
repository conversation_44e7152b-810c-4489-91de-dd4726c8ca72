package com.geeksec.flinkTool.descriptor;

import com.geeksec.analysisFunction.analysisEntity.protocolRelatedInfo.X509Cert;
import com.geeksec.analysisFunction.analysisEntity.webshell.KafkaConfig;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;

/**
 * <AUTHOR>
 * @Date 2024/1/25
 */

public class CustomDescriptor {

    public static final MapStateDescriptor<Integer, Integer> modelSwitchStateDescriptor = new MapStateDescriptor<>(
            "modelSwitch",
            BasicTypeInfo.INT_TYPE_INFO,
            BasicTypeInfo.INT_TYPE_INFO
    );
    public static final MapStateDescriptor<String, KafkaConfig> subscribeKafkaDescriptor = new MapStateDescriptor<>(
            "subscribeKafka",
            BasicTypeInfo.STRING_TYPE_INFO,
            TypeInformation.of(KafkaConfig.class)
    );

    public static final MapStateDescriptor<String, Row> labelRowDescriptor = new MapStateDescriptor<>(
            "labelRow",
            BasicTypeInfo.STRING_TYPE_INFO,
            TypeInformation.of(Row.class)
    );

    public static final MapStateDescriptor<String, X509Cert> certDescriptor = new MapStateDescriptor<>(
            "certInfo",
            BasicTypeInfo.STRING_TYPE_INFO,
            TypeInformation.of(X509Cert.class)
    );

}







