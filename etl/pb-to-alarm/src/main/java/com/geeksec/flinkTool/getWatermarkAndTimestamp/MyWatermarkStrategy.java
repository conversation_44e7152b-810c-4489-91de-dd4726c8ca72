package com.geeksec.flinkTool.getWatermarkAndTimestamp;

import org.apache.flink.api.common.eventtime.*;

/**
 * <AUTHOR>
 * @Date 2022/12/8
 */

public class MyWatermarkStrategy implements WatermarkStrategy {

    private long MyDelay;
    private int MyEventTimeTimeStamp;

    @Override
    public TimestampAssigner createTimestampAssigner(TimestampAssignerSupplier.Context context) {
        MyEventTimeTimeStamp myEventTimeTimeStamp = new MyEventTimeTimeStamp();
        myEventTimeTimeStamp.set_Time_Stamp_Position(MyEventTimeTimeStamp);
        return myEventTimeTimeStamp;
    }
    @Override
    public WatermarkGenerator createWatermarkGenerator(WatermarkGeneratorSupplier.Context context) {
        MyEventTimeWaterMarks watermark = new MyEventTimeWaterMarks();
        watermark.setDelay(MyDelay);
        return watermark;
    }

    public void setMyDelay(long MyDelay) {
        this.MyDelay = MyDelay;
    }
    public void setMyEventTimeTimeStamp(int MyEventTimeTimeStamp) {
        this.MyEventTimeTimeStamp = MyEventTimeTimeStamp;
    }
}
