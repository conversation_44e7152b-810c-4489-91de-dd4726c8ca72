cert.kafka.bootstrap.servers.host: kafka
cert.cluster.redis.host: redis
cert.cluster.elasticsearch.host: elasticsearch
cert.lmdb.db.path: http://host.docker.internal:59000/cert/read_lmdb
cert.cluster.elasticsearch.port: 9200
cert.kafka.bootstrap.servers.port: 9094
cert.cluster.redis.port: 6379
cert.cluster.elasticsearch.index.user: cert_user
cert.cluster.elasticsearch.index.system: cert_system
cert.kafka.group.id: analysis_cert_01
cert.kafka.System_group.id: System_analysis_cert_01
cert.kafka.topic.name: certfile
cert.kafka.system_topic.name: certfile_system
kafka.auto.offset.reset.mode: latest
redis.timeout: 10000
redis.pool.max: 50
redis.expire.time: 86400
mysql.database.host: **********************************************************************************************************
mysql.analysis.host: **************************************************************************************************************
mongo.database.host: ********************************************

nebula.space.name: gs_analysis_graph
#nebula.space.name: gs_analysis_graph_test
nebula.graph.port: 9669
nebula.graph.username: root
nebula.graph.password: nebula
nebula.meta.addr: host.docker.internal:9559
nebula.graph.addr: host.docker.internal:9669
nebula.graph.host: host.docker.internal
#cert.kafka.bootstrap.servers.host: ***************
#cert.cluster.redis.host: ***************
#cert.cluster.elasticsearch.host: ***************
#cert.lmdb.db.path: http://***************:59000/cert/read_lmdb
#cert.kafka.bootstrap.servers.host: ***********
#cert.cluster.redis.host: ***********
#cert.cluster.elasticsearch.host: ***********
#cert.cluster.elasticsearch.scan.host: ***********
#cert.cluster.elasticsearch.port: 9211
#cert.cluster.elasticsearch.scan.port: 9200
#cert.kafka.bootstrap.servers.port: 9092
#cert.cluster.redis.port: 6379
#cert.cluster.elasticsearch.index.user: cert_user
#cert.cluster.elasticsearch.index.system: cert_system
#cert.kafka.group.id: analysis_cert_01
#cert.kafka.System_group.id: System_analysis_cert_01
#cert.kafka.topic.name: certfile
#cert.kafka.system_topic.name: certfile_system
#kafka.auto.offset.reset.mode: latest
#redis.timeout: 10000
#redis.pool.max: 50
#redis.expire.time: 86400
#mysql.database.host: ***************************************************************************************************************
#mysql.analysis.host: *******************************************************************************************************************
#mongo.database.host: ***********************************************************************
#nebula.meta.addr: ***********:9559
#nebula.graph.addr: ***********:9669
#nebula.graph.host: ***********

#cert.kafka.bootstrap.servers.host: ***************
#cert.cluster.redis.host: ***************
#cert.cluster.elasticsearch.host: ***************
#cert.cluster.elasticsearch.port: 9200
#cert.kafka.bootstrap.servers.port: 9092
#cert.cluster.redis.port: 6379
#cert.cluster.elasticsearch.index.user: cert_user
#cert.cluster.elasticsearch.index.system: cert_system
#cert.kafka.group.id: analysis_cert_01
#cert.kafka.System_group.id: System_analysis_cert_01
#cert.kafka.topic.name: certfile
#cert.kafka.system_topic.name: certfile_system
#kafka.auto.offset.reset.mode: latest
#redis.timeout: 10000
#redis.pool.max: 50
#redis.expire.time: 86400
#mysql.database.host: *******************************************************************************************************************
#mysql.analysis.host: ***********************************************************************************************************************
#mongo.database.host: ***************************************************************************
#nebula.meta.addr: ***************:9559
#nebula.graph.addr: ***************:9669
#nebula.graph.host: ***************

mysql.database.user: root
mysql.database.password: simpleuse23306p
redis.database.password: simpleuse6379p

nebula.pool.max.size: 200
nebula.pool.min.size: 50
nebula.pool.idle.time: 180000
nebula.pool.timeout: 300000
nebula.session.size : 200

lmdb.maxsize: 1099511627776
lmdb.path: /data/lmdb/cert/cert_system
lmdb.path.user: /data/lmdb/cert/cert_user
lmdb.oid.maxsize: 1073741824
lmdb.oid.path: /data/lmdb/oid/
## production environment 5TB
#lmdb.maxsize: 5497558138880

#lmdb.maxsize: 1073741824
#lmdb.path: E:/lmdb_data/cert_system
#lmdb.path.user: E:/lmdb_data/cert_user
#lmdb.oid.maxsize: 1073741824
#lmdb.oid.path: E:/lmdb_data/oid