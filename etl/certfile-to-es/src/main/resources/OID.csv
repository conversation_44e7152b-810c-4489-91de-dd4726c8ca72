OID,Description,type
*******.4.1.46222.1.10,Domain validation,DV
*******.4.1.58723.1.3,Domain validation,DV
2.16.840.1.114414.********,Domain validated certificate,DV
*******.4.1.29836.1.6,Domain Validation Certificate Policy,DV
*******.4.1.4146.1.10,Domain validation certificate policy,DV
0.4.0.2042.1.6,Domain Validated Certificate Policy (DVCP),DV
*******.4.1.4146.10.2.3,Domain Validation (DV) authentication policy,DV
*******.4.1.4146.1.10.20,SignTrust Domain Validation Certificate Policy,DV
*********.22,Secure Sockets Layer (SSL) server Domain Validated (DV),DV
2.16.840.1.114412.1.2,Domain‐validated Secure Sockets Layer (SSL) certificate,DV
*******.4.1.14777.1.2.4,Domain Validated (DV) Secure Sockets Layer (SSL) certificate,DV
*********.22.1,Secure Sockets Layer (SSL) server Domain Validated (DV) policy,DV
*********.23,Secure Sockets Layer (SSL) server wildcard Domain Validated (DV),DV
*******.4.1.4146.1.10.10,AlphaSSL (previously BelSign) domain validation certificate policy,DV
*********.23.1,Secure Sockets Layer (SSL) server wildcard Domain Validated (DV) policy,DV
*******.4.1.15862.*******,B-Trust domain validation Secure Sockets Layer (SSL) qualified certificate,DV
*******.4.1.15862.*******,B-Trust domain validation Secure Sockets Layer (SSL) qualified certificate,DV
*******.4.1.4146.10.3.3,Domain Validation (DV) Secure/Multipurpose Internet Mail Extension (S/MIME) policy,DV
**********.2.1,Issuing certification authority's compliance with the CA/Browser Forum's Baseline Requirements - No entity identity asserted,DV
*******.4.1.58723.1.2,Organization validation,OV
2.16.840.1.114414.********,Organization validated certificate,OV
2.16.792.*******.1.2,Organization validation certificate,OV
*********.21,Code signing Organization Validated (OV),OV
*******.4.1.4146.1.20,Organization validation certificate policy,OV
*******.4.1.4146.10.4.2,Organization validation code signing policy,OV
*********.21.1,Code signing Organization Validated (OV) policy,OV
*******.4.1.4146.10.4.1,Organization Validation (OV) code signing policy,OV
*******.4.1.19389.2.4.1,Organization Validation (OV) Code-Sign certificate,OV
*******.4.1.4146.10.2.2,Organization Validation (OV) authentication policy,OV
2.16.788.*******.1.2.1,Organization validation Secure Sockets Layer (SSL),OV
*******.4.1.8024.*********.1,Organization-validated certificate,OV
*******.4.1.4146.1.20.1,Testing profile for organization-validated certificates,OV
*********.20,Secure Sockets Layer (SSL) server Organization Validated (OV),OV
2.16.840.1.114412.3.1,Organization-validated certificates used to sign standard objects,OV
*******.4.1.14777.1.2.1,Organization Validated (OV) Secure Sockets Layer (SSL) certificate,OV
2.16.788.1.2.6.1.9.1.1,Organization validation Secure Sockets Layer (SSL) and wildcard SSL,OV
2.16.788.1.2.6.1.9.2.1,Organization validation Secure Sockets Layer (SSL) and wildcard SSL,OV
*********.20.1,Secure Sockets Layer (SSL) server Organization Validated (OV) policy,OV
*********.19,Secure Sockets Layer (SSL) server wildcard Organization Validated (OV),OV
*******.4.1.43054.1,Certificate policy for organization validation Secure Sockets Layer (SSL),OV
*********.19.1,Secure Sockets Layer (SSL) server wildcard Organization Validated (OV) policy,OV
*******.4.1.15862.*******,B-Trust organization validation Secure Sockets Layer (SSL) qualified certificate,OV
*******.4.1.15862.*******,B-Trust organization validation Secure Sockets Layer (SSL) qualified certificate,OV
*******.4.1.4146.10.3.2,Organization Validation (OV) Secure Multipurpose Internet Mail Extension (S/MIME) policy,OV
**********.2.2,Certificates issued in accordance with the CA/Browser Forum's Baseline Requirements - Organization identity asserted,OV
*******.4.1.15862.*******,B-Trust organization-validation Secure Sockets Layer (SSL) qualified certificate according to payment services Directive 2015/2366,OV
*******.4.1.311.60,Extended validation,EV
*******.4.1.48679.100,Extended validation,EV
*******.4.1.55594.1.1.1,Extended validation,EV
*******.4.1.58723.1.1,Extended validation,EV
*******.4.1.52331.2,Extended Validation (EV),EV
*******.4.1.30360.*******.*******,Extended validation certificate,EV
*******.4.1.34697.1.1,Extended validation certificate,EV
*******.4.1.5237.1.1.3,Extended validation certificate,EV
2.16.792.*******.1.5,Extended validation certificate,EV
2.16.840.*********.1.1.5,Extended validation certificate,EV
2.16.840.1.113839.0.6.3,Extended validation certificate,EV
2.16.840.1.114413.********,Extended Validation Subscribers,EV
2.16.788.1.2.6.1.9.1.4,Extended validation code signing,EV
2.16.788.1.2.6.1.9.2.4,Extended validation code signing,EV
*******.4.1.48679.100.3,High assurance; extended validation,EV
*******.4.1.4788.2.200.1,Extended Validation (EV) certificate,EV
*******.4.1.4788.2.202.1,Extended Validation (EV) certificate,EV
*********.18,Code signing Extended Validation (EV),EV
*******.4.1.31247.1.3,DigiSSL Extended Validation Extension,EV
*******.4.1.46222.1.2,Extended Validation (EV) code signing,EV
2.16.840.1.114412.3.2,Extended Validation (EV) code signing,EV
**********.3,Extended Validation (EV) code signing,EV
*******.4.1.29836.1.10,Extended Validation Certificate Policy,EV
*******.4.1.40578.1.1.1,Extended Validation Assurance Policies,EV
*******.4.1.60593.1.1,Extended validation certificate policy,EV
*******.4.1.60593.1.1,Extended validation certificate policy,EV
*********.18.1,Code signing Extended Validation (EV) policy,EV
*******.4.1.58723.1.1.2,Code signing Extended Validation (EV) policy,EV
0.4.0.2042.1.4,Extended Validation Certificate Policy (EVCP),EV
*******.4.1.48679.3.7.37,Extended Validation Certificate Policy (EVCP),EV
2.16.156.112554.3,Extended Validation (EV) server certification,EV
*******.4.1.19389.2.4.2,Extended Validation (EV) Code-Sign certificate,EV
*******.4.1.37822.3.1,Extended validation certificate identification,EV
*******.4.1.4146.10.2.1,Extended Validation (EV) authentication policy,EV
*******.4.1.46222.1.1,Extended validation Secure Sockets Layer (SSL),EV
2.16.788.1.2.6.1.9.1.2,Extended validation Secure Sockets Layer (SSL),EV
2.16.788.1.2.6.1.9.2.2,Extended validation Secure Sockets Layer (SSL),EV
*******.4.1.32029.1.5.6,Extended validation (EV) extended key usage (EKU),EV
*******.4.1.311.60.2,Extended validation for certificate subject matter,EV
*******.4.1.32029.1.4.4.4.6,Extended validation (EV) certificate authority (CA),EV
*******.4.1.14433.2,Global trust Extended Validation-SSL certificate only,EV
*******.4.1.14777.6.1.2,Extended Validation (EV) electronic office certificate,EV
*******.4.1.8024.*********.2,extended validation certificate,EV
2.16.840.1.114412.2.1,DigiCert Extended Validation (EV) SSL/TLS certificates,EV
**********.1,Extended Validation (EV) guidelines certificate policy,EV
*******.4.1.4146.1.2,Extended validation certificate policy for code signing,EV
*********.17,Secure Sockets Layer (SSL) server Extended Validation (EV),EV
*******.4.1.58723.1.1.1,Secure Sockets Layer (SSL) Extended Validation (EV) policy,EV
*******.4.1.34697.2,Premium Extended Validation (EV) Certificate Authority (CA),EV
*******.4.1.48679.110,Extended validation and high assurance certificate services,EV
*******.4.1.38621.1.0,Extended Validation (EV) Secure Sockets Layer (SSL) authority,EV
*******.4.1.4146.10.1.1,Extended Validation (EV) Transport Layer Security (TLS) policy,EV
1.2.156.112570.1.1.3,Extended Validation (EV) Certification Practice Statement (CPS),EV
*******.4.1.14777.6.1.1,Extended Validation (EV) Secure Sockets Layer (SSL) certificate,EV
2.16.156.112554,China Financial Certification Authority Extended Validation (EV),EV
2.16.840.1.114412.2,Extended Validation (EV) Secure Sockets Layer (SSL) certificates,EV
*********.17.1,Secure Sockets Layer (SSL) server Extended Validation (EV) policy,EV
*******.4.1.37476.2.5.2.2.0.2,Policy for automatic certificate creation with extended validation,EV
*******.4.1.59537.5.1.1.2,Policy for automatic certificate creation with extended validation,EV
2.16.840.1.114412.1,Non Extended Validation (EV) Secure Sockets Layer (SSL) certificates,EV
*******.4.1.52331.2.10,Extended Validation (EV) for Certificate Practice Statement (CPS) v1.0,EV
*******.4.1.6449.1.2.1.5.1,Comodo Extended Validation (EV) Certification Practice Statement (CPS),EV
*******.4.1.14370.1.7,GeoTrust Certificate Policy certificates (non-EV [Extended Validation]),EV
2.16.840.1.113733.1.7.23.6,Verisign Certification Policy for Extended Validation (EV) certificates,EV
2.16.840.1.114028.10.1.2,Entrust Extended Validation (EV) Certification Practice Statement (CPS),EV
2.16.756.1.83.21.0,Root Extended Validation (EV) "CA 2" Certification Practice Statement (CPS),EV
0.4.0.2042.1.5,Extended Validation Certificate Policy requiring a secure user device (EVCP+),EV
2.16.840.1.113733.1.7.48.1,Thawte Extended Validation (EV) Certification Practice Statement (CPS) v. 3.3,EV
*******.4.1.52450.1.1.1,Certification Practice Statement (CPS) for Extended Validation (EV) guidelines,EV
*******.4.1.14370.1.6,GeoTrust Extended Validation (EV) Certification Practice Statement (CPS) v. 2.6,EV
2.16.578.1.26.1.3.3,Buypass Class 3 Extended Validation (EV) Certification Practice Statement (CPS),EV
2.16.840.1.114171.500.9,Extended Validation certificate policy for Wells Fargo WellsSecure certificates,EV
*******.4.1.45376.1.6,Mahardika Extended Validation (EV) Certification Practice Statement (CPS) v. 2.6,EV
2.16.840.1.114414.********,Starfield Extended Validation (EV) Certification Practice Statement (CPS) v. 2.0,EV
**********.31,Extended Validation (EV) Transport Layer Security (TLS) certificates for ".onion",EV
********.17.1.22,a.sign SSL Extended Validation (EV) Certification Practice Statement (CPS) v1.3.4,EV
*******.4.1.4146.1.1,Extended validation certificate policy for Secure Sockets Layer (SSL) certificates,EV
2.16.840.1.114404.*******.1,SecureTrust Extended Validation (EV) Certification Practice Statement (CPS) v1.1.1,EV
*******.4.1.4146.10.3.1,Extended Validation (EV) Secure/Multipurpose Internet Mail Extension (S/MIME) policy,EV
*******.4.1.782.*******.1,Network Solutions Extended Validation (EV) Certification Practice Statement (CPS) v. 1.1,EV
2.16.840.1.114412.*******,DigiCert Extended Validation (EV) Certification Practice Statement (CPS) v. 1.0.3 p. 56,EV
*******.4.1.15862.*******,B-Trust Extended Validation (EV) secure server Secure Sockets Layer (SSL) qualified certificate,EV
1.2.392.200091.100.731.1,SECOM Trust Systems Extended Validation (EV) Certification Practice Statement (CPS) (in Japanese),EV
*******.4.1.48679.********,Extended Validation Certificate Policy (EVCP) identity issuance Certification Practice Statement (CPS),EV
1.2.392.200091.100.721.1,"SECOM" Trust Systems Extended Validation (EV) Certification Practice Statement (CPS) (in Japanese), p. 2
*******.4.1.22234.*******.1,Keynectis Extended Validation (EV) Certificate Authority (CA) Certification Practice Statement (CPS) v 0.3,EV
*******.4.1.4146.2.4,Policy by which the time-stamping services operated by GlobalSign incorporate the time into IETF RFC 3161 responses specifically for extended validation code signing services,EV
0.4.0.194112.1.4,"QEVCP-w" policy for European Union qualified website certificate issued to a legal person and linking the website to that person based on the Extended Validation Certificate Guidelines
