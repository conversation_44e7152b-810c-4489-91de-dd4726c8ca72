[kdcdefaults]
kdc_ports = 192.168.101.7:21732
kdc_tcp_ports = ""

[libdefaults]
default_realm = HADOOP.COM
kdc_timeout = 2500
clockskew = 300
use_dns_lookup = 0
udp_preference_limit = 1465
max_retries = 5
dns_lookup_kdc = false
dns_lookup_realm = false
renewable = false
forwardable = false
renew_lifetime = 0m
max_renewable_life = 30m
allow_extend_version = false
default_ccache_name = FILE:/tmp//krb5cc_%{uid}

[realms]
HADOOP.COM = {
kdc = 192.168.101.6:21732
kdc = 192.168.101.7:21732
admin_server = 192.168.101.6:21730
admin_server = 192.168.101.7:21730
kpasswd_server = 192.168.101.6:21731
kpasswd_server = 192.168.101.7:21731
kpasswd_port = 21731
kadmind_port = 21730
kadmind_listen = 192.168.101.7:21730
kpasswd_listen = 192.168.101.7:21731
renewable = false
forwardable = false
renew_lifetime = 0m
max_renewable_life = 30m
acl_file = /opt/huawei/Bigdata/FusionInsight_BASE_6.5.1/install/FusionInsight-kerberos-1.17/kerberos/var/krb5kdc/kadm5.acl
key_stash_file = /opt/huawei/Bigdata/FusionInsight_BASE_6.5.1/install/FusionInsight-kerberos-1.17/kerberos/var/krb5kdc/.k5.HADOOP.COM
}

[domain_realm]
.hadoop.com = HADOOP.COM

[logging]
kdc = SYSLOG:INFO:DAEMON
admin_server = SYSLOG:INFO:DAEMON
default = SYSLOG:NOTICE:DAEMON
