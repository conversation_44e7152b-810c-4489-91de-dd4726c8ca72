{"CERT": {"NAME": "details", "BLOOMFILTER": "ROW", "VERSIONS": "1", "IN_MEMORY": "false", "KEEP_DELETED_CELLS": "FALSE", "DATA_BLOCK_ENCODING": "NONE", "TTL": "FOREVER", "COMPRESSION": "NONE", "MIN_VERSIONS": "0", "BLOCKCACHE": "true", "BLOCKSIZE": "65536", "REPLICATION_SCOPE": "0"}, "cert_knowledgebase": [{"NAME": "app_property_info", "BLOOMFILTER": "ROW", "VERSIONS": "1", "IN_MEMORY": "false", "KEEP_DELETED_CELLS": "FALSE", "DATA_BLOCK_ENCODING": "NONE", "TTL": "FOREVER", "COMPRESSION": "NONE", "MIN_VERSIONS": "0", "BLOCKCACHE": "true", "BLOCKSIZE": "65536", "REPLICATION_SCOPE": "0"}, {"NAME": "apt_info", "BLOOMFILTER": "ROW", "VERSIONS": "1", "IN_MEMORY": "false", "KEEP_DELETED_CELLS": "FALSE", "DATA_BLOCK_ENCODING": "NONE", "TTL": "FOREVER", "COMPRESSION": "NONE", "MIN_VERSIONS": "0", "BLOCKCACHE": "true", "BLOCKSIZE": "65536", "REPLICATION_SCOPE": "0"}, {"NAME": "believable_info", "BLOOMFILTER": "ROW", "VERSIONS": "1", "IN_MEMORY": "false", "KEEP_DELETED_CELLS": "FALSE", "DATA_BLOCK_ENCODING": "NONE", "TTL": "FOREVER", "COMPRESSION": "NONE", "MIN_VERSIONS": "0", "BLOCKCACHE": "true", "BLOCKSIZE": "65536", "REPLICATION_SCOPE": "0"}, {"NAME": "cert_info", "BLOOMFILTER": "ROW", "VERSIONS": "1", "IN_MEMORY": "false", "KEEP_DELETED_CELLS": "FALSE", "DATA_BLOCK_ENCODING": "NONE", "TTL": "FOREVER", "COMPRESSION": "NONE", "MIN_VERSIONS": "0", "BLOCKCACHE": "true", "BLOCKSIZE": "65536", "REPLICATION_SCOPE": "0"}, {"NAME": "scan_detection_info", "BLOOMFILTER": "ROW", "VERSIONS": "1", "IN_MEMORY": "false", "KEEP_DELETED_CELLS": "FALSE", "DATA_BLOCK_ENCODING": "NONE", "TTL": "FOREVER", "COMPRESSION": "NONE", "MIN_VERSIONS": "0", "BLOCKCACHE": "true", "BLOCKSIZE": "65536", "REPLICATION_SCOPE": "0"}, {"NAME": "threat_info", "BLOOMFILTER": "ROW", "VERSIONS": "1", "IN_MEMORY": "false", "KEEP_DELETED_CELLS": "FALSE", "DATA_BLOCK_ENCODING": "NONE", "TTL": "FOREVER", "COMPRESSION": "NONE", "MIN_VERSIONS": "0", "BLOCKCACHE": "true", "BLOCKSIZE": "65536", "REPLICATION_SCOPE": "0"}], "domain_knowledgebase": {"NAME": "domain_info", "BLOOMFILTER": "ROW", "VERSIONS": "1", "IN_MEMORY": "false", "KEEP_DELETED_CELLS": "FALSE", "DATA_BLOCK_ENCODING": "NONE", "TTL": "FOREVER", "COMPRESSION": "NONE", "MIN_VERSIONS": "0", "BLOCKCACHE": "true", "BLOCKSIZE": "65536", "REPLICATION_SCOPE": "0"}, "service_company_knowledgebase": {"NAME": "service_company_info", "BLOOMFILTER": "ROW", "VERSIONS": "1", "IN_MEMORY": "false", "KEEP_DELETED_CELLS": "FALSE", "DATA_BLOCK_ENCODING": "NONE", "TTL": "FOREVER", "COMPRESSION": "NONE", "MIN_VERSIONS": "0", "BLOCKCACHE": "true", "BLOCKSIZE": "65536", "REPLICATION_SCOPE": "0"}}