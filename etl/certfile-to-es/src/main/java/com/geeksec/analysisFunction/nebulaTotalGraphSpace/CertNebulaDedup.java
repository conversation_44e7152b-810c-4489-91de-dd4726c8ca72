package com.geeksec.analysisFunction.nebulaTotalGraphSpace;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.dbConnect.nebula.CertNebulaPoolUtils;
import com.geeksec.flinkTool.sideOutputTag.CertNebulaDedupOutPutTag;
import com.geeksec.utils.FileUtil;
import com.geeksec.utils.RedisUtils;
import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.net.NebulaPool;
import com.vesoft.nebula.client.graph.net.Session;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * <AUTHOR>
 * @Date 2023/10/12
 */

public class CertNebulaDedup extends ProcessFunction<Row, Row> {

    private static Properties properties = FileUtil.getProperties("/config.properties");
    public static final String NEBULA_GRAPH_ADDR = properties.getProperty("nebula.graph.addr");
    public static final String NEBULA_META_ADDR = properties.getProperty("nebula.meta.addr");
    public static final String NEBULA_GRAPH_SPACE = properties.getProperty("nebula.space.name");

    private static final Logger logger = LoggerFactory.getLogger(CertNebulaDedup.class);

    private static NebulaPool nebulaPool;
    private static transient JedisPool jedisPool = null;

    /**
     * 初始化定时器
     */
    private transient ScheduledFuture<?> scheduledFuture;
    private transient ScheduledExecutorService executorService;
    public static List<String> NebulaInitSQLList = new ArrayList<>();

    @Override
    public void open(Configuration parameters) throws Exception {
        // 初始化nebula连接池
        nebulaPool = CertNebulaPoolUtils.nebulaPool(CertNebulaPoolUtils.nebulaPoolConfig());

        // redis池 初始化.
        jedisPool = RedisUtils.initJedisPool();
        logger.info("生成jedisPool成功! {}", jedisPool.getNumIdle(),jedisPool.hashCode());

        super.open(parameters);
        InputStream NebulaInitSQLListStream = this.getClass().getClassLoader().getResourceAsStream("nebula_init.sql");
        BufferedReader NebulaInitSQLListBuffer = new BufferedReader(new InputStreamReader(NebulaInitSQLListStream));
        try {
            NebulaInitSQLList = FileUtil.loadNebulaInitSQLList(NebulaInitSQLListBuffer);
        } catch (Exception e) {
            throw new IOException("file load fail ==>", e);
        }

        executorService = Executors.newSingleThreadScheduledExecutor();
        final long interval = 600_000L; // 1 小时
        scheduledFuture = executorService.scheduleAtFixedRate(
                new Runnable() {
                    @Override
                    public void run(){
                        try {
                            // 检查当前时间是否在22点到24点之间
                            if (isTimeBetween()) {
                                    Session session = CertNebulaPoolUtils.getSession(nebulaPool);
                                    // 获取所有图空间
                                    ResultSet spacesResultSet = session.execute("SHOW SPACES");
                                    List<com.vesoft.nebula.Row> rows = spacesResultSet.getRows();
                                    List<String> spaces = new ArrayList<>();
                                    for(com.vesoft.nebula.Row resultRow:rows){
                                        byte[] spaceNameBytes = resultRow.getValues().get(0).getSVal();
                                        String spaceName = new String(spaceNameBytes, StandardCharsets.UTF_8);
                                        if(spaceName.split("_").length>3){
                                            spaces.add(spaceName);
                                        }
                                    }
                                    // 处理图空间
                                    manageSpaces(spaces);
                            }
                        } catch (Exception e) {
                            logger.error("Error managing graph spaces: ", e);
                        }
                    }
                },
                interval,
                interval,
                TimeUnit.MILLISECONDS);
    }

    @Override
    public void close() throws Exception {
        if (nebulaPool!=null){
            nebulaPool.close();
        }
        if (scheduledFuture != null) {
            scheduledFuture.cancel(false);
        }
        if (executorService != null) {
            executorService.shutdown();
        }
        if (jedisPool != null) {
            jedisPool.close();
        }
        super.close();
    }

    private boolean isTimeBetween() {
        SimpleDateFormat sdf = new SimpleDateFormat("HH");
        int currentHour = Integer.parseInt(sdf.format(new Date()));
        return currentHour >= 22 && currentHour < 24;
    }

    private void manageSpaces(List<String> spaces) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date currentDate = new Date();
        Date thirtyDaysAgo = new Date(currentDate.getTime() - TimeUnit.DAYS.toMillis(30));
        TreeMap<Date, String> spaceMap = new TreeMap<>();

        // 遍历现有图空间并存储到TreeMap
        for (String space : spaces) {
            String datePart = space.split("_")[3];
            try {
                spaceMap.put(sdf.parse(datePart), space);
            } catch (ParseException e) {
                logger.error("Failed to parse date from space name: {}", space, e);
            }
        }

        Session session = null;
        String spaceName = "";
        try {
            session = CertNebulaPoolUtils.getSession(nebulaPool);

            // 删除30天前的图空间
            for (Map.Entry<Date, String> entry : spaceMap.entrySet()) {
                if (entry.getKey().before(thirtyDaysAgo)) {
                    spaceName = entry.getValue();
                    session.execute("DROP SPACE " + spaceName);
                    logger.info("Deleted space: {}", spaceName);
                }
            }

            // 检查并创建缺失的图空间
            for (int i = 0; i < 30; i++) {
                Date targetDate = new Date(currentDate.getTime() + TimeUnit.DAYS.toMillis(i));
                String targetDateStr = sdf.format(targetDate);
                String graphName = NEBULA_GRAPH_SPACE + "_" + targetDateStr;
                if (!spaces.contains(graphName)){
                    String createSql = String.format("CREATE SPACE IF NOT EXISTS `%s` (partition_num=1,replica_factor=1,vid_type=FIXED_STRING(200));", graphName);
                    session.execute(createSql);
                    logger.info("Created space: {}", graphName);
                    initNebulaBySQL(session,graphName);
                }
            }
        } catch (Exception e) {
            logger.error("Error occurred while trying to drop space: {}", spaceName, e);
        }finally{
            if (session!=null){
                session.release();
            }
        }
    }

    public static void initNebulaBySQL(Session session,String spaceName){
        try {
            for(String sql:NebulaInitSQLList){
                String initSql = String.format("use %s;%s", spaceName,sql);
                session.execute(initSql);
            }
            logger.info("init tag and edge for space: {}", spaceName);
        } catch (Exception e) {
            logger.error("Error occurred while init tag and edge for space: {}", spaceName, e);
        }
    }

    @Override
    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
        String dedupType = (String) row.getField(0);
        X509Cert x509Cert = (X509Cert) row.getField(1);
        Jedis jedis = null;
        String key = "";

        try {
            jedis = RedisUtils.getJedis(jedisPool);
            jedis.select(11);

            switch (dedupType){
                // 分类设置sql语句
                case "cert_id":
                    String cert_id = x509Cert.getCertId();
                    key = cert_id;
                    break;
                case "issuer_md5":
                    String issuer_md5 = x509Cert.getIssuerMD5();
                    key = "issuer_" + issuer_md5;
                    break;
                case "subject_md5":
                    String subject_md5 = x509Cert.getSubjectMD5();
                    key = "subject_" + subject_md5;
                    break;
                case "ORG":
                    String ORG = x509Cert.getCompany();
                    key = ORG;
                    break;
                case "URL":
                    String URL = (String) row.getField(2);
                    key = URL;;
                    break;
                default:
                    break;
            }

            // 如果是证书需要更新，如果是实体，不需要更新
            if(jedis.exists(key)){
                switch (dedupType){
                    case "cert_id":
                        context.output(CertNebulaDedupOutPutTag.Dedup_Nebula_Cert,row);
                        break;
                    case "issuer_md5":
                        logger.info("issuer 重复，无需更新");
                        break;
                    case "subject_md5":
                        logger.info("subject 重复，无需更新");
                        break;
                    case "URL":
                        logger.info("URL 重复，无需更新");
                        break;
                    case "ORG":
                        logger.info("ORG 重复，无需更新");
                        break;
                    default:
                        break;
                }
            }else {
                switch (dedupType){
                    case "cert_id":
                        jedis.setex(key,86400,"1");
                        context.output(CertNebulaDedupOutPutTag.Not_Dedup_Nebula_Cert,row);
                        break;
                    case "issuer_md5":
                        jedis.setex(key,86400,"1");
                        context.output(CertNebulaDedupOutPutTag.Not_Dedup_Nebula_Issuer,row);
                        break;
                    case "subject_md5":
                        jedis.setex(key,86400,"1");
                        context.output(CertNebulaDedupOutPutTag.Not_Dedup_Nebula_Subject,row);
                        break;
                    case "URL":
                        jedis.setex(key,86400,"1");
                        context.output(CertNebulaDedupOutPutTag.Not_Dedup_Nebula_URL,row);
                        break;
                    case "ORG":
                        jedis.setex(key,86400,"1");
                        context.output(CertNebulaDedupOutPutTag.Not_Dedup_Nebula_ORG,row);
                        break;
                    default:
                        break;
                }
            }
        }catch (Exception e){
            logger.error("redis连接获取失败");
        }finally {
            if (jedis != null){
                jedis.close();
            }
        }
    }
}
