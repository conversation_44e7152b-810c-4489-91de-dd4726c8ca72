package com.geeksec.analysisFunction.nebulaDayGraphSpace;

import static com.geeksec.task.FlinkCertAnalysis.PA1;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.analysisFunction.infoSink.nebula.CertNebulaSinkFunctionByDay;
import com.geeksec.flinkTool.sideOutputTag.CertNebulaDedupOutPutTagByDay;
import java.util.List;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

/**
* <AUTHOR>
* @Date  2024/8/19
*/

public class NebulaHandlerByDay {

    public static void nebulaInfoHandleFunctionByDay(SingleOutputStreamOperator<X509Cert> writeCertModelStream){

        // 对所有入库ES的证书进行Nebula的入库操作
        SingleOutputStreamOperator<Row> NebulaDedupStream = writeCertModelStream.map((MapFunction<X509Cert, Row>) x509Cert -> {
            Row row = new Row(2);
            row.setField(0,"cert_id");
            row.setField(1,x509Cert);
            return row;
        }).name("当日：cert_id信息提取").setParallelism(PA1)
        .process(new CertNebulaDedupByDay()).name("当日：nebula查询证书sha1是否已经insert").setParallelism(PA1);

        // 对所有入库的证书进行签发机构和所有者的信息提取以及入库nebula
        SingleOutputStreamOperator<Row> NebulaIssuerStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {
                String issuer_md5 = x509Cert.getIssuerMD5();
                if(issuer_md5 != null && !issuer_md5.isEmpty()){
                    Row row = new Row(2);
                    row.setField(0,"issuer_md5");
                    row.setField(1,x509Cert);
                    collector.collect(row);
                }
            }
        }).name("当日：签发机构的信息提取").setParallelism(PA1)
        .process(new CertNebulaDedupByDay()).name("当日：nebula查询证书实体issuer是否已经insert").setParallelism(PA1);

        SingleOutputStreamOperator<Row> NebulaSubjectStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {
                String subject_md5 = x509Cert.getSubjectMD5();
                if(subject_md5 != null && !subject_md5.isEmpty()){
                    Row row = new Row(2);
                    row.setField(0,"subject_md5");
                    row.setField(1,x509Cert);
                    collector.collect(row);
                }
            }
        }).name("当日：所有者的信息提取").setParallelism(PA1)
        .process(new CertNebulaDedupByDay()).name("当日：nebula查询证书实体subject是否已经insert").setParallelism(PA1);

        SingleOutputStreamOperator<Row> NebulaURLStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {
                List<String> URLList = x509Cert.getAssociateURL();
                if(URLList.size()>0){
                    for(String URL:URLList){
                        Row row = new Row(2);
                        row.setField(0,"URL");
                        row.setField(1,x509Cert);
                        row.setField(2,URL);
                        collector.collect(row);
                    }
                }
            }
        }).name("当日：URL点的信息提取").setParallelism(PA1)
        .process(new CertNebulaDedupByDay()).name("当日：nebula查询证书关联 URL 是否已经insert").setParallelism(PA1);

        SingleOutputStreamOperator<Row> NebulaORGStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {
                String ORG = x509Cert.getCompany();
                if(ORG != null && !ORG.isEmpty()){
                    Row row = new Row(2);
                    row.setField(0,"ORG");
                    row.setField(1,x509Cert);
                    collector.collect(row);
                }
            }
        }).name("当日：ORG点的信息提取").setParallelism(PA1)
        .process(new CertNebulaDedupByDay()).name("当日：nebula查询证书实体 ORG 是否已经insert").setParallelism(PA1);

        /**
         * 总体图空间的写入
         * */
        // 不重实体进行insert点插入
        CertNebulaSinkFunctionByDay.updateVertexData(NebulaDedupStream.getSideOutput(CertNebulaDedupOutPutTagByDay.Dedup_Nebula_Cert_ByDay));
        CertNebulaSinkFunctionByDay.insertVertexData(NebulaDedupStream.getSideOutput(CertNebulaDedupOutPutTagByDay.Not_Dedup_Nebula_Cert_ByDay));
        CertNebulaSinkFunctionByDay.insertIssuerVertexData(NebulaIssuerStream.getSideOutput(CertNebulaDedupOutPutTagByDay.Not_Dedup_Nebula_Issuer_ByDay));
        CertNebulaSinkFunctionByDay.insertSubjectVertexData(NebulaSubjectStream.getSideOutput(CertNebulaDedupOutPutTagByDay.Not_Dedup_Nebula_Subject_ByDay));
        CertNebulaSinkFunctionByDay.insertURLVertexData(NebulaURLStream.getSideOutput(CertNebulaDedupOutPutTagByDay.Not_Dedup_Nebula_URL_ByDay));
        CertNebulaSinkFunctionByDay.insertORGVertexData(NebulaORGStream.getSideOutput(CertNebulaDedupOutPutTagByDay.Not_Dedup_Nebula_ORG_ByDay));

        // 证书直接写入，证书部分提取的关联关联关系
        CertNebulaSinkFunctionByDay.insertSubjectRelatedCert(writeCertModelStream);
        CertNebulaSinkFunctionByDay.insertIssuerRelatedCert(writeCertModelStream);
        CertNebulaSinkFunctionByDay.insertDomainBelongToOrg(writeCertModelStream);
        CertNebulaSinkFunctionByDay.insertIpBelongToOrg(writeCertModelStream);
        CertNebulaSinkFunctionByDay.insertCertBelongToOrg(writeCertModelStream);
        CertNebulaSinkFunctionByDay.insertDomainUrlRelated(writeCertModelStream);
        CertNebulaSinkFunctionByDay.insertIpUrlRelated(writeCertModelStream);
        CertNebulaSinkFunctionByDay.insertCertUrlRelated(writeCertModelStream);
    }
}
