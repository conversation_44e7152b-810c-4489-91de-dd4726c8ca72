package com.geeksec.analysisFunction.hbaseKnowledgeCollision;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.dbConnect.hbase.HbaseUtils;
import com.geeksec.flinkTool.sideOutputTag.AlarmOutPutTag;
import java.util.*;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
* <AUTHOR>
* @Date  2024/10/10
*/

public class KnowledgeCollisionMapFunction extends ProcessFunction<X509Cert, X509Cert> {

    private final static Logger logger = LoggerFactory.getLogger(KnowledgeCollisionMapFunction.class);
    private static transient GenericObjectPool<Connection> HbasePool = null;

    public static final String HBASE_CERT_KNOWLEDGE_TABLE_NAME = "cert_knowledgebase";
    public static final String HBASE_DOMAIN_KNOWLEDGE_NAME = "domain_knowledgebase";
    public static final String HBASE_SERVICE_COMPANY_KNOWLEDGE_TABLE_NAME = "service_company_knowledgebase";

    public static final String APT_COLUMN = "apt_info";
    public static final String THREAT_COLUMN = "threat_info";
    public static final String APP_COLUMN = "app_property_info";
    public static final String SCAN_COLUMN = "scan_detection_info";
    public static final String TRUST_COLUMN = "trust";
    public static final String DOMAIN_COLUMN = "domain_info";
    public static final String COMPANY_COLUMN = "service_company_info";
    public static final String CRL_COLUMN = "crl";

    public static final String APT_KEY = "apt_name";
    public static final String THREAT_KEY = "threat_type";
    public static final String APP_KEY = "app_property";
    public static final String SCAN_KEY = "scan_detection";
    public static final String TRUST_ISSUER_KEY = "issuer";
    public static final String TRUST_BUILTIN_KEY = "built_in";
    public static final String ALEXA_KEY = "alexa_rank";
    public static final String WHOIS_KEY = "whois";
    public static final String SUFFIX_KEY = "suffix";
    public static final String COMPANY_KEY = "company_name";
    public static final String CRL_KEY = "issuer";

    public static final String APT_RESULT_KEY = "APT证书知识库碰撞结果";
    public static final String THREAT_RESULT_KEY = "威胁证书库知识库碰撞结果";
    public static final String APP_RESULT_KEY = "APP证书及资产库碰撞结果";
    public static final String SCAN_RESULT_KEY = "扫描探测库碰撞结果";
    public static final String TRUST_ISSUER_RESULT_KEY = "可信白名单证书知识库碰撞结果";
    public static final String TRUST_BUILTIN_RESULT_KEY = "可信内置证书知识库碰撞结果";
    public static final String ALEXA_RESULT_KEY = "域名信息库知识库Alexa关联信息碰撞结果";
    public static final String WHOIS_RESULT_KEY = "域名信息库知识库Whois关联信息碰撞结果";
    public static final String SUFFIX_RESULT_KEY = "域名信息库知识库suffix关联信息碰撞结果";
    public static final String COMPANY_RESULT_KEY = "证书服务商信息知识库碰撞结果";
    public static final String CRL_RESULT_KEY = "吊销证书知识库碰撞结果";

    @Override
    public void close() throws Exception {
        super.close();
        if (HbasePool!=null){
            HbasePool.close();
        }
    }


    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // Hbase 初始化
        HbasePool = HbaseUtils.initHbasePool();
        logger.info("生成 HbasePool 成功! {}", HbasePool.getNumIdle(), HbasePool.hashCode());
    }

    @Override
    public void processElement(X509Cert x509Cert, ProcessFunction<X509Cert, X509Cert>.Context context, Collector<X509Cert> collector) throws Exception {


        Connection connection = null;
        String sha1 = x509Cert.getASN1SHA1();
        List<String> domainList = x509Cert.getAssociateDomain();
        String company = x509Cert.getCompany();

        List<Map<String,List<String>>> KnowledgeCollisionResult = new ArrayList<>();

        try{
            connection = HbaseUtils.getHbaseClient(HbasePool);
            Table certKnowledgeTable = connection.getTable(TableName.valueOf(HBASE_CERT_KNOWLEDGE_TABLE_NAME));
            Table domainKnowledgeTable = connection.getTable(TableName.valueOf(HBASE_DOMAIN_KNOWLEDGE_NAME));
            Table serviceCompanyKnowledgetable = connection.getTable(TableName.valueOf(HBASE_SERVICE_COMPANY_KNOWLEDGE_TABLE_NAME));

            // 证书 SHA1 关联 APT 碰撞
            Get APTGet = new Get(Bytes.toBytes(sha1));
            APTGet.addFamily(Bytes.toBytes(APT_COLUMN));
            APTGet.addColumn(Bytes.toBytes(APT_COLUMN), Bytes.toBytes(APT_KEY));
            Result APTResult = certKnowledgeTable.get(APTGet);
            if (!APTResult.isEmpty()) {
                if(APTResult.getRow().length>0){
                    byte[] value = APTResult.getValue(Bytes.toBytes(APT_COLUMN), Bytes.toBytes(APT_KEY));
                    if (value != null) {
                        String APTValueStr = Bytes.toString(value);
                        Map<String,List<String>> APTResultMap = new HashMap<>();
                        APTResultMap.put(APT_RESULT_KEY, Arrays.asList(APTValueStr));
                        KnowledgeCollisionResult.add(APTResultMap);
                        logger.info("成功碰撞APT证书知识库数据");
                    } else {
                        System.out.println("APT列没有值");
                    }
                }
            }

            // 证书 SHA1 关联 威胁 信息碰撞
            Get threatGet = new Get(Bytes.toBytes(sha1));
            threatGet.addFamily(Bytes.toBytes(THREAT_COLUMN));
            threatGet.addColumn(Bytes.toBytes(THREAT_COLUMN), Bytes.toBytes(THREAT_KEY));
            Result threatResult = certKnowledgeTable.get(threatGet);
            if (!threatResult.isEmpty()) {
                if(threatResult.getRow().length>0){
                    byte[] value = threatResult.getValue(Bytes.toBytes(THREAT_COLUMN), Bytes.toBytes(THREAT_KEY));
                    if (value != null) {
                        String threatValueStr = Bytes.toString(value);
                        Map<String,List<String>> threatResultMap = new HashMap<>();
                        threatResultMap.put(THREAT_RESULT_KEY, Arrays.asList(threatValueStr));
                        KnowledgeCollisionResult.add(threatResultMap);
                        logger.info("成功碰撞威胁证书知识库数据");
                    } else {
                        System.out.println("threat列没有值");
                    }
                }
            }

            // 证书 SHA1 关联 app 信息碰撞
            Get appGet = new Get(Bytes.toBytes(sha1));
            appGet.addFamily(Bytes.toBytes(APP_COLUMN));
            appGet.addColumn(Bytes.toBytes(APP_COLUMN), Bytes.toBytes(APP_KEY));
            Result appResult = certKnowledgeTable.get(appGet);
            if (!appResult.isEmpty()) {
                if(appResult.getRow().length>0){
                    byte[] value = appResult.getValue(Bytes.toBytes(APP_COLUMN), Bytes.toBytes(APP_KEY));
                    if (value != null) {
                        String appValueStr = Bytes.toString(value);
                        Map<String,List<String>> appResultMap = new HashMap<>();
                        appResultMap.put(APP_RESULT_KEY, Arrays.asList(appValueStr));
                        KnowledgeCollisionResult.add(appResultMap);
                        logger.info("成功碰撞APP证书知识库数据");
                    } else {
                        System.out.println("app列没有值");
                    }
                }
            }

            // 证书 SHA1 关联 扫描探测 信息碰撞
            Get scanGet = new Get(Bytes.toBytes(sha1));
            scanGet.addFamily(Bytes.toBytes(SCAN_COLUMN));
            scanGet.addColumn(Bytes.toBytes(SCAN_COLUMN), Bytes.toBytes(SCAN_KEY));
            Result scanResult = certKnowledgeTable.get(scanGet);
            if (!scanResult.isEmpty()) {
                if(scanResult.getRow().length>0){
                    byte[] value = scanResult.getValue(Bytes.toBytes(SCAN_COLUMN), Bytes.toBytes(SCAN_KEY));
                    if (value != null) {
                        String scanValueStr = Bytes.toString(value);
                        Map<String,List<String>> scanResultMap = new HashMap<>();
                        scanResultMap.put(SCAN_RESULT_KEY, Arrays.asList(scanValueStr));
                        KnowledgeCollisionResult.add(scanResultMap);
                        logger.info("成功碰撞扫描探测证书知识库数据");
                    } else {
                        System.out.println("scan列没有值");
                    }
                }
            }

            // 证书 SHA1 关联 可信白名单证书 信息碰撞
            Get issuerGet = new Get(Bytes.toBytes(sha1));
            issuerGet.addFamily(Bytes.toBytes(TRUST_COLUMN));
            issuerGet.addColumn(Bytes.toBytes(TRUST_COLUMN), Bytes.toBytes(TRUST_ISSUER_KEY));
            Result issuerResult = certKnowledgeTable.get(issuerGet);
            if (!issuerResult.isEmpty()) {
                if(issuerResult.getRow().length>0){
                    byte[] value = issuerResult.getValue(Bytes.toBytes(TRUST_COLUMN), Bytes.toBytes(TRUST_ISSUER_KEY));
                    if (value != null) {
                        String issuerValueStr = Bytes.toString(value);
                        Map<String,List<String>> issuerResultMap = new HashMap<>();
                        issuerResultMap.put(TRUST_ISSUER_RESULT_KEY, Arrays.asList(issuerValueStr));
                        KnowledgeCollisionResult.add(issuerResultMap);
                        logger.info("成功碰撞可信白名单证书知识库数据");
                    } else {
                        System.out.println("issuer列没有值");
                    }
                }
            }

            // 证书 SHA1 关联 可信内置证书 信息碰撞
            Get builtinGet = new Get(Bytes.toBytes(sha1));
            builtinGet.addFamily(Bytes.toBytes(TRUST_COLUMN));
            builtinGet.addColumn(Bytes.toBytes(TRUST_COLUMN), Bytes.toBytes(TRUST_BUILTIN_KEY));
            Result builtinResult = certKnowledgeTable.get(builtinGet);
            if (!builtinResult.isEmpty()) {
                if(builtinResult.getRow().length>0){
                    byte[] value = builtinResult.getValue(Bytes.toBytes(TRUST_COLUMN), Bytes.toBytes(TRUST_BUILTIN_KEY));
                    if (value != null) {
                        String builtinValueStr = Bytes.toString(value);
                        Map<String,List<String>> builtinResultMap = new HashMap<>();
                        builtinResultMap.put(TRUST_BUILTIN_RESULT_KEY, Arrays.asList(builtinValueStr));
                        KnowledgeCollisionResult.add(builtinResultMap);
                        logger.info("成功碰撞可信内置证书知识库数据");
                    } else {
                        System.out.println("builtin列没有值");
                    }
                }
            }

            for (String domain: domainList){
                // 证书关联 域名alexa 碰撞
                Get alexaGet = new Get(Bytes.toBytes(domain));
                alexaGet.addFamily(Bytes.toBytes(DOMAIN_COLUMN));
                alexaGet.addColumn(Bytes.toBytes(DOMAIN_COLUMN), Bytes.toBytes(ALEXA_KEY));
                Result alexaResult = domainKnowledgeTable.get(alexaGet);
                if (!alexaResult.isEmpty()) {
                    if(alexaResult.getRow().length>0){
                        byte[] value = alexaResult.getValue(Bytes.toBytes(DOMAIN_COLUMN), Bytes.toBytes(ALEXA_KEY));
                        if (value != null) {
                            String alexaValueStr = Bytes.toString(value);
                            Map<String,List<String>> alexaResultMap = new HashMap<>();
                            alexaResultMap.put(ALEXA_RESULT_KEY, Arrays.asList(alexaValueStr));
                            KnowledgeCollisionResult.add(alexaResultMap);
                            logger.info("成功碰撞Alexa域名信息知识库数据");
                        } else {
                            System.out.println("alexa列没有值");
                        }
                    }
                }

                // 证书关联 域名whois 碰撞
                Get whoisGet = new Get(Bytes.toBytes(domain));
                whoisGet.addFamily(Bytes.toBytes(DOMAIN_COLUMN));
                whoisGet.addColumn(Bytes.toBytes(DOMAIN_COLUMN), Bytes.toBytes(WHOIS_KEY));
                Result whoisResult = domainKnowledgeTable.get(whoisGet);
                if (!whoisResult.isEmpty()) {
                    if(whoisResult.getRow().length>0){
                        byte[] value = whoisResult.getValue(Bytes.toBytes(DOMAIN_COLUMN), Bytes.toBytes(WHOIS_KEY));
                        if (value != null) {
                            String whoisValueStr = Bytes.toString(value);
                            Map<String,List<String>> whoisResultMap = new HashMap<>();
                            whoisResultMap.put(WHOIS_RESULT_KEY, Arrays.asList(whoisValueStr));
                            KnowledgeCollisionResult.add(whoisResultMap);
                            logger.info("成功碰撞Whois域名知识库数据");
                        } else {
                            System.out.println("whois列没有值");
                        }
                    }
                }

                // 证书关联 域名 suffix 碰撞
                Get suffixGet = new Get(Bytes.toBytes(domain));
                suffixGet.addFamily(Bytes.toBytes(DOMAIN_COLUMN));
                suffixGet.addColumn(Bytes.toBytes(DOMAIN_COLUMN), Bytes.toBytes(SUFFIX_KEY));
                Result suffixResult = domainKnowledgeTable.get(suffixGet);
                if (!suffixResult.isEmpty()) {
                    if(suffixResult.getRow().length>0){
                        byte[] value = suffixResult.getValue(Bytes.toBytes(DOMAIN_COLUMN), Bytes.toBytes(SUFFIX_KEY));
                        if (value != null) {
                            String suffixValueStr = Bytes.toString(value);
                            Map<String,List<String>> suffixResultMap = new HashMap<>();
                            suffixResultMap.put(SUFFIX_RESULT_KEY, Arrays.asList(suffixValueStr));
                            KnowledgeCollisionResult.add(suffixResultMap);
                            logger.info("成功碰撞suffix域名知识库数据");
                        } else {
                            System.out.println("suffix列没有值");
                        }
                    }
                }
            }

            // 证书关联 company 碰撞
            Get companyGet = new Get(Bytes.toBytes(company));
            companyGet.addFamily(Bytes.toBytes(COMPANY_COLUMN));
            companyGet.addColumn(Bytes.toBytes(COMPANY_COLUMN), Bytes.toBytes(COMPANY_KEY));
            Result companyResult = serviceCompanyKnowledgetable.get(companyGet);
            if (!companyResult.isEmpty()) {
                if(companyResult.getRow().length>0){
                    byte[] value = companyResult.getValue(Bytes.toBytes(COMPANY_COLUMN), Bytes.toBytes(COMPANY_KEY));
                    if (value != null) {
                        String companyValueStr = Bytes.toString(value);
                        Map<String,List<String>> companyResultMap = new HashMap<>();
                        companyResultMap.put(COMPANY_RESULT_KEY, Arrays.asList(companyValueStr));
                        KnowledgeCollisionResult.add(companyResultMap);
                        logger.info("成功碰撞证书服务商知识库数据");
                    } else {
                        System.out.println("company列没有值");
                    }
                }
            }

            // 更新 CRL 吊销证书知识库碰撞结果
            Get crlGet = new Get(Bytes.toBytes(sha1));
            crlGet.addFamily(Bytes.toBytes(CRL_COLUMN));
            crlGet.addColumn(Bytes.toBytes(CRL_COLUMN), Bytes.toBytes(CRL_KEY));
            Result crlResult = certKnowledgeTable.get(crlGet);
            if (!crlResult.isEmpty()) {
                if(crlResult.getRow().length>0){
                    byte[] value = crlResult.getValue(Bytes.toBytes(CRL_COLUMN), Bytes.toBytes(CRL_KEY));
                    if (value != null) {
                        String crlValueStr = Bytes.toString(value);
                        Map<String,List<String>> crlResultMap = new HashMap<>();
                        crlResultMap.put(CRL_RESULT_KEY, Arrays.asList(crlValueStr));
                        KnowledgeCollisionResult.add(crlResultMap);
                        logger.info("成功碰撞吊销证书知识库数据");
                        // 添加吊销证书标签
                        List<String> tagList = x509Cert.getTagList();
                        tagList.add("Withdraw Cert");
                        x509Cert.setTagList(tagList);

                    } else {
                        System.out.println("crl列没有值");
                    }
                }
            }

        }catch (Exception e){
            logger.error("hbase查询失败，报错：{}",e.toString());
        }finally{
            if(connection!=null){
                HbaseUtils.returnHbaseClient(connection,HbasePool);
            }
        }

        // 更新证书的KnowledgeCollisionResult
        x509Cert.setKnowledgeCollisionResult(KnowledgeCollisionResult);

        // 如果有APT和威胁证书的碰撞结果，则产生告警
        for (Map<String,List<String>> result:KnowledgeCollisionResult){
            if (result.containsKey(APT_RESULT_KEY)){
                context.output(AlarmOutPutTag.Alarm_APT_CERT_KNOWLEDGE_ALARM,x509Cert);
            }
            if(result.containsKey(THREAT_RESULT_KEY)){
                context.output(AlarmOutPutTag.Alarm_THREAT_CERT_KNOWLEDGE_ALARM,x509Cert);
            }
        }

        collector.collect(x509Cert);
    }
}
