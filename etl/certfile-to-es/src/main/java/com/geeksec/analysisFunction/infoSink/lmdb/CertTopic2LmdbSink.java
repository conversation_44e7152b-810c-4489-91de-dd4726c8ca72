package com.geeksec.analysisFunction.infoSink.lmdb;

import static org.lmdbjava.ByteBufferProxy.PROXY_OPTIMAL;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.utils.FileUtil;
import java.io.File;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.lmdbjava.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 证书元数据(topic: certfile, certfile_system)入lmdb
 * <AUTHOR>
 */
@Slf4j
public class CertTopic2LmdbSink extends RichSinkFunction<X509Cert> {
    private static final Logger logger = LoggerFactory.getLogger(CertTopic2LmdbSink.class);

    private static final long TIME_INTERVAL = 10_000L;
    private static final int BATCH_SIZE = 100;

    private final Properties properties = FileUtil.getProperties("/config.properties");
    private final Long lmdbMaxSize = Long.valueOf(properties.getOrDefault("lmdb.maxsize", "1099511627776").toString());
    private final String lmdbParentDir = properties.getOrDefault("lmdb.path.user", "/data/lmdb/cert/cert_user").toString();

    private transient Env<ByteBuffer> env;
    private transient Dbi<ByteBuffer> dbi;

    private transient List<byte[][]> cacheDoc;

    private transient ScheduledExecutorService executorService;
    private transient ScheduledFuture<?> scheduledFuture;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 初始化 LMDB 环境
        File lmdbDir = new File(lmdbParentDir);
        if (!lmdbDir.exists()) {
            logger.info("LMDB Directory {} Created: {}", lmdbDir, lmdbDir.mkdirs());
        }

        // 创建 LMDB 环境并设置最大容量
        env = Env.create(PROXY_OPTIMAL)
                .setMapSize(lmdbMaxSize)
                .open(lmdbDir, EnvFlags.MDB_NOTLS);

        // 打开数据库
        dbi = env.openDbi((byte[]) null, DbiFlags.MDB_CREATE);
        logger.info("LMDB Environment and Database initialized");

        // 初始化缓存
        cacheDoc = new ArrayList<>();

        // 初始化定时任务
        executorService = Executors.newSingleThreadScheduledExecutor();
        scheduledFuture = executorService.scheduleAtFixedRate(
                new Runnable() {
                    @Override
                    public void run() {
                        // 如果缓存中有数据，则写入 LMDB
                        if (!cacheDoc.isEmpty()) {
                            try {
                                writeToLmdb(cacheDoc); // 将缓存中的数据写入 LMDB
                                cacheDoc.clear(); // 清空缓存
                                logger.info("Scheduled LMDB write completed, cache cleared");
                            } catch (Exception e) {
                                logger.error("Failed to write LMDB data in scheduled task", e);
                            }
                        }
                    }
                },
                // 初始延迟
                TIME_INTERVAL,
                // 执行间隔
                TIME_INTERVAL,
                TimeUnit.MILLISECONDS
        );
        logger.info("Scheduled task initialized with interval: {} ms", TIME_INTERVAL);
    }

    @Override
    public void invoke(X509Cert cert, Context context) throws Exception {
        // 将数据添加到缓存
        cacheDoc.add(new byte[][]{
                cert.getASN1SHA1().getBytes(StandardCharsets.UTF_8),
                cert.getCert()
        });

        // 如果缓存大小达到 BATCH_SIZE，立即写入 LMDB
        if (cacheDoc.size() >= BATCH_SIZE) {
            logger.info("Cache size reached batch size, writing to LMDB");
            writeToLmdb(cacheDoc);
            cacheDoc.clear();
        }
    }

    /**
     * 将数据写入 LMDB
     *
     * @param dataList 数据列表，每个元素是一个键值对 byte[][]
     */
    private void writeToLmdb(List<byte[][]> dataList) {
        try (Txn<ByteBuffer> txn = env.txnWrite()) {
            // 遍历数据列表，将每个键值对写入 LMDB
            for (byte[][] data : dataList) {
                ByteBuffer key = ByteBuffer.allocateDirect(data[0].length);
                ByteBuffer value = ByteBuffer.allocateDirect(data[1].length);

                key.put(data[0]).flip();
                value.put(data[1]).flip();

                dbi.put(txn, key, value);

                key.clear();
                value.clear();
            }
            // 提交事务
            txn.commit();
        } catch (Exception e) {
            logger.error("Failed to write LMDB data", e);
            throw new RuntimeException("Failed to write LMDB data", e);
        }
    }

    @Override
    public void close() throws Exception {
        // 关闭定时任务
        if (scheduledFuture != null) {
            scheduledFuture.cancel(true);
        }
        if (executorService != null) {
            executorService.shutdown();
        }
        // 关闭 LMDB 资源
        if (dbi != null) {
            dbi.close();
        }
        if (env != null) {
            env.close();
        }
        logger.info("LMDB Environment and Database closed");
        super.close();
    }
}