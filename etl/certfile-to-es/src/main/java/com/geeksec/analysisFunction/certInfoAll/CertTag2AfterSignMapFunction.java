package com.geeksec.analysisFunction.certInfoAll;

import com.geeksec.analysisFunction.analysisEntity.ThreatInfo;
import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.utils.CertFormatUtil;
import com.geeksec.utils.FileUtil;
import com.geeksec.utils.MysqlUtils;
import com.geeksec.utils.TorAddressValidator;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
* <AUTHOR>
* @Date  2024/8/14
*/

public class CertTag2AfterSignMapFunction extends RichMapFunction<X509Cert, X509Cert> {
    protected static final Logger LOG = LoggerFactory.getLogger(CertTag2AfterSignMapFunction.class);

    public static List<ThreatInfo> All_Threat_Info_List;
    public static List<String> All_Threat_IP_List;
    public static List<String> All_Threat_Domain_List;

    /** 目前威胁情报库没有C2的，先用Bot的*/
    public static List<String> C2_Threat_IP_List;
    public static List<String> C2_Threat_Domain_List;

    /** 目前威胁情报库没有远程控制的，先用RAT的*/
    public static List<String> RAT_Threat_IP_List;
    public static List<String> RAT_Threat_Domain_List;

    /** 威胁情报tagID */
    public static String IOC_IP_TAG = "865";
    public static String IOC_DOMAIN_TAG = "866";

    /** 异常标签tagID */
    public static String MINE_TAG = "858";
    public static String C2_TAG = "257";
    public static String VPN_TAG = "252";

    /** 写会第三方知识库tagID */
    public static String APT28_TAG = "847";
    public static String APT29_TAG = "293";
    public static String APTPatchWork_TAG = "281";
    public static String TOR_V3_TAG = "284";
    public static String TOR_V2_TAG = "859";

    public static List<String> MINE_DOMAIN_LIST = new ArrayList<>();

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        All_Threat_Info_List = MysqlUtils.getAllThreatInfoList();
        All_Threat_IP_List = MysqlUtils.getSpecificTargetTypeThreatInfoList(All_Threat_Info_List,"ip");
        All_Threat_Domain_List = MysqlUtils.getSpecificTargetTypeThreatInfoList(All_Threat_Info_List,"domain");

        C2_Threat_IP_List = MysqlUtils.getSpecificThreatInfoList(All_Threat_Info_List,"Bot","ip");
        C2_Threat_Domain_List = MysqlUtils.getSpecificThreatInfoList(All_Threat_Info_List,"Bot","domain");

        RAT_Threat_IP_List = MysqlUtils.getSpecificThreatInfoList(All_Threat_Info_List,"RAT","ip");
        RAT_Threat_Domain_List = MysqlUtils.getSpecificThreatInfoList(All_Threat_Info_List,"RAT","domain");

        InputStream mine_stream = this.getClass().getClassLoader().getResourceAsStream("mine_domain.csv");
        BufferedReader mine_buffer = new BufferedReader(new InputStreamReader(mine_stream));
        //加载配置文件
        try {
            MINE_DOMAIN_LIST = FileUtil.loadMineDomain(mine_buffer);
        } catch (Exception e) {
            throw new IOException("file load fail ==>", e);
        }
    }

    @Override
    public void close() throws Exception {
        super.close();
    }

    @Override
    public X509Cert map(X509Cert x509Cert) throws Exception {

        getAPT28Cert(x509Cert);
        getAPT29Cert(x509Cert);
        getAPTPatchWorkCert(x509Cert);
        getTorCert(x509Cert);
        getDistributedServicesCert(x509Cert);
        getIOCIPCert(x509Cert);
        getMaliciousDomainCert(x509Cert);
        getC2Cert(x509Cert);
        getRemoteControlCert(x509Cert);
        getMineCert(x509Cert);


        return x509Cert;
    }

    private void getMineCert(X509Cert x509Cert) {
        List<String> tagList = x509Cert.getTagList();
        List<String> domains = x509Cert.getAssociateDomain();
        for(String domain:domains){
            if(MINE_DOMAIN_LIST.contains(domain)){
                tagList.add("Mine Cert");
                x509Cert.setIocDomain(domain);
                break;
            }
        }
        x509Cert.setTagList(tagList);
    }

    /**
     * 远控 证书检测，Remote Control Cert
     * */
    private void getRemoteControlCert(X509Cert x509Cert) {
        List<String> tagList = x509Cert.getTagList();
        List<String> domains = x509Cert.getAssociateDomain();
        List<String> IPs = x509Cert.getAssociateIP();
        for(String domain:domains){
            if(RAT_Threat_Domain_List.contains(domain)){
                tagList.add("Remote Control Cert");
                x509Cert.setTagList(tagList);
                return;
            }
        }
        for(String ip:IPs){
            if(RAT_Threat_IP_List.contains(ip)){
                tagList.add("Remote Control Cert");
                x509Cert.setTagList(tagList);
                return;
            }
        }
    }

    /**
     * C2 证书检测，C&C Cert
     * */
    private void getC2Cert(X509Cert x509Cert) {
        List<String> tagList = x509Cert.getTagList();
        List<String> domains = x509Cert.getAssociateDomain();
        List<String> IPs = x509Cert.getAssociateIP();
        for(String domain:domains){
            if(C2_Threat_Domain_List.contains(domain)){
                tagList.add("C&C Cert");
                x509Cert.setTagList(tagList);
                return;
            }
        }
        for(String ip:IPs){
            if(C2_Threat_IP_List.contains(ip)){
                tagList.add("C&C Cert");
                x509Cert.setTagList(tagList);
                return;
            }
        }
    }

    /**
     * 失陷IP 证书检测，IOC IP Cert
     * */
    private void getIOCIPCert(X509Cert x509Cert) {
        List<String> tagList = x509Cert.getTagList();
        List<String> IPs = x509Cert.getAssociateIP();
        for(String ip:IPs){
            if(All_Threat_IP_List.contains(ip)){
                tagList.add("IOC IP Cert");
                x509Cert.setIocIP(ip);
                break;
            }
        }
        x509Cert.setTagList(tagList);
    }


    /**
     * 恶意域名 证书检测，Malicious Domain Cert
     * */
    private void getMaliciousDomainCert(X509Cert x509Cert) {
        List<String> tagList = x509Cert.getTagList();
        List<String> domains = x509Cert.getAssociateDomain();
        for(String domain:domains){
            if(All_Threat_Domain_List.contains(domain)){
                tagList.add("Malicious Domain Cert");
                x509Cert.setIocDomain(domain);
                break;
            }
        }
        x509Cert.setTagList(tagList);
    }

    /**
     * 分布式部署服务 证书检测，Distributed Services Cert
     * */
    private static void getDistributedServicesCert(X509Cert x509Cert) {
        List<String> tagList = x509Cert.getTagList();
        List<String> domains = x509Cert.getAssociateDomain();
        if(hasCommonSubdomain(domains)){
            tagList.add("Distributed Services Cert");
        }
        x509Cert.setTagList(tagList);
    }

    /**
     * APT28 证书检测，APT28
     * */
    private static void getAPT28Cert(X509Cert x509Cert){
        List<String> tagList = x509Cert.getTagList();
        if (tagList.contains("Free Cert") && tagList.contains("Recent Registered Cert")
                && tagList.contains("Unhot TLD Cert")){
            tagList.add("APT28");
        }
        x509Cert.setTagList(tagList);
    }

    /**
     * APT29 证书检测，APT29
     * */
    private static void getAPT29Cert(X509Cert x509Cert){
        List<String> tagList = x509Cert.getTagList();
        if (tagList.contains("Lost CertList") && tagList.contains("Special Key ID")
                && tagList.contains("IP in SAN") && tagList.contains("Wild card in Issuer")
                && tagList.contains("Long Duration Cert")){
            tagList.add("APT29");
        }
        x509Cert.setTagList(tagList);
    }

    /**
     * APTPatchWork 证书检测，Patchwork Cert
     * */
    private static void getAPTPatchWorkCert(X509Cert x509Cert){
        HashMap subject = CertFormatUtil.objectToMap(x509Cert.getSubject());
        String subjectCN = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "CN", ""));
        List<String> tagList = x509Cert.getTagList();
        if (tagList.contains("Self Signed Cert") && subjectCN.equals("testexp")){
            tagList.add("Patchwork Cert");
        }
        x509Cert.setTagList(tagList);

    }

    /**
     * Tor 证书检测
     * */
    private static void getTorCert(X509Cert x509Cert) {
        HashMap subject = CertFormatUtil.objectToMap(x509Cert.getSubject());
        String subjectCN = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "CN", ""));
        String checkResult = TorAddressValidator.validateTorAddress(subjectCN);

        if(TorAddressValidator.NOT_TOR.equals(checkResult)){
            return;
        }
        if(TorAddressValidator.TOR_V3.equals(checkResult)){
            List<String> tagList = x509Cert.getTagList();
            tagList.add("Tor V3 Cert");
            tagList.add("Network Penetration Cert");
            x509Cert.setTagList(tagList);
        }
        if(TorAddressValidator.TOR_V2.equals(checkResult)){
            List<String> tagList = x509Cert.getTagList();
            tagList.add("Tor V2 Cert");
            tagList.add("Network Penetration Cert");
            x509Cert.setTagList(tagList);
        }
    }


    /**
     * 检测证书中所有关联域名的构成方式
     * 如果是分布式服务部署整肃
     * 则除了开头的子域名，后面都是一致的
     * 这样的数量超过3，则认为是分布式服务部署证书
     * */
    public static boolean hasCommonSubdomain(List<String> domains) {
        Map<String, Integer> subdomainCounts = new HashMap<>();

        for (String domain : domains) {
            String[] parts = domain.split("\\.");
            int length = parts.length;

            // 构建1到x-1的部分，如果长度小于2，则使用整个域名
            StringBuilder subdomain = new StringBuilder();
            for (int i = 1; i < length - 1; i++) {
                subdomain.append(parts[i]);
                if (i < length - 2) {
                    subdomain.append(".");
                }
            }

            // 统计每个子域名的出现次数
            subdomainCounts.put(subdomain.toString(), subdomainCounts.getOrDefault(subdomain.toString(), 0) + 1);
        }

        // 检查是否有任何子域名的出现次数超过3
        for (Integer count : subdomainCounts.values()) {
            if (count > 3) {
                return true;
            }
        }
        return false;
    }
}
