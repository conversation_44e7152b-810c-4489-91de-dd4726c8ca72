package com.geeksec.analysisFunction.infoSink.hbase;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.dbConnect.hbase.HbaseUtils;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
* <AUTHOR>
* @Date  2024/9/27
*/

public class ScanCertWriteBack extends RichSinkFunction<JSONObject> {


    private static final Logger logger = LoggerFactory.getLogger(ScanCertWriteBack.class);

    private static transient GenericObjectPool<Connection> HbasePool = null;

    private static final String HBASE_CERT_TABLE_NAME = "CERT";

    @Override
    public void close() throws Exception {
        super.close();
        if (HbasePool!=null){
            HbasePool.close();
        }
    }


    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // Hbase 初始化
        HbasePool = HbaseUtils.initHbasePool();
        logger.info("生成 HbasePool 成功! {}", HbasePool.getNumIdle(), HbasePool.hashCode());
    }


    @Override
    public void invoke(JSONObject value, Context context) throws Exception {
        String rowKey = (String) value.getOrDefault("ASN1SHA1","");
        List<String> labelsValue = (List<String>) value.getOrDefault("Labels",new ArrayList<>());

        Connection connection = null;
        try{
            connection = HbaseUtils.getHbaseClient(HbasePool);
            Table table = connection.getTable(TableName.valueOf(HBASE_CERT_TABLE_NAME));
            // 检索指定列族和值的行
            Get get = new Get(Bytes.toBytes(rowKey));
            Result result = table.get(get);
            if (!result.isEmpty()) {
                // 更新列族details中的列Labels的值
                Put put = new Put(Bytes.toBytes(rowKey));
                put.addColumn(Bytes.toBytes("details"), Bytes.toBytes("Labels"), Bytes.toBytes(labelsValue.toString()));
                put.addColumn(Bytes.toBytes("details"), Bytes.toBytes("handle_status"), Bytes.toBytes("1"));

                table.put(put);
                logger.error("成功更新扫描证书SHA1：{}",rowKey);
            } else {
                logger.warn("更新扫描证书SHA1：{}，没有找到指定的行",rowKey);
            }


        }catch (Exception e){
            logger.error("hbase更新失败，{}",e.toString());
        }finally{
            if(connection!=null){
                HbaseUtils.returnHbaseClient(connection,HbasePool);
            }
        }

    }
}

