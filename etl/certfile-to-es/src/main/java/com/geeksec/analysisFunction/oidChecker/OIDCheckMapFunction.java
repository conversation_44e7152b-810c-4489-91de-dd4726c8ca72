package com.geeksec.analysisFunction.oidChecker;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.UncommonOID;
import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.dbConnect.lmdb.LmdbClient;
import com.geeksec.utils.FileUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/12/18
 */

public class OIDCheckMapFunction extends RichMapFunction<X509Cert, X509Cert> {

    private static final Logger LOG = LoggerFactory.getLogger(OIDCheckMapFunction.class);

    private static Properties properties = FileUtil.getProperties("/config.properties");
    private static String lmdbPath = properties.getOrDefault("lmdb.oid.path", "/data/lmdb/oid/").toString();

    private static String lmdbMaxSize = properties.getOrDefault("lmdb.oid.maxsize", "1099511627776").toString();;

    private LmdbClient lmdbClient = null;


    @Override
    public void open(Configuration parameters) throws Exception {
        lmdbClient = new LmdbClient(lmdbPath,lmdbMaxSize);
        LOG.info("lmdb query address is: [ {} ]", lmdbPath);
    }

    @Override
    public void close() throws Exception {
        super.close();
    }

    @Override
    public X509Cert map(X509Cert x509Cert) throws Exception {
        // 不常见oid的识别
        List<UncommonOID> uncommonOids = x509Cert.getUncommonOIDs();
        List<UncommonOID> resultUncommonOids = new ArrayList<>();
        for (UncommonOID uncommonOid:uncommonOids){
            String oid = uncommonOid.getOID();
            try{
                String descriptionJsonString = lmdbClient.getJsonByOid(oid);
                JSONObject jsonObject = JSONObject.parseObject(descriptionJsonString);
                String description = jsonObject.getOrDefault("description","").toString();
                uncommonOid.setDescription(description);
            }catch (Exception e){
                LOG.error("LMDB查询OID失败，error--->{}",e.toString());
            }

            resultUncommonOids.add(uncommonOid);
        }
        x509Cert.setUncommonOIDs(resultUncommonOids);
        return x509Cert;
    }
}
