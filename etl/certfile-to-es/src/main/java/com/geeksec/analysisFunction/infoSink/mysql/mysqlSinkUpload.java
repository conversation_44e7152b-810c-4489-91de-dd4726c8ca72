package com.geeksec.analysisFunction.infoSink.mysql;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.utils.MysqlUtils;
import java.util.ArrayList;
import java.util.List;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/5/6
 */

public class mysqlSinkUpload extends RichSinkFunction<Row> implements SinkFunction<Row> {
    private static final Logger LOG = LoggerFactory.getLogger(mysqlSinkUpload.class);


    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void invoke(Row x509Cert_row, Context context) throws Exception {

        List<String> task_batch_info = (List<String>) x509Cert_row.getField(0);
        X509Cert x509Cert = (X509Cert) x509Cert_row.getField(1);
        List<String> task_batch_info_not_stop = new ArrayList<>();
        for(String task_batch:task_batch_info){
            boolean stop_import = MysqlUtils.mysql_stop_import(task_batch,x509Cert.getASN1SHA1());
            if (!stop_import){
                task_batch_info_not_stop.add(task_batch);
            }else {
                LOG.info("写入步骤中，是用户导入证书,但是终止导入的批次为——{}——",task_batch);
            }
        }
        LOG.info("是用户导入证书,且未终止导入的批次为——{}——",task_batch_info_not_stop);

        // 操作tb_cert_upload_file表
        int rowsAffected_upload = MysqlUtils.setMysqlUploadCertInfo(x509Cert,task_batch_info_not_stop);
        if (rowsAffected_upload==0){
            LOG.info("证书写入upload_file表错误");
        } else if (rowsAffected_upload==-1) {
            LOG.error("证书在查询redis和mysql时失败");
        } else {
            LOG.info("成功修改upload_file表条数:--{}--",rowsAffected_upload);
        }
    }

    @Override
    public void close() throws Exception {
        super.close();
    }
}
