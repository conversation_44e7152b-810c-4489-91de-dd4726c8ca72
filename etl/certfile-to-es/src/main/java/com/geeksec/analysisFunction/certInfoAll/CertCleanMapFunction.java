package com.geeksec.analysisFunction.certInfoAll;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
* <AUTHOR>
* @Date  2024/10/20
*/

public class CertCleanMapFunction extends RichMapFunction<X509Cert, X509Cert> {

    private static final Logger logger = LoggerFactory.getLogger(CertCleanMapFunction.class);

    @Override
    public void close() throws Exception {
        super.close();
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }


    @Override
    public X509Cert map(X509Cert x509Cert) throws Exception {
        List<String> domainList = x509Cert.getAssociateDomain();
        List<String> formatDomainList = new ArrayList<>();

        for (String domain:domainList){
            String formatDomain = formatDomain(domain);
            formatDomainList.add(formatDomain);
        }
        x509Cert.setAssociateDomain(formatDomainList);
        return x509Cert;
    }

    /**
     * 格式化域名，消除大小写差异，限制长度，去除特殊字符。
     *
     * @param domain 原始域名字符串
     * @return 格式化后的域名
     */
    public static String formatDomain(String domain) {

        // 转换为小写
        domain = domain.toLowerCase();

        // 去除特殊字符，只保留字母、数字、点和连字符
        String sanitized = domain.replaceAll("[^a-z0-9\\-\\.]", "");

        // 限制域名长度
        if (sanitized.length() > 255) {
            sanitized = DigestUtils.md5Hex(sanitized);
        }

        return sanitized;
    }
}
