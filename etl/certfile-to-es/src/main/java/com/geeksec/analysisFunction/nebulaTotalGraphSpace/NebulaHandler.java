package com.geeksec.analysisFunction.nebulaTotalGraphSpace;

import static com.geeksec.task.FlinkCertAnalysis.PA1;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.analysisFunction.infoSink.nebula.CertNebulaSinkFunction;
import com.geeksec.flinkTool.sideOutputTag.CertNebulaDedupOutPutTag;
import java.util.List;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

/**
* <AUTHOR>
* @Date  2024/8/19
*/

public class NebulaHandler {

    public static void nebulaInfoHandleFunction(SingleOutputStreamOperator<X509Cert> writeCertModelStream){

        // 对所有入库ES的证书进行Nebula的入库操作
        SingleOutputStreamOperator<Row> NebulaDedupStream = writeCertModelStream.map((MapFunction<X509Cert, Row>) x509Cert -> {
            Row row = new Row(2);
            row.setField(0,"cert_id");
            row.setField(1,x509Cert);
            return row;
        }).name("cert_id信息提取").setParallelism(PA1)
        .process(new CertNebulaDedup()).name("nebula查询证书sha1是否已经insert").setParallelism(PA1);

        // 对所有入库的证书进行签发机构和所有者的信息提取以及入库nebula
        SingleOutputStreamOperator<Row> NebulaIssuerStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {
                String issuer_md5 = x509Cert.getIssuerMD5();
                if(issuer_md5 != null && !issuer_md5.isEmpty()){
                    Row row = new Row(2);
                    row.setField(0,"issuer_md5");
                    row.setField(1,x509Cert);
                    collector.collect(row);
                }
            }
        }).name("签发机构的信息提取").setParallelism(PA1)
        .process(new CertNebulaDedup()).name("nebula查询证书实体issuer是否已经insert").setParallelism(PA1);

        SingleOutputStreamOperator<Row> NebulaSubjectStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {
                String subject_md5 = x509Cert.getSubjectMD5();
                if(subject_md5 != null && !subject_md5.isEmpty()){
                    Row row = new Row(2);
                    row.setField(0,"subject_md5");
                    row.setField(1,x509Cert);
                    collector.collect(row);
                }
            }
        }).name("所有者的信息提取").setParallelism(PA1)
        .process(new CertNebulaDedup()).name("nebula查询证书实体subject是否已经insert").setParallelism(PA1);

        // TODO 写入URL点去重
        SingleOutputStreamOperator<Row> NebulaURLStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {
                List<String> URLList = x509Cert.getAssociateURL();
                if(URLList.size()>0){
                    for(String URL:URLList){
                        Row row = new Row(2);
                        row.setField(0,"URL");
                        row.setField(1,x509Cert);
                        row.setField(2,URL);
                        collector.collect(row);
                    }
                }
            }
        }).name("URL点的信息提取").setParallelism(PA1)
        .process(new CertNebulaDedup()).name("nebula查询证书关联 URL 是否已经insert").setParallelism(PA1);

        // TODO 写入ORG点去重
        SingleOutputStreamOperator<Row> NebulaORGStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {
                String ORG = x509Cert.getCompany();
                if(ORG != null && !ORG.isEmpty()){
                    Row row = new Row(2);
                    row.setField(0,"ORG");
                    row.setField(1,x509Cert);
                    collector.collect(row);
                }
            }
        }).name("ORG点的信息提取").setParallelism(PA1)
        .process(new CertNebulaDedup()).name("nebula查询证书实体 ORG 是否已经insert").setParallelism(PA1);

        /**
         * 总体图空间的写入
         * */
        // 不重实体进行insert点插入
        CertNebulaSinkFunction.updateVertexData(NebulaDedupStream.getSideOutput(CertNebulaDedupOutPutTag.Dedup_Nebula_Cert));
        CertNebulaSinkFunction.insertVertexData(NebulaDedupStream.getSideOutput(CertNebulaDedupOutPutTag.Not_Dedup_Nebula_Cert));
        CertNebulaSinkFunction.insertIssuerVertexData(NebulaIssuerStream.getSideOutput(CertNebulaDedupOutPutTag.Not_Dedup_Nebula_Issuer));
        CertNebulaSinkFunction.insertSubjectVertexData(NebulaSubjectStream.getSideOutput(CertNebulaDedupOutPutTag.Not_Dedup_Nebula_Subject));
        CertNebulaSinkFunction.insertURLVertexData(NebulaURLStream.getSideOutput(CertNebulaDedupOutPutTag.Not_Dedup_Nebula_URL));
        CertNebulaSinkFunction.insertORGVertexData(NebulaORGStream.getSideOutput(CertNebulaDedupOutPutTag.Not_Dedup_Nebula_ORG));
        CertNebulaSinkFunction.insertFDOMAINVertexData(writeCertModelStream);
        CertNebulaSinkFunction.insertDOMAINVertexData(writeCertModelStream);
        CertNebulaSinkFunction.insertIPVertexData(writeCertModelStream);

        // 证书直接写入，证书部分提取的关联关联关系
        CertNebulaSinkFunction.insertSubjectRelatedCert(writeCertModelStream);
        CertNebulaSinkFunction.insertIssuerRelatedCert(writeCertModelStream);
        CertNebulaSinkFunction.insertDomainBelongToOrg(writeCertModelStream);
        CertNebulaSinkFunction.insertfDomainBelongToOrg(writeCertModelStream);
        CertNebulaSinkFunction.insertIpBelongToOrg(writeCertModelStream);
        CertNebulaSinkFunction.insertCertBelongToOrg(writeCertModelStream);
        CertNebulaSinkFunction.insertDomainUrlRelated(writeCertModelStream);
        CertNebulaSinkFunction.insertIpUrlRelated(writeCertModelStream);
        CertNebulaSinkFunction.insertCertUrlRelated(writeCertModelStream);
        CertNebulaSinkFunction.insertCertHasLabel(writeCertModelStream);

        // zntp
        CertNebulaSinkFunction.insertCertCertRelated(writeCertModelStream);
        CertNebulaSinkFunction.insertCertValidateDomain(writeCertModelStream);
        CertNebulaSinkFunction.insertCertFDomainRelated(writeCertModelStream);
        CertNebulaSinkFunction.insertCertIpRelated(writeCertModelStream);
    }
}
