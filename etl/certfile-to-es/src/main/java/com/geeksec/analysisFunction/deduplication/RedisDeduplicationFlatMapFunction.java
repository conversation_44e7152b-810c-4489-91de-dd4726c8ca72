package com.geeksec.analysisFunction.deduplication;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.utils.EsUtils;
import com.geeksec.utils.TimeRedisUtils;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * <AUTHOR>
 * @Date 2023/8/24
 */

public class RedisDeduplicationFlatMapFunction extends RichFlatMapFunction<X509Cert, X509Cert> {
    protected static final Logger LOG = LoggerFactory.getLogger(RedisDeduplicationFlatMapFunction.class);
    public static transient JedisPool jedisPool = null;
    public static transient GenericObjectPool<RestHighLevelClient> EsPool = null;

    @Override
    public void open(Configuration parameters) throws Exception {
        // redis池 初始化.
        jedisPool = TimeRedisUtils.initJedisPool();
        LOG.info("生成jedisPool成功! 活跃连接数——{}——，空闲数——{}——，等待数——{}——", jedisPool.getNumActive(),jedisPool.getNumIdle(),jedisPool.getNumWaiters());
        // ES 初始化
        EsPool = EsUtils.initEsPool();
        LOG.info("生成EsPool成功! {}", EsPool.getNumIdle(),EsPool.hashCode());
        super.open(parameters);
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (jedisPool != null) {
            jedisPool.close();
        }
        if (EsPool != null) {
            EsPool.close();
        }
    }

    @Override
    public void flatMap(X509Cert x509Cert, Collector<X509Cert> collector){
        String sourceType = x509Cert.getCertSource();
        if ("User".equals(sourceType)){
            collector.collect(x509Cert);
        }else {
            Jedis redisClient = null;
            try{
                redisClient = TimeRedisUtils.getJedis(jedisPool);
                redisClient.select(15);
                //直接往redis里面写
                boolean isRedisDedup = TimeRedisUtils.checkRedisDedup(x509Cert,redisClient);
                if (!isRedisDedup){
                    collector.collect(x509Cert);
                }

            }catch (Exception e){
                LOG.error("redis设置失败，error--->{},SHA1 is--->{}",e,x509Cert.getASN1SHA1());
                collector.collect(x509Cert);
            }finally {
                if (redisClient != null){
                    redisClient.close();
                }
            }

        }
    }
}
