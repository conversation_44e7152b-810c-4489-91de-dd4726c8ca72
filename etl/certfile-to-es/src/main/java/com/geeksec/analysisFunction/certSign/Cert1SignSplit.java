package com.geeksec.analysisFunction.certSign;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.dbConnect.lmdb.LmdbClient;
import com.geeksec.flinkTool.sideOutputTag.signOutPutTag;
import com.geeksec.utils.CertFormatUtil;
import com.geeksec.utils.CertificateParser;
import com.geeksec.utils.EsUtils;
import com.geeksec.utils.FileUtil;
import java.io.File;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.*;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.elasticsearch.client.RestHighLevelClient;
import org.lmdbjava.Dbi;
import org.lmdbjava.DbiFlags;
import org.lmdbjava.Env;
import org.lmdbjava.EnvFlags;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.geeksec.analysisFunction.certSplitAll.CertSplit.dbi;
import static com.geeksec.analysisFunction.certSplitAll.CertSplit.env;
import static org.lmdbjava.ByteBufferProxy.PROXY_OPTIMAL;

/**
 * <AUTHOR>
 * @Date 2023/1/17
 */

public class Cert1SignSplit extends ProcessFunction<Row, Row> {
    protected static final Logger LOG = LoggerFactory.getLogger(Cert1SignSplit.class);
    private static transient GenericObjectPool<RestHighLevelClient> EsPool = null;
    private final Properties properties = FileUtil.getProperties("/config.properties");
    private final Long lmdbMaxSize = Long.valueOf(properties.getOrDefault("lmdb.maxsize", "1099511627776").toString());
    private final String lmdbParentDirSystem = properties.getOrDefault("lmdb.path", "/data/lmdb/cert/cert_system").toString();
    private final String lmdbParentDirUser = properties.getOrDefault("lmdb.path", "/data/lmdb/cert/cert_user").toString();

//    private static transient Env<ByteBuffer> env;
//    private static transient Dbi<ByteBuffer> dbi;

    public static transient Env<ByteBuffer> envUser;
    public static transient Dbi<ByteBuffer> dbiUser;

    public static String SYSTEM_CERT_INDEX = null;

    @Override
    public void open(Configuration parameters) throws Exception {
        LOG.info("Initialization of global parameters starts.");
        Map<String, String> globalParam = getRuntimeContext().getExecutionConfig().getGlobalJobParameters().toMap();

        // 系统证书Index
        SYSTEM_CERT_INDEX = globalParam.get("es.system.index");
        LOG.info("cert system index:[ {} ].", SYSTEM_CERT_INDEX);

        // ES 初始化
        EsPool = EsUtils.initEsPool();
        LOG.info("生成EsPool成功! {}", EsPool.getNumIdle(),EsPool.hashCode());

//        // 初始化 LMDB 环境
//        File lmdbDir = new File(lmdbParentDirSystem);
//        if (!lmdbDir.exists()) {
//            LOG.info("LMDB Directory {} Created: {}", lmdbDir, lmdbDir.mkdirs());
//        }
//
//        // 创建 LMDB 环境并设置最大容量
//        env = Env.create(PROXY_OPTIMAL)
//                .setMapSize(lmdbMaxSize)
//                .open(lmdbDir, EnvFlags.MDB_NOTLS);
//
//        // 打开数据库
//        dbi = env.openDbi((byte[]) null, DbiFlags.MDB_CREATE);
//        LOG.info("SYSTEM LMDB Environment and Database initialized");

        // 初始化 LMDB 环境
        File lmdbDirUser = new File(lmdbParentDirUser);
        if (!lmdbDirUser.exists()) {
            LOG.info("LMDB Directory {} Created: {}", lmdbDirUser, lmdbDirUser.mkdirs());
        }

        // 创建 LMDB 环境并设置最大容量
        envUser = Env.create(PROXY_OPTIMAL)
                .setMapSize(lmdbMaxSize)
                .open(lmdbDirUser, EnvFlags.MDB_NOTLS);

        // 打开数据库
        dbiUser = envUser.openDbi((byte[]) null, DbiFlags.MDB_CREATE);
        LOG.info("USER LMDB Environment and Database initialized");
    }

    @Override
    public void close() throws Exception {
        if (EsPool != null) {
            EsPool.close();
        }
//        // 关闭 LMDB 资源
//        if (dbi != null) {
//            dbi.close();
//        }
//        if (env != null) {
//            env.close();
//        }
        if (dbiUser != null) {
            dbiUser.close();
        }
        if (envUser != null) {
            envUser.close();
        }
        super.close();
    }

    @Override//应保持x509子证书的fatherList字段与fatherListMap的key相匹配
    public void processElement(Row signRow, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
        X509Cert x509Cert = (X509Cert) signRow.getField(0);
        Map<String,X509Cert> fatherListMap = (Map<String, X509Cert>) signRow.getField(1);
        List<String> fatherList = x509Cert.getFatherCertIDList();
        List<String> tagList = x509Cert.getTagList();
        X509Cert testCert = x509Cert;
        OutputTag<Row> signStop = signOutPutTag.stop_sign1;
        OutputTag<Row> signContinue = signOutPutTag.continue_sign1;
        comprehensiveCertSign(signRow, context, testCert, tagList, x509Cert, signStop, fatherList, fatherListMap, signContinue, EsPool, env, dbi, envUser, dbiUser);
    }

    /**
    **
     * @param signRow 验签的传递Row
     * @param context 用于输出侧边流
     * @param testCert 用于验签的证书
     * @param tagList 原证书的标签列表
     * @param x509Cert 原证书
     * @param signStop OutputTag停止验签标志
     * @param signContinue OutputTag继续验签标志
     * @param fatherList 原证书的父证书列表
     * @param fatherListMap 验签的传递父证书Map
     * @param EsPool 用于验签的ES连接池
     * @param env 用于验签的lmdbClient连接
     * @param dbi 用于验签的lmdbClient连接
     *
     *  */
    public static void comprehensiveCertSign(Row signRow, ProcessFunction<Row, Row>.Context context,
                                             X509Cert testCert, List<String> tagList, X509Cert x509Cert,
                                             OutputTag<Row> signStop, List<String> fatherList,
                                             Map<String, X509Cert> fatherListMap, OutputTag<Row> signContinue
                                             , GenericObjectPool<RestHighLevelClient> EsPool,
                                             Env<ByteBuffer> env, Dbi<ByteBuffer> dbi, Env<ByteBuffer> envUser, Dbi<ByteBuffer> dbiUser) throws Exception {
        // 验证证书是否是签发链可信,在验签过程发现该证书是一个白证书
        // 直接合并父证书列表
        RestHighLevelClient esClient = null;
        try {
            esClient = EsUtils.getClient(EsPool);
            //如果当前证书在系统中存在且是一个签发链可信证书，打上白名单标签并返回。
            List<String> whiteFatherList = findExistWhiteCACert(testCert,esClient);
            if (whiteFatherList!=null) {
                updateTagList(tagList,"White CA Cert");
                x509Cert.setTagList(tagList);
                updateFatherWithKnownWhiteCaCert(fatherList,whiteFatherList);
                x509Cert.setFatherCertIDList(fatherList);
                signRow.setField(0, x509Cert);
                context.output(signStop, signRow);
                return;
            }
        }catch (Exception e){
            LOG.error("获取EsClient失败，error--->{}",e.toString());
        }finally {
            if(esClient != null){
                EsUtils.returnClient(esClient,EsPool);
            }
        }

        //将现有证书转化为原始文件准备验签
        //初始化一个x509证书实例certificate,并把当前的证书cert中的byte信息拷贝到标准证书实例中。
        X509Certificate certificate;
        try {
            certificate = CertificateParser.parseCertificate(testCert.getCert());
        } catch (CertificateException e) {
            context.output(signStop, signRow);
            return;
        }

        /* 查父证书 */
        RestHighLevelClient esClient1 = null;
        // ES中父证书结果采用Map<SHA1,MAP>的结构，如果查询到多个父证书，则会有多个SAH1
        HashMap<String, Map> fatherEsCertResult = null;
        try{
            esClient1 = EsUtils.getClient(EsPool);
            //获取父证书hashmap
            fatherEsCertResult = getFatherCert(testCert,esClient1);
        }catch (Exception e){
            LOG.error("获取EsClient失败，error--->{}",e.toString());
        }finally {
            if(esClient1 != null){
                EsUtils.returnClient(esClient1,EsPool);
            }
        }

        // 查ES返回父证书为空，如果是null说明ES查询问题，不打标
        if (fatherEsCertResult == null){
            LOG.error("获取EsClient失败，查询ES父证书报错，无法查询，返回空");
            context.output(signStop, signRow);
            return;
        }

        // 查ES返回父证书为空列表，未查询到父证书
        if(fatherEsCertResult.isEmpty()){
            updateTagList(tagList,"Lost CertList");
            x509Cert.setTagList(tagList);
            signRow.setField(0, x509Cert);
            LOG.debug("无法查询到父证书.");
            context.output(signStop, signRow);
            return;
        }

        // 判断ES中查询到的父证书的数量判断是否存在多证书链
        Set<String> fatherSha1List = fatherEsCertResult.keySet();
        if(fatherSha1List.size()>1){
            updateTagList(tagList,"Multi CertList");
        }

        // 对每一个父证书进行验证,并验证合法性
        Boolean isFake = null;
        HashMap fatherSuccessVerifyMap = new HashMap<>();
        for (String sha1 : fatherSha1List) {
            if (getIllegal(testCert,(HashMap) fatherEsCertResult.get(sha1))){
                updateTagList(tagList,"Illegal Cert");
                updateTagList(tagList,"Insecure Chain");
            }
            //从备用数据中找ES中重复的数据。找到了才进行验签
            HashMap fatherEsMap = (HashMap) fatherEsCertResult.get(sha1);
            if (!ObjectUtils.allNotNull(fatherEsMap) || fatherEsMap.isEmpty()) {
                continue;
            }

            String fatherPublicKeyStr = fatherEsMap.getOrDefault("PublicKey", "").toString();
            String fatherPubAlg = fatherEsMap.getOrDefault("PublicKeyAlgorithm", "").toString();
            if(fatherPubAlg.contains(".") && fatherPubAlg.contains("(")){
                fatherPubAlg = fatherEsMap.getOrDefault("PubAlgOid", "").toString();
            }
            PublicKey fatherPublicKey = CertFormatUtil.getPublicKey(fatherPublicKeyStr,fatherPubAlg);
            if (fatherPublicKey == null){
                LOG.error("父证书公钥解析异常，异常key：——{}——，异常算法：——{}——",fatherPublicKeyStr,fatherPubAlg);
                continue;
            }
            //以下三个异常存在一个便算伪造证书。
            //利用之前的证书的原始文件和父证书的原始文件进行验签
            Boolean fakeBoolean = isFakeCert(certificate, fatherPublicKey);
            if(fakeBoolean==null){
                continue;
            }
            if (!fakeBoolean) {
                // 有一个验签通过则进入后续逻辑，确认父证书存在，更新证书链
                isFake = false;
                fatherSuccessVerifyMap = fatherEsMap;
                break;
            } else {
                isFake = true;
                fatherSuccessVerifyMap = fatherEsMap;
            }
        }

        // 父证书查了一圈isFake没有赋值，打上缺失证书链标签，可能原因，伪造证书公钥，知识库中无父证书
        if (isFake == null) {
            updateTagList(tagList,"Lost CertList");
            x509Cert.setTagList(tagList);
            signRow.setField(0, x509Cert);
            context.output(signStop, signRow);
            return;
        }

        String fatherSha1 = fatherSuccessVerifyMap.get("ASN1SHA1").toString();
        String certSource = fatherSuccessVerifyMap.get("CertSource").toString();


        if (isFake) {
            updateTagList(tagList,"Fake Cert");
            fatherList.add(fatherSha1);
            x509Cert.setTagList(tagList);
            x509Cert.setFatherCertIDList(fatherList);
            signRow.setField(0, x509Cert);
            context.output(signStop, signRow);
            return;
        }

        // !isFake 验签成功！
        // 若证书存在于cert_system,则表示为系统证书，否则对其父证书做再做效验
        boolean isSelfSign = isSelfSignCert(fatherSuccessVerifyMap);
        // 如果父证书列表中已经有了该证书SHA1，且该证书不是根自签名证书，则出现循环验签，证书链乱序
        if(fatherList.contains(fatherSha1) && !isSelfSign){
            updateTagList(tagList,"Chain Mess");
            x509Cert.setTagList(tagList);
            signRow.setField(0, x509Cert);
            context.output(signStop, signRow);
            return;
        }

        boolean sysCaCert = false;
        if ("System".equals(certSource)) {
            sysCaCert = true;
        }else {
            try {
                sysCaCert = LmdbClient.isSysCaCert(fatherSha1,env,dbi);
            }catch (Exception e){
                LOG.error("SYSTEM LMDB 查询失败，error--->{}",e.toString());
            }
        }

        // 如果已经出现了自签名，并且不是系统证书，就说明存在自建的根证书，打标未知CA
        if(isSelfSign && !sysCaCert){
            tagList.add("Unknown CA");
            x509Cert.setTagList(tagList);
            fatherList.add(fatherSha1);
            x509Cert.setFatherCertIDList(fatherList);
            signRow.setField(0, x509Cert);
            context.output(signStop, signRow);
            return;
        }

        //判断是否是系统证书
        if (sysCaCert) {
            updateTagList(tagList,"White CA Cert");
            fatherList.add(fatherSha1);
            x509Cert.setTagList(tagList);
            x509Cert.setFatherCertIDList(fatherList);
            signRow.setField(0, x509Cert);
            context.output(signStop, signRow);
            return;
        }

        // 如果都不是，那就需要进行进一步验签了，肯定不是系统证书，只用查用户证书了
        byte[] fatherCertByte = queryFatherByte(fatherSha1, envUser, dbiUser);

        //提前进行父证书的更新
        fatherList.add(fatherSha1);
        x509Cert.setTagList(tagList);
        x509Cert.setFatherCertIDList(fatherList);
        signRow.setField(0, x509Cert);

        if(fatherCertByte == null){
            context.output(signStop, signRow);
            return;
        }

        if(fatherCertByte.length == 0){
            context.output(signStop, signRow);
            return;
        }

        X509Cert cert = new X509Cert(fatherCertByte);
        try {
            //要进一步验签的话需要更新父证书的map信息
            CertFormatUtil.parseContent(cert);
            fatherListMap.put(fatherSha1,cert);
            signRow.setField(1, fatherListMap);
            context.output(signContinue, signRow);
            return;
        } catch (CertificateException e) {
            context.output(signStop, signRow);
            return;
        }
    }

    private static void updateFatherWithKnownWhiteCaCert(List<String> fatherList, List<String> whiteFatherList) {
        for (String fatherSha1 : whiteFatherList) {
            if (!fatherList.contains(fatherSha1)) {
                fatherList.add(fatherSha1);
            }
        }
    }

    public static void updateTagList(List<String> tagList,String tagText){
        if (!tagList.contains(tagText)){
            tagList.add(tagText);
        }
    }


    /**
     * 是否是伪造证书？
     *
     * @param cert  证书
     * @param fatherPublicKey 父证书公钥
     * @return yes or no.
     */
    public static Boolean isFakeCert(Certificate cert, PublicKey fatherPublicKey) {
        Boolean result = false;

        //抛出以下三个异常，被认为该证书是伪造的。
        try {
            cert.verify(fatherPublicKey);
        } catch (NoSuchAlgorithmException | InvalidKeyException | SignatureException e) {
            result = true;
        } catch (Exception e) {
            result = null;
            LOG.warn("father certificate publicKey parse fail.");
        }

        return result;
    }


    /**
     * 白名单证书是否存在
     * 第一次不需要，能进验签肯定不是白名单证书
     * 只查user
     * @param x509Cert
     * @return
     */
    public static List<String> findExistWhiteCACert(X509Cert x509Cert, RestHighLevelClient esClient) throws Exception {

        List<String> fatherList = null;

        HashMap<String, Map> result = EsUtils.matchQuery("cert_user", "ASN1SHA1", x509Cert.getASN1SHA1(), null, null,esClient);

        if (!result.isEmpty()) {
            Set<String> dataIds = result.keySet();
            for (String dataId : dataIds) {
                Map data = result.get(dataId);
                List<String> tags = CertFormatUtil.castList(data.getOrDefault("Tags", new ArrayList<>()), String.class);
                if (tags.contains("227")) {
                    fatherList = (List<String>) data.getOrDefault("FatherCertID", new ArrayList<>());
                    return fatherList;
                }
            }
        } else {
            return null;
        }
        return null;
    }

    // 根据issuerMD5以及issuerKeyId进行父证书匹配
    public static HashMap<String, Map> getFatherCert(X509Cert x509Cert, RestHighLevelClient EsClient1) throws Exception {
        HashMap<String, Map> result;

        HashMap extension = CertFormatUtil.objectToMap(x509Cert.getExtension());
        String fatherKey = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "authorityKeyIdentifier", ""));

        String issuerMD5 = x509Cert.getIssuerMD5();

        //模糊匹配父证书，必须满足颁发机构匹配MD5，颁发机构issuerKeyId
        result = EsUtils.nestedQuery("cert_*", "Extension", "SubjectMD5", issuerMD5, "subjectKeyIdentifier", fatherKey,
                null, null, EsClient1);
        return result;
    }

    /**
     * 用ES查出来的父证书SHA1列表查询父证书信息列表
     *
     * @param fatherSha1 父证书keys
     * @return 父证书列表
     */
    public static byte[] queryFatherByte(String fatherSha1, Env<ByteBuffer> env, Dbi<ByteBuffer> dbi) throws Exception {
        byte[] certByteStringLmdb = null;

        try{
            // lmdb查询
            certByteStringLmdb = LmdbClient.getCertByteBySha1(env, dbi, fatherSha1);
        }catch (Exception e){
            LOG.error("lmdb查询失败，error--->{},fatherRowKeys is--->{}",e,fatherSha1);
        }

        if(certByteStringLmdb!=null){
            return certByteStringLmdb;
        }
        LOG.warn("Lmdb没有查询到证书数据：——{}——",fatherSha1);
        return null;
    }

    /**
     * base64 解码
     *
     * @param base64Str 用base64加密过的字符串
     * @return cert
     */
    public static byte[] decodeBase64(String base64Str) {
        // 若字符串为空
        if ("null".equals(base64Str) || base64Str.length() < 1) {
            return "".getBytes();
        }

        return Base64.getDecoder().decode(base64Str.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 当前证书的父证书查到了，但是当前证书的有效期范围和父证书冲突，或者父证书的IsCA字段不为CA证书
     * */
    public static boolean getIllegal(X509Cert x509Cert, HashMap x509Cert_Father){
        HashMap extension = (HashMap) x509Cert_Father.get("Extension");
        if (extension==null){
            return false;
        }
        if (extension.get("basicConstraints")==null){
            return false;
        }
        String basicConstraints = (String) extension.get("basicConstraints");
        boolean isCA  = basicConstraints.contains("CA:TRUE");
        long notAfter = Long.parseLong(x509Cert.getNotAfter().replaceAll("Z", ""));
        long notBefore = Long.parseLong(x509Cert.getNotBefore().replaceAll("Z", ""));
        String fatherNotAfterString = (String) x509Cert_Father.get("NotAfter");
        String fatherNotBeforeString = (String) x509Cert_Father.get("NotBefore");
        long fatherNotAfter = Long.parseLong(fatherNotAfterString.replaceAll("Z", ""));
        long fatherNotBefore = Long.parseLong(fatherNotBeforeString.replaceAll("Z", ""));

        //三个条件全部满足即当前证书CA身份没有不合法问题
        return fatherNotBefore >= notBefore || fatherNotAfter <= notAfter || !isCA;
    }

    public static boolean isSelfSignCert(HashMap fatherMap){
        HashMap extension = (HashMap) fatherMap.getOrDefault("Extension","");
        String basicConstraints = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "basicConstraints", ""));
        String subjectMD5 = (String) fatherMap.getOrDefault("SubjectMD5","");
        String issuerMD5 = (String) fatherMap.getOrDefault("IssuerMD5","");
        String subjectKeyId = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "subjectKeyIdentifier", ""));//取出subject的KeyId值
        if ("null".equals(subjectKeyId)) {
            subjectKeyId = "";
        }

        String issuerKeyId = String.valueOf(CertFormatUtil.readDataFromKeys(extension, "authorityKeyIdentifier", ""));//取出issuer的KeyId值
        if ("null".equals(issuerKeyId)) {
            issuerKeyId = "";
        }
        String testIssuerKeyId = (issuerKeyId.split(",")[0]).replaceAll("keyid:", "");
        if (basicConstraints.contains("CA:TRUE")) {
            return subjectMD5.equals(issuerMD5) || (!"".equals(subjectKeyId) && subjectKeyId.equals(testIssuerKeyId));
        }else {
            return false;
        }
    }
}
