package com.geeksec.analysisFunction.errorCorrecting;

import static com.geeksec.analysisFunction.certInfoAll.CertScoreMapRichFunction.tagsToTagId;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.utils.CertFormatUtil;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/8/7
 */

public class ErrorCorrectingCertSplitFunction extends ProcessFunction<X509Cert, JSONObject> {

    private static final Logger LOG = LoggerFactory.getLogger(ErrorCorrectingCertSplitFunction.class);

    public static final OutputTag<JSONObject> errorUserCertResult = new OutputTag<>("errorUserCertResult", TypeInformation.of(JSONObject.class));
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void processElement(X509Cert x509Cert, ProcessFunction<X509Cert, JSONObject>.Context context, Collector<JSONObject> collector) throws Exception {
        String certType = x509Cert.getCertSource();
        x509Cert.setTagList(tagsToTagId(x509Cert.getTagList()));
        CertFormatUtil.setCertTreatLevel(x509Cert);

        //证书的分来源分流
        if ("User".equals(certType)) {
            context.output(errorUserCertResult, JSONObject.parseObject(JSON.toJSONString(x509Cert)));
        } else {
            LOG.error("出现了已知以外的证书来源类型!");
        }
    }

    @Override
    public void close() throws Exception {
        super.close();
    }
}
