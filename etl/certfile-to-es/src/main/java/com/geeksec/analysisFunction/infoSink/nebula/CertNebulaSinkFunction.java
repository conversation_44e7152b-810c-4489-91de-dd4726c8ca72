package com.geeksec.analysisFunction.infoSink.nebula;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.utils.CertFormatUtil;
import com.geeksec.utils.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.connector.nebula.connection.NebulaClientOptions;
import org.apache.flink.connector.nebula.connection.NebulaGraphConnectionProvider;
import org.apache.flink.connector.nebula.connection.NebulaMetaConnectionProvider;
import org.apache.flink.connector.nebula.sink.NebulaBatchOutputFormat;
import org.apache.flink.connector.nebula.sink.NebulaEdgeBatchOutputFormat;
import org.apache.flink.connector.nebula.sink.NebulaSinkFunction;
import org.apache.flink.connector.nebula.sink.NebulaVertexBatchOutputFormat;
import org.apache.flink.connector.nebula.statement.EdgeExecutionOptions;
import org.apache.flink.connector.nebula.statement.ExecutionOptions;
import org.apache.flink.connector.nebula.statement.VertexExecutionOptions;
import org.apache.flink.connector.nebula.utils.WriteModeEnum;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.*;

import static com.geeksec.task.FlinkCertAnalysis.PA1;
import static com.geeksec.task.FlinkCertAnalysis.PA4;

/**
 * <AUTHOR>
 * @Date 2023/10/16
 */

@Slf4j
public class CertNebulaSinkFunction {

    private static Properties properties = FileUtil.getProperties("/config.properties");
    public static final String NEBULA_GRAPH_ADDR = properties.getProperty("nebula.graph.addr");
    public static final String NEBULA_META_ADDR = properties.getProperty("nebula.meta.addr");
    public static final String NEBULA_GRAPH_SPACE = properties.getProperty("nebula.space.name");
    public static final int BATCH_INTERVAL_MS = 100;


    // Nebula Conn通用配置
    private static NebulaClientOptions nebulaClientOptions = new NebulaClientOptions.NebulaClientOptionsBuilder()
            .setGraphAddress(NEBULA_GRAPH_ADDR)
            .setMetaAddress(NEBULA_META_ADDR)
            .build();
    private static NebulaGraphConnectionProvider graphConnectionProvider = new NebulaGraphConnectionProvider(nebulaClientOptions);
    private static NebulaMetaConnectionProvider metaConnectionProvider = new NebulaMetaConnectionProvider(nebulaClientOptions);

    /**
     * 不重复证书插入操作
     */
    public static void insertVertexData(DataStream<Row> certNotDedupStream) {
        VertexExecutionOptions executionOptions =
                new VertexExecutionOptions.ExecutionOptionBuilder()
                        .setGraphSpace(NEBULA_GRAPH_SPACE)
                        .setTag("CERT")
                        .setIdIndex(0)
                        .setFields(Arrays.asList("cert_id", "first_seen", "last_seen", "black_list", "white_list", "source_type", "cert_md5", "remark"))
                        .setPositions(Arrays.asList(0, 1, 2, 3, 4, 5, 6, 7))
                        .setBatchIntervalMs(BATCH_INTERVAL_MS)
                        .setBatch(20).builder();

        NebulaVertexBatchOutputFormat outputFormat = new NebulaVertexBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> CertRowStream = certNotDedupStream.map((MapFunction<Row, Row>) row -> {
            X509Cert x509Cert = (X509Cert) row.getField(1);
            Row certSinkRow = new Row(8);
            certSinkRow.setField(0, x509Cert.getCertId());
            certSinkRow.setField(1, x509Cert.getImportTime());
            certSinkRow.setField(2, x509Cert.getImportTime());
            certSinkRow.setField(3, x509Cert.getBlackList());
            certSinkRow.setField(4, x509Cert.getWhiteList());
            certSinkRow.setField(5, sourceToInt(x509Cert.getCertSource()));
            certSinkRow.setField(6, x509Cert.getASN1MD5());
            certSinkRow.setField(7, StringUtil.EMPTY_STRING);
            return certSinkRow;
        }).name("X509Cert转化为Row").setParallelism(PA4);
        CertRowStream.addSink(nebulaSinkFunction).name("未重复证书直接Sink").setParallelism(PA4);
    }

    private static Integer sourceToInt(String certSource) {
        switch (certSource) {
            case "User":
                return 0;
            case "PushData":
                return 1;
            case "Scan":
                return 2;
            case "System":
                return 3;
            default:
                return null;
        }
    }

    /**
     * 重复证书更新操作
     */
    public static void updateVertexData(DataStream<Row> certDedupStream) {
        VertexExecutionOptions executionOptions =
                new VertexExecutionOptions.ExecutionOptionBuilder()
                        .setGraphSpace(NEBULA_GRAPH_SPACE)
                        .setTag("CERT")
                        .setIdIndex(0)
                        .setFields(Arrays.asList("first_seen", "last_seen", "black_list", "white_list", "cert_md5"))
                        .setPositions(Arrays.asList(1, 2, 3, 4, 5))
                        .setWriteMode(WriteModeEnum.UPDATE)
                        .setBatchIntervalMs(BATCH_INTERVAL_MS)
                        .setBatch(20).builder();

        NebulaVertexBatchOutputFormat outputFormat = new NebulaVertexBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> CertRowStream = certDedupStream.map((MapFunction<Row, Row>) row -> {
            X509Cert x509Cert = (X509Cert) row.getField(1);

            Row certSinkRow = new Row(6);
            certSinkRow.setField(0, x509Cert.getCertId());
            certSinkRow.setField(1, x509Cert.getImportTime());
            certSinkRow.setField(2, x509Cert.getImportTime());
            certSinkRow.setField(3, x509Cert.getBlackList());
            certSinkRow.setField(4, x509Cert.getWhiteList());
            certSinkRow.setField(5, x509Cert.getASN1MD5());
            return certSinkRow;
        }).name("X509Cert转化为Row").setParallelism(PA4);
        CertRowStream.addSink(nebulaSinkFunction).name("重复证书直接update").setParallelism(PA4);
    }

    /**
     * 不重复Issuer插入操作
     */
    public static void insertIssuerVertexData(DataStream<Row> certNotDedupStream) {
        VertexExecutionOptions executionOptions =
                new VertexExecutionOptions.ExecutionOptionBuilder()
                        .setGraphSpace(NEBULA_GRAPH_SPACE)
                        .setTag("ISSUER")
                        .setIdIndex(0)
                        .setFields(Arrays.asList("issuer_md5", "common_name", "country", "object_name", "first_seen", "last_seen"))
                        .setPositions(Arrays.asList(0, 1, 2, 3, 4, 5))
                        .setBatchIntervalMs(BATCH_INTERVAL_MS)
                        .setBatch(20).builder();

        NebulaVertexBatchOutputFormat outputFormat = new NebulaVertexBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> CertRowStream = certNotDedupStream.map((MapFunction<Row, Row>) row -> {
            X509Cert x509Cert = (X509Cert) row.getField(1);

            Row certSinkRow = new Row(6);
            HashMap issuer = CertFormatUtil.objectToMap(x509Cert.getIssuer());
            String issuerCN = String.valueOf(CertFormatUtil.readDataFromKeys(issuer, "CN", StringUtil.EMPTY_STRING));//取出issuer的CN值
            if ("null".equals(issuerCN)) {
                issuerCN = StringUtil.EMPTY_STRING;
            }
            String issuerC = String.valueOf(CertFormatUtil.readDataFromKeys(issuer, "C", StringUtil.EMPTY_STRING));//取出issuer的C值
            if ("null".equals(issuerC)) {
                issuerC = StringUtil.EMPTY_STRING;
            }
            String issuerO = String.valueOf(CertFormatUtil.readDataFromKeys(issuer, "O", StringUtil.EMPTY_STRING));//取出issuer的O值
            if ("null".equals(issuerO)) {
                issuerO = StringUtil.EMPTY_STRING;
            }
            certSinkRow.setField(0, "issuer_" + x509Cert.getIssuerMD5());
            certSinkRow.setField(1, issuerCN);
            certSinkRow.setField(2, issuerC);
            certSinkRow.setField(3, issuerO);
            certSinkRow.setField(4, x509Cert.getImportTime());
            certSinkRow.setField(5, x509Cert.getImportTime());
            return certSinkRow;
        }).name("X509Cert转化为Row").setParallelism(PA4);
        CertRowStream.addSink(nebulaSinkFunction).name("未重复issuer直接Sink").setParallelism(PA4);
    }


    /**
     * 不重复Subject插入操作
     */
    public static void insertSubjectVertexData(DataStream<Row> certNotDedupStream) {
        VertexExecutionOptions executionOptions = new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setTag("SUBJECT")
                .setIdIndex(0)
                .setFields(Arrays.asList("subject_md5", "common_name", "country", "object_name", "first_seen", "last_seen"))
                .setPositions(Arrays.asList(0, 1, 2, 3, 4, 5))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(20).builder();

        NebulaVertexBatchOutputFormat outputFormat = new NebulaVertexBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> CertRowStream = certNotDedupStream.map((MapFunction<Row, Row>) row -> {
            X509Cert x509Cert = (X509Cert) row.getField(1);

            Row certSinkRow = new Row(6);
            HashMap subject = CertFormatUtil.objectToMap(x509Cert.getSubject());
            String subjectCN = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "CN", StringUtil.EMPTY_STRING));//取出issuer的CN值
            if ("null".equals(subjectCN)) {
                subjectCN = StringUtil.EMPTY_STRING;
            }
            String subjectC = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "C", StringUtil.EMPTY_STRING));//取出issuer的C值
            if ("null".equals(subjectC)) {
                subjectC = StringUtil.EMPTY_STRING;
            }
            String subjectO = String.valueOf(CertFormatUtil.readDataFromKeys(subject, "O", StringUtil.EMPTY_STRING));//取出issuer的O值
            if ("null".equals(subjectO)) {
                subjectO = StringUtil.EMPTY_STRING;
            }
            certSinkRow.setField(0, "subject_" + x509Cert.getSubjectMD5());
            certSinkRow.setField(1, subjectCN);
            certSinkRow.setField(2, subjectC);
            certSinkRow.setField(3, subjectO);
            certSinkRow.setField(4, x509Cert.getImportTime());
            certSinkRow.setField(5, x509Cert.getImportTime());
            return certSinkRow;
        }).name("X509Cert转化为Row").setParallelism(PA4);
        CertRowStream.addSink(nebulaSinkFunction).name("未重复subject直接Sink").setParallelism(PA4);
    }


    /**
     * 不重复 URL 插入操作
     */
    public static void insertURLVertexData(DataStream<Row> certDedupStream) {
        VertexExecutionOptions executionOptions = new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setTag("URL")
                .setIdIndex(0)
                .setFields(Arrays.asList("url_key", "black_list", "white_list", "first_seen", "last_seen"))
                .setPositions(Arrays.asList(0, 1, 2, 3, 4))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(20).builder();

        NebulaVertexBatchOutputFormat outputFormat = new NebulaVertexBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> CertRowStream = certDedupStream.map((MapFunction<Row, Row>) row -> {
            X509Cert x509Cert = (X509Cert) row.getField(1);
            String URL = (String) row.getField(2);

            Row certSinkRow = new Row(5);
            certSinkRow.setField(0, URL);
            certSinkRow.setField(1, 0);
            certSinkRow.setField(2, 0);
            certSinkRow.setField(3, x509Cert.getImportTime());
            certSinkRow.setField(4, x509Cert.getImportTime());
            return certSinkRow;
        }).name("X509Cert转化为Row").setParallelism(PA4);
        CertRowStream.addSink(nebulaSinkFunction).name("不重复URl直接写入").setParallelism(PA4);
    }

    /**
     * 不重复 ORG 插入操作
     */
    public static void insertORGVertexData(DataStream<Row> certDedupStream) {
        VertexExecutionOptions executionOptions = new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setTag("ORG")
                .setIdIndex(0)
                .setFields(Arrays.asList("org_name", "org_desc", "black_list", "white_list", "remark", "first_seen", "last_seen"))
                .setPositions(Arrays.asList(1, 2, 3, 4, 5, 6, 7))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(20).builder();

        NebulaVertexBatchOutputFormat outputFormat = new NebulaVertexBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> CertRowStream = certDedupStream.map((MapFunction<Row, Row>) row -> {
            X509Cert x509Cert = (X509Cert) row.getField(1);
            String company = x509Cert.getCompany();
            String company_md5 = DigestUtils.md5Hex(company);

            Row certSinkRow = new Row(8);
            certSinkRow.setField(0, company_md5);
            certSinkRow.setField(1, company);
            certSinkRow.setField(2, company);
            certSinkRow.setField(3, 0);
            certSinkRow.setField(4, 0);
            certSinkRow.setField(5, "");
            certSinkRow.setField(6, x509Cert.getImportTime());
            certSinkRow.setField(7, x509Cert.getImportTime());
            return certSinkRow;
        }).name("X509Cert转化为Row").setParallelism(PA4);
        CertRowStream.addSink(nebulaSinkFunction).name("不重复 ORG 直接写入").setParallelism(PA4);
    }

    /**
     * 不重复 FDOMAIN 插入操作
     */
    public static void insertFDOMAINVertexData(SingleOutputStreamOperator<X509Cert> certDedupStream) {
        VertexExecutionOptions executionOptions =
                new VertexExecutionOptions.ExecutionOptionBuilder()
                        .setGraphSpace(NEBULA_GRAPH_SPACE)
                        .setTag("FDOMAIN")
                        .setIdIndex(1)
                        .setFields(Arrays.asList("fdomain_addr", "first_seen", "last_seen"))
                        .setPositions(Arrays.asList(0, 2, 3))
                        .setBatchIntervalMs(BATCH_INTERVAL_MS)
                        .setBatch(20).builder();

        NebulaVertexBatchOutputFormat outputFormat = new NebulaVertexBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> CertRowStream = certDedupStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {
                List<String> relatedDomain = x509Cert.getAssociateDomain();
                if (relatedDomain.size() > 0) {
                    for (String domain : relatedDomain) {
                        if (domain.startsWith("*.")) {
                            Row fdomainTagRow = new Row(4);
                            fdomainTagRow.setField(0, domain);
                            fdomainTagRow.setField(1, domain.length() > 200 ? DigestUtils.md5Hex(domain) : domain);
                            fdomainTagRow.setField(2, x509Cert.getImportTime());
                            fdomainTagRow.setField(3, x509Cert.getImportTime());
                            collector.collect(fdomainTagRow);
                        }
                    }
                }
            }
        }).name("X509Cert转化为Row").setParallelism(PA4);
        CertRowStream.addSink(nebulaSinkFunction).name("不重复 FDOMAIN 直接写入").setParallelism(PA4);
    }

    /**
     * 不重复 DOMAIN 插入操作
     */
    public static void insertDOMAINVertexData(SingleOutputStreamOperator<X509Cert> certDedupStream) {
        VertexExecutionOptions executionOptions =
                new VertexExecutionOptions.ExecutionOptionBuilder()
                        .setGraphSpace(NEBULA_GRAPH_SPACE)
                        .setTag("DOMAIN")
                        .setIdIndex(1)
                        .setFields(Arrays.asList("domain_addr", "first_seen", "last_seen", "black_list", "white_list", "remark", "alexa_rank", "bytes", "average_bps", "whois"))
                        .setPositions(Arrays.asList(0, 2, 3, 4, 5, 6, 7, 8, 9, 10))
                        .setBatchIntervalMs(BATCH_INTERVAL_MS)
                        .setBatch(20).builder();

        NebulaVertexBatchOutputFormat outputFormat = new NebulaVertexBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> CertRowStream = certDedupStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {
                List<String> relatedDomain = x509Cert.getAssociateDomain();
                if (!relatedDomain.isEmpty()) {
                    for (String domain : relatedDomain) {
                        if (!domain.startsWith("*.")) {
                            Row fdomainTagRow = new Row(12);
                            fdomainTagRow.setField(0, domain);
                            fdomainTagRow.setField(1, domain.length() > 200 ? DigestUtils.md5Hex(domain) : domain);
                            fdomainTagRow.setField(2, x509Cert.getImportTime());
                            fdomainTagRow.setField(3, x509Cert.getImportTime());
                            fdomainTagRow.setField(4, x509Cert.getBlackList());
                            fdomainTagRow.setField(5, x509Cert.getWhiteList());
                            fdomainTagRow.setField(6, "");
                            fdomainTagRow.setField(7, 0);
                            fdomainTagRow.setField(8, 0);
                            fdomainTagRow.setField(9, 0);
                            fdomainTagRow.setField(10, "");
                            fdomainTagRow.setField(11, x509Cert.getTaskId());
                            collector.collect(fdomainTagRow);
                        }
                    }
                }
            }
        }).name("X509Cert转化为Row").setParallelism(PA4);
        CertRowStream.addSink(nebulaSinkFunction).name("不重复 DOMAIN 直接写入").setParallelism(PA4);
    }

    /**
     * 不重复 IP 插入操作
     */
    public static void insertIPVertexData(SingleOutputStreamOperator<X509Cert> certDedupStream) {
        VertexExecutionOptions executionOptions =
                new VertexExecutionOptions.ExecutionOptionBuilder()
                        .setGraphSpace(NEBULA_GRAPH_SPACE)
                        .setTag("IP")
                        .setIdIndex(1) // vid
                        .setFields(Arrays.asList("first_seen", "last_seen", "times", "ip_addr", "ip_key", "version", "city", "country", "bytes", "packets", "black_list", "white_list", "remark", "send_bytes", "recv_bytes", "average_bps"))
                        .setPositions(Arrays.asList(2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17))
                        .setBatchIntervalMs(BATCH_INTERVAL_MS)
                        .setBatch(200)
                        .builder();

        NebulaVertexBatchOutputFormat outputFormat = new NebulaVertexBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> CertRowStream = certDedupStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {
                List<String> associateIP = x509Cert.getAssociateIP();
                if (!associateIP.isEmpty()) {
                    for (String ip : associateIP) {
                        Row clientIpTagRow = new Row(19);
                        clientIpTagRow.setField(0, "IP_TAG");
                        clientIpTagRow.setField(1, ip);// vid
                        clientIpTagRow.setField(2, x509Cert.getImportTime());
                        clientIpTagRow.setField(3, x509Cert.getImportTime());
                        clientIpTagRow.setField(4, 0);
                        clientIpTagRow.setField(5, ip);
                        clientIpTagRow.setField(6, ip);
                        clientIpTagRow.setField(7, "V4");
                        clientIpTagRow.setField(8, "");
                        clientIpTagRow.setField(9, "");
                        clientIpTagRow.setField(10, 0);
                        clientIpTagRow.setField(11, 0);
                        clientIpTagRow.setField(12, 0);
                        clientIpTagRow.setField(13, 0);
                        clientIpTagRow.setField(14, "");
                        clientIpTagRow.setField(15, 0);
                        clientIpTagRow.setField(16,0);
                        clientIpTagRow.setField(17, 0);
                        clientIpTagRow.setField(18, 0);
                        collector.collect(clientIpTagRow);
                    }
                }
            }
        }).name("X509Cert转化为Row").setParallelism(PA4);
        CertRowStream.addSink(nebulaSinkFunction).name("不重复 IP 直接写入").setParallelism(PA4);
    }

    /**
     * Subject 关联 Cert
     */
    public static void insertSubjectRelatedCert(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("subject_related")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100).builder();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.map(new MapFunction<X509Cert, Row>() {
            @Override
            public Row map(X509Cert x509Cert) throws Exception {
                Row SubjectRelatedCertEdgeRow = new Row(3);
                SubjectRelatedCertEdgeRow.setField(0, x509Cert.getCertId());
                SubjectRelatedCertEdgeRow.setField(1, "subject_" + x509Cert.getSubjectMD5());
                SubjectRelatedCertEdgeRow.setField(2, 0);
                return SubjectRelatedCertEdgeRow;
            }
        }).name("Subject 关联 Cert 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("Subject 关联 Cert 边写入").setParallelism(PA1);
    }


    /**
     * Issuer 关联 Cert
     */
    public static void insertIssuerRelatedCert(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("issuer_related")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100).builder();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.map(new MapFunction<X509Cert, Row>() {
            @Override
            public Row map(X509Cert x509Cert) throws Exception {
                Row IssuerRelatedCertEdgeRow = new Row(3);
                IssuerRelatedCertEdgeRow.setField(0, x509Cert.getCertId());
                IssuerRelatedCertEdgeRow.setField(1, "issuer_" + x509Cert.getIssuerMD5());
                IssuerRelatedCertEdgeRow.setField(2, 0);
                return IssuerRelatedCertEdgeRow;
            }
        }).name("Issuer 关联 Cert 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("Issuer 关联 Cert 边写入").setParallelism(PA1);
    }

    /**
     * Domain 关联 Org（company）
     * domain_belong_to_org
     */
    public static void insertDomainBelongToOrg(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("domain_belong_to_org")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100).builder();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {

                List<String> relatedDomain = x509Cert.getAssociateDomain();
                String company = x509Cert.getCompany();
                if (!"".equals(company) && relatedDomain.size() > 0) {
                    for (String domain : relatedDomain) {
                        if (!domain.startsWith("*.")) {
                            Row DomainBelongToOrgRow = new Row(3);
                            DomainBelongToOrgRow.setField(0, domain);
                            DomainBelongToOrgRow.setField(1, DigestUtils.md5Hex(company));
                            DomainBelongToOrgRow.setField(2, 0);
                            collector.collect(DomainBelongToOrgRow);
                        }
                    }
                }
            }
        }).name("domain_belong_to_org 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("domain_belong_to_org 边写入").setParallelism(PA1);
    }

    /**
     * fDomain 关联 Org（company）
     * domain_belong_to_org
     */
    public static void insertfDomainBelongToOrg(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("fDomain_belong_to_org")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100).builder();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {
                List<String> relatedDomain = x509Cert.getAssociateDomain();
                String company = x509Cert.getCompany();
                if (!"".equals(company) && relatedDomain.size() > 0) {
                    for (String domain : relatedDomain) {
                        if (domain.startsWith("*.")) {
                            Row fDomainBelongToOrgRow = new Row(3);
                            fDomainBelongToOrgRow.setField(0, domain);
                            fDomainBelongToOrgRow.setField(1, DigestUtils.md5Hex(company));
                            fDomainBelongToOrgRow.setField(2, 0);
                            collector.collect(fDomainBelongToOrgRow);
                        }
                    }
                }
            }
        }).name("fDomain_belong_to_org 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("fDomain_belong_to_org 边写入").setParallelism(PA1);
    }

    /**
     * IP 关联 Org（company）
     * ip_belong_to_org
     */
    public static void insertIpBelongToOrg(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("ip_belong_to_org")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatch(100).builder();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {

                List<String> relatedIp = x509Cert.getAssociateIP();
                String company = x509Cert.getCompany();
                if (!"".equals(company) && relatedIp.size() > 0) {
                    for (String Ip : relatedIp) {
                        Row IpBelongToOrgRow = new Row(3);
                        IpBelongToOrgRow.setField(0, Ip);
                        IpBelongToOrgRow.setField(1, DigestUtils.md5Hex(company));
                        IpBelongToOrgRow.setField(2, 0);
                        collector.collect(IpBelongToOrgRow);
                    }
                }
            }
        }).name("ip_belong_to_org 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("ip_belong_to_org 边写入").setParallelism(PA1);
    }

    /**
     * Cert 关联 Org（company）
     * cert_belong_to_org
     */
    public static void insertCertBelongToOrg(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("cert_belong_to_org")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100).builder();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {
                String company = x509Cert.getCompany();
                if (!"".equals(company)) {
                    Row CertBelongToOrgRow = new Row(3);
                    CertBelongToOrgRow.setField(0, x509Cert.getCertId());
                    CertBelongToOrgRow.setField(1, DigestUtils.md5Hex(company));
                    CertBelongToOrgRow.setField(2, 0);
                    collector.collect(CertBelongToOrgRow);
                }
            }
        }).name("cert_belong_to_org 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("cert_belong_to_org 边写入").setParallelism(PA1);
    }

    /**
     * Domain 关联 Url
     * domain_url_related
     */
    public static void insertDomainUrlRelated(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("domain_url_related")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100).builder();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {

                List<String> relatedDomain = x509Cert.getAssociateDomain();
                List<String> relatedURL = x509Cert.getAssociateURL();
                if (relatedURL.size() > 0 && relatedDomain.size() > 0) {
                    for (String Domain : relatedDomain) {
                        for (String URL : relatedURL) {
                            Row DomainUrlRelatedRow = new Row(3);
                            DomainUrlRelatedRow.setField(0, Domain);
                            DomainUrlRelatedRow.setField(1, URL);
                            DomainUrlRelatedRow.setField(2, 0);
                            collector.collect(DomainUrlRelatedRow);
                        }
                    }
                }
            }
        }).name("domain_url_related 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("domain_url_related 边写入").setParallelism(PA1);
    }

    /**
     * IP 关联 Url
     * ip_url_related
     */
    public static void insertIpUrlRelated(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("ip_url_related")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100).builder();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {

                List<String> relatedIp = x509Cert.getAssociateIP();
                List<String> relatedURL = x509Cert.getAssociateURL();
                if (relatedURL.size() > 0 && relatedIp.size() > 0) {
                    for (String Ip : relatedIp) {
                        for (String URL : relatedURL) {
                            Row IpUrlRelatedRow = new Row(3);
                            IpUrlRelatedRow.setField(0, Ip);
                            IpUrlRelatedRow.setField(1, URL);
                            IpUrlRelatedRow.setField(2, 0);
                            collector.collect(IpUrlRelatedRow);
                        }
                    }
                }
            }
        }).name("ip_url_related 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("ip_url_related 边写入").setParallelism(PA1);
    }

    /**
     * Cert 关联 Url
     * cert_url_related
     */
    public static void insertCertUrlRelated(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("ip_url_related")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100).builder();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {

                List<String> relatedURL = x509Cert.getAssociateURL();
                if (relatedURL.size() > 0) {
                    for (String URL : relatedURL) {
                        Row CertUrlRelatedRow = new Row(3);
                        CertUrlRelatedRow.setField(0, x509Cert.getCertId());
                        CertUrlRelatedRow.setField(1, URL);
                        CertUrlRelatedRow.setField(2, 0);
                        collector.collect(CertUrlRelatedRow);
                    }
                }
            }
        }).name("cert_url_related 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("cert_url_related 边写入").setParallelism(PA1);
    }

    /**
     * 证书标签关系入库
     *
     * @param writeCertModelStream
     */
    public static void insertCertHasLabel(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("has_label")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Arrays.asList("analysis_by", "remark"))
                .setPositions(Arrays.asList(3, 4))
                .setBatchIntervalMs(BATCH_INTERVAL_MS)
                .setBatch(100)
                .builder();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {
                List<String> certTagList = x509Cert.getTagList();
                if (certTagList.size() > 0) {
                    for (String certTag : certTagList) {
                        Row CertUrlRelatedRow = new Row(5);
                        CertUrlRelatedRow.setField(0, x509Cert.getCertId());
                        CertUrlRelatedRow.setField(1, certTag);
                        CertUrlRelatedRow.setField(2, 0);
                        CertUrlRelatedRow.setField(3, "Add_Label");
                        CertUrlRelatedRow.setField(4, "");
                        collector.collect(CertUrlRelatedRow);
                    }
                }
            }
        }).name("has_label 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("has_label 边写入").setParallelism(PA1);
    }

    // zntp
    public static void insertCertCertRelated(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("cert_sign_cert")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchSize(100)
                .setBatchIntervalMs(2000)
                .build();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {
                List<String> fatherCertIdList = x509Cert.getFatherCertIDList();
                if (!fatherCertIdList.isEmpty()) {
                    String fatherCertId = fatherCertIdList.get(0);
                    if(!fatherCertId.isEmpty()){
                        Row CertSignCertRow = new Row(3);
                        CertSignCertRow.setField(0,fatherCertId);
                        CertSignCertRow.setField(1,x509Cert.getCertId());
                        CertSignCertRow.setField(2,0);
                        collector.collect(CertSignCertRow);
                    }
                }
            }
        }).name("cert_sign_cert 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("cert_sign_cert 边写入").setParallelism(PA1);
    }


    public static void insertCertValidateDomain(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("cert_validate_domain")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchSize(100)
                .setBatchIntervalMs(2000)
                .build();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {

                List<String> relatedDomain = x509Cert.getAssociateDomain();
                if(relatedDomain != null && !relatedDomain.isEmpty()){
                    for(String domain:relatedDomain){
                        if(!domain.contains("*.")){
                            Row certRelatedDomainRow = new Row(3);
                            certRelatedDomainRow.setField(0,x509Cert.getCertId());
                            certRelatedDomainRow.setField(1,domain);
                            certRelatedDomainRow.setField(2,0);
                            collector.collect(certRelatedDomainRow);
                        }
                    }
                }
            }
        }).name("cert_validate_domain 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("cert_validate_domain 边写入").setParallelism(PA1);
    }

    public static void insertCertFDomainRelated(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("cert_validate_fDomain")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchSize(100)
                .setBatchIntervalMs(2000)
                .build();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {

                List<String> relatedDomain = x509Cert.getAssociateDomain();
                if(relatedDomain != null && !relatedDomain.isEmpty()){
                    for(String domain:relatedDomain){
                        if(domain.contains("*.")){
                            Row certRelatedFDomainRow = new Row(3);
                            certRelatedFDomainRow.setField(0,x509Cert.getCertId());
                            certRelatedFDomainRow.setField(1,domain);
                            certRelatedFDomainRow.setField(2,0);
                            collector.collect(certRelatedFDomainRow);
                        }
                    }
                }
            }
        }).name("cert_validate_fDomain 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("cert_validate_fDomain 边写入").setParallelism(PA1);
    }

    public static void insertCertIpRelated(SingleOutputStreamOperator<X509Cert> writeCertModelStream) {
        EdgeExecutionOptions executionOptions = new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE)
                .setEdge("cert_related_ip")
                .setSrcIndex(0)
                .setDstIndex(1)
                .setRankIndex(2)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchSize(100)
                .setBatchIntervalMs(2000)
                .build();

        NebulaEdgeBatchOutputFormat outputFormat = new NebulaEdgeBatchOutputFormat(
                graphConnectionProvider, metaConnectionProvider, executionOptions);
        NebulaSinkFunction<Row> nebulaSinkFunction = new NebulaSinkFunction<>(outputFormat);
        DataStream<Row> dataStream = writeCertModelStream.flatMap(new FlatMapFunction<X509Cert, Row>() {
            @Override
            public void flatMap(X509Cert x509Cert, Collector<Row> collector) throws Exception {

                List<String> relatedIp = x509Cert.getAssociateIP();
                if(relatedIp != null && !relatedIp.isEmpty()){
                    for(String ip:relatedIp){
                        Row certRelatedIpRow = new Row(3);
                        certRelatedIpRow.setField(0,x509Cert.getCertId());
                        certRelatedIpRow.setField(1,ip);
                        certRelatedIpRow.setField(2,0);
                        collector.collect(certRelatedIpRow);
                    }
                }
            }
        }).name("cert_related_ip 边生成").setParallelism(PA1);
        dataStream.addSink(nebulaSinkFunction).name("cert_related_ip 边写入").setParallelism(PA1);
    }
}
