package com.geeksec.analysisFunction.analysisEntity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/12/19
 */

@Data
public class UncommonOID {
    /**
     * 目标OID：OID
     */
    @JSONField(name = "OID")
    private String OID;

    /**
     * 目标所属的字段:
     */
    @JSONField(name = "Type")
    private String Type;

    /**
     * 目标OID的详情
     */
    @JSONField(name = "Description")
    private String Description;
}
