package com.geeksec.analysisFunction.certSign;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.flinkTool.sideOutputTag.signOutPutTag;
import com.geeksec.utils.FileUtil;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 * @Date 2023/1/17
 */

public class CertIfSign extends ProcessFunction<X509Cert, Row> {

    public static Map<String,String> validationTypeMap = new HashMap<>();

    @Override
    public void open(Configuration parameters) throws Exception {
        InputStream validationTypeMap_stream = this.getClass().getClassLoader().getResourceAsStream("OID.csv");
        BufferedReader validationTypeMap_buffer = new BufferedReader(new InputStreamReader(validationTypeMap_stream));
        //加载配置文件
        try {
            validationTypeMap = FileUtil.loadValidationTypeMap(validationTypeMap_buffer);
        } catch (Exception e) {
            throw new IOException("file load fail ==>", e);
        }
        super.open(parameters);
    }

    @Override
    public void processElement(X509Cert x509Cert, ProcessFunction<X509Cert, Row>.Context context, Collector<Row> collector) throws Exception {
        List<String> tag_list = x509Cert.getTagList();
        Row sign_row = new Row(2);
        sign_row.setField(0,x509Cert);
        sign_row.setField(1,new HashMap<String,X509Cert>());
        if (tag_list.contains("Self Signed Cert")){
            HashMap<String,String> x509Extension = x509Cert.getExtension();
            if (x509Extension!= null){
                String basicConstraints = x509Extension.getOrDefault("basicConstraints","No basicConstraints");
                if (!basicConstraints.contains("CA:TRUE")){
                    tag_list.add("Unknown CA");
                    x509Cert.setTagList(tag_list);
                }
            }
            List<String> FatherCertID = new ArrayList<>();
            FatherCertID.add(x509Cert.getASN1SHA1());
            x509Cert.setFatherCertIDList(FatherCertID);
            context.output(signOutPutTag.goto_score, sign_row);
        }else {
            context.output(signOutPutTag.goto_sign, sign_row);
        }
    }
}
