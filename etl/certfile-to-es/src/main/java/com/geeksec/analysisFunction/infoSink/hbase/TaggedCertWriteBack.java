package com.geeksec.analysisFunction.infoSink.hbase;

import static com.geeksec.analysisFunction.certInfoAll.CertTag2AfterSignMapFunction.*;
import static com.geeksec.analysisFunction.hbaseKnowledgeCollision.KnowledgeCollisionMapFunction.*;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.dbConnect.hbase.HbaseUtils;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
* <AUTHOR>
* @Date  2024/10/18
*/

public class TaggedCertWriteBack extends RichSinkFunction<JSONObject> {

    private static final Logger logger = LoggerFactory.getLogger(TaggedCertWriteBack.class);

    private static transient GenericObjectPool<Connection> HbasePool = null;

    private static final String HBASE_CERT_TABLE_NAME = "CERT";

    @Override
    public void close() throws Exception {
        super.close();
        if (HbasePool!=null){
            HbasePool.close();
        }
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // Hbase 初始化
        HbasePool = HbaseUtils.initHbasePool();
        logger.info("生成 HbasePool 成功! {}", HbasePool.getNumIdle(), HbasePool.hashCode());
    }


    /**
    * APT28_TAG, APT29_TAG, APTPatchWork_TAG, TOR_V3_TAG, TOR_V2_TAG;
    * */
    @Override
    public void invoke(JSONObject value, Context context) throws Exception {
        String rowKey = (String) value.getOrDefault("ASN1SHA1","");
        List<String> labelsValue = (List<String>) value.getOrDefault("Labels",new ArrayList<>());

        Connection connection = null;

        try{
            connection = HbaseUtils.getHbaseClient(HbasePool);
            Table table = connection.getTable(TableName.valueOf(HBASE_CERT_KNOWLEDGE_TABLE_NAME));

            // APT知识库回写
            if(labelsValue.contains(APT28_TAG)){
                Put put = new Put(Bytes.toBytes(rowKey));
                put.addColumn(Bytes.toBytes(APT_COLUMN), Bytes.toBytes(APT_KEY), Bytes.toBytes("APT28"));
                table.put(put);
            }
            if(labelsValue.contains(APT29_TAG)){
                Put put = new Put(Bytes.toBytes(rowKey));
                put.addColumn(Bytes.toBytes(APT_COLUMN), Bytes.toBytes(APT_KEY), Bytes.toBytes("APT29"));
                table.put(put);
            }
            if(labelsValue.contains(APTPatchWork_TAG)){
                Put put = new Put(Bytes.toBytes(rowKey));
                put.addColumn(Bytes.toBytes(APT_COLUMN), Bytes.toBytes(APT_KEY), Bytes.toBytes("APTPatchWork"));
                table.put(put);
            }

            // 威胁知识库回写
            if(labelsValue.contains(TOR_V3_TAG) || labelsValue.contains(TOR_V2_TAG)){
                Put put = new Put(Bytes.toBytes(rowKey));
                put.addColumn(Bytes.toBytes(THREAT_COLUMN), Bytes.toBytes(THREAT_KEY), Bytes.toBytes("Tor"));
                table.put(put);
            }
        }catch (Exception e){
            logger.error("hbase更新失败，{}",e.toString());
        }finally{
            if(connection!=null){
                HbaseUtils.returnHbaseClient(connection,HbasePool);
            }
        }

    }

}
