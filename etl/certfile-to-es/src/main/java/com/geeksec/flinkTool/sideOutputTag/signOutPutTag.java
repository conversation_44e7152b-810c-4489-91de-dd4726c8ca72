package com.geeksec.flinkTool.sideOutputTag;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * <AUTHOR>
 * @Date 2023/1/17
 */

public class signOutPutTag {
    public static final OutputTag<Row> goto_sign = new OutputTag<>("stop_sign", TypeInformation.of(Row.class));
    public static final OutputTag<Row> goto_score = new OutputTag<>("continue_sign",TypeInformation.of(Row.class));
    public static final OutputTag<Row> stop_sign1 = new OutputTag<>("stop_sign1", TypeInformation.of(Row.class));
    public static final OutputTag<Row> continue_sign1 = new OutputTag<>("continue_sign1",TypeInformation.of(Row.class));
    public static final OutputTag<Row> stop_sign2 = new OutputTag<>("stop_sign2", TypeInformation.of(Row.class));
    public static final OutputTag<Row> continue_sign2 = new OutputTag<>("continue_sign2",TypeInformation.of(Row.class));
    public static final OutputTag<Row> stop_sign3 = new OutputTag<>("stop_sign3", TypeInformation.of(Row.class));
    public static final OutputTag<Row> continue_sign3 = new OutputTag<>("continue_sign3",TypeInformation.of(Row.class));
    public static final OutputTag<Row> stop_sign4 = new OutputTag<>("stop_sign4", TypeInformation.of(Row.class));
    public static final OutputTag<Row> continue_sign4 = new OutputTag<>("continue_sign4",TypeInformation.of(Row.class));
    public static final OutputTag<Row> stop_sign5 = new OutputTag<>("stop_sign5",TypeInformation.of(Row.class));
    public static final OutputTag<Row> continue_sign5 = new OutputTag<>("continue_sign5",TypeInformation.of(Row.class));


}
