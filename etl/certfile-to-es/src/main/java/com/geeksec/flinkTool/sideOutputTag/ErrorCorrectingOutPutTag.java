package com.geeksec.flinkTool.sideOutputTag;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.util.OutputTag;

/**
 * <AUTHOR>
 * @Date 2023/7/5
 */

public class ErrorCorrectingOutPutTag {
    public static final OutputTag<X509Cert> errorUserCert = new OutputTag<>("errorUserCert", TypeInformation.of(X509Cert.class));
    public static final OutputTag<X509Cert> normalUserCert = new OutputTag<>("normalUserCert", TypeInformation.of(X509Cert.class));
    public static final OutputTag<X509Cert> errorScanCert = new OutputTag<>("errorScanCert", TypeInformation.of(X509Cert.class));
    public static final OutputTag<X509Cert> normalScanCert = new OutputTag<>("normalScanCert", TypeInformation.of(X509Cert.class));
    public static final OutputTag<X509Cert> errorPushDataCert = new OutputTag<>("errorPushDataCert", TypeInformation.of(X509Cert.class));
    public static final OutputTag<X509Cert> normalPushDataCert = new OutputTag<>("normalPushDataCert", TypeInformation.of(X509Cert.class));

    public static final OutputTag<X509Cert> SuccessNumNegativeCert = new OutputTag<>("errorUserCert", TypeInformation.of(X509Cert.class));
    public static final OutputTag<X509Cert> FailNumNegativeCert = new OutputTag<>("normalUserCert", TypeInformation.of(X509Cert.class));
    public static final OutputTag<X509Cert> SuccessNumPositiveCert = new OutputTag<>("errorScanCert", TypeInformation.of(X509Cert.class));
    public static final OutputTag<X509Cert> FailNumPositiveCert = new OutputTag<>("normalScanCert", TypeInformation.of(X509Cert.class));
    public static final OutputTag<X509Cert> SuccessReverseCert = new OutputTag<>("errorPushDataCert", TypeInformation.of(X509Cert.class));
    public static final OutputTag<X509Cert> FailReverseCert = new OutputTag<>("normalPushDataCert", TypeInformation.of(X509Cert.class));

    public static final OutputTag<X509Cert> normalSystemCert = new OutputTag<>("normalSystemCert", TypeInformation.of(X509Cert.class));
}
