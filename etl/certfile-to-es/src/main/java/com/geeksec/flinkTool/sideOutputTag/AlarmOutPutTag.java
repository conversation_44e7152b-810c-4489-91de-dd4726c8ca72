package com.geeksec.flinkTool.sideOutputTag;

import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.util.OutputTag;

/**
 * <AUTHOR>
 * @Date 2022/12/29
 */
public class AlarmOutPutTag {
    public static final OutputTag<X509Cert> Alarm_APT_CERT_KNOWLEDGE_ALARM = new OutputTag<>("Alarm_APT_CERT_KNOWLEDGE", TypeInformation.of(X509Cert.class));
    public static final OutputTag<X509Cert> Alarm_THREAT_CERT_KNOWLEDGE_ALARM = new OutputTag<>("Alarm_THREAT_CERT_KNOWLEDGE_ALARM", TypeInformation.of(X509Cert.class));
}
