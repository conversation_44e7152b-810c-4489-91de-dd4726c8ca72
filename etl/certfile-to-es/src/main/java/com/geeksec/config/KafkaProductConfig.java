package com.geeksec.config;

import java.util.Properties;
import org.apache.flink.configuration.Configuration;
import org.apache.kafka.clients.consumer.ConsumerConfig;

/**
 * @Author: GuanHao
 * @Date: 2022/6/13 10:03
 * @Description： <Functions List>
 */
public class KafkaProductConfig {

    public static Properties getKafkaConsumerConfig(Configuration conf) {
        Properties kafkaConsumer = new Properties();

        //封装kafka的连接地址
        kafkaConsumer.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, conf.getString("kafka.host","localhost") + ":" + conf.getString("kafka.port","9092"));
        //指定消费者id
        kafkaConsumer.put(ConsumerConfig.GROUP_ID_CONFIG, conf.getString("kafka.group.id","error_group"));
        // 设置从最早offset开始读取
        kafkaConsumer.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG,conf.getString("auto.offset.reset","latest"));
        // 其他参数
        kafkaConsumer.put(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG, "60000");
        kafkaConsumer.put("batch.size", 409600);
        kafkaConsumer.put("linger.ms", 300);
        kafkaConsumer.put("buffer.memory", 256 * 1024 * 1024);
        kafkaConsumer.put("max.request.size", 10 * 1024 * 1024);
        kafkaConsumer.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG,5000);

        return kafkaConsumer;
    }
}
