package com.geeksec.config;

import com.geeksec.utils.FileUtil;
import java.util.Properties;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/6/29
 */

public class globalConfig {
    protected static final Logger LOG = LoggerFactory.getLogger(globalConfig.class);

    /**
     * 初始化用户证书导入kafka，全局参数
     *
     * @return 参数
     */
    public static Configuration initGlobalJobParameters(StreamExecutionEnvironment env) {
        LOG.info("Initialization global parameters.");
        Configuration conf = new Configuration();

        Properties properties = FileUtil.getProperties("/config.properties");

        String zooHost = properties.getOrDefault("cert.cluster.zookeeper.host", "localhost").toString();
        conf.setString("zoo.host", zooHost);

        int zooPort = Integer.parseInt(properties.getOrDefault("cert.cluster.zookeeper.port", "2181").toString());
        conf.setInteger("zoo.post", zooPort);

        Object lmdbPath = properties.get("lmdb.path");
        if (!ObjectUtils.allNotNull(lmdbPath)) {
            throw new NullPointerException("lmdb path is empty.");
        }
        conf.setString("lmdb.path", lmdbPath.toString());

        String esHost = properties.getOrDefault("cert.cluster.elasticsearch.host", "localhost").toString();
        conf.setString("es.host", esHost);

        int esPort = Integer.parseInt(properties.getOrDefault("cert.cluster.elasticsearch.port", "9200").toString());
        conf.setInteger("es.post", esPort);

        Object userIndex = properties.get("cert.cluster.elasticsearch.index.user");
        if (!ObjectUtils.allNotNull(userIndex)) {
            throw new NullPointerException("ES user index is empty.");
        }
        conf.setString("es.user.index", userIndex.toString());

        Object systemIndex = properties.get("cert.cluster.elasticsearch.index.system");
        if (!ObjectUtils.allNotNull(systemIndex)) {
            throw new NullPointerException("ES system index is empty.");
        }
        conf.setString("es.system.index", systemIndex.toString());

        String kafkaHost = properties.getOrDefault("cert.kafka.bootstrap.servers.host", "localhost").toString();
        conf.setString("kafka.host", kafkaHost);

        int kafkaPort = Integer.parseInt(properties.getOrDefault("cert.kafka.bootstrap.servers.port", "9092").toString());
        conf.setInteger("kafka.port", kafkaPort);

        Object kafkaGroup = properties.get("cert.kafka.group.id");
        if (!ObjectUtils.allNotNull(kafkaGroup)) {
            throw new NullPointerException("Kafka group id is empty.");
        }
        conf.setString("kafka.group.id", kafkaGroup.toString());

        Object kafkaSystemGroup = properties.get("cert.kafka.System_group.id");
        if (!ObjectUtils.allNotNull(kafkaSystemGroup)) {
            throw new NullPointerException("Kafka System group id is empty.");
        }
        conf.setString("kafka_system.group.id", kafkaSystemGroup.toString());

        Object kafkaTopic = properties.get("cert.kafka.topic.name");
        if (!ObjectUtils.allNotNull(kafkaTopic)) {
            throw new NullPointerException("Kafka topic is empty.");
        }
        conf.setString("kafka.topic", kafkaTopic.toString());

        Object kafkaTopic_system = properties.get("cert.kafka.system_topic.name");
        if (!ObjectUtils.allNotNull(kafkaTopic_system)) {
            throw new NullPointerException("Kafka System topic is empty.");
        }
        conf.setString("kafka_system.topic", kafkaTopic_system.toString());

        String kafkaOffsetMode = properties.getOrDefault("kafka.auto.offset.reset.mode", "latest").toString();
        conf.setString("auto.offset.reset", kafkaOffsetMode);

        String redisHost = properties.getOrDefault("cert.cluster.redis.host", "localhost").toString();
        conf.setString("redis.host", redisHost);

        Integer redisPort = Integer.valueOf(properties.getOrDefault("cert.cluster.redis.port", "6379").toString());
        conf.setInteger("redis.port", redisPort);

        String mysqlHost = properties.getOrDefault("mysql.database.host","mysql").toString();
        if (!ObjectUtils.allNotNull(mysqlHost)) {
            throw new NullPointerException("mysqlHost is empty.");
        }
        conf.setString("mysql.host", mysqlHost);

        String mysqlUser = properties.getOrDefault("mysql.database.user","root").toString();
        if (!ObjectUtils.allNotNull(mysqlUser)) {
            throw new NullPointerException("mysqlUser is empty.");
        }
        conf.setString("mysql.user", mysqlUser);

        String mysqlPassword = properties.getOrDefault("mysql.database.password","simpleuse23306p").toString();
        if (!ObjectUtils.allNotNull(mysqlPassword)) {
            throw new NullPointerException("mysqlPassword is empty.");
        }
        conf.setString("mysql.password", mysqlPassword);

        env.getConfig().setGlobalJobParameters(conf);
        return conf;
    }
}
