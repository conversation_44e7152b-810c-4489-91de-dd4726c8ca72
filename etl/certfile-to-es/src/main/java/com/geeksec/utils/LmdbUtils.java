//package com.geeksec.utils;
//
//import com.alibaba.fastjson.JSONObject;
//import com.geeksec.bigdata.PoolTest.LmdbPoolFactory;
//import com.geeksec.bigdata.lmdb.LmdbClient;
//import org.apache.commons.lang3.ObjectUtils;
//import org.apache.commons.pool.impl.GenericObjectPool;
//import org.apache.http.HttpEntity;
//import org.apache.http.client.config.RequestConfig;
//import org.apache.http.client.methods.CloseableHttpResponse;
//import org.apache.http.client.methods.HttpPost;
//import org.apache.http.entity.StringEntity;
//import org.apache.http.impl.client.CloseableHttpClient;
//import org.apache.http.impl.client.HttpClients;
//import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.io.IOException;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @Date 2022/10/27
// */
//
//public class LmdbUtils {
//    public static Logger logger = LoggerFactory.getLogger(LmdbUtils.class);
//
//    public static GenericObjectPool<LmdbClient> initLmdbPool(){
//        GenericObjectPool.Config LmdbPoolConfig = new GenericObjectPool.Config();
//        LmdbPoolConfig.maxActive=1000;
//        LmdbPoolConfig.maxIdle=1000;
//        LmdbPoolConfig.maxWait=60;
//        LmdbPoolConfig.minIdle=10;
//        LmdbPoolConfig.testOnBorrow=false;
//        LmdbPoolConfig.testOnReturn=false;
//        LmdbPoolConfig.whenExhaustedAction=1;
//        LmdbPoolFactory lmdbPoolFactory = new LmdbPoolFactory();
//        return new GenericObjectPool<>(lmdbPoolFactory,LmdbPoolConfig);
//    }
//
//    public static LmdbClient getClient(GenericObjectPool<LmdbClient> clientPool) throws Exception{
//        LmdbClient lmdbClient = clientPool.borrowObject();
//        logger.info("从池中取一个对象"+lmdbClient);
//        return lmdbClient;
//    }
//
//    public static void returnClient(LmdbClient client,GenericObjectPool<LmdbClient> clientPool) throws Exception{
//        logger.info("使用完毕后，归还对象"+client);
//        clientPool.returnObject(client);
//    }
//
//    public static Map<String, String> queryData(List<String> fatherRowKeys, LmdbClient lmdbClient) {
//        HashMap<String, String> queryData = new HashMap<>(10);
//
//        //优先查询redis, redis个数为0 再远程查询
//        JSONObject responseData = HttpUtils.sendPost(LmdbClient.GetLmdbPath(lmdbClient), fatherRowKeys);
//        if ((ObjectUtils.isNotEmpty(responseData)) && ("true".equals(responseData.getString("status")))) {
//            String message = responseData.getString("message");
//            if (ObjectUtils.isNotEmpty(message)) {
//                JSONObject messageJson = JSONObject.parseObject(message);
//                for (Map.Entry<String, Object> next : messageJson.entrySet()) {
//                    String key = next.getKey();
//                    String certBase64 = String.valueOf(next.getValue());
//                    queryData.put(key, certBase64);
//                }
//            }
//        }
//        return queryData;
//    }
//
//    private HttpEntity Post(String url, List<String> paramList) {
//
//        // 创建httpClient实例对象
//        try {
//            PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
//            cm.setMaxTotal(100);
//            cm.setDefaultMaxPerRoute(10);
//            //设置数据传输的最长时间
//            CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(cm).build();
//            HttpPost httpPost = new HttpPost(url);
//
//            RequestConfig requestConfig = RequestConfig.custom()
//                    //设置创建连接的最长时间
//                    .setConnectTimeout(10000)
//                    //设置获取连接的最长时间
//                    .setConnectionRequestTimeout(500)
//                    //设置数据传输的最长时间
//                    .setSocketTimeout(10 * 1000)
//                    .build();
//            httpPost.setConfig(requestConfig);
//
//            // 创建post请求方法实例对象
//            httpPost.setHeader("Content-Type", "application/json;charset=utf-8");
//            StringEntity requestEntity = new StringEntity(JSONObject.toJSONString(paramList), "utf-8");
//            httpPost.setEntity(requestEntity);
//            CloseableHttpResponse response = httpClient.execute(httpPost);
//
//            return response.getEntity();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        return null;
//    }
//
//
//}
