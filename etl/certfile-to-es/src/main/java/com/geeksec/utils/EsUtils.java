package com.geeksec.utils;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysisFunction.analysisEntity.cert.X509Cert;
import com.geeksec.dbConnect.PoolTest.ESPoolFactory;
import com.geeksec.dbConnect.PoolTest.ESPoolFactoryScan;
import java.io.IOException;
import java.util.*;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.elasticsearch.ElasticsearchSecurityException;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.*;
import org.elasticsearch.action.support.replication.ReplicationResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.common.xcontent.XContentFactory;
import org.elasticsearch.index.IndexNotFoundException;
import org.elasticsearch.index.query.*;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2022/10/26
 */

public class EsUtils {
    public static Logger logger = LoggerFactory.getLogger(EsUtils.class);
    private static Properties properties = FileUtil.getProperties("/config.properties");
    public static final String TaskId = properties.getProperty("ES.TaskId");
    public static final String BatchId = properties.getProperty("ES.BatchId");
    public static GenericObjectPool<RestHighLevelClient> initEsPool() {
        GenericObjectPool.Config EsPoolConfig = new GenericObjectPool.Config();
        EsPoolConfig.maxActive = 20;
        EsPoolConfig.maxIdle = 20;
        EsPoolConfig.maxWait = 100;
        EsPoolConfig.minIdle = 10;
        EsPoolConfig.testOnBorrow = false;
        EsPoolConfig.testOnReturn = false;
        EsPoolConfig.whenExhaustedAction = 1;
        ESPoolFactory esPoolFactory = new ESPoolFactory();
        return new GenericObjectPool<>(esPoolFactory, EsPoolConfig);
    }

    public static GenericObjectPool<RestHighLevelClient> initScanEsPool() {
        GenericObjectPool.Config EsPoolConfig = new GenericObjectPool.Config();
        EsPoolConfig.maxActive = 20;
        EsPoolConfig.maxIdle = 20;
        EsPoolConfig.maxWait = 100;
        EsPoolConfig.minIdle = 10;
        EsPoolConfig.testOnBorrow = false;
        EsPoolConfig.testOnReturn = false;
        EsPoolConfig.whenExhaustedAction = 1;
        ESPoolFactoryScan esPoolFactoryScan = new ESPoolFactoryScan();
        return new GenericObjectPool<>(esPoolFactoryScan, EsPoolConfig);
    }

    public static RestHighLevelClient getClient(GenericObjectPool<RestHighLevelClient> clientPool) throws Exception {
        RestHighLevelClient restHighLevelClient = clientPool.borrowObject();
//        logger.info("从池中取一个对象"+restHighLevelClient);
        return restHighLevelClient;
    }

    public static void returnClient(RestHighLevelClient client, GenericObjectPool<RestHighLevelClient> clientPool) throws Exception {
//        logger.info("使用完毕后，归还对象"+client);
        clientPool.returnObject(client);
    }

    //批量写入
    public static void createMapDocumentBulk(String index, List<JSONObject> certJsonList, RestHighLevelClient client) {
        BulkRequest request = new BulkRequest();
        try {
            for (JSONObject certJson : certJsonList) {
                IndexRequest indexRequest = new IndexRequest();
                request.add(indexRequest.index(index).source(certJson));
            }

            client.bulk(request, RequestOptions.DEFAULT);
        } catch (ElasticsearchSecurityException | IOException e) {
            e.printStackTrace();
        }
    }

    public static HashMap<String, Map> queryCACert(String index, String notAfter, String notBefore,
                                                   String measures, String field, String queryString,
                                                   String subjectMD5, String issuerMD5, RestHighLevelClient client) {

        HashMap<String, Map> result = new HashMap<>();
        Integer number = 0;
        SearchResponse response = null;

        //具有CA权限的
        RegexpQueryBuilder regexpQueryBuilder = QueryBuilders.regexpQuery("Extension" + "." + "basicConstraints" + ".keyword", ".*CA:TRUE.*");

        //大于等于父证书NotAfter,比父证书先结束
        RangeQueryBuilder notAfterRange = QueryBuilders.rangeQuery("NotAfter").gte(notAfter);
        //小于等父证书NotBefore，比父证书后开始
        RangeQueryBuilder notBeforeRange = QueryBuilders.rangeQuery("NotBefore").lte(notBefore);

        //父证书匹配规则
        TermQueryBuilder termQueryBuilder = QueryBuilders.termQuery(measures + "." + field + ".keyword", queryString);
        TermQueryBuilder termQueryBuilder2 = QueryBuilders.termQuery(subjectMD5, issuerMD5);

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .must(regexpQueryBuilder)
                .must(notBeforeRange)
                .must(notAfterRange)
                .should(termQueryBuilder)
                .should(termQueryBuilder2)
                .minimumShouldMatch("1");//如果同时有should和must的话，should就代表有或者没有都可以，故使用MiniMumShouldMatch来至少满足一项should

        try {
            SearchRequest request = new SearchRequest();
            request.indices(index);

            SearchSourceBuilder builder = new SearchSourceBuilder();
            builder.query(boolQuery);
            request.source(builder);

            response = client.search(request, RequestOptions.DEFAULT);

        } catch (ElasticsearchSecurityException e) {
            logger.error("ES is abnormal during the search, query content is: " + "");
            return result;
        } catch (IndexNotFoundException e1) {
            logger.error("index not found exception: " + index);
            return result;
        } catch (IOException e) {
            e.printStackTrace();
        }

        SearchHits searchHits = response.getHits();
        logger.debug("Match query: [" + measures + "." + field + "] [" + queryString + "].");
        logger.debug("Total match found:" + searchHits.getTotalHits());
        SearchHit[] hits = searchHits.getHits();
        for (SearchHit searchHit : hits) {
            number += 1;
            result.put((String) searchHit.getSourceAsMap().get("ASN1SHA1"), searchHit.getSourceAsMap());
        }
        return result;
    }

    public static HashMap<String, Map> matchQuery(String indices, String field, String queryString,
                                                  String notField, String notValue, RestHighLevelClient client) {

        SearchResponse response = null;
        HashMap<String, Map> result = new HashMap<>();

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery(field, queryString));

        if (notField != null) {
            boolQueryBuilder.mustNot(QueryBuilders.termQuery(notField, notValue));
        }

        try {
            SearchRequest request = new SearchRequest();
            request.indices(indices);

            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(boolQueryBuilder);
            request.source(sourceBuilder);
            response = client.search(request, RequestOptions.DEFAULT);

            SearchHits searchHits = response.getHits();
            logger.debug("Match query: [" + field + "] [" + queryString + "].");
            logger.debug("Total match found:" + searchHits.getTotalHits());
            SearchHit[] hits = searchHits.getHits();
            Integer number = 0;
            for (SearchHit searchHit : hits) {
                number += 1;
                result.put((String) searchHit.getSourceAsMap().get("ASN1SHA1"), searchHit.getSourceAsMap());
            }
        } catch (ElasticsearchSecurityException e) {
            logger.error("ES is abnormal during the search, query content is: " + queryString);
            return result;
        } catch (IndexNotFoundException e1) {
            logger.error("index not found exception: " + indices);
            return result;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    public static HashMap<String, Map> nestedQuery(String indices, String measures, String subjectMD5,
                                                   String issuerMD5, String field, String queryString,
                                                   String notField, String notValue, RestHighLevelClient client) {
        HashMap<String, Map> result = new HashMap<>();
        Integer number = 0;
        SearchResponse response = null;

        TermQueryBuilder termQueryBuilder = QueryBuilders.termQuery(measures + "." + field + ".keyword", queryString);
        TermQueryBuilder termQueryBuilder2 = QueryBuilders.termQuery(subjectMD5, issuerMD5);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .should(termQueryBuilder)
                .should(termQueryBuilder2);

        if (notField != null) {
            boolQuery.mustNot(QueryBuilders.termQuery(notField, notValue));
        }

        try {
            SearchRequest request = new SearchRequest();
            request.indices(indices);

            SearchSourceBuilder builder = new SearchSourceBuilder();
            builder.query(boolQuery);
            request.source(builder);

            response = client.search(request, RequestOptions.DEFAULT);

        } catch (ElasticsearchSecurityException e) {
            logger.error("ES is abnormal during the search, query content is: " + queryString);
            return result;
        } catch (IndexNotFoundException e1) {
            logger.error("index not found exception: " + indices);
            return result;
        } catch (IOException e) {
            e.printStackTrace();
        }

        SearchHits searchHits = null;
        if (response != null) {
            searchHits = response.getHits();
            logger.debug("Match query: [" + measures + "." + field + "] [" + queryString + "].");
            logger.debug("Total match found:" + searchHits.getTotalHits());
            SearchHit[] hits = searchHits.getHits();
            System.out.println(searchHits.getTotalHits());
            for (SearchHit searchHit : hits) {
                number += 1;
                result.put((String) searchHit.getSourceAsMap().get("ASN1SHA1"), searchHit.getSourceAsMap());
            }
        }
        return result;
    }

    public static void createBulkAsyncUpdate(String index, List<JSONObject> certJsonList, RestHighLevelClient client) {
        BulkRequest request = new BulkRequest();
        try {
            for (JSONObject certJson : certJsonList) {
                final XContentBuilder builder = XContentFactory.jsonBuilder()
                        .startObject();

                final String id = certJson.getString("ASN1SHA1");

                for (String key : certJson.keySet()) {
                    builder.field(key, certJson.get(key));
                }
                builder.endObject();

                List<String> tagList = (List<String>) certJson.getOrDefault("Labels", null);
//                if (tagList.contains("207")){
//                    IndexRequest indexRequest = new IndexRequest().index(index).type("CollisionCert").source(certJson)
//                            .opType("index").id(id);
//                    UpdateRequest updateRequest = new UpdateRequest().index(index).type("CollisionCert").doc(builder)
//                            .id(id).upsert(indexRequest);
//                    request.add(updateRequest);
//                }else{
                IndexRequest indexRequest = new IndexRequest().index(index).source(certJson)
                        .opType("index").id(id);
                UpdateRequest updateRequest = new UpdateRequest().index(index).doc(builder)
                        .id(id).upsert(indexRequest);
                request.add(updateRequest);
//                }
            }

            client.bulkAsync(request, RequestOptions.DEFAULT, new ActionListener<BulkResponse>() {
                @Override
                public void onResponse(BulkResponse bulkItemResponses) {
                    System.out.println("展示Response");
                    logger.info(String.valueOf(bulkItemResponses));
                }

                @Override
                public void onFailure(Exception e) {
                    System.out.println("展示Failure");
                    logger.error(String.valueOf(e));
                }
            });
        } catch (ElasticsearchSecurityException e) {
            e.printStackTrace();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void createBulkAsyncIndex(String index, List<JSONObject> certJsonList, RestHighLevelClient client) {
        BulkRequest request = new BulkRequest();
        try {
            for (JSONObject certJson : certJsonList) {
                IndexRequest indexRequest = new IndexRequest().index(index).source(certJson).opType("index");
                request.add(indexRequest);
            }

            client.bulkAsync(request, RequestOptions.DEFAULT, new ActionListener<BulkResponse>() {
                @Override
                public void onResponse(BulkResponse bulkItemResponses) {
                    BulkItemResponse[] bulkItemResponse = bulkItemResponses.getItems();
                    for (BulkItemResponse response : bulkItemResponse) {
                        String id = response.getId();
                        DocWriteResponse write_response = response.getResponse();
                        ReplicationResponse.ShardInfo info = write_response.getShardInfo();
                        String status = write_response.getResult().getLowercase();
                        if (status.equals("updated")) {
                            if (info.getSuccessful() == 1) {
                                logger.info("成功覆写证书id=——{}——", id);
                            } else {
                                logger.info("覆写失败id=——{}——,Failures=——{}——", id, Arrays.toString(info.getFailures()));
                            }
                        } else if (status.equals("created")) {
                            if (info.getSuccessful() == 1) {
                                logger.info("成功创建证书id=——{}——", id);
                            } else {
                                logger.info("创建失败id=——{}——,Failures=——{}——", id, Arrays.toString(info.getFailures()));
                            }
                        }
                    }
                }

                @Override
                public void onFailure(Exception e) {
                    logger.error("批量写入出现错误，报错——{}——,导入的证书doc：{}", String.valueOf(e),certJsonList);
                }
            });
        } catch (ElasticsearchSecurityException e) {
            e.printStackTrace();
        }
    }

    public static HashMap<String, Map> MultiMatchQuery(String indices, String field, List<String> queryStringList,
                                                       String notField, String notValue, RestHighLevelClient client) {

        HashMap<String, Map> result = new HashMap<>();

        MultiSearchRequest multiSearchRequest = new MultiSearchRequest();
        for (String queryString : queryStringList) {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termQuery(field, queryString));
            if (notField != null) {
                boolQueryBuilder.mustNot(QueryBuilders.termQuery(notField, notValue));
            }
            SearchRequest request = new SearchRequest();
            request.indices(indices);

            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(boolQueryBuilder);
            request.source(sourceBuilder);
            multiSearchRequest.add(request);
        }
        MultiSearchResponse multiSearchResponse = null;

        try {
            multiSearchResponse = client.msearch(multiSearchRequest, RequestOptions.DEFAULT);

            List<MultiSearchResponse.Item> Responses = Arrays.asList(multiSearchResponse.getResponses());
            for (MultiSearchResponse.Item response : Responses) {
                SearchResponse searchResponse = response.getResponse();
                if (searchResponse == null) {
                    continue;
                }
                SearchHits searchHits = searchResponse.getHits();
                SearchHit[] hits = searchHits.getHits();
                Integer number = 0;
                for (SearchHit searchHit : hits) {
                    number += 1;
                    result.put((String) searchHit.getSourceAsMap().get("ASN1SHA1"), searchHit.getSourceAsMap());
                }
            }
        } catch (ElasticsearchSecurityException e) {
            logger.error("ES is abnormal during the search, query content is: ");
            return result;
        } catch (IndexNotFoundException e1) {
            logger.error("index not found exception: " + indices);
            return result;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    public static String scanESHash(int length, String HashType, LinkedList<String> PositiveHash,
                                    RestHighLevelClient client) {
        try {
            Scroll scroll = new Scroll(TimeValue.timeValueMinutes(1L)); // 设置滚动时间间隔
            SearchRequest searchRequest = new SearchRequest("cert_user");
            searchRequest.scroll(scroll);

            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

            // 只匹配文档数量一致的
            boolQuery.must(QueryBuilders.existsQuery(HashType)) // Ensure the field exists in the document
                    .must(QueryBuilders.existsQuery("ASN1MD5"))// 确保没检查到错误证书
                    .must(QueryBuilders.scriptQuery(
                            // Use a script to check the size of the list field
                            Script.parse("doc['" + HashType + "'].size() == " + length)
                    ));

            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.size(1000); // 每次从ES获取的文档数量

            searchRequest.source(searchSourceBuilder);

            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            String scrollId = searchResponse.getScrollId();

            while (true) {
                SearchHits hits = searchResponse.getHits();

                // 处理每个文档
                for (SearchHit hit : hits) {
                    Map<String, Object> sourceAsMap = hit.getSourceAsMap();
                    List<String> fieldValue = (List<String>) sourceAsMap.get(HashType);
                    // 处理Hash列表
                    int wrong_count = 0;
                    for(int i=0;i<length;i++){
                        if(!PositiveHash.get(i).equals(fieldValue.get(i))){
                            wrong_count+=1;
                        }
                        if(wrong_count>1){
                            break;
                        }
                        if(wrong_count==1 && i==length-1){
                            return (String) sourceAsMap.get("ASN1SHA1");
                        }
                    }
                }

                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                scrollRequest.scroll(scroll);
                searchResponse = client.scroll(scrollRequest, RequestOptions.DEFAULT);
                scrollId = searchResponse.getScrollId();

                if (hits.getHits().length == 0) {
                    break; // 没有更多的文档，结束循环
                }
            }
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);
            ClearScrollResponse clearScrollResponse = client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String scanESHashNum(int length, String HashType, LinkedList<String> PositiveHash,
                                    RestHighLevelClient client) {
        try {
            Scroll scroll = new Scroll(TimeValue.timeValueMinutes(1L)); // 设置滚动时间间隔
            SearchRequest searchRequest = new SearchRequest("cert_user");
            searchRequest.scroll(scroll);

            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

            // 只匹配文档数量+-1的
            boolQuery.must(QueryBuilders.existsQuery(HashType)) // Ensure the field exists in the document
                    .must(QueryBuilders.existsQuery("ASN1MD5"))// 确保没检查到错误证书
                    .must(QueryBuilders.scriptQuery(
                            // Use a script to check the size of the list field
                            Script.parse("doc['" + HashType + "'].size() >= " + (length-1) + " && doc['" + HashType + "'].size() <= " + (length+1))
                    ));

            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.size(1000); // 每次从ES获取的文档数量

            searchRequest.source(searchSourceBuilder);

            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            String scrollId = searchResponse.getScrollId();

            while (true) {
                SearchHits hits = searchResponse.getHits();

                // 处理每个文档
                for (SearchHit hit : hits) {
                    Map<String, Object> sourceAsMap = hit.getSourceAsMap();
                    List<String> fieldValue = (List<String>) sourceAsMap.get(HashType);
                    int EsLength = fieldValue.size();
                    // 处理Hash列表
                    boolean status = true;
                    int trueCount = 0;
                    int changeCount = 0;
                    for(int i=0;i<Math.min(length,EsLength);i++){
                        if (i==0){
                            status = fieldValue.get(i).equals(PositiveHash.get(i));
                        }
                        if(PositiveHash.get(i).equals(fieldValue.get(i))!=status){
                            changeCount+=1;
                            status = !status;
                        }
                        if(PositiveHash.get(i).equals(fieldValue.get(i))){
                            trueCount+=1;
                        }
                        if(changeCount>1){
                            break;
                        }
                    }
                    if(changeCount==1 || (trueCount==Math.min(length,EsLength)&&length!=EsLength) ){
                        return (String) sourceAsMap.get("ASN1SHA1");
                    }
                }

                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                scrollRequest.scroll(scroll);
                searchResponse = client.scroll(scrollRequest, RequestOptions.DEFAULT);
                scrollId = searchResponse.getScrollId();

                if (hits.getHits().length == 0) {
                    break; // 没有更多的文档，结束循环
                }
            }
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);
            ClearScrollResponse clearScrollResponse = client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getErrorName(byte[] certByte,byte[] LmdbByte){
        String errorName = null;
        if(LmdbByte.length>certByte.length){
            errorName = "ByteLoss";
        } else if (LmdbByte.length<certByte.length) {
            errorName = "ByteRedundancy";
        }
        else {
            logger.error("证书纠错判断错误，不存在比特冗余或者比特缺失的情况");
        }
        return errorName;
    }

    // 更新索引中的列表字段，并根据特定条件检索文档
    public static void updateAndSearchDocuments(String certIndex, String SHA1, List<String> UserIDList, RestHighLevelClient client) throws IOException {
        // 构建更新请求
        XContentBuilder builder = XContentFactory.jsonBuilder();
        builder.startObject();
        builder.field("UserIDList", UserIDList);
        builder.endObject();

        // 执行更新操作
        UpdateByQueryRequest updateRequest = new UpdateByQueryRequest(certIndex);
        updateRequest.setQuery(QueryBuilders.termQuery("ASN1SHA1", SHA1));
        updateRequest.setScript(new Script(ScriptType.INLINE, "painless", "ctx._source.UserIDList = params.updatedList", Collections.singletonMap("updatedList", UserIDList)));

        BulkByScrollResponse response = client.updateByQuery(updateRequest, RequestOptions.DEFAULT);

        logger.info("更新SHA1为——{}——的证书的用户ID返回结果——{}——",SHA1,response.toString());
    }

    public static Long get_cert_signNum(X509Cert x509Cert, RestHighLevelClient ESClient) throws IOException {
        // 当前证书的SAH1值在ES中证书的父证书列表中，是间接签发关系，还是直接签发关系
        String SHA1 = x509Cert.getASN1SHA1();
        // 构建查询请求，三个索引一起查询肯定会有重复，实际值更小
        SearchRequest searchRequest = new SearchRequest("cert_user,cert_scan,cert_pushdata");
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(QueryBuilders.termQuery("FatherCertID", SHA1));
        sourceBuilder.size(0); // 设置size为0，表示不返回具体数据
        sourceBuilder.trackTotalHits(true); // 设置track_total_hits为true，以获取匹配文档的总数
        searchRequest.source(sourceBuilder);

        // 执行查询
        SearchResponse searchResponse = ESClient.search(searchRequest, RequestOptions.DEFAULT);

        // 获取匹配文档数量和总数
        long totalHits = searchResponse.getHits().getTotalHits().value;
        return totalHits;
    }

}
