package com.geeksec.utils;

import java.util.*;
import java.util.stream.Collectors;
import org.apache.commons.codec.digest.DigestUtils;

/**
 * <AUTHOR>
 * 针对证书提取过程中subject，issuer内部字段乱序的统一.
 * 首先检查两个字符串是否都在NAME_LIST_ORDER中，如果是，则按照索引顺序进行比较。如果只有一个在NAME_LIST_ORDER中，则它排在前面。如果两个都不在NAME_LIST_ORDER中，
 * 则认为是OID或未知字符串，我们通过compareOidsOrUnknown方法来比较它们。在compareOidsOrUnknown方法中，我们首先检查两个字符串是否都是数字OID。
 * 如果一个是数字OID而另一个不是，则数字OID排在前面。如果两个都不是数字OID，则按照字典序比较。如果两个都是数字OID，则通过compareNumericOids方法进行比较。
 * compareNumericOids方法首先比较OID的段数，如果不同，则段数少的排在前面。如果段数相同，则按照字典序比较每个段的值。
 * (一般来说，不会出现非已知字段或者非OID格式的数据)
 */
public class NameOidSort {

    private static final List<String> NAME_LIST_ORDER = Arrays.asList(
            "CN", "C", "L", "ST", "STREET_ADDRESS", "O", "OU", "SERIAL_NUMBER",
            "SURNAME", "GIVEN_NAME", "TITLE", "GENERATION_QUALIFIER", "X500_UNIQUE_IDENTIFIER", "DN_QUALIFIER", "PSEUDONYM", "USER_ID",
            "DOMAIN_COMPONENT", "EMAIL_ADDRESS", "JURISDICTION_COUNTRY_NAME", "JURISDICTION_LOCALITY_NAME", "JURISDICTION_STATE_OR_PROVINCE_NAME",
            "BUSINESS_CATEGORY", "POSTAL_ADDRESS", "POSTAL_CODE", "INN", "OGRN", "SNILS", "UNSTRUCTURED_NAME"
    );

    public static String sortMapByKey(Map<String, String> map) {
        Comparator<String> customComparator = (o1, o2) -> {
            int index1 = NAME_LIST_ORDER.indexOf(o1);
            int index2 = NAME_LIST_ORDER.indexOf(o2);

            if (index1 != -1 && index2 != -1) {
                return Integer.compare(index1, index2);
            } else if (index1 != -1) {
                return -1;
            } else if (index2 != -1) {
                return 1;
            } else {
                // Both are OIDs or unknown, handle them
                return compareOidsOrUnknown(o1, o2);
            }
        };

        return map.entrySet().stream()
                .sorted(Map.Entry.comparingByKey(customComparator))
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining(", "));
    }

    private static int compareOidsOrUnknown(String oid1, String oid2) {
        String[] parts1 = oid1.split("\\.");
        String[] parts2 = oid2.split("\\.");

        // Compare the number of parts
        if (parts1.length != parts2.length) {
            return Integer.compare(parts1.length, parts2.length);
        }

        // Compare each part
        for (int i = 0; i < parts1.length; i++) {
            // If the part is numeric, compare as integers, otherwise compare as strings
            try {
                int partCompare = Integer.compare(Integer.parseInt(parts1[i]), Integer.parseInt(parts2[i]));
                if (partCompare != 0) {
                    return partCompare;
                }
            } catch (NumberFormatException e) {
                return parts1[i].compareTo(parts2[i]);
            }
        }

        // OIDs are equal
        return 0;
    }

    public static void main(String[] args) {
        Map<String, String> map = new HashMap<>();
        map.put("ST", "California");
        map.put("CN", "cn");
        map.put("EMAIL_ADDRESS", "<EMAIL>");
        map.put("POSTAL_CODE", "94105");
        map.put("OU", "Tech Solutions");
        map.put("POSTAL_ADDRESS", "Future St");
        map.put("EMAIL_ADDRESS", "***************");
        map.put("CN", "Value8");
        map.put("L", "San Francisco");
        map.put("O", "comodo");
        map.put("STREET_ADDRESS", "Innovation Drive");


        String sortedString = sortMapByKey(map);
        System.out.println(sortedString);
        System.out.println(DigestUtils.md5Hex(sortedString));
    }
}