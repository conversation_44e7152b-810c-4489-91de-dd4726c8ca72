package com.geeksec.utils;

import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixList;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixListFactory;
import java.net.URL;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;

/**
 * <AUTHOR>
 * @Description：
 */
public class DomainUtils {
    /**
     *  正则表达式用于匹配域名
    * */
    private static final String DOMAIN_REGEX = "^(\\*\\.)?[a-zA-Z0-9-]{1,255}(\\.[a-zA-Z0-9-]{1,255})*\\.[a-zA-Z]{2,}$";

    /**
     * 正则表达式用于匹配URL
     * */
    private static final String URL_REGEX = "^(https?|ftp):\\/\\/" +
            "((([a-zA-Z0-9\\-]+\\.)+[a-zA-Z]{2,}|(\\d{1,3}\\.){3}\\d{1,3}|[a-zA-Z0-9\\-]+)(:\\d+)?)" +
            "(\\/[^\\s]*)?" +
            "(\\?[^\\s#]*)?" +
            "(#[^\\s]*)?$";

    public static void main(String[] args) {
        String urlString = "http://crl.globalsign.com/gsorganizationvalsha2g3.crl";
        if (isURL(urlString)) {
            // 解析URL并提取域名或IP
            try {
                URL url = new URL(urlString);
                String host = url.getHost();
                if(IpUtils.isIpAddress(host)){
                    System.out.println("是合法IP: " + host);
                }else {
                    System.out.println("不是合法IP: " + host);
                }
                if(isDomain(host)){
                    System.out.println("是合法Domain: " + host);
                }else {
                    System.out.println("不是是合法Domain: " + host);
                }

            } catch (Exception e) {
                System.out.println("Invalid URL: " + e.getMessage());
            }
        } else {
            System.out.println("The string is not a valid URL.");
        }

        String domain = "*.crl.globalsign.com";
        if(isDomain(domain)){
            System.out.println("是合法Domain: " + domain);
        }else {
            System.out.println("不是合法Domain: " + domain);
        }

        String domain1 = "p209-keyvalueservice.icloud.com.cn";
        PublicSuffixListFactory factory = new PublicSuffixListFactory();
        Properties properties = factory.getDefaults();
        properties.setProperty(PublicSuffixListFactory.PROPERTY_LIST_FILE, "/effective_tld_names.dat");
        PublicSuffixList suffixList = factory.build();
        String fdomain = suffixList.getRegistrableDomain(domain1);
        System.out.println("fdomain："+fdomain);
    }

    public static boolean isURL(String urlString) {
        Pattern pattern = Pattern.compile(URL_REGEX);
        Matcher matcher = pattern.matcher(urlString);
        return matcher.matches();
    }

    public static boolean isDomain(String domain) {
        Pattern pattern = Pattern.compile(DOMAIN_REGEX);
        Matcher matcher = pattern.matcher(domain);
        return matcher.matches();
    }


    /**
     * 判断当前域名是否有效
     * @param domainAddr
     * @return
     */
    public static Boolean isValidDomain(String domainAddr){
        // 判断是否为空
        if (StringUtil.isNullOrEmpty(domainAddr)){
            return false;
        }
        // 判断是否为IP类型
        if (IpUtils.isIpAddress(domainAddr)){
            return false;
        }
        // 判断是否满足Domain格式
        if (!isDomain(domainAddr)){
            return false;
        }
        // 判断是否为PTR请求
        String[] domainItems = domainAddr.split("\\.");
        String suffix = domainItems[domainItems.length - 1];
        if (suffix.toLowerCase().equals("arpa")){
            return false;
        }
        return true;
    }
}


