package com.geeksec.dbConnect.redis;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.Response;
import redis.clients.jedis.params.SetParams;

/**
 * @author: KEVIN.WANG
 * @createTime: 2022/9/18 17:36
 * @document:
 **/
public class RedisClient {

    /**
     * redis client
     */
    private Jedis jedis;

    /**
     * 设置过期时间为1天
     */
    private static SetParams params = new SetParams();

    static {
        params.ex(24 * 60 * 60);
    }

    public RedisClient(String host, Integer port) {
        jedis = new Jedis(host, port);
    }

    /**
     * 批量查询证书
     *
     * @param keys redis key list.
     * @return cert data.
     */
    public Map<String, String> queryBatchCert(List<String> keys) {
        HashMap<String, String> resultMap = new HashMap<>();
        HashMap<String, Response<String>> tmpMap = new HashMap<>(keys.size());
        Pipeline pipelined = jedis.pipelined();
        // 生成批量查询pipeline并查询
        for (String key : keys) {
            tmpMap.put(key, pipelined.get("CERT_" + key));
        }
        pipelined.sync();

        // 对返回数据进行查询
        Set<Map.Entry<String, Response<String>>> entries = tmpMap.entrySet();
        for (Map.Entry<String, Response<String>> entry : entries) {
            String certId = entry.getKey();
            String certBase64 = entry.getValue().get();
            if (StringUtils.isEmpty(certBase64)) {
                continue;
            }
            resultMap.put(certId, certBase64);
        }

        return resultMap;
    }

    /**
     * 批量写入证书
     *
     * @param writeData 待写入的数据
     */
    public void writeBatchCert(Map<String, String> writeData) {
        Pipeline pipeline = jedis.pipelined();
        Set<Map.Entry<String, String>> entries = writeData.entrySet();
        for (Map.Entry<String, String> entry : entries) {
            String key = entry.getKey();
            String value = entry.getValue();
            pipeline.set("CERT_" + key, value, params);
        }
        pipeline.sync();
    }

    /**
     * 关闭客户端
     */
    public void close() {
        if (!ObjectUtils.allNotNull(jedis)) {
            jedis.close();
        }
    }

}
