package com.geeksec.dbConnect.hbase;

import com.geeksec.utils.FileUtil;
import java.util.Properties;
import org.apache.commons.pool.BasePoolableObjectFactory;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
* <AUTHOR>
* @Date  2024/10/9
*/

public class HbasePoolFactory extends BasePoolableObjectFactory<Connection> {

    private static final Logger logger = LoggerFactory.getLogger(HbasePoolFactory.class);
    static Properties properties = FileUtil.getProperties("/config.properties");

    public static final String HBASE_QUORUM = properties.getOrDefault("hbase.quorum", "192.168.101.175").toString();
    public static final String HBASE_CLIENT_PORT = properties.getOrDefault("hbase.client-port", "2381").toString();
    public static final String HBASE_MASTER = properties.getOrDefault("hbase.master", "192.168.101.175:16010").toString();

    @Override
    public Connection makeObject() throws Exception {
        Connection connection = null;
        // Hadoop配置对象，用于创建HBase配置
        Configuration configuration = HBaseConfiguration.create();
        configuration.set("hbase.zookeeper.property.clientPort", HBASE_CLIENT_PORT);
        // 设置zookeeper的主机
        configuration.set("hbase.zookeeper.quorum", HBASE_QUORUM);
        configuration.set("hbase.master", HBASE_MASTER);
        try{
            connection = ConnectionFactory.createConnection(configuration);
        }catch (Exception e){
            logger.error("hbase客户端初始化失败");
        }
        return connection;
    }

    @Override
    public boolean validateObject(Connection connection) {
        return true;
    }

    @Override
    public void activateObject(Connection connection) throws Exception {
//        logger.info("对象被激活了"+restHighLevelClient);
    }

    @Override
    public void destroyObject(Connection connection) throws Exception {
//        logger.info("对象被销毁了"+restHighLevelClient);
    }

    @Override
    public void passivateObject(Connection connection) throws Exception {
//        logger.info("回收并进行钝化操作"+restHighLevelClient);
    }

    public static void main(String[] args) {
        Connection connection = null;
        // 连接HBase
        try {
            Configuration configuration = HBaseConfiguration.create();
            configuration.set("hbase.zookeeper.property.clientPort", HBASE_CLIENT_PORT);
            configuration.set("hbase.zookeeper.quorum", HBASE_QUORUM);// 设置zookeeper的主机
            configuration.set("hbase.master", HBASE_MASTER);
            connection = ConnectionFactory.createConnection(configuration);
            Admin admin = connection.getAdmin();
            Table table = connection.getTable(TableName.valueOf("CERT"));
            // 检查列族是否存在
            if (admin.getDescriptor(TableName.valueOf("CERT")).getColumnFamily(Bytes.toBytes("Labels"))!=null) {
                // 如果列族不存在，则创建列族
                admin.addColumnFamily(TableName.valueOf("CERT"), ColumnFamilyDescriptorBuilder.newBuilder(Bytes.toBytes("Labels")).build());
            }

//            // 准备Put操作
//            Put put = new Put(Bytes.toBytes("SHA1=a"));
//            put.addColumn(Bytes.toBytes("Labels"), Bytes.toBytes("your_column"), Bytes.toBytes("[1,2,3]"));
//
//            // 执行Put操作
//            table.put(put);

            System.out.println("操作完成");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
