package com.geeksec.dbConnect.PoolTest;

import com.geeksec.utils.MongodbUtils;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;

/**
 * <AUTHOR>
 * @Date 2023/12/22
 */

public class mongoTest {

    private static String mongoHost = "****************************************************";

//    private static transient GenericObjectPool<MongoClient> MongoPool = null;

//    public static void main(String[] args) throws Exception {
//
//        MongoPool = MongodbUtils.initMongoPool();
//        String OID = "*******.1.1";
//        MongoClient mongoClient = null;
//        try{
//            Long before = System.currentTimeMillis();
//            mongoClient = MongodbUtils.getClient(MongoPool);
//            Long after1 = System.currentTimeMillis();
//            System.out.println("时间:"+String.valueOf(after1-before));
//            String Description = MongodbUtils.getOIDDescription(OID,mongoClient);
//            Long after = System.currentTimeMillis();
//            System.out.println("时间:"+String.valueOf(after-before));
//            System.out.println("查询成功:"+Description);
//        }catch (Exception e){
//            System.out.println("报错:"+e);
//        }finally {
//            MongodbUtils.returnClient(mongoClient,MongoPool);
//        }
//
//        MongoPool.close();
//    }


        public static void main(String[] args) throws Exception {

//        MongoPool = MongodbUtils.initMongoPool();
        String OID = "*******.1.1";
        MongoClient mongoClient = null;
        try{
            mongoClient = MongoClients.create(mongoHost);
            Long before = System.currentTimeMillis();
            String Description = MongodbUtils.getOIDDescription(OID,mongoClient);
            Long after = System.currentTimeMillis();
            System.out.println("时间:"+String.valueOf(after-before));
            System.out.println("查询成功:"+Description);
        }catch (Exception e){
            System.out.println("报错:"+e);
        }
    }

}
