package com.geeksec.dbConnect.PoolTest;

import com.geeksec.utils.FileUtil;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import java.util.Properties;
import org.apache.commons.pool.BasePoolableObjectFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2023/12/18
 */

public class MongoPoolFactory extends BasePoolableObjectFactory<MongoClient> {
    private static Logger logger = LoggerFactory.getLogger(ESPoolFactory.class);

    Properties properties = FileUtil.getProperties("/config.properties");
    String mongoHost = properties.getOrDefault("mongo.database.host", "********************************************").toString();


    @Override
    public MongoClient makeObject(){
        MongoClient client = null;
        try{
            client = MongoClients.create(mongoHost);
        }catch (Exception e){
            e.printStackTrace();
        }
        return client;
    }

    @Override
    public boolean validateObject(MongoClient mongoClient) {
        return true;
    }

    @Override
    public void activateObject(MongoClient mongoClient) throws Exception {
//        logger.info("对象被激活了"+restHighLevelClient);
    }

    @Override
    public void destroyObject(MongoClient mongoClient) throws Exception {
//        logger.info("对象被销毁了"+restHighLevelClient);
    }

    @Override
    public void passivateObject(MongoClient mongoClient) throws Exception {
//        logger.info("回收并进行钝化操作"+restHighLevelClient);
    }
}
