package com.geeksec;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public enum SpecProtocolEnum {
    GODZILLA("29090", "哥斯拉"), SHARPYSHELL("29091", "SharPyShell"), WEEVELY("29092", "Weevely"), TIANXIE("29093", "天蝎"),
    JSPMASTER("29094", "jspmaster"), B374K("29095", "b374k"), CNKNIFE("29096", "中国菜刀"), XISE("29097", "Xise"),
    ANT_SWORD("29098", "蚁剑"), WEBACOO("29099", "WeBacoo"), WEBSHELL_SNIPER("29100", "Webshell-Sniper"), KAISHANFU("29101", "开山斧"),
    ALTMAN("29102", "<PERSON><PERSON>"), QUASIBOT("29103", "QuasiBot"), WEBSHELL_MANAGER("29104", "WebshellManager"), CKNIFE("29105", "cknife"),
    WEBKNIFE("29106", "WebKnife"), K8KNIFE("29107", "K8飞刀"), HATCHET("29108", "Hatchet"), XIAOLIKNIFE("29109", "小李飞刀"),
    PUPY("29110", "Pupy"), KOADIC("29111", "Koadic"), EMPIRE("29112", "Empire"), COBALT_STRIKE("29113", "CobaltStrike"),
    METASPLOIT("29114", "Metasploit"), MERLIN("29115", "merlin"), PYFUD("29116", "PyFUD"), QUASAR("29117", "Quasar"),
    W8AY("27120", "w8ay"), FinalShell("24231", "FinalShell"), Mobaxterm("24232", "Mobaxterm"), Putty("24233", "Putty"),
    SecureCRT("24234", "SecureCRT"), Xshell("24235", "Xshell"), XRKWQB("24122", "向日葵完全版"), XRKZK("24124", "向日葵-SOS 主控"),
    XRKBK("24125", "向日葵-SOS 被控"), TDJJ("24127", "ToDesk精简版"), Teamviewer("24128", "Teamviewer"), TDFZ("24129", "ToDesk辅助规则"),
    TDXZK("24130", "ToDesk正式版小IP主控"), TDDZK("24131", "ToDesk正式版大IP主控"),
    THCSSLDOS("27121", "THC-SSL-DOS"), REMCOS("26313", "Remcos"), GHOST("27127", "Ghost"), NANOCORE("27128", "NanoCore"),
    RINFO("28001", "Rinfo"), SMOKELOADER("28002", "SmokeLoader"), TOFSEE("28003", "Tofsee"), ZLOADER("28004", "Zloader"),
    QUKART("28005", "Qukart"), APT29("28006", "APT29"), APT32("28007", "APT32"), APT28("28008", "APT28"),
    APTC09("28009", "APTC09"), PHORPIEX("28010", "Phorpiex"), ANT_SWORD_PHP_RES("27079", "antSword_PHP自定义编码响应"), ANT_SWORD_PHP_REQ("27078", "antSword_PHP自定义编码请求"), ANT_SWORD_PHP_MID("27077", "antsword中间标签"),
    BEHANDER3("27074", "冰蝎3"), BEHANDER4("27075", "冰蝎4"), MINE_DOMAIN("9006", "Candidate Miner Domain"), MINE_IP("9008", "MinePool IP"),
    FINGER_SSL_MINE("14010", "挖矿软件指纹，NBMiner,unk_Miner,unknown_Miner"), UNK_FINGER("14011", "未知指纹，unk"), DRIDEX("27008", "Dridex"), EMOTET("20031", "Emotet"), QAKBOT("20032", "Qakbot"), HANCITOR("20033", "Hancitor"), TRICKBOT("20034", "Trickbot"), TROJAN_FINGER("14005", "木马指纹"), RAT_FINGER("14006", "恶意软件指纹"), PT_FINGER("14007", "渗透工具指纹"), TOR_FINGER("14008", "匿名通信指纹"), CRAW_FINGER("14009", "爬虫工具指纹"), TUNN_PROXY_FINGER("14014", "隧道代理指纹"), HACK_FINGER("14015", "黑客工具指纹"), WEB_LOG_BRUTE("21001", "Web登录暴破"), CDN_DOMAIN("9005", "CDN Domain"), CDN_IP("1008", "CDN IP"), NEOREG("26258", "Neoregeo"), TUNN_PROXY_SUO5("27073", "suo5代理隧道"), TUNN_ICMP("27123", "ICMP隐蔽隧道"), SPOOF_ICMP("27122", "伪造ICMP协议"), TUNN_TLS("27124", "TLS隐蔽隧道"), TUNN_NTP("27125", "NTP隐蔽隧道"), TUNN_TCP("27126", "TCP隐蔽隧道"), TUNN_HTTP("20016", "HTTP隐蔽隧道会话"), ICMP_PAYLOAD("27061", "ICMP白负载"), URCP("24332", "未知远程控制协议"), PIVOT("24333", "跳板节点"), RAT("24334", "远程控制攻击"), WEBSHELL_ENCRYPT("23501", "加密webshell连接"), WEBSHELL_UNENCRYPT("23502", "非加密webshell连接"), XRAY_SACN("27063", "Xray扫描器"), RDP_LOGIN_REQ("27064", "RDP登录请求"), RDP_LOGIN_RETURN("27065", "RDP登录返回"), SMB_LOGIN_REQ("27066", "SMB登录请求"), SMB_LOGIN_FAIL("27067", "SMB登陆失败"), ORACLE_CONNECT_REQ("27068", "Oracle连接请求"), ORACLE_LOGIN_FAIL("27069", "Oracle登陆失败"), MYSQL_LOGIN_FAIL("27071", "MySQL登陆失败"), DNS_TUNN_SERVER("1006", "DNS隧道服务器"), DNS_LEG_SERVER("10061", "合法DNS服务器"), DNS_ILLEG_SERVER("10062", "非法DNS服务器"), TUNN_DNS("29118", "DNS隐蔽隧道"), SPOOF_DNS("29119", "伪造DNS协议"), PORT_SCAN("3248", "端口扫描行为"), APP_SCAN("27062", "AppScan扫描器"), RANDOM_FINGER("1007", "指纹随机化服务端"), RANDOM_PASS_EXT("20056", "密码套件随机性"), RANDOM_ENCRYPT_EXT("20057", "加密扩展随机性"), ALG_RANDOM_ML("29120", "随机数对抗算法"), ALG_COMPRESS_ML("29121", "压缩函数对抗算法"), ALG_ENCODE_ML("29122", "编码对抗算法"), ALG_HIDDEN_ML("29123", "隐写对抗算法"), ALG_ADV_ML("29124", "行为对抗算法"), SPOOF_HTTP("27082", "伪造HTTP协议"), SPOOF_NTP("27083", "伪造NTP协议"), SPOOF_TLS("27083", "伪造TLS协议"), SPOOF_TCP_TUNN("27080", "疑似伪造TCP隐蔽信道"), SPOOF_TLS_TUNN("27081", "疑似伪造TLS隐蔽信道"), SRCP_C2("27089", "标准远程控制协议下的C2行为"), SUS_ACTIVATION("27088", "疑似激活行为"), SUS_CONTROL("27087", "疑似控制行为"), SUS_SERVER_HEARTBEAT("27086", "疑似服务端心跳"), SUS_CLIENT_HEARTBEAT("27085", "疑似客户端心跳"), ANYDESK("24132", "AnyDesk");

    private final String code;
    private final String name;

    SpecProtocolEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static SpecProtocolEnum fromCode(String code) {
        if (code == null) {
            log.error("Code is null");
            return null;
        }
        for (SpecProtocolEnum tool : SpecProtocolEnum.values()) {
            if (tool.getCode().equals(code)) {
                return tool;
            }
        }
        log.error("Invalid code for SpecProtocolEnum: {}", code);
        return null;
    }

    public static SpecProtocolEnum fromName(String name) {
        if (name == null) {
            log.error("Name is null");
            return null;
        }
        for (SpecProtocolEnum tool : SpecProtocolEnum.values()) {
            if (tool.getName().equals(name)) {
                return tool;
            }
        }
        log.error("Invalid name for SpecProtocolEnum: {}", name);
        return null;
    }
}
