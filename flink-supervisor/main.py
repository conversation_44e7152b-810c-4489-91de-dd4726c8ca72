import hashlib
import json
import logging
import os
import subprocess
import time
import traceback
from typing import Tuple, Optional, List, Dict

import requests
import schedule
import yaml
from requests.exceptions import ConnectionError, HTTPError, Timeout

# 配置日志格式
logging.basicConfig(
        format='%(asctime)s %(levelname)-8s %(message)s',
        level=logging.INFO,
        datefmt='%Y-%m-%d %H:%M:%S')

# 配置常量
PATHS = {
    'task_yaml': "/opt/flink-supervisor/config/task.yaml",
    'jar_base': "/opt/flink-supervisor/jobs",
    'docker_compose': "/opt/flink-supervisor/config/docker/docker-compose.yml"
}
FLINK_URL = "http://jobmanager:8081"
FLINK_SERVICES = ["jobmanager", "taskmanager"]
HTTP_TIMEOUT = 5
WAIT_TIMES = {
    'service_restart': 10,  # 服务重启间隔
    'cluster_stabilize': 40,  # 集群稳定等待时间
    'health_check_retry': 20  # 健康检查重试间隔
}

def calculate_md5(file_path: str) -> str:
    """计算文件的MD5值"""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        logging.error(f"Failed to calculate MD5 for {file_path}: {e}")
        return ""

def health_check_http(url: str, timeout: int = HTTP_TIMEOUT) -> Tuple[bool, Optional[requests.Response]]:
    """HTTP健康检查"""
    try:
        response = requests.get(url, timeout=timeout)
        response.raise_for_status()
        logging.info(f"URL {url} is healthy")
        return True, response
    except Timeout:
        logging.error(f"URL {url} is unhealthy: Timeout")
    except ConnectionError:
        logging.error(f"URL {url} is unhealthy: ConnectionError")
    except HTTPError as e:
        logging.error(f"URL {url} is unhealthy: HTTPError {e.response.status_code}")
    except Exception as e:
        logging.error(f"URL {url} is unhealthy: {str(e)}")
    return False, None

def execute_docker_compose(command: List[str]) -> bool:
    """执行docker compose命令"""
    try:
        base_cmd = ["docker", "compose", "-f", PATHS['docker_compose']]
        result = subprocess.run(
            base_cmd + command,
            check=True,
            capture_output=True,
            text=True
        )
        return True
    except subprocess.CalledProcessError as e:
        logging.error(f"Docker compose command failed: {e.stderr}")
        return False
    except Exception as e:
        logging.error(f"Failed to execute docker compose command: {str(e)}")
        return False

def recreate_service(service_name: str) -> bool:
    """重建指定服务"""
    logging.info(f"Recreating service: {service_name}")
    
    # 停止并删除容器
    if not execute_docker_compose(["rm", "-sf", service_name]):
        return False
        
    # 创建并启动新容器
    if not execute_docker_compose(["up", "-d", service_name]):
        return False
        
    logging.info(f"Successfully recreated {service_name}")
    return True

def wait_flink_cluster_ready(max_retry: int = 5) -> bool:
    """等待Flink集群就绪"""
    for retry_count in range(max_retry):
        status, _ = health_check_http(FLINK_URL)
        if status:
            logging.info("Flink cluster is ready")
            return True
            
        if retry_count < max_retry - 1:
            logging.warning(f"Flink cluster not ready, retry {retry_count + 1}/{max_retry}")
            time.sleep(WAIT_TIMES['health_check_retry'])
            
    logging.error("Flink cluster failed to become ready after maximum retries")
    return False

def check_flink():
    """检查Flink集群状态"""
    status, response = health_check_http(f"{FLINK_URL}/overview")
    if not status:
        logging.info("Flink unhealthy, restarting cluster")
        restart_flink()
        return

    try:
        infos = response.json()
        if infos["taskmanagers"] == 0 or infos["slots-available"] == 0:
            logging.info("Flink taskmanager is down, restarting cluster")
            restart_flink()
        else:
            logging.info("Flink cluster is healthy")
    except Exception as e:
        logging.error(f"Failed to parse Flink status: {e}")
        restart_flink()

def restart_flink() -> bool:
    """重启Flink集群"""
    logging.info("Restarting Flink services")
    
    for service in FLINK_SERVICES:
        if not recreate_service(service):
            logging.error(f"Failed to recreate {service}, aborting Flink restart")
            return False
        time.sleep(WAIT_TIMES['service_restart'])
    
    time.sleep(WAIT_TIMES['cluster_stabilize'])
    
    if wait_flink_cluster_ready():
        return start_flink_jobs()
    return False

def cancel_flink_job(job_id: str, job_name: str):
    """取消Flink作业"""
    cancel_url = f'{FLINK_URL}/jobs/{job_id}/yarn-cancel'
    try:
        logging.info(f"Canceling Flink job: {job_name} (ID: {job_id})")
        requests.get(cancel_url, timeout=HTTP_TIMEOUT)
    except Exception as e:
        logging.error(f"Failed to cancel job {job_name}: {e}")

def parse_job_config(jar_config: Dict) -> Dict:
    """解析单个作业配置"""
    jar = jar_config['jar']
    jar_name = jar["jar_name"]
    job_name = jar.get("task_name")
    
    # 解析任务代码
    job_code = None
    if job_name:
        idx = job_name.find("-")
        if idx != -1:
            job_code = job_name[:idx]
    
    return {
        "jar_name": jar_name,
        "jar_md5": jar["jar_md5"],
        "task_name": job_name,
        "job_code": job_code,
        "entryClass": jar["entryClass"],
        "parallelism": jar["parallelism"],
        "programArgs": jar["programArgs"],
        "savepointPath": jar["savepointPath"],
        "allowNonRestoredState": jar["allowNonRestoredState"]
    }

def get_running_jobs() -> Tuple[set, List[Dict]]:
    """获取正在运行的作业信息"""
    running_jobs = set()
    running_jar_list = []
    
    try:
        response = requests.get(f"{FLINK_URL}/jobs/overview", timeout=HTTP_TIMEOUT)
        jobs = response.json()["jobs"]
        
        for job in jobs:
            job_id = job["jid"]
            state = job["state"].lower()
            job_name = job["name"]

            # 取消异常状态的作业
            if state not in ["running", "canceled"]:
                cancel_flink_job(job_id, job_name)
                continue

            # 解析作业代码
            job_code = None
            idx = job_name.find("-")
            if idx != -1:
                job_code = job_name[:idx]

            # 处理运行中的作业
            if job_code and state == "running":
                if job_code in running_jobs:
                    logging.info(f"Found duplicate job with code: {job_code}, cancel it...")
                    cancel_flink_job(job_id, job_name)
                else:
                    running_jobs.add(job_code)
                    
        return running_jobs, running_jar_list
    except Exception as e:
        logging.error(f"Failed to get running jobs: {e}")
        return set(), []

def scan_jar_files() -> Dict:
    """扫描文件系统中的JAR文件"""
    jar_file_dict = {}
    md5_set = set()
    
    logging.info("Scanning flink jar files in filesystem")
    for root, _, files in os.walk(PATHS['jar_base']):
        for file_name in files:
            if file_name.endswith(".jar"):
                jar_id = file_name
                jar_file_path = os.path.join(root, jar_id)
                md5_value = calculate_md5(jar_file_path)
                
                if not md5_value or md5_value in md5_set:
                    continue
                    
                md5_set.add(md5_value)
                logging.info(f"jar_id: {jar_id} md5 hash: {md5_value}")
                jar_file_dict[jar_id] = {
                    "jar_id": jar_id,
                    "jar_md5": md5_value
                }
    
    return jar_file_dict

def get_flink_jars() -> List[Dict]:
    """获取Flink集群中的JAR列表"""
    try:
        response = requests.get(f"{FLINK_URL}/jars/", timeout=HTTP_TIMEOUT)
        return response.json()["files"]
    except Exception as e:
        logging.error(f"Failed to get Flink jars: {e}")
        return []

def submit_jar_job(jar_info: Dict) -> bool:
    """提交单个JAR作业"""
    try:
        jar_id = jar_info["jar_id"]
        entry_class = jar_info["entryClass"]
        start_url = f"{FLINK_URL}/jars/{jar_id}/run?entry-class={entry_class}"
        
        post_data = {
            "entryClass": entry_class,
            "parallelism": jar_info["parallelism"],
            "programArgs": jar_info["programArgs"],
            "savepointPath": jar_info["savepointPath"],
            "allowNonRestoredState": jar_info["allowNonRestoredState"]
        }
        
        logging.info(f"Starting jar task {jar_id} with entry class {entry_class}...")
        response = requests.post(start_url, data=json.dumps(post_data), timeout=HTTP_TIMEOUT)
        response.raise_for_status()
        
        time.sleep(10)  # 等待作业启动
        return True
    except Exception as e:
        logging.error(f"Failed to submit jar job {jar_id}: {e}")
        return False

def start_flink_jobs() -> bool:
    """启动Flink作业"""
    try:
        # 读取配置文件
        with open(PATHS['task_yaml'], 'r') as f:
            config = yaml.safe_load(f)
        
        # 解析作业配置
        jar_job_dict = {}
        task_jar_mapping = {}
        for jar_config in config['FlinkJarWatch']['jars']:
            job_info = parse_job_config(jar_config)
            jar_name = job_info["jar_name"]
            job_code = job_info["job_code"]
            
            jar_job_dict[jar_name] = job_info
            if job_code:
                task_jar_mapping[job_code] = jar_name

        # 获取运行中的作业
        running_jobs, running_jar_list = get_running_jobs()

        # 检查是否需要启动新作业
        need_start = False
        for job_code in task_jar_mapping:
            if job_code in running_jobs:
                logging.info(f"Flink job with code: {job_code} is running, skip...")
                running_jar_list.append(task_jar_mapping[job_code])
            else:
                need_start = True
                logging.info(f"Flink job with code: {job_code} is not running, start it later!")

        if not need_start:
            return True

        # 扫描本地JAR文件
        jar_file_dict = scan_jar_files()

        # 获取Flink集群中的JAR列表
        jar_flink_list = get_flink_jars()
        
        # 准备需要运行的JAR列表
        run_jar_list = []
        for jar_info in jar_flink_list:
            jar_id = jar_info["id"]
            jar_name = jar_info["name"]
            
            if jar_id not in jar_file_dict:
                continue
                
            jar_file_dict[jar_id]["jar_name"] = jar_name
            if jar_name in jar_job_dict:
                jar_task_md5 = jar_job_dict[jar_name]["jar_md5"]
                if jar_file_dict[jar_id]["jar_md5"] == jar_task_md5:
                    run_jar = jar_file_dict[jar_id]
                    run_jar.update(jar_job_dict[jar_name])
                    if jar_name not in running_jar_list:
                        run_jar_list.append(run_jar)

        # 提交作业
        for run_jar in run_jar_list:
            if not submit_jar_job(run_jar):
                logging.error(f"Failed to submit jar: {run_jar['jar_name']}")

        return True
    except Exception as e:
        logging.error(f"Failed to start Flink jobs: {e}")
        logging.error(traceback.format_exc())
        return False

def check_and_start_flink_jobs():
    """检查并启动Flink作业"""
    logging.info("Checking and starting Flink jobs")
    if wait_flink_cluster_ready():
        start_flink_jobs()

def main():
    """主函数"""
    logging.info("Starting Flink Supervisor")
    
    # 初始检查
    check_flink()
    check_and_start_flink_jobs()
    
    # 定时任务
    schedule.every(30).minutes.do(check_flink)
    schedule.every().hour.do(check_and_start_flink_jobs)

    # 主循环
    while True:
        try:
            schedule.run_pending()
            time.sleep(1)
        except Exception as e:
            logging.error(f"Error in main loop: {e}")
            time.sleep(5)  # 发生错误时等待一段时间再继续

if __name__ == '__main__':
    main()