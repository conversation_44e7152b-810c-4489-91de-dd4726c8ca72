
import request from '@/utils/request';
/* ---------------------------- 以下为证书详情相关接口 ----------------------------------- */
// 查询标签选择列表(全量/列表)
export function getTag(data) {
  return request({
    url: '/cert/tag/cert/search',
    method: 'POST',
    data
  });
}

// 添加证书标签
export function addTag(data) {
  return request({
    url: '/cert/tag/cert/add',
    method: 'POST',
    data
  });
}

// 证书详情
export function getCertDetail(params) {
  return request({
    url: `/cert/detail`,
    method: 'get',
    params
  });
}

// 查询错误证书详情
export function getErrorDetail(params) {
  return request({
    url: `/cert/error/detail`,
    method: 'get',
    params
  });
}

// 证书标签修改
export function modifyLabels(data) {
  return request({
    url: `/cert/modify/labels`,
    method: 'PUT',
    data
  });
}

// 证书备注修改
export function certRemark(data) {
  return request({
    url: `/cert/remark`,
    method: 'PUT',
    data,
  });
}

// 单个证书下载
export function certDownload(data) {
  return request({
    url: `/cert/download`,
    method: 'POST',
    data,
    responseType: "blob",
  });
}
/* ---------------------------- 以下为证书文件导入相关接口 ----------------------------------- */
// 证书导入进度&状态
export function getStatus() {
  return request({
    url: '/cert/cert/file/status',
    method: 'get',
  });
}

// 查询上传证书文件列表
export function getFileList(data) {
  return request({
    url: '/cert/cert/file/list',
    method: 'POST',
    data
  });
}

// 导入证书
export function getFileUpload(data) {
  return request({
    url: '/cert/cert/file/upload',
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}


// 查询上传证书文件列表
export function getLogTemplate(params) {
  return request({
    url: '/cert/log/template',
    method: 'GET',
    params
  });
}

// 查询上传证书文件列表
export function editLogTemplate(data) {
  return request({
    url: '/cert/log/template',
    method: 'PUT',
    data,
  });
}

// 单个证书下载
export function certLogDownload(data) {
  return request({
    url: `/cert/log/download`,
    method: 'POST',
    data,
    responseType: "blob",
  });
}
