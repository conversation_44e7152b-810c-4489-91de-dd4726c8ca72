import request from '@/utils/request';

// 任务的基本数据
export function task_info(data) {
  return request({
    url: '/task/config/info',
    method: 'get',
    params: data
  });
}

// 改变任务状态或者网口状态
export function task_put(data) {
  return request({
    url: '/task/config/analysis',
    method: 'PUT',
    data
  });
}

// 修改任务配置
export function config_edit(data) {
  return request({
    url: '/task/config/edit',
    method: 'POST',
    data
  });
}

// 查询探针同步进度
export function tz_check(data) {
  return request({
    url: '/task/config/analysis/code',
    method: 'GET',
    params: data
  });
}
